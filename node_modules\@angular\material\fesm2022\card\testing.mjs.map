{"version": 3, "file": "testing.mjs", "sources": ["../../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/card/testing/card-harness.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  ComponentHarnessConstructor,\n  ContentContainerComponentHarness,\n  HarnessPredicate,\n} from '@angular/cdk/testing';\nimport {CardHarnessFilters} from './card-harness-filters';\n\n/** Selectors for different sections of the mat-card that can container user content. */\nexport enum MatCardSection {\n  HEADER = '.mat-mdc-card-header',\n  CONTENT = '.mat-mdc-card-content',\n  ACTIONS = '.mat-mdc-card-actions',\n  FOOTER = '.mat-mdc-card-footer',\n}\n\n/** Harness for interacting with a mat-card in tests. */\nexport class MatCardHarness extends ContentContainerComponentHarness<MatCardSection> {\n  /** The selector for the host element of a `MatCard` instance. */\n  static hostSelector = '.mat-mdc-card';\n\n  /**\n   * Gets a `HarnessPredicate` that can be used to search for a card with specific attributes.\n   * @param options Options for filtering which card instances are considered a match.\n   * @return a `HarnessPredicate` configured with the given options.\n   */\n  static with<T extends MatCardHarness>(\n    this: ComponentHarnessConstructor<T>,\n    options: CardHarnessFilters = {},\n  ): HarnessPredicate<T> {\n    return new HarnessPredicate(this, options)\n      .addOption('text', options.text, (harness, text) =>\n        HarnessPredicate.stringMatches(harness.getText(), text),\n      )\n      .addOption('title', options.title, (harness, title) =>\n        HarnessPredicate.stringMatches(harness.getTitleText(), title),\n      )\n      .addOption('subtitle', options.subtitle, (harness, subtitle) =>\n        HarnessPredicate.stringMatches(harness.getSubtitleText(), subtitle),\n      );\n  }\n\n  private _title = this.locatorForOptional('.mat-mdc-card-title');\n  private _subtitle = this.locatorForOptional('.mat-mdc-card-subtitle');\n\n  /** Gets all of the card's content as text. */\n  async getText(): Promise<string> {\n    return (await this.host()).text();\n  }\n\n  /** Gets the cards's title text. */\n  async getTitleText(): Promise<string> {\n    return (await this._title())?.text() ?? '';\n  }\n\n  /** Gets the cards's subtitle text. */\n  async getSubtitleText(): Promise<string> {\n    return (await this._subtitle())?.text() ?? '';\n  }\n}\n"], "names": [], "mappings": ";;AAeA;IACY;AAAZ,CAAA,UAAY,cAAc,EAAA;AACxB,IAAA,cAAA,CAAA,QAAA,CAAA,GAAA,sBAA+B;AAC/B,IAAA,cAAA,CAAA,SAAA,CAAA,GAAA,uBAAiC;AACjC,IAAA,cAAA,CAAA,SAAA,CAAA,GAAA,uBAAiC;AACjC,IAAA,cAAA,CAAA,QAAA,CAAA,GAAA,sBAA+B;AACjC,CAAC,EALW,cAAc,KAAd,cAAc,GAKzB,EAAA,CAAA,CAAA;AAED;AACM,MAAO,cAAe,SAAQ,gCAAgD,CAAA;;AAElF,IAAA,OAAO,YAAY,GAAG,eAAe;AAErC;;;;AAIG;AACH,IAAA,OAAO,IAAI,CAET,OAAA,GAA8B,EAAE,EAAA;AAEhC,QAAA,OAAO,IAAI,gBAAgB,CAAC,IAAI,EAAE,OAAO;aACtC,SAAS,CAAC,MAAM,EAAE,OAAO,CAAC,IAAI,EAAE,CAAC,OAAO,EAAE,IAAI,KAC7C,gBAAgB,CAAC,aAAa,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,IAAI,CAAC;aAExD,SAAS,CAAC,OAAO,EAAE,OAAO,CAAC,KAAK,EAAE,CAAC,OAAO,EAAE,KAAK,KAChD,gBAAgB,CAAC,aAAa,CAAC,OAAO,CAAC,YAAY,EAAE,EAAE,KAAK,CAAC;aAE9D,SAAS,CAAC,UAAU,EAAE,OAAO,CAAC,QAAQ,EAAE,CAAC,OAAO,EAAE,QAAQ,KACzD,gBAAgB,CAAC,aAAa,CAAC,OAAO,CAAC,eAAe,EAAE,EAAE,QAAQ,CAAC,CACpE;;AAGG,IAAA,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,qBAAqB,CAAC;AACvD,IAAA,SAAS,GAAG,IAAI,CAAC,kBAAkB,CAAC,wBAAwB,CAAC;;AAGrE,IAAA,MAAM,OAAO,GAAA;QACX,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,IAAI,EAAE;;;AAInC,IAAA,MAAM,YAAY,GAAA;AAChB,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,EAAE,IAAI,EAAE;;;AAI5C,IAAA,MAAM,eAAe,GAAA;AACnB,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,SAAS,EAAE,GAAG,IAAI,EAAE,IAAI,EAAE;;;;;;"}