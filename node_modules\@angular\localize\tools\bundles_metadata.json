{"inputs": {"bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/diagnostics.js": {"bytes": 1396, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/src/utils/src/constants.js": {"bytes": 1886, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler/src/i18n/digest.mjs": {"bytes": 39585, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/src/utils/src/messages.js": {"bytes": 7507, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler/src/i18n/digest.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/src/utils/src/constants.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/src/utils/src/translations.js": {"bytes": 4915, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/src/utils/src/constants.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/src/utils/src/messages.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/src/utils/index.js": {"bytes": 337, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/src/utils/src/constants.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/src/utils/src/messages.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/src/utils/src/translations.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/src/translate.js": {"bytes": 3143, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/src/utils/index.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/src/localize/src/localize.js": {"bytes": 5267, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/src/utils/index.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/src/localize/index.js": {"bytes": 280, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/src/localize/src/localize.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/private.js": {"bytes": 887, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/src/localize/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/src/utils/index.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/index.js": {"bytes": 381, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/src/translate.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/private.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/source_file_utils.js": {"bytes": 15981, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/index.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/duplicates.js": {"bytes": 1798, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/diagnostics.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/source_file_utils.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/source_files/es2015_extract_plugin.js": {"bytes": 1166, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/source_file_utils.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/source_files/es5_extract_plugin.js": {"bytes": 1892, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/source_file_utils.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/extraction.js": {"bytes": 5017, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/source_files/es2015_extract_plugin.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/source_files/es5_extract_plugin.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/translation_files/utils.js": {"bytes": 2283, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/translation_files/arb_translation_serializer.js": {"bytes": 2929, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/translation_files/utils.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/translation_files/json_translation_serializer.js": {"bytes": 777, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/translation_files/utils.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/translation_files/legacy_message_id_migration_serializer.js": {"bytes": 1465, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/translation_files/format_options.js": {"bytes": 1515, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/translation_files/icu_parsing.js": {"bytes": 6830, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/translation_files/xml_file.js": {"bytes": 2452, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/translation_files/xliff1_translation_serializer.js": {"bytes": 8444, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/translation_files/format_options.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/translation_files/icu_parsing.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/translation_files/utils.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/translation_files/xml_file.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/translation_files/xliff2_translation_serializer.js": {"bytes": 8717, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/translation_files/format_options.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/translation_files/icu_parsing.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/translation_files/utils.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/translation_files/xml_file.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/translation_files/xmb_translation_serializer.js": {"bytes": 5023, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/translation_files/icu_parsing.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/translation_files/utils.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/translation_files/xml_file.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/source_files/es2015_translate_plugin.js": {"bytes": 2007, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/source_file_utils.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/source_files/es5_translate_plugin.js": {"bytes": 1847, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/source_file_utils.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/source_files/locale_plugin.js": {"bytes": 3916, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/source_file_utils.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/translation_parsers/arb_translation_parser.js": {"bytes": 2351, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/diagnostics.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/translation_parsers/simple_json_translation_parser.js": {"bytes": 2758, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/diagnostics.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/base_visitor.js": {"bytes": 630, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/translation_parsers/translation_parse_error.js": {"bytes": 963, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/translation_parsers/translation_utils.js": {"bytes": 5219, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/diagnostics.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/translation_parsers/translation_parse_error.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/message_serialization/message_serializer.js": {"bytes": 3350, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/base_visitor.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/translation_parsers/translation_parse_error.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/translation_parsers/translation_utils.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/message_serialization/target_message_renderer.js": {"bytes": 1717, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/index.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/translation_parsers/serialize_translation_message.js": {"bytes": 844, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/message_serialization/message_serializer.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/message_serialization/target_message_renderer.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/translation_parsers/translation_utils.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/translation_parsers/xliff1_translation_parser.js": {"bytes": 4953, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/diagnostics.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/base_visitor.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/translation_parsers/serialize_translation_message.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/translation_parsers/translation_utils.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/translation_parsers/xliff2_translation_parser.js": {"bytes": 5056, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/diagnostics.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/base_visitor.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/translation_parsers/serialize_translation_message.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/translation_parsers/translation_utils.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/translation_parsers/xtb_translation_parser.js": {"bytes": 3946, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/diagnostics.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/base_visitor.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/translation_parsers/serialize_translation_message.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/translation_parsers/translation_utils.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/index.js": {"bytes": 2469, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/diagnostics.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/duplicates.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/extraction.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/translation_files/arb_translation_serializer.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/translation_files/json_translation_serializer.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/translation_files/legacy_message_id_migration_serializer.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/translation_files/xliff1_translation_serializer.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/translation_files/xliff2_translation_serializer.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/translation_files/xmb_translation_serializer.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/source_file_utils.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/source_files/es2015_translate_plugin.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/source_files/es5_translate_plugin.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/source_files/locale_plugin.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/translation_parsers/arb_translation_parser.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/translation_parsers/simple_json_translation_parser.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/translation_parsers/xliff1_translation_parser.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/translation_parsers/xliff2_translation_parser.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/translation_parsers/xtb_translation_parser.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/index.js": {"bytes": 2851, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/duplicates.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/extraction.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/translation_files/arb_translation_serializer.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/translation_files/json_translation_serializer.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/translation_files/legacy_message_id_migration_serializer.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/translation_files/xliff1_translation_serializer.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/translation_files/xliff2_translation_serializer.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/translation_files/xmb_translation_serializer.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/cli.js": {"bytes": 3758, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/translation_files/format_options.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/index.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/migrate/migrate.js": {"bytes": 786, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/migrate/index.js": {"bytes": 1201, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/migrate/migrate.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/migrate/cli.js": {"bytes": 1540, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/migrate/index.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/output_path.js": {"bytes": 634, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/asset_files/asset_translation_handler.js": {"bytes": 1428, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/source_files/source_file_translation_handler.js": {"bytes": 4406, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/source_files/es2015_translate_plugin.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/source_files/es5_translate_plugin.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/source_files/locale_plugin.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/translation_loader.js": {"bytes": 5712, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translator.js": {"bytes": 1248, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/index.js": {"bytes": 2385, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/asset_files/asset_translation_handler.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/source_files/source_file_translation_handler.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/translation_loader.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/translation_parsers/arb_translation_parser.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/translation_parsers/simple_json_translation_parser.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/translation_parsers/xliff1_translation_parser.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/translation_parsers/xliff2_translation_parser.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/translation_parsers/xtb_translation_parser.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translator.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/cli.js": {"bytes": 4464, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/diagnostics.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/output_path.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/index.js", "kind": "import-statement"}]}}, "outputs": {"bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/bundles/index.js.map": {"imports": [], "exports": [], "inputs": {}, "bytes": 170}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/bundles/index.js": {"imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/bundles/chunk-AU6TZOIN.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/bundles/chunk-7JRN4H23.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/bundles/chunk-AIKR22BS.js", "kind": "import-statement"}], "exports": ["ArbTranslationParser", "ArbTranslationSerializer", "Diagnostics", "LegacyMessageIdMigrationSerializer", "MessageExtractor", "SimpleJsonTranslationParser", "SimpleJsonTranslationSerializer", "Xliff1TranslationParser", "Xliff1TranslationSerializer", "Xliff2TranslationParser", "Xliff2TranslationSerializer", "XmbTranslationSerializer", "XtbTranslationParser", "buildLocalizeReplacement", "checkDuplicateMessages", "isGlobalIdentifier", "makeEs2015TranslatePlugin", "makeEs5TranslatePlugin", "makeLocalePlugin", "translate", "unwrapExpressionsFromTemplateLiteral", "unwrapMessagePartsFromLocalizeCall", "unwrapMessagePartsFromTemplateLiteral", "unwrapSubstitutionsFromLocalizeCall"], "entryPoint": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/index.js", "inputs": {"bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/index.js": {"bytesInOutput": 129}}, "bytes": 2058}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/bundles/src/extract/cli.js.map": {"imports": [], "exports": [], "inputs": {}, "bytes": 2700}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/bundles/src/extract/cli.js": {"imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/bundles/chunk-AU6TZOIN.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/bundles/chunk-AIKR22BS.js", "kind": "import-statement"}], "exports": [], "entryPoint": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/cli.js", "inputs": {"bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/cli.js": {"bytesInOutput": 3096}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/index.js": {"bytesInOutput": 2103}}, "bytes": 6174}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/bundles/chunk-AU6TZOIN.js.map": {"imports": [], "exports": [], "inputs": {}, "bytes": 19067}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/bundles/chunk-AU6TZOIN.js": {"imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/bundles/chunk-AIKR22BS.js", "kind": "import-statement"}], "exports": ["ArbTranslationSerializer", "LegacyMessageIdMigrationSerializer", "MessageExtractor", "SimpleJsonTranslationSerializer", "Xliff1TranslationSerializer", "Xliff2TranslationSerializer", "XmbTranslationSerializer", "checkDuplicateMessages", "parseFormatOptions"], "inputs": {"bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/duplicates.js": {"bytesInOutput": 1244}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/extraction.js": {"bytesInOutput": 3187}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/source_files/es2015_extract_plugin.js": {"bytesInOutput": 774}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/source_files/es5_extract_plugin.js": {"bytesInOutput": 1056}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/translation_files/utils.js": {"bytesInOutput": 1180}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/translation_files/arb_translation_serializer.js": {"bytesInOutput": 1979}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/translation_files/json_translation_serializer.js": {"bytesInOutput": 428}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/translation_files/legacy_message_id_migration_serializer.js": {"bytesInOutput": 941}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/translation_files/format_options.js": {"bytesInOutput": 760}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/translation_files/xliff1_translation_serializer.js": {"bytesInOutput": 5407}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/translation_files/icu_parsing.js": {"bytesInOutput": 2210}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/translation_files/xml_file.js": {"bytesInOutput": 1934}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/translation_files/xliff2_translation_serializer.js": {"bytesInOutput": 5627}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/extract/translation_files/xmb_translation_serializer.js": {"bytesInOutput": 2873}}, "bytes": 32437}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/bundles/src/migrate/cli.js.map": {"imports": [], "exports": [], "inputs": {}, "bytes": 1525}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/bundles/src/migrate/cli.js": {"imports": [], "exports": [], "entryPoint": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/migrate/cli.js", "inputs": {"bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/migrate/cli.js": {"bytesInOutput": 1163}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/migrate/index.js": {"bytesInOutput": 829}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/migrate/migrate.js": {"bytesInOutput": 405}}, "bytes": 3211}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/bundles/src/translate/cli.js.map": {"imports": [], "exports": [], "inputs": {}, "bytes": 7870}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/bundles/src/translate/cli.js": {"imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/bundles/chunk-7JRN4H23.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/bundles/chunk-AIKR22BS.js", "kind": "import-statement"}], "exports": [], "entryPoint": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/cli.js", "inputs": {"bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/cli.js": {"bytesInOutput": 3511}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/output_path.js": {"bytesInOutput": 259}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/index.js": {"bytesInOutput": 1411}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/asset_files/asset_translation_handler.js": {"bytesInOutput": 984}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/source_files/source_file_translation_handler.js": {"bytesInOutput": 3131}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/translation_loader.js": {"bytesInOutput": 3482}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translator.js": {"bytesInOutput": 925}}, "bytes": 15297}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/bundles/chunk-7JRN4H23.js.map": {"imports": [], "exports": [], "inputs": {}, "bytes": 16000}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/bundles/chunk-7JRN4H23.js": {"imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/bundles/chunk-AIKR22BS.js", "kind": "import-statement"}], "exports": ["ArbTranslationParser", "SimpleJsonTranslationParser", "Xliff1TranslationParser", "Xliff2TranslationParser", "XtbTranslationParser", "makeEs2015TranslatePlugin", "makeEs5TranslatePlugin", "makeLocalePlugin"], "inputs": {"bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/source_files/es2015_translate_plugin.js": {"bytesInOutput": 945}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/source_files/es5_translate_plugin.js": {"bytesInOutput": 1040}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/source_files/locale_plugin.js": {"bytesInOutput": 1717}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/translation_parsers/arb_translation_parser.js": {"bytesInOutput": 1077}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/translation_parsers/simple_json_translation_parser.js": {"bytesInOutput": 1725}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/translation_parsers/xliff1_translation_parser.js": {"bytesInOutput": 3504}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/base_visitor.js": {"bytesInOutput": 421}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/message_serialization/message_serializer.js": {"bytesInOutput": 2418}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/translation_parsers/translation_parse_error.js": {"bytesInOutput": 582}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/translation_parsers/translation_utils.js": {"bytesInOutput": 3101}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/message_serialization/target_message_renderer.js": {"bytesInOutput": 1177}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/translation_parsers/serialize_translation_message.js": {"bytesInOutput": 428}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/translation_parsers/xliff2_translation_parser.js": {"bytesInOutput": 3581}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/translate/translation_files/translation_parsers/xtb_translation_parser.js": {"bytesInOutput": 2509}}, "bytes": 27329}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/bundles/chunk-AIKR22BS.js.map": {"imports": [], "exports": [], "inputs": {}, "bytes": 13877}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/bundles/chunk-AIKR22BS.js": {"imports": [], "exports": ["Diagnostics", "buildCodeFrameError", "buildLocalizeReplacement", "getLocation", "isBabelParseError", "isGlobalIdentifier", "isLocalize", "isNamedIdentifier", "makeParsedTranslation", "parseMessage", "parseTranslation", "serializeLocationPosition", "translate", "unwrapExpressionsFromTemplateLiteral", "unwrapMessagePartsFromLocalizeCall", "unwrapMessagePartsFromTemplateLiteral", "unwrapSubstitutionsFromLocalizeCall"], "inputs": {"bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/diagnostics.js": {"bytesInOutput": 877}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/src/source_file_utils.js": {"bytesInOutput": 10098}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/src/utils/src/constants.js": {"bytesInOutput": 114}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/src/utils/index.js": {"bytesInOutput": 0}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler/src/i18n/digest.mjs": {"bytesInOutput": 3759}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/src/utils/src/messages.js": {"bytesInOutput": 3300}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/src/utils/src/translations.js": {"bytesInOutput": 2614}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/index.js": {"bytesInOutput": 0}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/src/localize/src/localize.js": {"bytesInOutput": 0}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/src/localize/index.js": {"bytesInOutput": 0}, "bazel-out/darwin_arm64-fastbuild/bin/packages/localize/private.js": {"bytesInOutput": 0}}, "bytes": 22188}}}