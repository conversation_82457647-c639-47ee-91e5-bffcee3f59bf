{"version": 3, "file": "testing.mjs", "sources": ["../../../../../darwin_arm64-fastbuild-ST-2d99d9656325/bin/packages/common/testing/src/mock_platform_location.ts", "../../../../../darwin_arm64-fastbuild-ST-2d99d9656325/bin/packages/common/testing/src/navigation/provide_fake_platform_navigation.ts", "../../../../../darwin_arm64-fastbuild-ST-2d99d9656325/bin/packages/common/testing/src/location_mock.ts", "../../../../../darwin_arm64-fastbuild-ST-2d99d9656325/bin/packages/common/testing/src/mock_location_strategy.ts", "../../../../../darwin_arm64-fastbuild-ST-2d99d9656325/bin/packages/common/testing/src/provide_location_mocks.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  DOCUMENT,\n  LocationChangeEvent,\n  LocationChangeListener,\n  PlatformLocation,\n  ɵPlatformNavigation as PlatformNavigation,\n} from '../../index';\nimport {Inject, inject, Injectable, InjectionToken, Optional} from '@angular/core';\nimport {Subject} from 'rxjs';\n\nimport {FakeNavigation} from './navigation/fake_navigation';\n\n/**\n * Parser from https://tools.ietf.org/html/rfc3986#appendix-B\n * ^(([^:/?#]+):)?(//([^/?#]*))?([^?#]*)(\\?([^#]*))?(#(.*))?\n *  12            3  4          5       6  7        8 9\n *\n * Example: http://www.ics.uci.edu/pub/ietf/uri/#Related\n *\n * Results in:\n *\n * $1 = http:\n * $2 = http\n * $3 = //www.ics.uci.edu\n * $4 = www.ics.uci.edu\n * $5 = /pub/ietf/uri/\n * $6 = <undefined>\n * $7 = <undefined>\n * $8 = #Related\n * $9 = Related\n */\nconst urlParse = /^(([^:\\/?#]+):)?(\\/\\/([^\\/?#]*))?([^?#]*)(\\?([^#]*))?(#(.*))?/;\n\nfunction parseUrl(urlStr: string, baseHref: string) {\n  const verifyProtocol = /^((http[s]?|ftp):\\/\\/)/;\n  let serverBase: string | undefined;\n\n  // URL class requires full URL. If the URL string doesn't start with protocol, we need to add\n  // an arbitrary base URL which can be removed afterward.\n  if (!verifyProtocol.test(urlStr)) {\n    serverBase = 'http://empty.com/';\n  }\n  let parsedUrl: {\n    protocol: string;\n    hostname: string;\n    port: string;\n    pathname: string;\n    search: string;\n    hash: string;\n  };\n  try {\n    parsedUrl = new URL(urlStr, serverBase);\n  } catch (e) {\n    const result = urlParse.exec(serverBase || '' + urlStr);\n    if (!result) {\n      throw new Error(`Invalid URL: ${urlStr} with base: ${baseHref}`);\n    }\n    const hostSplit = result[4].split(':');\n    parsedUrl = {\n      protocol: result[1],\n      hostname: hostSplit[0],\n      port: hostSplit[1] || '',\n      pathname: result[5],\n      search: result[6],\n      hash: result[8],\n    };\n  }\n  if (parsedUrl.pathname && parsedUrl.pathname.indexOf(baseHref) === 0) {\n    parsedUrl.pathname = parsedUrl.pathname.substring(baseHref.length);\n  }\n  return {\n    hostname: (!serverBase && parsedUrl.hostname) || '',\n    protocol: (!serverBase && parsedUrl.protocol) || '',\n    port: (!serverBase && parsedUrl.port) || '',\n    pathname: parsedUrl.pathname || '/',\n    search: parsedUrl.search || '',\n    hash: parsedUrl.hash || '',\n  };\n}\n\n/**\n * Mock platform location config\n *\n * @publicApi\n */\nexport interface MockPlatformLocationConfig {\n  startUrl?: string;\n  appBaseHref?: string;\n}\n\n/**\n * Provider for mock platform location config\n *\n * @publicApi\n */\nexport const MOCK_PLATFORM_LOCATION_CONFIG = new InjectionToken<MockPlatformLocationConfig>(\n  'MOCK_PLATFORM_LOCATION_CONFIG',\n);\n\n/**\n * Mock implementation of URL state.\n *\n * @publicApi\n */\n@Injectable()\nexport class MockPlatformLocation implements PlatformLocation {\n  private baseHref: string = '';\n  private hashUpdate = new Subject<LocationChangeEvent>();\n  private popStateSubject = new Subject<LocationChangeEvent>();\n  private urlChangeIndex: number = 0;\n  private urlChanges: {\n    hostname: string;\n    protocol: string;\n    port: string;\n    pathname: string;\n    search: string;\n    hash: string;\n    state: unknown;\n  }[] = [{hostname: '', protocol: '', port: '', pathname: '/', search: '', hash: '', state: null}];\n\n  constructor(\n    @Inject(MOCK_PLATFORM_LOCATION_CONFIG) @Optional() config?: MockPlatformLocationConfig,\n  ) {\n    if (config) {\n      this.baseHref = config.appBaseHref || '';\n\n      const parsedChanges = this.parseChanges(\n        null,\n        config.startUrl || 'http://_empty_/',\n        this.baseHref,\n      );\n      this.urlChanges[0] = {...parsedChanges};\n    }\n  }\n\n  get hostname() {\n    return this.urlChanges[this.urlChangeIndex].hostname;\n  }\n  get protocol() {\n    return this.urlChanges[this.urlChangeIndex].protocol;\n  }\n  get port() {\n    return this.urlChanges[this.urlChangeIndex].port;\n  }\n  get pathname() {\n    return this.urlChanges[this.urlChangeIndex].pathname;\n  }\n  get search() {\n    return this.urlChanges[this.urlChangeIndex].search;\n  }\n  get hash() {\n    return this.urlChanges[this.urlChangeIndex].hash;\n  }\n  get state() {\n    return this.urlChanges[this.urlChangeIndex].state;\n  }\n\n  getBaseHrefFromDOM(): string {\n    return this.baseHref;\n  }\n\n  onPopState(fn: LocationChangeListener): VoidFunction {\n    const subscription = this.popStateSubject.subscribe(fn);\n    return () => subscription.unsubscribe();\n  }\n\n  onHashChange(fn: LocationChangeListener): VoidFunction {\n    const subscription = this.hashUpdate.subscribe(fn);\n    return () => subscription.unsubscribe();\n  }\n\n  get href(): string {\n    let url = `${this.protocol}//${this.hostname}${this.port ? ':' + this.port : ''}`;\n    url += `${this.pathname === '/' ? '' : this.pathname}${this.search}${this.hash}`;\n    return url;\n  }\n\n  get url(): string {\n    return `${this.pathname}${this.search}${this.hash}`;\n  }\n\n  private parseChanges(state: unknown, url: string, baseHref: string = '') {\n    // When the `history.state` value is stored, it is always copied.\n    state = JSON.parse(JSON.stringify(state));\n    return {...parseUrl(url, baseHref), state};\n  }\n\n  replaceState(state: any, title: string, newUrl: string): void {\n    const {pathname, search, state: parsedState, hash} = this.parseChanges(state, newUrl);\n\n    this.urlChanges[this.urlChangeIndex] = {\n      ...this.urlChanges[this.urlChangeIndex],\n      pathname,\n      search,\n      hash,\n      state: parsedState,\n    };\n  }\n\n  pushState(state: any, title: string, newUrl: string): void {\n    const {pathname, search, state: parsedState, hash} = this.parseChanges(state, newUrl);\n    if (this.urlChangeIndex > 0) {\n      this.urlChanges.splice(this.urlChangeIndex + 1);\n    }\n    this.urlChanges.push({\n      ...this.urlChanges[this.urlChangeIndex],\n      pathname,\n      search,\n      hash,\n      state: parsedState,\n    });\n    this.urlChangeIndex = this.urlChanges.length - 1;\n  }\n\n  forward(): void {\n    const oldUrl = this.url;\n    const oldHash = this.hash;\n    if (this.urlChangeIndex < this.urlChanges.length) {\n      this.urlChangeIndex++;\n    }\n    this.emitEvents(oldHash, oldUrl);\n  }\n\n  back(): void {\n    const oldUrl = this.url;\n    const oldHash = this.hash;\n    if (this.urlChangeIndex > 0) {\n      this.urlChangeIndex--;\n    }\n    this.emitEvents(oldHash, oldUrl);\n  }\n\n  historyGo(relativePosition: number = 0): void {\n    const oldUrl = this.url;\n    const oldHash = this.hash;\n    const nextPageIndex = this.urlChangeIndex + relativePosition;\n    if (nextPageIndex >= 0 && nextPageIndex < this.urlChanges.length) {\n      this.urlChangeIndex = nextPageIndex;\n    }\n    this.emitEvents(oldHash, oldUrl);\n  }\n\n  getState(): unknown {\n    return this.state;\n  }\n\n  /**\n   * Browsers are inconsistent in when they fire events and perform the state updates\n   * The most easiest thing to do in our mock is synchronous and that happens to match\n   * Firefox and Chrome, at least somewhat closely\n   *\n   * https://github.com/WICG/navigation-api#watching-for-navigations\n   * https://docs.google.com/document/d/1Pdve-DJ1JCGilj9Yqf5HxRJyBKSel5owgOvUJqTauwU/edit#heading=h.3ye4v71wsz94\n   * popstate is always sent before hashchange:\n   * https://developer.mozilla.org/en-US/docs/Web/API/Window/popstate_event#when_popstate_is_sent\n   */\n  private emitEvents(oldHash: string, oldUrl: string) {\n    this.popStateSubject.next({\n      type: 'popstate',\n      state: this.getState(),\n      oldUrl,\n      newUrl: this.url,\n    } as LocationChangeEvent);\n    if (oldHash !== this.hash) {\n      this.hashUpdate.next({\n        type: 'hashchange',\n        state: null,\n        oldUrl,\n        newUrl: this.url,\n      } as LocationChangeEvent);\n    }\n  }\n}\n\n/**\n * Mock implementation of URL state.\n */\n@Injectable()\nexport class FakeNavigationPlatformLocation implements PlatformLocation {\n  private readonly _platformNavigation: FakeNavigation;\n\n  constructor() {\n    const platformNavigation = inject(PlatformNavigation);\n    if (!(platformNavigation instanceof FakeNavigation)) {\n      throw new Error(\n        'FakePlatformNavigation cannot be used without FakeNavigation. Use ' +\n          '`provideFakeNavigation` to have all these services provided together.',\n      );\n    }\n    this._platformNavigation = platformNavigation;\n  }\n\n  private config = inject(MOCK_PLATFORM_LOCATION_CONFIG, {optional: true});\n  getBaseHrefFromDOM(): string {\n    return this.config?.appBaseHref ?? '';\n  }\n\n  onPopState(fn: LocationChangeListener): VoidFunction {\n    this._platformNavigation.window.addEventListener('popstate', fn);\n    return () => this._platformNavigation.window.removeEventListener('popstate', fn);\n  }\n\n  onHashChange(fn: LocationChangeListener): VoidFunction {\n    this._platformNavigation.window.addEventListener('hashchange', fn as any);\n    return () => this._platformNavigation.window.removeEventListener('hashchange', fn as any);\n  }\n\n  get href(): string {\n    return this._platformNavigation.currentEntry.url!;\n  }\n  get protocol(): string {\n    return new URL(this._platformNavigation.currentEntry.url!).protocol;\n  }\n  get hostname(): string {\n    return new URL(this._platformNavigation.currentEntry.url!).hostname;\n  }\n  get port(): string {\n    return new URL(this._platformNavigation.currentEntry.url!).port;\n  }\n  get pathname(): string {\n    return new URL(this._platformNavigation.currentEntry.url!).pathname;\n  }\n  get search(): string {\n    return new URL(this._platformNavigation.currentEntry.url!).search;\n  }\n  get hash(): string {\n    return new URL(this._platformNavigation.currentEntry.url!).hash;\n  }\n\n  pushState(state: any, title: string, url: string): void {\n    this._platformNavigation.pushState(state, title, url);\n  }\n\n  replaceState(state: any, title: string, url: string): void {\n    this._platformNavigation.replaceState(state, title, url);\n  }\n\n  forward(): void {\n    this._platformNavigation.forward();\n  }\n\n  back(): void {\n    this._platformNavigation.back();\n  }\n\n  historyGo(relativePosition: number = 0): void {\n    this._platformNavigation.go(relativePosition);\n  }\n\n  getState(): unknown {\n    return this._platformNavigation.currentEntry.getHistoryState();\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  DOCUMENT,\n  PlatformLocation,\n  ɵPlatformNavigation as PlatformNavigation,\n} from '../../../index';\nimport {inject, InjectionToken, Provider} from '@angular/core';\n\nimport {\n  FakeNavigationPlatformLocation,\n  MOCK_PLATFORM_LOCATION_CONFIG,\n} from '../mock_platform_location';\n\nimport {FakeNavigation} from './fake_navigation';\n\nconst FAKE_NAVIGATION = new InjectionToken<FakeNavigation>('fakeNavigation', {\n  providedIn: 'root',\n  factory: () => {\n    const config = inject(MOCK_PLATFORM_LOCATION_CONFIG, {optional: true});\n    const baseFallback = 'http://_empty_/';\n    const startUrl = new URL(config?.startUrl || baseFallback, baseFallback);\n    // TODO(atscott): If we want to replace MockPlatformLocation with FakeNavigationPlatformLocation\n    // as the default in TestBed, we will likely need to use setSynchronousTraversalsForTesting(true);\n    return new FakeNavigation(inject(DOCUMENT), startUrl.href as `http${string}`);\n  },\n});\n\n/**\n * Return a provider for the `FakeNavigation` in place of the real Navigation API.\n */\nexport function provideFakePlatformNavigation(): Provider[] {\n  return [\n    {\n      provide: PlatformNavigation,\n      useFactory: () => inject(FAKE_NAVIGATION),\n    },\n    {provide: PlatformLocation, useClass: FakeNavigationPlatformLocation},\n  ];\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  Location,\n  LocationStrategy,\n  PopStateEvent,\n  ɵnormalizeQueryParams as normalizeQueryParams,\n} from '@angular/common';\nimport {Injectable} from '@angular/core';\nimport {Subject, SubscriptionLike} from 'rxjs';\n\n/**\n * A spy for {@link Location} that allows tests to fire simulated location events.\n *\n * @publicApi\n */\n@Injectable()\nexport class SpyLocation implements Location {\n  urlChanges: string[] = [];\n  private _history: LocationState[] = [new LocationState('', '', null)];\n  private _historyIndex: number = 0;\n  /** @internal */\n  _subject = new Subject<PopStateEvent>();\n  /** @internal */\n  _basePath: string = '';\n  /** @internal */\n  _locationStrategy: LocationStrategy = null!;\n  /** @internal */\n  _urlChangeListeners: ((url: string, state: unknown) => void)[] = [];\n  /** @internal */\n  _urlChangeSubscription: SubscriptionLike | null = null;\n\n  /** @docs-private */\n  ngOnDestroy(): void {\n    this._urlChangeSubscription?.unsubscribe();\n    this._urlChangeListeners = [];\n  }\n\n  setInitialPath(url: string) {\n    this._history[this._historyIndex].path = url;\n  }\n\n  setBaseHref(url: string) {\n    this._basePath = url;\n  }\n\n  path(): string {\n    return this._history[this._historyIndex].path;\n  }\n\n  getState(): unknown {\n    return this._history[this._historyIndex].state;\n  }\n\n  isCurrentPathEqualTo(path: string, query: string = ''): boolean {\n    const givenPath = path.endsWith('/') ? path.substring(0, path.length - 1) : path;\n    const currPath = this.path().endsWith('/')\n      ? this.path().substring(0, this.path().length - 1)\n      : this.path();\n\n    return currPath == givenPath + (query.length > 0 ? '?' + query : '');\n  }\n\n  simulateUrlPop(pathname: string) {\n    this._subject.next({'url': pathname, 'pop': true, 'type': 'popstate'});\n  }\n\n  simulateHashChange(pathname: string) {\n    const path = this.prepareExternalUrl(pathname);\n    this.pushHistory(path, '', null);\n\n    this.urlChanges.push('hash: ' + pathname);\n    // the browser will automatically fire popstate event before each `hashchange` event, so we need\n    // to simulate it.\n    this._subject.next({'url': pathname, 'pop': true, 'type': 'popstate'});\n    this._subject.next({'url': pathname, 'pop': true, 'type': 'hashchange'});\n  }\n\n  prepareExternalUrl(url: string): string {\n    if (url.length > 0 && !url.startsWith('/')) {\n      url = '/' + url;\n    }\n    return this._basePath + url;\n  }\n\n  go(path: string, query: string = '', state: any = null) {\n    path = this.prepareExternalUrl(path);\n\n    this.pushHistory(path, query, state);\n\n    const locationState = this._history[this._historyIndex - 1];\n    if (locationState.path == path && locationState.query == query) {\n      return;\n    }\n\n    const url = path + (query.length > 0 ? '?' + query : '');\n    this.urlChanges.push(url);\n    this._notifyUrlChangeListeners(path + normalizeQueryParams(query), state);\n  }\n\n  replaceState(path: string, query: string = '', state: any = null) {\n    path = this.prepareExternalUrl(path);\n\n    const history = this._history[this._historyIndex];\n\n    history.state = state;\n\n    if (history.path == path && history.query == query) {\n      return;\n    }\n\n    history.path = path;\n    history.query = query;\n\n    const url = path + (query.length > 0 ? '?' + query : '');\n    this.urlChanges.push('replace: ' + url);\n    this._notifyUrlChangeListeners(path + normalizeQueryParams(query), state);\n  }\n\n  forward() {\n    if (this._historyIndex < this._history.length - 1) {\n      this._historyIndex++;\n      this._subject.next({\n        'url': this.path(),\n        'state': this.getState(),\n        'pop': true,\n        'type': 'popstate',\n      });\n    }\n  }\n\n  back() {\n    if (this._historyIndex > 0) {\n      this._historyIndex--;\n      this._subject.next({\n        'url': this.path(),\n        'state': this.getState(),\n        'pop': true,\n        'type': 'popstate',\n      });\n    }\n  }\n\n  historyGo(relativePosition: number = 0): void {\n    const nextPageIndex = this._historyIndex + relativePosition;\n    if (nextPageIndex >= 0 && nextPageIndex < this._history.length) {\n      this._historyIndex = nextPageIndex;\n      this._subject.next({\n        'url': this.path(),\n        'state': this.getState(),\n        'pop': true,\n        'type': 'popstate',\n      });\n    }\n  }\n\n  onUrlChange(fn: (url: string, state: unknown) => void): VoidFunction {\n    this._urlChangeListeners.push(fn);\n\n    this._urlChangeSubscription ??= this.subscribe((v) => {\n      this._notifyUrlChangeListeners(v.url, v.state);\n    });\n\n    return () => {\n      const fnIndex = this._urlChangeListeners.indexOf(fn);\n      this._urlChangeListeners.splice(fnIndex, 1);\n\n      if (this._urlChangeListeners.length === 0) {\n        this._urlChangeSubscription?.unsubscribe();\n        this._urlChangeSubscription = null;\n      }\n    };\n  }\n\n  /** @internal */\n  _notifyUrlChangeListeners(url: string = '', state: unknown) {\n    this._urlChangeListeners.forEach((fn) => fn(url, state));\n  }\n\n  subscribe(\n    onNext: (value: any) => void,\n    onThrow?: ((error: any) => void) | null,\n    onReturn?: (() => void) | null,\n  ): SubscriptionLike {\n    return this._subject.subscribe({\n      next: onNext,\n      error: onThrow ?? undefined,\n      complete: onReturn ?? undefined,\n    });\n  }\n\n  normalize(url: string): string {\n    return null!;\n  }\n\n  private pushHistory(path: string, query: string, state: any) {\n    if (this._historyIndex > 0) {\n      this._history.splice(this._historyIndex + 1);\n    }\n    this._history.push(new LocationState(path, query, state));\n    this._historyIndex = this._history.length - 1;\n  }\n}\n\nclass LocationState {\n  constructor(\n    public path: string,\n    public query: string,\n    public state: any,\n  ) {}\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {LocationStrategy} from '@angular/common';\nimport {Injectable} from '@angular/core';\nimport {Subject} from 'rxjs';\n\n/**\n * A mock implementation of {@link LocationStrategy} that allows tests to fire simulated\n * location events.\n *\n * @publicApi\n */\n@Injectable()\nexport class MockLocationStrategy extends LocationStrategy {\n  internalBaseHref: string = '/';\n  internalPath: string = '/';\n  internalTitle: string = '';\n  urlChanges: string[] = [];\n  /** @internal */\n  _subject = new Subject<_MockPopStateEvent>();\n  private stateChanges: any[] = [];\n  constructor() {\n    super();\n  }\n\n  simulatePopState(url: string): void {\n    this.internalPath = url;\n    this._subject.next(new _MockPopStateEvent(this.path()));\n  }\n\n  override path(includeHash: boolean = false): string {\n    return this.internalPath;\n  }\n\n  override prepareExternalUrl(internal: string): string {\n    if (internal.startsWith('/') && this.internalBaseHref.endsWith('/')) {\n      return this.internalBaseHref + internal.substring(1);\n    }\n    return this.internalBaseHref + internal;\n  }\n\n  override pushState(ctx: any, title: string, path: string, query: string): void {\n    // Add state change to changes array\n    this.stateChanges.push(ctx);\n\n    this.internalTitle = title;\n\n    const url = path + (query.length > 0 ? '?' + query : '');\n    this.internalPath = url;\n\n    const externalUrl = this.prepareExternalUrl(url);\n    this.urlChanges.push(externalUrl);\n  }\n\n  override replaceState(ctx: any, title: string, path: string, query: string): void {\n    // Reset the last index of stateChanges to the ctx (state) object\n    this.stateChanges[(this.stateChanges.length || 1) - 1] = ctx;\n\n    this.internalTitle = title;\n\n    const url = path + (query.length > 0 ? '?' + query : '');\n    this.internalPath = url;\n\n    const externalUrl = this.prepareExternalUrl(url);\n    this.urlChanges.push('replace: ' + externalUrl);\n  }\n\n  override onPopState(fn: (value: any) => void): void {\n    this._subject.subscribe({next: fn});\n  }\n\n  override getBaseHref(): string {\n    return this.internalBaseHref;\n  }\n\n  override back(): void {\n    if (this.urlChanges.length > 0) {\n      this.urlChanges.pop();\n      this.stateChanges.pop();\n      const nextUrl = this.urlChanges.length > 0 ? this.urlChanges[this.urlChanges.length - 1] : '';\n      this.simulatePopState(nextUrl);\n    }\n  }\n\n  override forward(): void {\n    throw 'not implemented';\n  }\n\n  override getState(): unknown {\n    return this.stateChanges[(this.stateChanges.length || 1) - 1];\n  }\n}\n\nclass _MockPopStateEvent {\n  pop: boolean = true;\n  type: string = 'popstate';\n  constructor(public newUrl: string) {}\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Location, LocationStrategy} from '../../index';\nimport {Provider} from '@angular/core';\n\nimport {SpyLocation} from './location_mock';\nimport {MockLocationStrategy} from './mock_location_strategy';\n\n/**\n * Returns mock providers for the `Location` and `LocationStrategy` classes.\n * The mocks are helpful in tests to fire simulated location events.\n *\n * @publicApi\n */\nexport function provideLocationMocks(): Provider[] {\n  return [\n    {provide: Location, useClass: SpyLocation},\n    {provide: LocationStrategy, useClass: MockLocationStrategy},\n  ];\n}\n"], "names": ["FakeNavigation", "normalizeQueryParams", "LocationStrategy"], "mappings": ";;;;;;;;;;;;;;;;AAoBA;;;;;;;;;;;;;;;;;;AAkBG;AACH,MAAM,QAAQ,GAAG,+DAA+D;AAEhF,SAAS,QAAQ,CAAC,MAAc,EAAE,QAAgB,EAAA;IAChD,MAAM,cAAc,GAAG,wBAAwB;AAC/C,IAAA,IAAI,UAA8B;;;IAIlC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;QAChC,UAAU,GAAG,mBAAmB;;AAElC,IAAA,IAAI,SAOH;AACD,IAAA,IAAI;QACF,SAAS,GAAG,IAAI,GAAG,CAAC,MAAM,EAAE,UAAU,CAAC;;IACvC,OAAO,CAAC,EAAE;AACV,QAAA,MAAM,MAAM,GAAG,QAAQ,CAAC,IAAI,CAAC,UAAU,IAAI,EAAE,GAAG,MAAM,CAAC;QACvD,IAAI,CAAC,MAAM,EAAE;YACX,MAAM,IAAI,KAAK,CAAC,CAAA,aAAA,EAAgB,MAAM,CAAe,YAAA,EAAA,QAAQ,CAAE,CAAA,CAAC;;QAElE,MAAM,SAAS,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC;AACtC,QAAA,SAAS,GAAG;AACV,YAAA,QAAQ,EAAE,MAAM,CAAC,CAAC,CAAC;AACnB,YAAA,QAAQ,EAAE,SAAS,CAAC,CAAC,CAAC;AACtB,YAAA,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC,IAAI,EAAE;AACxB,YAAA,QAAQ,EAAE,MAAM,CAAC,CAAC,CAAC;AACnB,YAAA,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC;AACjB,YAAA,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC;SAChB;;AAEH,IAAA,IAAI,SAAS,CAAC,QAAQ,IAAI,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;AACpE,QAAA,SAAS,CAAC,QAAQ,GAAG,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC;;IAEpE,OAAO;QACL,QAAQ,EAAE,CAAC,CAAC,UAAU,IAAI,SAAS,CAAC,QAAQ,KAAK,EAAE;QACnD,QAAQ,EAAE,CAAC,CAAC,UAAU,IAAI,SAAS,CAAC,QAAQ,KAAK,EAAE;QACnD,IAAI,EAAE,CAAC,CAAC,UAAU,IAAI,SAAS,CAAC,IAAI,KAAK,EAAE;AAC3C,QAAA,QAAQ,EAAE,SAAS,CAAC,QAAQ,IAAI,GAAG;AACnC,QAAA,MAAM,EAAE,SAAS,CAAC,MAAM,IAAI,EAAE;AAC9B,QAAA,IAAI,EAAE,SAAS,CAAC,IAAI,IAAI,EAAE;KAC3B;AACH;AAYA;;;;AAIG;MACU,6BAA6B,GAAG,IAAI,cAAc,CAC7D,+BAA+B;AAGjC;;;;AAIG;MAEU,oBAAoB,CAAA;IACvB,QAAQ,GAAW,EAAE;AACrB,IAAA,UAAU,GAAG,IAAI,OAAO,EAAuB;AAC/C,IAAA,eAAe,GAAG,IAAI,OAAO,EAAuB;IACpD,cAAc,GAAW,CAAC;AAC1B,IAAA,UAAU,GAQZ,CAAC,EAAC,QAAQ,EAAE,EAAE,EAAE,QAAQ,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,QAAQ,EAAE,GAAG,EAAE,MAAM,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,KAAK,EAAE,IAAI,EAAC,CAAC;AAEhG,IAAA,WAAA,CACqD,MAAmC,EAAA;QAEtF,IAAI,MAAM,EAAE;YACV,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,WAAW,IAAI,EAAE;AAExC,YAAA,MAAM,aAAa,GAAG,IAAI,CAAC,YAAY,CACrC,IAAI,EACJ,MAAM,CAAC,QAAQ,IAAI,iBAAiB,EACpC,IAAI,CAAC,QAAQ,CACd;YACD,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,EAAC,GAAG,aAAa,EAAC;;;AAI3C,IAAA,IAAI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,QAAQ;;AAEtD,IAAA,IAAI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,QAAQ;;AAEtD,IAAA,IAAI,IAAI,GAAA;QACN,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,IAAI;;AAElD,IAAA,IAAI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,QAAQ;;AAEtD,IAAA,IAAI,MAAM,GAAA;QACR,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,MAAM;;AAEpD,IAAA,IAAI,IAAI,GAAA;QACN,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,IAAI;;AAElD,IAAA,IAAI,KAAK,GAAA;QACP,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,KAAK;;IAGnD,kBAAkB,GAAA;QAChB,OAAO,IAAI,CAAC,QAAQ;;AAGtB,IAAA,UAAU,CAAC,EAA0B,EAAA;QACnC,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,EAAE,CAAC;AACvD,QAAA,OAAO,MAAM,YAAY,CAAC,WAAW,EAAE;;AAGzC,IAAA,YAAY,CAAC,EAA0B,EAAA;QACrC,MAAM,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;AAClD,QAAA,OAAO,MAAM,YAAY,CAAC,WAAW,EAAE;;AAGzC,IAAA,IAAI,IAAI,GAAA;QACN,IAAI,GAAG,GAAG,CAAA,EAAG,IAAI,CAAC,QAAQ,CAAA,EAAA,EAAK,IAAI,CAAC,QAAQ,CAAA,EAAG,IAAI,CAAC,IAAI,GAAG,GAAG,GAAG,IAAI,CAAC,IAAI,GAAG,EAAE,CAAA,CAAE;QACjF,GAAG,IAAI,CAAG,EAAA,IAAI,CAAC,QAAQ,KAAK,GAAG,GAAG,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAA,EAAG,IAAI,CAAC,MAAM,CAAA,EAAG,IAAI,CAAC,IAAI,CAAA,CAAE;AAChF,QAAA,OAAO,GAAG;;AAGZ,IAAA,IAAI,GAAG,GAAA;AACL,QAAA,OAAO,CAAG,EAAA,IAAI,CAAC,QAAQ,CAAG,EAAA,IAAI,CAAC,MAAM,CAAG,EAAA,IAAI,CAAC,IAAI,EAAE;;AAG7C,IAAA,YAAY,CAAC,KAAc,EAAE,GAAW,EAAE,WAAmB,EAAE,EAAA;;AAErE,QAAA,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QACzC,OAAO,EAAC,GAAG,QAAQ,CAAC,GAAG,EAAE,QAAQ,CAAC,EAAE,KAAK,EAAC;;AAG5C,IAAA,YAAY,CAAC,KAAU,EAAE,KAAa,EAAE,MAAc,EAAA;QACpD,MAAM,EAAC,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,IAAI,EAAC,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,MAAM,CAAC;AAErF,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG;AACrC,YAAA,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,CAAC;YACvC,QAAQ;YACR,MAAM;YACN,IAAI;AACJ,YAAA,KAAK,EAAE,WAAW;SACnB;;AAGH,IAAA,SAAS,CAAC,KAAU,EAAE,KAAa,EAAE,MAAc,EAAA;QACjD,MAAM,EAAC,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,IAAI,EAAC,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,MAAM,CAAC;AACrF,QAAA,IAAI,IAAI,CAAC,cAAc,GAAG,CAAC,EAAE;YAC3B,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC;;AAEjD,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;AACnB,YAAA,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,CAAC;YACvC,QAAQ;YACR,MAAM;YACN,IAAI;AACJ,YAAA,KAAK,EAAE,WAAW;AACnB,SAAA,CAAC;QACF,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC;;IAGlD,OAAO,GAAA;AACL,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG;AACvB,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI;QACzB,IAAI,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE;YAChD,IAAI,CAAC,cAAc,EAAE;;AAEvB,QAAA,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,MAAM,CAAC;;IAGlC,IAAI,GAAA;AACF,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG;AACvB,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI;AACzB,QAAA,IAAI,IAAI,CAAC,cAAc,GAAG,CAAC,EAAE;YAC3B,IAAI,CAAC,cAAc,EAAE;;AAEvB,QAAA,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,MAAM,CAAC;;IAGlC,SAAS,CAAC,mBAA2B,CAAC,EAAA;AACpC,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG;AACvB,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI;AACzB,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,cAAc,GAAG,gBAAgB;AAC5D,QAAA,IAAI,aAAa,IAAI,CAAC,IAAI,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE;AAChE,YAAA,IAAI,CAAC,cAAc,GAAG,aAAa;;AAErC,QAAA,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,MAAM,CAAC;;IAGlC,QAAQ,GAAA;QACN,OAAO,IAAI,CAAC,KAAK;;AAGnB;;;;;;;;;AASG;IACK,UAAU,CAAC,OAAe,EAAE,MAAc,EAAA;AAChD,QAAA,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC;AACxB,YAAA,IAAI,EAAE,UAAU;AAChB,YAAA,KAAK,EAAE,IAAI,CAAC,QAAQ,EAAE;YACtB,MAAM;YACN,MAAM,EAAE,IAAI,CAAC,GAAG;AACM,SAAA,CAAC;AACzB,QAAA,IAAI,OAAO,KAAK,IAAI,CAAC,IAAI,EAAE;AACzB,YAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;AACnB,gBAAA,IAAI,EAAE,YAAY;AAClB,gBAAA,KAAK,EAAE,IAAI;gBACX,MAAM;gBACN,MAAM,EAAE,IAAI,CAAC,GAAG;AACM,aAAA,CAAC;;;AApKlB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,oBAAoB,kBAgBrB,6BAA6B,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;sHAhB5B,oBAAoB,EAAA,CAAA;;sGAApB,oBAAoB,EAAA,UAAA,EAAA,CAAA;kBADhC;;0BAiBI,MAAM;2BAAC,6BAA6B;;0BAAG;;AAyJ5C;;AAEG;MAEU,8BAA8B,CAAA;AACxB,IAAA,mBAAmB;AAEpC,IAAA,WAAA,GAAA;AACE,QAAA,MAAM,kBAAkB,GAAG,MAAM,CAAC,kBAAkB,CAAC;AACrD,QAAA,IAAI,EAAE,kBAAkB,YAAYA,eAAc,CAAC,EAAE;YACnD,MAAM,IAAI,KAAK,CACb,oEAAoE;AAClE,gBAAA,uEAAuE,CAC1E;;AAEH,QAAA,IAAI,CAAC,mBAAmB,GAAG,kBAAkB;;IAGvC,MAAM,GAAG,MAAM,CAAC,6BAA6B,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;IACxE,kBAAkB,GAAA;AAChB,QAAA,OAAO,IAAI,CAAC,MAAM,EAAE,WAAW,IAAI,EAAE;;AAGvC,IAAA,UAAU,CAAC,EAA0B,EAAA;QACnC,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,gBAAgB,CAAC,UAAU,EAAE,EAAE,CAAC;AAChE,QAAA,OAAO,MAAM,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,mBAAmB,CAAC,UAAU,EAAE,EAAE,CAAC;;AAGlF,IAAA,YAAY,CAAC,EAA0B,EAAA;QACrC,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,gBAAgB,CAAC,YAAY,EAAE,EAAS,CAAC;AACzE,QAAA,OAAO,MAAM,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,mBAAmB,CAAC,YAAY,EAAE,EAAS,CAAC;;AAG3F,IAAA,IAAI,IAAI,GAAA;AACN,QAAA,OAAO,IAAI,CAAC,mBAAmB,CAAC,YAAY,CAAC,GAAI;;AAEnD,IAAA,IAAI,QAAQ,GAAA;AACV,QAAA,OAAO,IAAI,GAAG,CAAC,IAAI,CAAC,mBAAmB,CAAC,YAAY,CAAC,GAAI,CAAC,CAAC,QAAQ;;AAErE,IAAA,IAAI,QAAQ,GAAA;AACV,QAAA,OAAO,IAAI,GAAG,CAAC,IAAI,CAAC,mBAAmB,CAAC,YAAY,CAAC,GAAI,CAAC,CAAC,QAAQ;;AAErE,IAAA,IAAI,IAAI,GAAA;AACN,QAAA,OAAO,IAAI,GAAG,CAAC,IAAI,CAAC,mBAAmB,CAAC,YAAY,CAAC,GAAI,CAAC,CAAC,IAAI;;AAEjE,IAAA,IAAI,QAAQ,GAAA;AACV,QAAA,OAAO,IAAI,GAAG,CAAC,IAAI,CAAC,mBAAmB,CAAC,YAAY,CAAC,GAAI,CAAC,CAAC,QAAQ;;AAErE,IAAA,IAAI,MAAM,GAAA;AACR,QAAA,OAAO,IAAI,GAAG,CAAC,IAAI,CAAC,mBAAmB,CAAC,YAAY,CAAC,GAAI,CAAC,CAAC,MAAM;;AAEnE,IAAA,IAAI,IAAI,GAAA;AACN,QAAA,OAAO,IAAI,GAAG,CAAC,IAAI,CAAC,mBAAmB,CAAC,YAAY,CAAC,GAAI,CAAC,CAAC,IAAI;;AAGjE,IAAA,SAAS,CAAC,KAAU,EAAE,KAAa,EAAE,GAAW,EAAA;QAC9C,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC,KAAK,EAAE,KAAK,EAAE,GAAG,CAAC;;AAGvD,IAAA,YAAY,CAAC,KAAU,EAAE,KAAa,EAAE,GAAW,EAAA;QACjD,IAAI,CAAC,mBAAmB,CAAC,YAAY,CAAC,KAAK,EAAE,KAAK,EAAE,GAAG,CAAC;;IAG1D,OAAO,GAAA;AACL,QAAA,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE;;IAGpC,IAAI,GAAA;AACF,QAAA,IAAI,CAAC,mBAAmB,CAAC,IAAI,EAAE;;IAGjC,SAAS,CAAC,mBAA2B,CAAC,EAAA;AACpC,QAAA,IAAI,CAAC,mBAAmB,CAAC,EAAE,CAAC,gBAAgB,CAAC;;IAG/C,QAAQ,GAAA;QACN,OAAO,IAAI,CAAC,mBAAmB,CAAC,YAAY,CAAC,eAAe,EAAE;;kHAxErD,8BAA8B,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;sHAA9B,8BAA8B,EAAA,CAAA;;sGAA9B,8BAA8B,EAAA,UAAA,EAAA,CAAA;kBAD1C;;;ACvQD,MAAM,eAAe,GAAG,IAAI,cAAc,CAAiB,gBAAgB,EAAE;AAC3E,IAAA,UAAU,EAAE,MAAM;IAClB,OAAO,EAAE,MAAK;AACZ,QAAA,MAAM,MAAM,GAAG,MAAM,CAAC,6BAA6B,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;QACtE,MAAM,YAAY,GAAG,iBAAiB;AACtC,QAAA,MAAM,QAAQ,GAAG,IAAI,GAAG,CAAC,MAAM,EAAE,QAAQ,IAAI,YAAY,EAAE,YAAY,CAAC;;;AAGxE,QAAA,OAAO,IAAIA,eAAc,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,QAAQ,CAAC,IAAuB,CAAC;KAC9E;AACF,CAAA,CAAC;AAEF;;AAEG;SACa,6BAA6B,GAAA;IAC3C,OAAO;AACL,QAAA;AACE,YAAA,OAAO,EAAE,kBAAkB;AAC3B,YAAA,UAAU,EAAE,MAAM,MAAM,CAAC,eAAe,CAAC;AAC1C,SAAA;AACD,QAAA,EAAC,OAAO,EAAE,gBAAgB,EAAE,QAAQ,EAAE,8BAA8B,EAAC;KACtE;AACH;;AC5BA;;;;AAIG;MAEU,WAAW,CAAA;IACtB,UAAU,GAAa,EAAE;AACjB,IAAA,QAAQ,GAAoB,CAAC,IAAI,aAAa,CAAC,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,CAAC;IAC7D,aAAa,GAAW,CAAC;;AAEjC,IAAA,QAAQ,GAAG,IAAI,OAAO,EAAiB;;IAEvC,SAAS,GAAW,EAAE;;IAEtB,iBAAiB,GAAqB,IAAK;;IAE3C,mBAAmB,GAA8C,EAAE;;IAEnE,sBAAsB,GAA4B,IAAI;;IAGtD,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,sBAAsB,EAAE,WAAW,EAAE;AAC1C,QAAA,IAAI,CAAC,mBAAmB,GAAG,EAAE;;AAG/B,IAAA,cAAc,CAAC,GAAW,EAAA;QACxB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,IAAI,GAAG,GAAG;;AAG9C,IAAA,WAAW,CAAC,GAAW,EAAA;AACrB,QAAA,IAAI,CAAC,SAAS,GAAG,GAAG;;IAGtB,IAAI,GAAA;QACF,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,IAAI;;IAG/C,QAAQ,GAAA;QACN,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,KAAK;;AAGhD,IAAA,oBAAoB,CAAC,IAAY,EAAE,KAAA,GAAgB,EAAE,EAAA;QACnD,MAAM,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI;QAChF,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,QAAQ,CAAC,GAAG;AACvC,cAAE,IAAI,CAAC,IAAI,EAAE,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,MAAM,GAAG,CAAC;AACjD,cAAE,IAAI,CAAC,IAAI,EAAE;QAEf,OAAO,QAAQ,IAAI,SAAS,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,KAAK,GAAG,EAAE,CAAC;;AAGtE,IAAA,cAAc,CAAC,QAAgB,EAAA;AAC7B,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAC,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,UAAU,EAAC,CAAC;;AAGxE,IAAA,kBAAkB,CAAC,QAAgB,EAAA;QACjC,MAAM,IAAI,GAAG,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC;QAC9C,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,EAAE,EAAE,IAAI,CAAC;QAEhC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;;;AAGzC,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAC,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,UAAU,EAAC,CAAC;AACtE,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAC,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,YAAY,EAAC,CAAC;;AAG1E,IAAA,kBAAkB,CAAC,GAAW,EAAA;AAC5B,QAAA,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;AAC1C,YAAA,GAAG,GAAG,GAAG,GAAG,GAAG;;AAEjB,QAAA,OAAO,IAAI,CAAC,SAAS,GAAG,GAAG;;AAG7B,IAAA,EAAE,CAAC,IAAY,EAAE,QAAgB,EAAE,EAAE,QAAa,IAAI,EAAA;AACpD,QAAA,IAAI,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC;QAEpC,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC;AAEpC,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;AAC3D,QAAA,IAAI,aAAa,CAAC,IAAI,IAAI,IAAI,IAAI,aAAa,CAAC,KAAK,IAAI,KAAK,EAAE;YAC9D;;QAGF,MAAM,GAAG,GAAG,IAAI,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,KAAK,GAAG,EAAE,CAAC;AACxD,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC;AACzB,QAAA,IAAI,CAAC,yBAAyB,CAAC,IAAI,GAAGC,qBAAoB,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC;;AAG3E,IAAA,YAAY,CAAC,IAAY,EAAE,QAAgB,EAAE,EAAE,QAAa,IAAI,EAAA;AAC9D,QAAA,IAAI,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC;QAEpC,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC;AAEjD,QAAA,OAAO,CAAC,KAAK,GAAG,KAAK;AAErB,QAAA,IAAI,OAAO,CAAC,IAAI,IAAI,IAAI,IAAI,OAAO,CAAC,KAAK,IAAI,KAAK,EAAE;YAClD;;AAGF,QAAA,OAAO,CAAC,IAAI,GAAG,IAAI;AACnB,QAAA,OAAO,CAAC,KAAK,GAAG,KAAK;QAErB,MAAM,GAAG,GAAG,IAAI,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,KAAK,GAAG,EAAE,CAAC;QACxD,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,GAAG,GAAG,CAAC;AACvC,QAAA,IAAI,CAAC,yBAAyB,CAAC,IAAI,GAAGA,qBAAoB,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC;;IAG3E,OAAO,GAAA;AACL,QAAA,IAAI,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;YACjD,IAAI,CAAC,aAAa,EAAE;AACpB,YAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;AACjB,gBAAA,KAAK,EAAE,IAAI,CAAC,IAAI,EAAE;AAClB,gBAAA,OAAO,EAAE,IAAI,CAAC,QAAQ,EAAE;AACxB,gBAAA,KAAK,EAAE,IAAI;AACX,gBAAA,MAAM,EAAE,UAAU;AACnB,aAAA,CAAC;;;IAIN,IAAI,GAAA;AACF,QAAA,IAAI,IAAI,CAAC,aAAa,GAAG,CAAC,EAAE;YAC1B,IAAI,CAAC,aAAa,EAAE;AACpB,YAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;AACjB,gBAAA,KAAK,EAAE,IAAI,CAAC,IAAI,EAAE;AAClB,gBAAA,OAAO,EAAE,IAAI,CAAC,QAAQ,EAAE;AACxB,gBAAA,KAAK,EAAE,IAAI;AACX,gBAAA,MAAM,EAAE,UAAU;AACnB,aAAA,CAAC;;;IAIN,SAAS,CAAC,mBAA2B,CAAC,EAAA;AACpC,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,GAAG,gBAAgB;AAC3D,QAAA,IAAI,aAAa,IAAI,CAAC,IAAI,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE;AAC9D,YAAA,IAAI,CAAC,aAAa,GAAG,aAAa;AAClC,YAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;AACjB,gBAAA,KAAK,EAAE,IAAI,CAAC,IAAI,EAAE;AAClB,gBAAA,OAAO,EAAE,IAAI,CAAC,QAAQ,EAAE;AACxB,gBAAA,KAAK,EAAE,IAAI;AACX,gBAAA,MAAM,EAAE,UAAU;AACnB,aAAA,CAAC;;;AAIN,IAAA,WAAW,CAAC,EAAyC,EAAA;AACnD,QAAA,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,EAAE,CAAC;QAEjC,IAAI,CAAC,sBAAsB,KAAK,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,KAAI;YACnD,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,KAAK,CAAC;AAChD,SAAC,CAAC;AAEF,QAAA,OAAO,MAAK;YACV,MAAM,OAAO,GAAG,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,EAAE,CAAC;YACpD,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC;YAE3C,IAAI,IAAI,CAAC,mBAAmB,CAAC,MAAM,KAAK,CAAC,EAAE;AACzC,gBAAA,IAAI,CAAC,sBAAsB,EAAE,WAAW,EAAE;AAC1C,gBAAA,IAAI,CAAC,sBAAsB,GAAG,IAAI;;AAEtC,SAAC;;;AAIH,IAAA,yBAAyB,CAAC,GAAA,GAAc,EAAE,EAAE,KAAc,EAAA;AACxD,QAAA,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;;AAG1D,IAAA,SAAS,CACP,MAA4B,EAC5B,OAAuC,EACvC,QAA8B,EAAA;AAE9B,QAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC;AAC7B,YAAA,IAAI,EAAE,MAAM;YACZ,KAAK,EAAE,OAAO,IAAI,SAAS;YAC3B,QAAQ,EAAE,QAAQ,IAAI,SAAS;AAChC,SAAA,CAAC;;AAGJ,IAAA,SAAS,CAAC,GAAW,EAAA;AACnB,QAAA,OAAO,IAAK;;AAGN,IAAA,WAAW,CAAC,IAAY,EAAE,KAAa,EAAE,KAAU,EAAA;AACzD,QAAA,IAAI,IAAI,CAAC,aAAa,GAAG,CAAC,EAAE;YAC1B,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;;AAE9C,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,aAAa,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;QACzD,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC;;kHAvLpC,WAAW,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;sHAAX,WAAW,EAAA,CAAA;;sGAAX,WAAW,EAAA,UAAA,EAAA,CAAA;kBADvB;;AA4LD,MAAM,aAAa,CAAA;AAER,IAAA,IAAA;AACA,IAAA,KAAA;AACA,IAAA,KAAA;AAHT,IAAA,WAAA,CACS,IAAY,EACZ,KAAa,EACb,KAAU,EAAA;QAFV,IAAI,CAAA,IAAA,GAAJ,IAAI;QACJ,IAAK,CAAA,KAAA,GAAL,KAAK;QACL,IAAK,CAAA,KAAA,GAAL,KAAK;;AAEf;;AC5MD;;;;;AAKG;AAEG,MAAO,oBAAqB,SAAQ,gBAAgB,CAAA;IACxD,gBAAgB,GAAW,GAAG;IAC9B,YAAY,GAAW,GAAG;IAC1B,aAAa,GAAW,EAAE;IAC1B,UAAU,GAAa,EAAE;;AAEzB,IAAA,QAAQ,GAAG,IAAI,OAAO,EAAsB;IACpC,YAAY,GAAU,EAAE;AAChC,IAAA,WAAA,GAAA;AACE,QAAA,KAAK,EAAE;;AAGT,IAAA,gBAAgB,CAAC,GAAW,EAAA;AAC1B,QAAA,IAAI,CAAC,YAAY,GAAG,GAAG;AACvB,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,kBAAkB,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;;IAGhD,IAAI,CAAC,cAAuB,KAAK,EAAA;QACxC,OAAO,IAAI,CAAC,YAAY;;AAGjB,IAAA,kBAAkB,CAAC,QAAgB,EAAA;AAC1C,QAAA,IAAI,QAAQ,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;YACnE,OAAO,IAAI,CAAC,gBAAgB,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC;;AAEtD,QAAA,OAAO,IAAI,CAAC,gBAAgB,GAAG,QAAQ;;AAGhC,IAAA,SAAS,CAAC,GAAQ,EAAE,KAAa,EAAE,IAAY,EAAE,KAAa,EAAA;;AAErE,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC;AAE3B,QAAA,IAAI,CAAC,aAAa,GAAG,KAAK;QAE1B,MAAM,GAAG,GAAG,IAAI,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,KAAK,GAAG,EAAE,CAAC;AACxD,QAAA,IAAI,CAAC,YAAY,GAAG,GAAG;QAEvB,MAAM,WAAW,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC;AAChD,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC;;AAG1B,IAAA,YAAY,CAAC,GAAQ,EAAE,KAAa,EAAE,IAAY,EAAE,KAAa,EAAA;;AAExE,QAAA,IAAI,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,GAAG;AAE5D,QAAA,IAAI,CAAC,aAAa,GAAG,KAAK;QAE1B,MAAM,GAAG,GAAG,IAAI,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,KAAK,GAAG,EAAE,CAAC;AACxD,QAAA,IAAI,CAAC,YAAY,GAAG,GAAG;QAEvB,MAAM,WAAW,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC;QAChD,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;;AAGxC,IAAA,UAAU,CAAC,EAAwB,EAAA;QAC1C,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAC,IAAI,EAAE,EAAE,EAAC,CAAC;;IAG5B,WAAW,GAAA;QAClB,OAAO,IAAI,CAAC,gBAAgB;;IAGrB,IAAI,GAAA;QACX,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;AAC9B,YAAA,IAAI,CAAC,UAAU,CAAC,GAAG,EAAE;AACrB,YAAA,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE;AACvB,YAAA,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,EAAE;AAC7F,YAAA,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC;;;IAIzB,OAAO,GAAA;AACd,QAAA,MAAM,iBAAiB;;IAGhB,QAAQ,GAAA;AACf,QAAA,OAAO,IAAI,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,CAAC;;kHA5EpD,oBAAoB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;sHAApB,oBAAoB,EAAA,CAAA;;sGAApB,oBAAoB,EAAA,UAAA,EAAA,CAAA;kBADhC;;AAiFD,MAAM,kBAAkB,CAAA;AAGH,IAAA,MAAA;IAFnB,GAAG,GAAY,IAAI;IACnB,IAAI,GAAW,UAAU;AACzB,IAAA,WAAA,CAAmB,MAAc,EAAA;QAAd,IAAM,CAAA,MAAA,GAAN,MAAM;;AAC1B;;ACzFD;;;;;AAKG;SACa,oBAAoB,GAAA;IAClC,OAAO;AACL,QAAA,EAAC,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,WAAW,EAAC;AAC1C,QAAA,EAAC,OAAO,EAAEC,kBAAgB,EAAE,QAAQ,EAAE,oBAAoB,EAAC;KAC5D;AACH;;;;"}