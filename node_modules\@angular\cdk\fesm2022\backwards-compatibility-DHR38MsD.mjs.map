{"version": 3, "file": "backwards-compatibility-DHR38MsD.mjs", "sources": ["../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/platform/features/backwards-compatibility.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Renderer2, VERSION} from '@angular/core';\n\n// TODO(crisbeto): replace interface with the one from core when making breaking changes for v20.\n/** Options when binding events manually. */\nexport interface _ListenerOptions {\n  capture?: boolean;\n  once?: boolean;\n  passive?: boolean;\n}\n\n// TODO(crisbeto): remove this function when making breaking changes for v20.\n/**\n * Binds an event listener with specific options in a backwards-compatible way.\n * This function is necessary, because `Renderer2.listen` only supports listener options\n * after 19.1 and during the v19 period we support any 19.x version.\n * @docs-private\n */\nexport function _bindEventWithOptions(\n  renderer: Renderer2,\n  target: EventTarget,\n  eventName: string,\n  callback: (event: any) => boolean | void,\n  options: _ListenerOptions,\n): () => void {\n  const major = parseInt(VERSION.major);\n  const minor = parseInt(VERSION.minor);\n\n  // Event options in `listen` are only supported in 19.1 and beyond.\n  // We also allow 0.0.x, because that indicates a build at HEAD.\n  if (major > 19 || (major === 19 && minor > 0) || (major === 0 && minor === 0)) {\n    return renderer.listen(target, eventName, callback, options);\n  }\n\n  target.addEventListener(eventName, callback, options);\n\n  return () => {\n    target.removeEventListener(eventName, callback, options);\n  };\n}\n"], "names": [], "mappings": ";;AAkBA;AACA;;;;;AAKG;AACG,SAAU,qBAAqB,CACnC,QAAmB,EACnB,MAAmB,EACnB,SAAiB,EACjB,QAAwC,EACxC,OAAyB,EAAA;IAEzB,MAAM,KAAK,GAAG,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC;IACrC,MAAM,KAAK,GAAG,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC;;;IAIrC,IAAI,KAAK,GAAG,EAAE,KAAK,KAAK,KAAK,EAAE,IAAI,KAAK,GAAG,CAAC,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;AAC7E,QAAA,OAAO,QAAQ,CAAC,MAAM,CAAC,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,CAAC;;IAG9D,MAAM,CAAC,gBAAgB,CAAC,SAAS,EAAE,QAAQ,EAAE,OAAO,CAAC;AAErD,IAAA,OAAO,MAAK;QACV,MAAM,CAAC,mBAAmB,CAAC,SAAS,EAAE,QAAQ,EAAE,OAAO,CAAC;AAC1D,KAAC;AACH;;;;"}