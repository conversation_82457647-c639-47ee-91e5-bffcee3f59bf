{"name": "gradecenter", "version": "1.0.0", "description": "grade center", "type": "module", "main": "index.js", "scripts": {"test:coverage": "npm run test:coverage --workspaces --if-present -- --passWithNoTests", "format:check": "prettier --check .", "lint": "eslint .", "start": "concurrently \"npm run start:server\" \"npm run start:client\"", "start:server": "npm start --workspace=Server", "start:client": "npm start --workspace=Client", "test": "npm test --workspaces --if-present -- --passWithNoTests", "format": "prettier --write .", "setup": "cd Server && npm install && cd ../Client && npm install", "start:db": "docker-compose up -d", "stop:db": "docker-compose down", "start:all": "npm run start:db && concurrently \"npm run start:server\" \"npm run start:client\""}, "repository": {"type": "git", "url": "git+https://github.com/UCLL-FEG-Leuven/project-2425-34-five.git"}, "keywords": ["grade", "center"], "author": "<PERSON>", "license": "ISC", "bugs": {"url": "https://github.com/UCLL-FEG-Leuven/project-2425-34-five/issues"}, "homepage": "https://github.com/UCLL-FEG-Leuven/project-2425-34-five#readme", "devDependencies": {"@angular-eslint/eslint-plugin": "^19.4.0", "@angular-eslint/eslint-plugin-template": "^19.4.0", "@angular-eslint/template-parser": "^19.4.0", "@angular/localize": "^19.2.0", "@types/jest": "^29.5.14", "autoprefixer": "^10.4.21", "concurrently": "^9.1.0", "eslint": "^9.26.0", "eslint-plugin-angular": "^4.1.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jest": "^28.11.0", "glob": "^11.0.2", "globals": "^16.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-preset-angular": "^14.5.5", "jsdom": "^26.1.0", "prettier": "^3.5.3", "rimraf": "^6.0.1", "typescript-eslint": "^8.32.1"}, "workspaces": ["Server", "Client"], "overrides": {"glob": "^11.0.2", "rimraf": "^6.0.1", "inflight": "^2.0.0", "debug": "^4.3.4", "abab": "latest", "domexception": "latest", "mime": "^3.0.0", "form-data": "^4.0.0", "qs": "^6.11.2", "ws": "^8.16.0", "xmlhttprequest": "^1.8.0", "cookie": "^0.7.0", "engine.io-client": "^6.5.3", "socket.io-client": "^4.7.4", "socket.io-parser": "^4.2.4", "parsejson": "^0.0.3", "parseuri": "^0.0.6", "hoek": "^6.1.3", "boom": "^7.3.0", "cryptiles": "^4.1.3", "sntp": "^3.0.2", "hawk": "^9.0.1", "tunnel-agent": "^0.6.0", "request": "^2.88.2", "winston": "^3.11.0"}, "dependencies": {"client": "^0.0.1"}}