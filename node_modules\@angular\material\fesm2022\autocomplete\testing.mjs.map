{"version": 3, "file": "testing.mjs", "sources": ["../../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/autocomplete/testing/autocomplete-harness.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {coerceBooleanProperty} from '@angular/cdk/coercion';\nimport {\n  ComponentHarness,\n  ComponentHarnessConstructor,\n  HarnessPredicate,\n  TestElement,\n} from '@angular/cdk/testing';\nimport {\n  MatOptgroupHarness,\n  MatOptionHarness,\n  OptgroupHarnessFilters,\n  OptionHarnessFilters,\n} from '../../core/testing';\nimport {AutocompleteHarnessFilters} from './autocomplete-harness-filters';\n\nexport class MatAutocompleteHarness extends ComponentHarness {\n  private _documentRootLocator = this.documentRootLocatorFactory();\n\n  /** The selector for the host element of a `MatAutocomplete` instance. */\n  static hostSelector = '.mat-mdc-autocomplete-trigger';\n\n  /**\n   * Gets a `HarnessPredicate` that can be used to search for an autocomplete with specific\n   * attributes.\n   * @param options Options for filtering which autocomplete instances are considered a match.\n   * @return a `HarnessPredicate` configured with the given options.\n   */\n  static with<T extends MatAutocompleteHarness>(\n    this: ComponentHarnessConstructor<T>,\n    options: AutocompleteHarnessFilters = {},\n  ): HarnessPredicate<T> {\n    return new HarnessPredicate(this, options)\n      .addOption('value', options.value, (harness, value) =>\n        HarnessPredicate.stringMatches(harness.getValue(), value),\n      )\n      .addOption('disabled', options.disabled, async (harness, disabled) => {\n        return (await harness.isDisabled()) === disabled;\n      });\n  }\n\n  /** Gets the value of the autocomplete input. */\n  async getValue(): Promise<string> {\n    return (await this.host()).getProperty<string>('value');\n  }\n\n  /** Whether the autocomplete input is disabled. */\n  async isDisabled(): Promise<boolean> {\n    const disabled = (await this.host()).getAttribute('disabled');\n    return coerceBooleanProperty(await disabled);\n  }\n\n  /** Focuses the autocomplete input. */\n  async focus(): Promise<void> {\n    return (await this.host()).focus();\n  }\n\n  /** Blurs the autocomplete input. */\n  async blur(): Promise<void> {\n    return (await this.host()).blur();\n  }\n\n  /** Whether the autocomplete input is focused. */\n  async isFocused(): Promise<boolean> {\n    return (await this.host()).isFocused();\n  }\n\n  /** Enters text into the autocomplete. */\n  async enterText(value: string): Promise<void> {\n    return (await this.host()).sendKeys(value);\n  }\n\n  /** Clears the input value. */\n  async clear(): Promise<void> {\n    return (await this.host()).clear();\n  }\n\n  /** Gets the options inside the autocomplete panel. */\n  async getOptions(filters?: Omit<OptionHarnessFilters, 'ancestor'>): Promise<MatOptionHarness[]> {\n    if (!(await this.isOpen())) {\n      throw new Error('Unable to retrieve options for autocomplete. Autocomplete panel is closed.');\n    }\n\n    return this._documentRootLocator.locatorForAll(\n      MatOptionHarness.with({\n        ...(filters || {}),\n        ancestor: await this._getPanelSelector(),\n      } as OptionHarnessFilters),\n    )();\n  }\n\n  /** Gets the option groups inside the autocomplete panel. */\n  async getOptionGroups(\n    filters?: Omit<OptgroupHarnessFilters, 'ancestor'>,\n  ): Promise<MatOptgroupHarness[]> {\n    if (!(await this.isOpen())) {\n      throw new Error(\n        'Unable to retrieve option groups for autocomplete. Autocomplete panel is closed.',\n      );\n    }\n\n    return this._documentRootLocator.locatorForAll(\n      MatOptgroupHarness.with({\n        ...(filters || {}),\n        ancestor: await this._getPanelSelector(),\n      } as OptgroupHarnessFilters),\n    )();\n  }\n\n  /** Selects the first option matching the given filters. */\n  async selectOption(filters: OptionHarnessFilters): Promise<void> {\n    await this.focus(); // Focus the input to make sure the autocomplete panel is shown.\n    const options = await this.getOptions(filters);\n    if (!options.length) {\n      throw Error(`Could not find a mat-option matching ${JSON.stringify(filters)}`);\n    }\n    await options[0].click();\n  }\n\n  /** Whether the autocomplete is open. */\n  async isOpen(): Promise<boolean> {\n    const panel = await this._getPanel();\n    return !!panel && (await panel.hasClass(`mat-mdc-autocomplete-visible`));\n  }\n\n  /** Gets the panel associated with this autocomplete trigger. */\n  private async _getPanel(): Promise<TestElement | null> {\n    // Technically this is static, but it needs to be in a\n    // function, because the autocomplete's panel ID can changed.\n    return this._documentRootLocator.locatorForOptional(await this._getPanelSelector())();\n  }\n\n  /** Gets the selector that can be used to find the autocomplete trigger's panel. */\n  protected async _getPanelSelector(): Promise<string> {\n    return `#${await (await this.host()).getAttribute('aria-controls')}`;\n  }\n}\n"], "names": [], "mappings": ";;;;;AAuBM,MAAO,sBAAuB,SAAQ,gBAAgB,CAAA;AAClD,IAAA,oBAAoB,GAAG,IAAI,CAAC,0BAA0B,EAAE;;AAGhE,IAAA,OAAO,YAAY,GAAG,+BAA+B;AAErD;;;;;AAKG;AACH,IAAA,OAAO,IAAI,CAET,OAAA,GAAsC,EAAE,EAAA;AAExC,QAAA,OAAO,IAAI,gBAAgB,CAAC,IAAI,EAAE,OAAO;aACtC,SAAS,CAAC,OAAO,EAAE,OAAO,CAAC,KAAK,EAAE,CAAC,OAAO,EAAE,KAAK,KAChD,gBAAgB,CAAC,aAAa,CAAC,OAAO,CAAC,QAAQ,EAAE,EAAE,KAAK,CAAC;AAE1D,aAAA,SAAS,CAAC,UAAU,EAAE,OAAO,CAAC,QAAQ,EAAE,OAAO,OAAO,EAAE,QAAQ,KAAI;YACnE,OAAO,CAAC,MAAM,OAAO,CAAC,UAAU,EAAE,MAAM,QAAQ;AAClD,SAAC,CAAC;;;AAIN,IAAA,MAAM,QAAQ,GAAA;AACZ,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,WAAW,CAAS,OAAO,CAAC;;;AAIzD,IAAA,MAAM,UAAU,GAAA;AACd,QAAA,MAAM,QAAQ,GAAG,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,YAAY,CAAC,UAAU,CAAC;AAC7D,QAAA,OAAO,qBAAqB,CAAC,MAAM,QAAQ,CAAC;;;AAI9C,IAAA,MAAM,KAAK,GAAA;QACT,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,KAAK,EAAE;;;AAIpC,IAAA,MAAM,IAAI,GAAA;QACR,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,IAAI,EAAE;;;AAInC,IAAA,MAAM,SAAS,GAAA;QACb,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,SAAS,EAAE;;;IAIxC,MAAM,SAAS,CAAC,KAAa,EAAA;AAC3B,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,QAAQ,CAAC,KAAK,CAAC;;;AAI5C,IAAA,MAAM,KAAK,GAAA;QACT,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,KAAK,EAAE;;;IAIpC,MAAM,UAAU,CAAC,OAAgD,EAAA;QAC/D,IAAI,EAAE,MAAM,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE;AAC1B,YAAA,MAAM,IAAI,KAAK,CAAC,4EAA4E,CAAC;;QAG/F,OAAO,IAAI,CAAC,oBAAoB,CAAC,aAAa,CAC5C,gBAAgB,CAAC,IAAI,CAAC;AACpB,YAAA,IAAI,OAAO,IAAI,EAAE,CAAC;AAClB,YAAA,QAAQ,EAAE,MAAM,IAAI,CAAC,iBAAiB,EAAE;SACjB,CAAC,CAC3B,EAAE;;;IAIL,MAAM,eAAe,CACnB,OAAkD,EAAA;QAElD,IAAI,EAAE,MAAM,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE;AAC1B,YAAA,MAAM,IAAI,KAAK,CACb,kFAAkF,CACnF;;QAGH,OAAO,IAAI,CAAC,oBAAoB,CAAC,aAAa,CAC5C,kBAAkB,CAAC,IAAI,CAAC;AACtB,YAAA,IAAI,OAAO,IAAI,EAAE,CAAC;AAClB,YAAA,QAAQ,EAAE,MAAM,IAAI,CAAC,iBAAiB,EAAE;SACf,CAAC,CAC7B,EAAE;;;IAIL,MAAM,YAAY,CAAC,OAA6B,EAAA;AAC9C,QAAA,MAAM,IAAI,CAAC,KAAK,EAAE,CAAC;QACnB,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;AAC9C,QAAA,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;YACnB,MAAM,KAAK,CAAC,CAAA,qCAAA,EAAwC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAE,CAAA,CAAC;;AAEhF,QAAA,MAAM,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE;;;AAI1B,IAAA,MAAM,MAAM,GAAA;AACV,QAAA,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,SAAS,EAAE;AACpC,QAAA,OAAO,CAAC,CAAC,KAAK,KAAK,MAAM,KAAK,CAAC,QAAQ,CAAC,CAA8B,4BAAA,CAAA,CAAC,CAAC;;;AAIlE,IAAA,MAAM,SAAS,GAAA;;;AAGrB,QAAA,OAAO,IAAI,CAAC,oBAAoB,CAAC,kBAAkB,CAAC,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC,EAAE;;;AAI7E,IAAA,MAAM,iBAAiB,GAAA;AAC/B,QAAA,OAAO,CAAI,CAAA,EAAA,MAAM,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,YAAY,CAAC,eAAe,CAAC,EAAE;;;;;;"}