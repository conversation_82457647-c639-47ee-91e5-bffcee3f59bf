{"name": "@schematics/angular", "version": "19.2.13", "description": "Schematics specific to Angular", "homepage": "https://github.com/angular/angular-cli", "keywords": ["Angular CLI", "Angular DevKit", "angular", "blueprints", "code generation", "devkit", "schematics", "sdk"], "exports": {"./package.json": "./package.json", "./utility": "./utility/index.js", "./utility/*": "./utility/*.js", "./migrations/migration-collection.json": "./migrations/migration-collection.json", "./*": "./*.js"}, "schematics": "./collection.json", "dependencies": {"@angular-devkit/core": "19.2.13", "@angular-devkit/schematics": "19.2.13", "jsonc-parser": "3.3.1"}, "repository": {"type": "git", "url": "https://github.com/angular/angular-cli.git"}, "packageManager": "pnpm@9.15.6", "engines": {"node": "^18.19.1 || ^20.11.1 || >=22.0.0", "npm": "^6.11.0 || ^7.5.6 || >=8.0.0", "yarn": ">= 1.13.0"}, "author": "Angular Authors", "license": "MIT", "bugs": {"url": "https://github.com/angular/angular-cli/issues"}}