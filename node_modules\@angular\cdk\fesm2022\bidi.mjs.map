{"version": 3, "file": "bidi.mjs", "sources": ["../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/bidi/dir.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/bidi/bidi-module.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Directive, Output, Input, EventEmitter, AfterContentInit, OnDestroy} from '@angular/core';\n\nimport {Direction, Directionality, _resolveDirectionality} from './directionality';\n\n/**\n * Directive to listen for changes of direction of part of the DOM.\n *\n * Provides itself as Directionality such that descendant directives only need to ever inject\n * Directionality to get the closest direction.\n */\n@Directive({\n  selector: '[dir]',\n  providers: [{provide: Directionality, useExisting: Dir}],\n  host: {'[attr.dir]': '_rawDir'},\n  exportAs: 'dir',\n})\nexport class Dir implements Directionality, AfterContentInit, OnDestroy {\n  /** Normalized direction that accounts for invalid/unsupported values. */\n  private _dir: Direction = 'ltr';\n\n  /** Whether the `value` has been set to its initial value. */\n  private _isInitialized: boolean = false;\n\n  /** Direction as passed in by the consumer. */\n  _rawDir: string;\n\n  /** Event emitted when the direction changes. */\n  @Output('dirChange') readonly change = new EventEmitter<Direction>();\n\n  /** @docs-private */\n  @Input()\n  get dir(): Direction {\n    return this._dir;\n  }\n  set dir(value: Direction | 'auto') {\n    const previousValue = this._dir;\n\n    // Note: `_resolveDirectionality` resolves the language based on the browser's language,\n    // whereas the browser does it based on the content of the element. Since doing so based\n    // on the content can be expensive, for now we're doing the simpler matching.\n    this._dir = _resolveDirectionality(value);\n    this._rawDir = value;\n\n    if (previousValue !== this._dir && this._isInitialized) {\n      this.change.emit(this._dir);\n    }\n  }\n\n  /** Current layout direction of the element. */\n  get value(): Direction {\n    return this.dir;\n  }\n\n  /** Initialize once default value has been set. */\n  ngAfterContentInit() {\n    this._isInitialized = true;\n  }\n\n  ngOnDestroy() {\n    this.change.complete();\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {NgModule} from '@angular/core';\nimport {Dir} from './dir';\n\n@NgModule({\n  imports: [Dir],\n  exports: [Dir],\n})\nexport class BidiModule {}\n"], "names": [], "mappings": ";;;;;;AAYA;;;;;AAKG;MAOU,GAAG,CAAA;;IAEN,IAAI,GAAc,KAAK;;IAGvB,cAAc,GAAY,KAAK;;AAGvC,IAAA,OAAO;;AAGuB,IAAA,MAAM,GAAG,IAAI,YAAY,EAAa;;AAGpE,IAAA,IACI,GAAG,GAAA;QACL,OAAO,IAAI,CAAC,IAAI;;IAElB,IAAI,GAAG,CAAC,KAAyB,EAAA;AAC/B,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,IAAI;;;;AAK/B,QAAA,IAAI,CAAC,IAAI,GAAG,sBAAsB,CAAC,KAAK,CAAC;AACzC,QAAA,IAAI,CAAC,OAAO,GAAG,KAAK;QAEpB,IAAI,aAAa,KAAK,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,cAAc,EAAE;YACtD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;;;;AAK/B,IAAA,IAAI,KAAK,GAAA;QACP,OAAO,IAAI,CAAC,GAAG;;;IAIjB,kBAAkB,GAAA;AAChB,QAAA,IAAI,CAAC,cAAc,GAAG,IAAI;;IAG5B,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;;uGA3Cb,GAAG,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAH,GAAG,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,OAAA,EAAA,MAAA,EAAA,EAAA,GAAA,EAAA,KAAA,EAAA,EAAA,OAAA,EAAA,EAAA,MAAA,EAAA,WAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,UAAA,EAAA,SAAA,EAAA,EAAA,EAAA,SAAA,EAJH,CAAC,EAAC,OAAO,EAAE,cAAc,EAAE,WAAW,EAAE,GAAG,EAAC,CAAC,EAAA,QAAA,EAAA,CAAA,KAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAI7C,GAAG,EAAA,UAAA,EAAA,CAAA;kBANf,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,OAAO;oBACjB,SAAS,EAAE,CAAC,EAAC,OAAO,EAAE,cAAc,EAAE,WAAW,EAAK,GAAA,EAAC,CAAC;AACxD,oBAAA,IAAI,EAAE,EAAC,YAAY,EAAE,SAAS,EAAC;AAC/B,oBAAA,QAAQ,EAAE,KAAK;AAChB,iBAAA;8BAY+B,MAAM,EAAA,CAAA;sBAAnC,MAAM;uBAAC,WAAW;gBAIf,GAAG,EAAA,CAAA;sBADN;;;MCvBU,UAAU,CAAA;uGAAV,UAAU,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;wGAAV,UAAU,EAAA,OAAA,EAAA,CAHX,GAAG,CAAA,EAAA,OAAA,EAAA,CACH,GAAG,CAAA,EAAA,CAAA;wGAEF,UAAU,EAAA,CAAA;;2FAAV,UAAU,EAAA,UAAA,EAAA,CAAA;kBAJtB,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACR,OAAO,EAAE,CAAC,GAAG,CAAC;oBACd,OAAO,EAAE,CAAC,GAAG,CAAC;AACf,iBAAA;;;;;"}