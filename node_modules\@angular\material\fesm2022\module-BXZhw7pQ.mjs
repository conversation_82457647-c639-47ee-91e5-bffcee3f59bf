import { ObserversModule } from '@angular/cdk/observers';
import * as i0 from '@angular/core';
import { NgModule } from '@angular/core';
import { j as <PERSON><PERSON><PERSON><PERSON><PERSON>, M as <PERSON><PERSON><PERSON><PERSON>, b as <PERSON><PERSON><PERSON><PERSON>, c as <PERSON><PERSON><PERSON>, e as <PERSON><PERSON><PERSON><PERSON><PERSON>, g as <PERSON><PERSON><PERSON><PERSON> } from './form-field-DqPi4knt.mjs';
import { M as MatCommonModule } from './common-module-WayjW0Pb.mjs';

class MatFormFieldModule {
    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: "12.0.0", version: "19.2.6", ngImport: i0, type: MatFormFieldModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });
    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: "14.0.0", version: "19.2.6", ngImport: i0, type: MatFormFieldModule, imports: [MatCommonModule,
            ObserversModule,
            MatFormField,
            Mat<PERSON><PERSON><PERSON>,
            <PERSON><PERSON><PERSON>r,
            <PERSON><PERSON><PERSON>,
            MatPrefix,
            MatSuffix], exports: [<PERSON><PERSON>orm<PERSON>ield, MatLabel, <PERSON><PERSON>int, <PERSON><PERSON><PERSON><PERSON>, Mat<PERSON>refix, MatSuffix, MatCommonModule] });
    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: "12.0.0", version: "19.2.6", ngImport: i0, type: MatFormFieldModule, imports: [MatCommonModule,
            ObserversModule, MatCommonModule] });
}
i0.ɵɵngDeclareClassMetadata({ minVersion: "12.0.0", version: "19.2.6", ngImport: i0, type: MatFormFieldModule, decorators: [{
            type: NgModule,
            args: [{
                    imports: [
                        MatCommonModule,
                        ObserversModule,
                        MatFormField,
                        MatLabel,
                        MatError,
                        MatHint,
                        MatPrefix,
                        MatSuffix,
                    ],
                    exports: [MatFormField, MatLabel, MatHint, MatError, MatPrefix, MatSuffix, MatCommonModule],
                }]
        }] });

export { MatFormFieldModule as M };
//# sourceMappingURL=module-BXZhw7pQ.mjs.map
