{"version": 3, "file": "datepicker.mjs", "sources": ["../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/datepicker/datepicker-errors.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/datepicker/datepicker-intl.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/datepicker/calendar-body.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/datepicker/calendar-body.html", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/datepicker/date-selection-model.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/datepicker/date-range-selection-strategy.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/datepicker/month-view.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/datepicker/month-view.html", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/datepicker/multi-year-view.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/datepicker/multi-year-view.html", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/datepicker/year-view.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/datepicker/year-view.html", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/datepicker/calendar.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/datepicker/calendar-header.html", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/datepicker/calendar.html", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/datepicker/datepicker-base.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/datepicker/datepicker-content.html", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/datepicker/datepicker.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/datepicker/datepicker-input-base.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/datepicker/datepicker-input.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/datepicker/datepicker-toggle.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/datepicker/datepicker-toggle.html", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/datepicker/date-range-input.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/datepicker/date-range-input.html", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/datepicker/aria-accessible-name.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/datepicker/date-range-input-parts.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/datepicker/date-range-picker.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/datepicker/datepicker-actions.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/datepicker/datepicker-module.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/datepicker/datepicker-animations.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/** @docs-private */\nexport function createMissingDateImplError(provider: string) {\n  return Error(\n    `MatDatepicker: No provider found for ${provider}. You must add one of the following ` +\n      `to your app config: provideNativeDateAdapter, provideDateFnsAdapter, ` +\n      `provideLuxonDateAdapter, provideMomentDateAdapter, or provide a custom implementation.`,\n  );\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Injectable} from '@angular/core';\nimport {Subject} from 'rxjs';\n\n/** Datepicker data that requires internationalization. */\n@Injectable({providedIn: 'root'})\nexport class MatDatepickerIntl {\n  /**\n   * Stream that emits whenever the labels here are changed. Use this to notify\n   * components if the labels have changed after initialization.\n   */\n  readonly changes: Subject<void> = new Subject<void>();\n\n  /** A label for the calendar popup (used by screen readers). */\n  calendarLabel = 'Calendar';\n\n  /** A label for the button used to open the calendar popup (used by screen readers). */\n  openCalendarLabel = 'Open calendar';\n\n  /** Label for the button used to close the calendar popup. */\n  closeCalendarLabel = 'Close calendar';\n\n  /** A label for the previous month button (used by screen readers). */\n  prevMonthLabel = 'Previous month';\n\n  /** A label for the next month button (used by screen readers). */\n  nextMonthLabel = 'Next month';\n\n  /** A label for the previous year button (used by screen readers). */\n  prevYearLabel = 'Previous year';\n\n  /** A label for the next year button (used by screen readers). */\n  nextYearLabel = 'Next year';\n\n  /** A label for the previous multi-year button (used by screen readers). */\n  prevMultiYearLabel = 'Previous 24 years';\n\n  /** A label for the next multi-year button (used by screen readers). */\n  nextMultiYearLabel = 'Next 24 years';\n\n  /** A label for the 'switch to month view' button (used by screen readers). */\n  switchToMonthViewLabel = 'Choose date';\n\n  /** A label for the 'switch to year view' button (used by screen readers). */\n  switchToMultiYearViewLabel = 'Choose month and year';\n\n  /**\n   * A label for the first date of a range of dates (used by screen readers).\n   * @deprecated Provide your own internationalization string.\n   * @breaking-change 17.0.0\n   */\n  startDateLabel = 'Start date';\n\n  /**\n   * A label for the last date of a range of dates (used by screen readers).\n   * @deprecated Provide your own internationalization string.\n   * @breaking-change 17.0.0\n   */\n  endDateLabel = 'End date';\n\n  /**\n   * A label for the Comparison date of a range of dates (used by screen readers).\n   */\n  comparisonDateLabel = 'Comparison range';\n\n  /** Formats a range of years (used for visuals). */\n  formatYearRange(start: string, end: string): string {\n    return `${start} \\u2013 ${end}`;\n  }\n\n  /** Formats a label for a range of years (used by screen readers). */\n  formatYearRangeLabel(start: string, end: string): string {\n    return `${start} to ${end}`;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Platform, _bindEventWithOptions} from '@angular/cdk/platform';\nimport {\n  ChangeDetectionStrategy,\n  Component,\n  ElementRef,\n  EventEmitter,\n  Input,\n  Output,\n  ViewEncapsulation,\n  NgZone,\n  OnChanges,\n  SimpleChanges,\n  OnDestroy,\n  AfterViewChecked,\n  inject,\n  afterNextRender,\n  Injector,\n  Renderer2,\n} from '@angular/core';\nimport {_IdGenerator} from '@angular/cdk/a11y';\nimport {NgClass} from '@angular/common';\nimport {_CdkPrivateStyleLoader} from '@angular/cdk/private';\nimport {_StructuralStylesLoader} from '../core';\nimport {MatDatepickerIntl} from './datepicker-intl';\n\n/** Extra CSS classes that can be associated with a calendar cell. */\nexport type MatCalendarCellCssClasses = string | string[] | Set<string> | {[key: string]: any};\n\n/** Function that can generate the extra classes that should be added to a calendar cell. */\nexport type MatCalendarCellClassFunction<D> = (\n  date: D,\n  view: 'month' | 'year' | 'multi-year',\n) => MatCalendarCellCssClasses;\n\nlet uniqueIdCounter = 0;\n\n/**\n * An internal class that represents the data corresponding to a single calendar cell.\n * @docs-private\n */\nexport class MatCalendarCell<D = any> {\n  readonly id = uniqueIdCounter++;\n\n  constructor(\n    public value: number,\n    public displayValue: string,\n    public ariaLabel: string,\n    public enabled: boolean,\n    public cssClasses: MatCalendarCellCssClasses = {},\n    public compareValue = value,\n    public rawValue?: D,\n  ) {}\n}\n\n/** Event emitted when a date inside the calendar is triggered as a result of a user action. */\nexport interface MatCalendarUserEvent<D> {\n  value: D;\n  event: Event;\n}\n\n/** Event options that can be used to bind an active, capturing event. */\nconst activeCapturingEventOptions = {\n  passive: false,\n  capture: true,\n};\n\n/** Event options that can be used to bind a passive, capturing event. */\nconst passiveCapturingEventOptions = {\n  passive: true,\n  capture: true,\n};\n\n/** Event options that can be used to bind a passive, non-capturing event. */\nconst passiveEventOptions = {passive: true};\n\n/**\n * An internal component used to display calendar data in a table.\n * @docs-private\n */\n@Component({\n  selector: '[mat-calendar-body]',\n  templateUrl: 'calendar-body.html',\n  styleUrl: 'calendar-body.css',\n  host: {\n    'class': 'mat-calendar-body',\n  },\n  exportAs: 'matCalendarBody',\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  imports: [NgClass],\n})\nexport class MatCalendarBody<D = any> implements OnChanges, OnDestroy, AfterViewChecked {\n  private _elementRef = inject<ElementRef<HTMLElement>>(ElementRef);\n  private _ngZone = inject(NgZone);\n  private _platform = inject(Platform);\n  private _intl = inject(MatDatepickerIntl);\n  private _eventCleanups: (() => void)[];\n\n  /**\n   * Used to skip the next focus event when rendering the preview range.\n   * We need a flag like this, because some browsers fire focus events asynchronously.\n   */\n  private _skipNextFocus: boolean;\n\n  /**\n   * Used to focus the active cell after change detection has run.\n   */\n  private _focusActiveCellAfterViewChecked = false;\n\n  /** The label for the table. (e.g. \"Jan 2017\"). */\n  @Input() label: string;\n\n  /** The cells to display in the table. */\n  @Input() rows: MatCalendarCell[][];\n\n  /** The value in the table that corresponds to today. */\n  @Input() todayValue: number;\n\n  /** Start value of the selected date range. */\n  @Input() startValue: number;\n\n  /** End value of the selected date range. */\n  @Input() endValue: number;\n\n  /** The minimum number of free cells needed to fit the label in the first row. */\n  @Input() labelMinRequiredCells: number;\n\n  /** The number of columns in the table. */\n  @Input() numCols: number = 7;\n\n  /** The cell number of the active cell in the table. */\n  @Input() activeCell: number = 0;\n\n  ngAfterViewChecked() {\n    if (this._focusActiveCellAfterViewChecked) {\n      this._focusActiveCell();\n      this._focusActiveCellAfterViewChecked = false;\n    }\n  }\n\n  /** Whether a range is being selected. */\n  @Input() isRange: boolean = false;\n\n  /**\n   * The aspect ratio (width / height) to use for the cells in the table. This aspect ratio will be\n   * maintained even as the table resizes.\n   */\n  @Input() cellAspectRatio: number = 1;\n\n  /** Start of the comparison range. */\n  @Input() comparisonStart: number | null;\n\n  /** End of the comparison range. */\n  @Input() comparisonEnd: number | null;\n\n  /** Start of the preview range. */\n  @Input() previewStart: number | null = null;\n\n  /** End of the preview range. */\n  @Input() previewEnd: number | null = null;\n\n  /** ARIA Accessible name of the `<input matStartDate/>` */\n  @Input() startDateAccessibleName: string | null;\n\n  /** ARIA Accessible name of the `<input matEndDate/>` */\n  @Input() endDateAccessibleName: string | null;\n\n  /** Emits when a new value is selected. */\n  @Output() readonly selectedValueChange = new EventEmitter<MatCalendarUserEvent<number>>();\n\n  /** Emits when the preview has changed as a result of a user action. */\n  @Output() readonly previewChange = new EventEmitter<\n    MatCalendarUserEvent<MatCalendarCell | null>\n  >();\n\n  @Output() readonly activeDateChange = new EventEmitter<MatCalendarUserEvent<number>>();\n\n  /** Emits the date at the possible start of a drag event. */\n  @Output() readonly dragStarted = new EventEmitter<MatCalendarUserEvent<D>>();\n\n  /** Emits the date at the conclusion of a drag, or null if mouse was not released on a date. */\n  @Output() readonly dragEnded = new EventEmitter<MatCalendarUserEvent<D | null>>();\n\n  /** The number of blank cells to put at the beginning for the first row. */\n  _firstRowOffset: number;\n\n  /** Padding for the individual date cells. */\n  _cellPadding: string;\n\n  /** Width of an individual cell. */\n  _cellWidth: string;\n\n  /** ID for the start date label. */\n  _startDateLabelId: string;\n\n  /** ID for the end date label. */\n  _endDateLabelId: string;\n\n  /** ID for the comparison start date label. */\n  _comparisonStartDateLabelId: string;\n\n  /** ID for the comparison end date label. */\n  _comparisonEndDateLabelId: string;\n\n  private _didDragSinceMouseDown = false;\n\n  private _injector = inject(Injector);\n\n  comparisonDateAccessibleName = this._intl.comparisonDateLabel;\n\n  /**\n   * Tracking function for rows based on their identity. Ideally we would use some sort of\n   * key on the row, but that would require a breaking change for the `rows` input. We don't\n   * use the built-in identity tracking, because it logs warnings.\n   */\n  _trackRow = (row: MatCalendarCell[]) => row;\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    const renderer = inject(Renderer2);\n    const idGenerator = inject(_IdGenerator);\n    this._startDateLabelId = idGenerator.getId('mat-calendar-body-start-');\n    this._endDateLabelId = idGenerator.getId('mat-calendar-body-end-');\n    this._comparisonStartDateLabelId = idGenerator.getId('mat-calendar-body-comparison-start-');\n    this._comparisonEndDateLabelId = idGenerator.getId('mat-calendar-body-comparison-end-');\n\n    inject(_CdkPrivateStyleLoader).load(_StructuralStylesLoader);\n\n    this._ngZone.runOutsideAngular(() => {\n      const element = this._elementRef.nativeElement;\n      const cleanups = [\n        // `touchmove` is active since we need to call `preventDefault`.\n        _bindEventWithOptions(\n          renderer,\n          element,\n          'touchmove',\n          this._touchmoveHandler,\n          activeCapturingEventOptions,\n        ),\n        _bindEventWithOptions(\n          renderer,\n          element,\n          'mouseenter',\n          this._enterHandler,\n          passiveCapturingEventOptions,\n        ),\n        _bindEventWithOptions(\n          renderer,\n          element,\n          'focus',\n          this._enterHandler,\n          passiveCapturingEventOptions,\n        ),\n        _bindEventWithOptions(\n          renderer,\n          element,\n          'mouseleave',\n          this._leaveHandler,\n          passiveCapturingEventOptions,\n        ),\n        _bindEventWithOptions(\n          renderer,\n          element,\n          'blur',\n          this._leaveHandler,\n          passiveCapturingEventOptions,\n        ),\n        _bindEventWithOptions(\n          renderer,\n          element,\n          'mousedown',\n          this._mousedownHandler,\n          passiveEventOptions,\n        ),\n        _bindEventWithOptions(\n          renderer,\n          element,\n          'touchstart',\n          this._mousedownHandler,\n          passiveEventOptions,\n        ),\n      ];\n\n      if (this._platform.isBrowser) {\n        cleanups.push(\n          renderer.listen('window', 'mouseup', this._mouseupHandler),\n          renderer.listen('window', 'touchend', this._touchendHandler),\n        );\n      }\n\n      this._eventCleanups = cleanups;\n    });\n  }\n\n  /** Called when a cell is clicked. */\n  _cellClicked(cell: MatCalendarCell, event: MouseEvent): void {\n    // Ignore \"clicks\" that are actually canceled drags (eg the user dragged\n    // off and then went back to this cell to undo).\n    if (this._didDragSinceMouseDown) {\n      return;\n    }\n\n    if (cell.enabled) {\n      this.selectedValueChange.emit({value: cell.value, event});\n    }\n  }\n\n  _emitActiveDateChange(cell: MatCalendarCell, event: FocusEvent): void {\n    if (cell.enabled) {\n      this.activeDateChange.emit({value: cell.value, event});\n    }\n  }\n\n  /** Returns whether a cell should be marked as selected. */\n  _isSelected(value: number) {\n    return this.startValue === value || this.endValue === value;\n  }\n\n  ngOnChanges(changes: SimpleChanges) {\n    const columnChanges = changes['numCols'];\n    const {rows, numCols} = this;\n\n    if (changes['rows'] || columnChanges) {\n      this._firstRowOffset = rows && rows.length && rows[0].length ? numCols - rows[0].length : 0;\n    }\n\n    if (changes['cellAspectRatio'] || columnChanges || !this._cellPadding) {\n      this._cellPadding = `${(50 * this.cellAspectRatio) / numCols}%`;\n    }\n\n    if (columnChanges || !this._cellWidth) {\n      this._cellWidth = `${100 / numCols}%`;\n    }\n  }\n\n  ngOnDestroy() {\n    this._eventCleanups.forEach(cleanup => cleanup());\n  }\n\n  /** Returns whether a cell is active. */\n  _isActiveCell(rowIndex: number, colIndex: number): boolean {\n    let cellNumber = rowIndex * this.numCols + colIndex;\n\n    // Account for the fact that the first row may not have as many cells.\n    if (rowIndex) {\n      cellNumber -= this._firstRowOffset;\n    }\n\n    return cellNumber == this.activeCell;\n  }\n\n  /**\n   * Focuses the active cell after the microtask queue is empty.\n   *\n   * Adding a 0ms setTimeout seems to fix Voiceover losing focus when pressing PageUp/PageDown\n   * (issue #24330).\n   *\n   * Determined a 0ms by gradually increasing duration from 0 and testing two use cases with screen\n   * reader enabled:\n   *\n   * 1. Pressing PageUp/PageDown repeatedly with pausing between each key press.\n   * 2. Pressing and holding the PageDown key with repeated keys enabled.\n   *\n   * Test 1 worked roughly 95-99% of the time with 0ms and got a little bit better as the duration\n   * increased. Test 2 got slightly better until the duration was long enough to interfere with\n   * repeated keys. If the repeated key speed was faster than the timeout duration, then pressing\n   * and holding pagedown caused the entire page to scroll.\n   *\n   * Since repeated key speed can verify across machines, determined that any duration could\n   * potentially interfere with repeated keys. 0ms would be best because it almost entirely\n   * eliminates the focus being lost in Voiceover (#24330) without causing unintended side effects.\n   * Adding delay also complicates writing tests.\n   */\n  _focusActiveCell(movePreview = true) {\n    afterNextRender(\n      () => {\n        setTimeout(() => {\n          const activeCell: HTMLElement | null = this._elementRef.nativeElement.querySelector(\n            '.mat-calendar-body-active',\n          );\n\n          if (activeCell) {\n            if (!movePreview) {\n              this._skipNextFocus = true;\n            }\n\n            activeCell.focus();\n          }\n        });\n      },\n      {injector: this._injector},\n    );\n  }\n\n  /** Focuses the active cell after change detection has run and the microtask queue is empty. */\n  _scheduleFocusActiveCellAfterViewChecked() {\n    this._focusActiveCellAfterViewChecked = true;\n  }\n\n  /** Gets whether a value is the start of the main range. */\n  _isRangeStart(value: number) {\n    return isStart(value, this.startValue, this.endValue);\n  }\n\n  /** Gets whether a value is the end of the main range. */\n  _isRangeEnd(value: number) {\n    return isEnd(value, this.startValue, this.endValue);\n  }\n\n  /** Gets whether a value is within the currently-selected range. */\n  _isInRange(value: number): boolean {\n    return isInRange(value, this.startValue, this.endValue, this.isRange);\n  }\n\n  /** Gets whether a value is the start of the comparison range. */\n  _isComparisonStart(value: number) {\n    return isStart(value, this.comparisonStart, this.comparisonEnd);\n  }\n\n  /** Whether the cell is a start bridge cell between the main and comparison ranges. */\n  _isComparisonBridgeStart(value: number, rowIndex: number, colIndex: number) {\n    if (!this._isComparisonStart(value) || this._isRangeStart(value) || !this._isInRange(value)) {\n      return false;\n    }\n\n    let previousCell: MatCalendarCell | undefined = this.rows[rowIndex][colIndex - 1];\n\n    if (!previousCell) {\n      const previousRow = this.rows[rowIndex - 1];\n      previousCell = previousRow && previousRow[previousRow.length - 1];\n    }\n\n    return previousCell && !this._isRangeEnd(previousCell.compareValue);\n  }\n\n  /** Whether the cell is an end bridge cell between the main and comparison ranges. */\n  _isComparisonBridgeEnd(value: number, rowIndex: number, colIndex: number) {\n    if (!this._isComparisonEnd(value) || this._isRangeEnd(value) || !this._isInRange(value)) {\n      return false;\n    }\n\n    let nextCell: MatCalendarCell | undefined = this.rows[rowIndex][colIndex + 1];\n\n    if (!nextCell) {\n      const nextRow = this.rows[rowIndex + 1];\n      nextCell = nextRow && nextRow[0];\n    }\n\n    return nextCell && !this._isRangeStart(nextCell.compareValue);\n  }\n\n  /** Gets whether a value is the end of the comparison range. */\n  _isComparisonEnd(value: number) {\n    return isEnd(value, this.comparisonStart, this.comparisonEnd);\n  }\n\n  /** Gets whether a value is within the current comparison range. */\n  _isInComparisonRange(value: number) {\n    return isInRange(value, this.comparisonStart, this.comparisonEnd, this.isRange);\n  }\n\n  /**\n   * Gets whether a value is the same as the start and end of the comparison range.\n   * For context, the functions that we use to determine whether something is the start/end of\n   * a range don't allow for the start and end to be on the same day, because we'd have to use\n   * much more specific CSS selectors to style them correctly in all scenarios. This is fine for\n   * the regular range, because when it happens, the selected styles take over and still show where\n   * the range would've been, however we don't have these selected styles for a comparison range.\n   * This function is used to apply a class that serves the same purpose as the one for selected\n   * dates, but it only applies in the context of a comparison range.\n   */\n  _isComparisonIdentical(value: number) {\n    // Note that we don't need to null check the start/end\n    // here, because the `value` will always be defined.\n    return this.comparisonStart === this.comparisonEnd && value === this.comparisonStart;\n  }\n\n  /** Gets whether a value is the start of the preview range. */\n  _isPreviewStart(value: number) {\n    return isStart(value, this.previewStart, this.previewEnd);\n  }\n\n  /** Gets whether a value is the end of the preview range. */\n  _isPreviewEnd(value: number) {\n    return isEnd(value, this.previewStart, this.previewEnd);\n  }\n\n  /** Gets whether a value is inside the preview range. */\n  _isInPreview(value: number) {\n    return isInRange(value, this.previewStart, this.previewEnd, this.isRange);\n  }\n\n  /** Gets ids of aria descriptions for the start and end of a date range. */\n  _getDescribedby(value: number): string | null {\n    if (!this.isRange) {\n      return null;\n    }\n\n    if (this.startValue === value && this.endValue === value) {\n      return `${this._startDateLabelId} ${this._endDateLabelId}`;\n    } else if (this.startValue === value) {\n      return this._startDateLabelId;\n    } else if (this.endValue === value) {\n      return this._endDateLabelId;\n    }\n\n    if (this.comparisonStart !== null && this.comparisonEnd !== null) {\n      if (value === this.comparisonStart && value === this.comparisonEnd) {\n        return `${this._comparisonStartDateLabelId} ${this._comparisonEndDateLabelId}`;\n      } else if (value === this.comparisonStart) {\n        return this._comparisonStartDateLabelId;\n      } else if (value === this.comparisonEnd) {\n        return this._comparisonEndDateLabelId;\n      }\n    }\n\n    return null;\n  }\n\n  /**\n   * Event handler for when the user enters an element\n   * inside the calendar body (e.g. by hovering in or focus).\n   */\n  private _enterHandler = (event: Event) => {\n    if (this._skipNextFocus && event.type === 'focus') {\n      this._skipNextFocus = false;\n      return;\n    }\n\n    // We only need to hit the zone when we're selecting a range.\n    if (event.target && this.isRange) {\n      const cell = this._getCellFromElement(event.target as HTMLElement);\n\n      if (cell) {\n        this._ngZone.run(() => this.previewChange.emit({value: cell.enabled ? cell : null, event}));\n      }\n    }\n  };\n\n  private _touchmoveHandler = (event: TouchEvent) => {\n    if (!this.isRange) return;\n\n    const target = getActualTouchTarget(event);\n    const cell = target ? this._getCellFromElement(target as HTMLElement) : null;\n\n    if (target !== event.target) {\n      this._didDragSinceMouseDown = true;\n    }\n\n    // If the initial target of the touch is a date cell, prevent default so\n    // that the move is not handled as a scroll.\n    if (getCellElement(event.target as HTMLElement)) {\n      event.preventDefault();\n    }\n\n    this._ngZone.run(() => this.previewChange.emit({value: cell?.enabled ? cell : null, event}));\n  };\n\n  /**\n   * Event handler for when the user's pointer leaves an element\n   * inside the calendar body (e.g. by hovering out or blurring).\n   */\n  private _leaveHandler = (event: Event) => {\n    // We only need to hit the zone when we're selecting a range.\n    if (this.previewEnd !== null && this.isRange) {\n      if (event.type !== 'blur') {\n        this._didDragSinceMouseDown = true;\n      }\n\n      // Only reset the preview end value when leaving cells. This looks better, because\n      // we have a gap between the cells and the rows and we don't want to remove the\n      // range just for it to show up again when the user moves a few pixels to the side.\n      if (\n        event.target &&\n        this._getCellFromElement(event.target as HTMLElement) &&\n        !(\n          (event as MouseEvent).relatedTarget &&\n          this._getCellFromElement((event as MouseEvent).relatedTarget as HTMLElement)\n        )\n      ) {\n        this._ngZone.run(() => this.previewChange.emit({value: null, event}));\n      }\n    }\n  };\n\n  /**\n   * Triggered on mousedown or touchstart on a date cell.\n   * Respsonsible for starting a drag sequence.\n   */\n  private _mousedownHandler = (event: Event) => {\n    if (!this.isRange) return;\n\n    this._didDragSinceMouseDown = false;\n    // Begin a drag if a cell within the current range was targeted.\n    const cell = event.target && this._getCellFromElement(event.target as HTMLElement);\n    if (!cell || !this._isInRange(cell.compareValue)) {\n      return;\n    }\n\n    this._ngZone.run(() => {\n      this.dragStarted.emit({\n        value: cell.rawValue,\n        event,\n      });\n    });\n  };\n\n  /** Triggered on mouseup anywhere. Respsonsible for ending a drag sequence. */\n  private _mouseupHandler = (event: Event) => {\n    if (!this.isRange) return;\n\n    const cellElement = getCellElement(event.target as HTMLElement);\n    if (!cellElement) {\n      // Mouseup happened outside of datepicker. Cancel drag.\n      this._ngZone.run(() => {\n        this.dragEnded.emit({value: null, event});\n      });\n      return;\n    }\n\n    if (cellElement.closest('.mat-calendar-body') !== this._elementRef.nativeElement) {\n      // Mouseup happened inside a different month instance.\n      // Allow it to handle the event.\n      return;\n    }\n\n    this._ngZone.run(() => {\n      const cell = this._getCellFromElement(cellElement);\n      this.dragEnded.emit({value: cell?.rawValue ?? null, event});\n    });\n  };\n\n  /** Triggered on touchend anywhere. Respsonsible for ending a drag sequence. */\n  private _touchendHandler = (event: TouchEvent) => {\n    const target = getActualTouchTarget(event);\n\n    if (target) {\n      this._mouseupHandler({target} as unknown as Event);\n    }\n  };\n\n  /** Finds the MatCalendarCell that corresponds to a DOM node. */\n  private _getCellFromElement(element: HTMLElement): MatCalendarCell | null {\n    const cell = getCellElement(element);\n\n    if (cell) {\n      const row = cell.getAttribute('data-mat-row');\n      const col = cell.getAttribute('data-mat-col');\n\n      if (row && col) {\n        return this.rows[parseInt(row)][parseInt(col)];\n      }\n    }\n\n    return null;\n  }\n}\n\n/** Checks whether a node is a table cell element. */\nfunction isTableCell(node: Node | undefined | null): node is HTMLTableCellElement {\n  return node?.nodeName === 'TD';\n}\n\n/**\n * Gets the date table cell element that is or contains the specified element.\n * Or returns null if element is not part of a date cell.\n */\nfunction getCellElement(element: HTMLElement): HTMLElement | null {\n  let cell: HTMLElement | undefined;\n  if (isTableCell(element)) {\n    cell = element;\n  } else if (isTableCell(element.parentNode)) {\n    cell = element.parentNode as HTMLElement;\n  } else if (isTableCell(element.parentNode?.parentNode)) {\n    cell = element.parentNode!.parentNode as HTMLElement;\n  }\n\n  return cell?.getAttribute('data-mat-row') != null ? cell : null;\n}\n\n/** Checks whether a value is the start of a range. */\nfunction isStart(value: number, start: number | null, end: number | null): boolean {\n  return end !== null && start !== end && value < end && value === start;\n}\n\n/** Checks whether a value is the end of a range. */\nfunction isEnd(value: number, start: number | null, end: number | null): boolean {\n  return start !== null && start !== end && value >= start && value === end;\n}\n\n/** Checks whether a value is inside of a range. */\nfunction isInRange(\n  value: number,\n  start: number | null,\n  end: number | null,\n  rangeEnabled: boolean,\n): boolean {\n  return (\n    rangeEnabled &&\n    start !== null &&\n    end !== null &&\n    start !== end &&\n    value >= start &&\n    value <= end\n  );\n}\n\n/**\n * Extracts the element that actually corresponds to a touch event's location\n * (rather than the element that initiated the sequence of touch events).\n */\nfunction getActualTouchTarget(event: TouchEvent): Element | null {\n  const touchLocation = event.changedTouches[0];\n  return document.elementFromPoint(touchLocation.clientX, touchLocation.clientY);\n}\n", "<!--\n  If there's not enough space in the first row, create a separate label row. We mark this row as\n  aria-hidden because we don't want it to be read out as one of the weeks in the month.\n-->\n@if (_firstRowOffset < labelMinRequiredCells) {\n  <tr aria-hidden=\"true\">\n    <td class=\"mat-calendar-body-label\"\n        [attr.colspan]=\"numCols\"\n        [style.paddingTop]=\"_cellPadding\"\n        [style.paddingBottom]=\"_cellPadding\">\n      {{label}}\n    </td>\n  </tr>\n}\n\n<!-- Create the first row separately so we can include a special spacer cell. -->\n@for (row of rows; track _trackRow(row); let rowIndex = $index) {\n  <tr role=\"row\">\n    <!--\n      This cell is purely decorative, but we can't put `aria-hidden` or `role=\"presentation\"` on it,\n      because it throws off the week days for the rest of the row on NVDA. The aspect ratio of the\n      table cells is maintained by setting the top and bottom padding as a percentage of the width\n      (a variant of the trick described here: https://www.w3schools.com/howto/howto_css_aspect_ratio.asp).\n    -->\n    @if (rowIndex === 0 && _firstRowOffset) {\n      <td\n        class=\"mat-calendar-body-label\"\n        [attr.colspan]=\"_firstRowOffset\"\n        [style.paddingTop]=\"_cellPadding\"\n        [style.paddingBottom]=\"_cellPadding\">\n        {{_firstRowOffset >= labelMinRequiredCells ? label : ''}}\n      </td>\n    }\n    <!--\n      Each gridcell in the calendar contains a button, which signals to assistive technology that the\n      cell is interactable, as well as the selection state via `aria-pressed`. See #23476 for\n      background.\n    -->\n    @for (item of row; track item.id; let colIndex = $index) {\n      <td\n        role=\"gridcell\"\n        class=\"mat-calendar-body-cell-container\"\n        [style.width]=\"_cellWidth\"\n        [style.paddingTop]=\"_cellPadding\"\n        [style.paddingBottom]=\"_cellPadding\"\n        [attr.data-mat-row]=\"rowIndex\"\n        [attr.data-mat-col]=\"colIndex\"\n      >\n        <button\n            type=\"button\"\n            class=\"mat-calendar-body-cell\"\n            [ngClass]=\"item.cssClasses\"\n            [tabindex]=\"_isActiveCell(rowIndex, colIndex) ? 0 : -1\"\n            [class.mat-calendar-body-disabled]=\"!item.enabled\"\n            [class.mat-calendar-body-active]=\"_isActiveCell(rowIndex, colIndex)\"\n            [class.mat-calendar-body-range-start]=\"_isRangeStart(item.compareValue)\"\n            [class.mat-calendar-body-range-end]=\"_isRangeEnd(item.compareValue)\"\n            [class.mat-calendar-body-in-range]=\"_isInRange(item.compareValue)\"\n            [class.mat-calendar-body-comparison-bridge-start]=\"_isComparisonBridgeStart(item.compareValue, rowIndex, colIndex)\"\n            [class.mat-calendar-body-comparison-bridge-end]=\"_isComparisonBridgeEnd(item.compareValue, rowIndex, colIndex)\"\n            [class.mat-calendar-body-comparison-start]=\"_isComparisonStart(item.compareValue)\"\n            [class.mat-calendar-body-comparison-end]=\"_isComparisonEnd(item.compareValue)\"\n            [class.mat-calendar-body-in-comparison-range]=\"_isInComparisonRange(item.compareValue)\"\n            [class.mat-calendar-body-preview-start]=\"_isPreviewStart(item.compareValue)\"\n            [class.mat-calendar-body-preview-end]=\"_isPreviewEnd(item.compareValue)\"\n            [class.mat-calendar-body-in-preview]=\"_isInPreview(item.compareValue)\"\n            [attr.aria-label]=\"item.ariaLabel\"\n            [attr.aria-disabled]=\"!item.enabled || null\"\n            [attr.aria-pressed]=\"_isSelected(item.compareValue)\"\n            [attr.aria-current]=\"todayValue === item.compareValue ? 'date' : null\"\n            [attr.aria-describedby]=\"_getDescribedby(item.compareValue)\"\n            (click)=\"_cellClicked(item, $event)\"\n            (focus)=\"_emitActiveDateChange(item, $event)\">\n            <span class=\"mat-calendar-body-cell-content mat-focus-indicator\"\n              [class.mat-calendar-body-selected]=\"_isSelected(item.compareValue)\"\n              [class.mat-calendar-body-comparison-identical]=\"_isComparisonIdentical(item.compareValue)\"\n              [class.mat-calendar-body-today]=\"todayValue === item.compareValue\">\n              {{item.displayValue}}\n            </span>\n            <span class=\"mat-calendar-body-cell-preview\" aria-hidden=\"true\"></span>\n        </button>\n      </td>\n    }\n  </tr>\n}\n\n<span [id]=\"_startDateLabelId\" class=\"mat-calendar-body-hidden-label\">\n  {{startDateAccessibleName}}\n</span>\n<span [id]=\"_endDateLabelId\" class=\"mat-calendar-body-hidden-label\">\n  {{endDateAccessibleName}}\n</span>\n<span [id]=\"_comparisonStartDateLabelId\" class=\"mat-calendar-body-hidden-label\">\n  {{comparisonDateAccessibleName}} {{startDateAccessibleName}}\n</span>\n<span [id]=\"_comparisonEndDateLabelId\" class=\"mat-calendar-body-hidden-label\">\n  {{comparisonDateAccessibleName}} {{endDateAccessibleName}}\n</span>\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {FactoryProvider, Injectable, Optional, SkipSelf, OnDestroy} from '@angular/core';\nimport {DateAdapter} from '../core';\nimport {Observable, Subject} from 'rxjs';\n\n/** A class representing a range of dates. */\nexport class DateRange<D> {\n  /**\n   * Ensures that objects with a `start` and `end` property can't be assigned to a variable that\n   * expects a `DateRange`\n   */\n  // tslint:disable-next-line:no-unused-variable\n  private _disableStructuralEquivalency: never;\n\n  constructor(\n    /** The start date of the range. */\n    readonly start: D | null,\n    /** The end date of the range. */\n    readonly end: D | null,\n  ) {}\n}\n\n/**\n * Conditionally picks the date type, if a DateRange is passed in.\n * @docs-private\n */\nexport type ExtractDateTypeFromSelection<T> = T extends DateRange<infer D> ? D : NonNullable<T>;\n\n/**\n * Event emitted by the date selection model when its selection changes.\n * @docs-private\n */\nexport interface DateSelectionModelChange<S> {\n  /** New value for the selection. */\n  selection: S;\n\n  /** Object that triggered the change. */\n  source: unknown;\n\n  /** Previous value */\n  oldValue?: S;\n}\n\n/**\n * A selection model containing a date selection.\n * @docs-private\n */\n@Injectable()\nexport abstract class MatDateSelectionModel<S, D = ExtractDateTypeFromSelection<S>>\n  implements OnDestroy\n{\n  private readonly _selectionChanged = new Subject<DateSelectionModelChange<S>>();\n\n  /** Emits when the selection has changed. */\n  selectionChanged: Observable<DateSelectionModelChange<S>> = this._selectionChanged;\n\n  protected constructor(\n    /** The current selection. */\n    readonly selection: S,\n    protected _adapter: DateAdapter<D>,\n  ) {\n    this.selection = selection;\n  }\n\n  /**\n   * Updates the current selection in the model.\n   * @param value New selection that should be assigned.\n   * @param source Object that triggered the selection change.\n   */\n  updateSelection(value: S, source: unknown) {\n    const oldValue = (this as {selection: S}).selection;\n    (this as {selection: S}).selection = value;\n    this._selectionChanged.next({selection: value, source, oldValue});\n  }\n\n  ngOnDestroy() {\n    this._selectionChanged.complete();\n  }\n\n  protected _isValidDateInstance(date: D): boolean {\n    return this._adapter.isDateInstance(date) && this._adapter.isValid(date);\n  }\n\n  /** Adds a date to the current selection. */\n  abstract add(date: D | null): void;\n\n  /** Checks whether the current selection is valid. */\n  abstract isValid(): boolean;\n\n  /** Checks whether the current selection is complete. */\n  abstract isComplete(): boolean;\n\n  /** Clones the selection model. */\n  abstract clone(): MatDateSelectionModel<S, D>;\n}\n\n/**\n * A selection model that contains a single date.\n * @docs-private\n */\n@Injectable()\nexport class MatSingleDateSelectionModel<D> extends MatDateSelectionModel<D | null, D> {\n  constructor(adapter: DateAdapter<D>) {\n    super(null, adapter);\n  }\n\n  /**\n   * Adds a date to the current selection. In the case of a single date selection, the added date\n   * simply overwrites the previous selection\n   */\n  add(date: D | null) {\n    super.updateSelection(date, this);\n  }\n\n  /** Checks whether the current selection is valid. */\n  isValid(): boolean {\n    return this.selection != null && this._isValidDateInstance(this.selection);\n  }\n\n  /**\n   * Checks whether the current selection is complete. In the case of a single date selection, this\n   * is true if the current selection is not null.\n   */\n  isComplete() {\n    return this.selection != null;\n  }\n\n  /** Clones the selection model. */\n  clone() {\n    const clone = new MatSingleDateSelectionModel<D>(this._adapter);\n    clone.updateSelection(this.selection, this);\n    return clone;\n  }\n}\n\n/**\n * A selection model that contains a date range.\n * @docs-private\n */\n@Injectable()\nexport class MatRangeDateSelectionModel<D> extends MatDateSelectionModel<DateRange<D>, D> {\n  constructor(adapter: DateAdapter<D>) {\n    super(new DateRange<D>(null, null), adapter);\n  }\n\n  /**\n   * Adds a date to the current selection. In the case of a date range selection, the added date\n   * fills in the next `null` value in the range. If both the start and the end already have a date,\n   * the selection is reset so that the given date is the new `start` and the `end` is null.\n   */\n  add(date: D | null): void {\n    let {start, end} = this.selection;\n\n    if (start == null) {\n      start = date;\n    } else if (end == null) {\n      end = date;\n    } else {\n      start = date;\n      end = null;\n    }\n\n    super.updateSelection(new DateRange<D>(start, end), this);\n  }\n\n  /** Checks whether the current selection is valid. */\n  isValid(): boolean {\n    const {start, end} = this.selection;\n\n    // Empty ranges are valid.\n    if (start == null && end == null) {\n      return true;\n    }\n\n    // Complete ranges are only valid if both dates are valid and the start is before the end.\n    if (start != null && end != null) {\n      return (\n        this._isValidDateInstance(start) &&\n        this._isValidDateInstance(end) &&\n        this._adapter.compareDate(start, end) <= 0\n      );\n    }\n\n    // Partial ranges are valid if the start/end is valid.\n    return (\n      (start == null || this._isValidDateInstance(start)) &&\n      (end == null || this._isValidDateInstance(end))\n    );\n  }\n\n  /**\n   * Checks whether the current selection is complete. In the case of a date range selection, this\n   * is true if the current selection has a non-null `start` and `end`.\n   */\n  isComplete(): boolean {\n    return this.selection.start != null && this.selection.end != null;\n  }\n\n  /** Clones the selection model. */\n  clone() {\n    const clone = new MatRangeDateSelectionModel<D>(this._adapter);\n    clone.updateSelection(this.selection, this);\n    return clone;\n  }\n}\n\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nexport function MAT_SINGLE_DATE_SELECTION_MODEL_FACTORY(\n  parent: MatSingleDateSelectionModel<unknown>,\n  adapter: DateAdapter<unknown>,\n) {\n  return parent || new MatSingleDateSelectionModel(adapter);\n}\n\n/**\n * Used to provide a single selection model to a component.\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nexport const MAT_SINGLE_DATE_SELECTION_MODEL_PROVIDER: FactoryProvider = {\n  provide: MatDateSelectionModel,\n  deps: [[new Optional(), new SkipSelf(), MatDateSelectionModel], DateAdapter],\n  useFactory: MAT_SINGLE_DATE_SELECTION_MODEL_FACTORY,\n};\n\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nexport function MAT_RANGE_DATE_SELECTION_MODEL_FACTORY(\n  parent: MatSingleDateSelectionModel<unknown>,\n  adapter: DateAdapter<unknown>,\n) {\n  return parent || new MatRangeDateSelectionModel(adapter);\n}\n\n/**\n * Used to provide a range selection model to a component.\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nexport const MAT_RANGE_DATE_SELECTION_MODEL_PROVIDER: FactoryProvider = {\n  provide: MatDateSelectionModel,\n  deps: [[new Optional(), new SkipSelf(), MatDateSelectionModel], DateAdapter],\n  useFactory: MAT_RANGE_DATE_SELECTION_MODEL_FACTORY,\n};\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Injectable, InjectionToken, Optional, SkipSelf, FactoryProvider} from '@angular/core';\nimport {DateAdapter} from '../core';\nimport {DateRange} from './date-selection-model';\n\n/** Injection token used to customize the date range selection behavior. */\nexport const MAT_DATE_RANGE_SELECTION_STRATEGY = new InjectionToken<\n  MatDateRangeSelectionStrategy<any>\n>('MAT_DATE_RANGE_SELECTION_STRATEGY');\n\n/** Object that can be provided in order to customize the date range selection behavior. */\nexport interface MatDateRangeSelectionStrategy<D> {\n  /**\n   * Called when the user has finished selecting a value.\n   * @param date Date that was selected. Will be null if the user cleared the selection.\n   * @param currentRange Range that is currently show in the calendar.\n   * @param event DOM event that triggered the selection. Currently only corresponds to a `click`\n   *    event, but it may get expanded in the future.\n   */\n  selectionFinished(date: D | null, currentRange: DateRange<D>, event: Event): DateRange<D>;\n\n  /**\n   * Called when the user has activated a new date (e.g. by hovering over\n   * it or moving focus) and the calendar tries to display a date range.\n   *\n   * @param activeDate Date that the user has activated. Will be null if the user moved\n   *    focus to an element that's no a calendar cell.\n   * @param currentRange Range that is currently shown in the calendar.\n   * @param event DOM event that caused the preview to be changed. Will be either a\n   *    `mouseenter`/`mouseleave` or `focus`/`blur` depending on how the user is navigating.\n   */\n  createPreview(activeDate: D | null, currentRange: DateRange<D>, event: Event): DateRange<D>;\n\n  /**\n   * Called when the user has dragged a date in the currently selected range to another\n   * date. Returns the date updated range that should result from this interaction.\n   *\n   * @param dateOrigin The date the user started dragging from.\n   * @param originalRange The originally selected date range.\n   * @param newDate The currently targeted date in the drag operation.\n   * @param event DOM event that triggered the updated drag state. Will be\n   *     `mouseenter`/`mouseup` or `touchmove`/`touchend` depending on the device type.\n   */\n  createDrag?(\n    dragOrigin: D,\n    originalRange: DateRange<D>,\n    newDate: D,\n    event: Event,\n  ): DateRange<D> | null;\n}\n\n/** Provides the default date range selection behavior. */\n@Injectable()\nexport class DefaultMatCalendarRangeStrategy<D> implements MatDateRangeSelectionStrategy<D> {\n  constructor(private _dateAdapter: DateAdapter<D>) {}\n\n  selectionFinished(date: D, currentRange: DateRange<D>) {\n    let {start, end} = currentRange;\n\n    if (start == null) {\n      start = date;\n    } else if (end == null && date && this._dateAdapter.compareDate(date, start) >= 0) {\n      end = date;\n    } else {\n      start = date;\n      end = null;\n    }\n\n    return new DateRange<D>(start, end);\n  }\n\n  createPreview(activeDate: D | null, currentRange: DateRange<D>) {\n    let start: D | null = null;\n    let end: D | null = null;\n\n    if (currentRange.start && !currentRange.end && activeDate) {\n      start = currentRange.start;\n      end = activeDate;\n    }\n\n    return new DateRange<D>(start, end);\n  }\n\n  createDrag(dragOrigin: D, originalRange: DateRange<D>, newDate: D) {\n    let start = originalRange.start;\n    let end = originalRange.end;\n\n    if (!start || !end) {\n      // Can't drag from an incomplete range.\n      return null;\n    }\n\n    const adapter = this._dateAdapter;\n\n    const isRange = adapter.compareDate(start, end) !== 0;\n    const diffYears = adapter.getYear(newDate) - adapter.getYear(dragOrigin);\n    const diffMonths = adapter.getMonth(newDate) - adapter.getMonth(dragOrigin);\n    const diffDays = adapter.getDate(newDate) - adapter.getDate(dragOrigin);\n\n    if (isRange && adapter.sameDate(dragOrigin, originalRange.start)) {\n      start = newDate;\n      if (adapter.compareDate(newDate, end) > 0) {\n        end = adapter.addCalendarYears(end, diffYears);\n        end = adapter.addCalendarMonths(end, diffMonths);\n        end = adapter.addCalendarDays(end, diffDays);\n      }\n    } else if (isRange && adapter.sameDate(dragOrigin, originalRange.end)) {\n      end = newDate;\n      if (adapter.compareDate(newDate, start) < 0) {\n        start = adapter.addCalendarYears(start, diffYears);\n        start = adapter.addCalendarMonths(start, diffMonths);\n        start = adapter.addCalendarDays(start, diffDays);\n      }\n    } else {\n      start = adapter.addCalendarYears(start, diffYears);\n      start = adapter.addCalendarMonths(start, diffMonths);\n      start = adapter.addCalendarDays(start, diffDays);\n      end = adapter.addCalendarYears(end, diffYears);\n      end = adapter.addCalendarMonths(end, diffMonths);\n      end = adapter.addCalendarDays(end, diffDays);\n    }\n\n    return new DateRange<D>(start, end);\n  }\n}\n\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nexport function MAT_CALENDAR_RANGE_STRATEGY_PROVIDER_FACTORY(\n  parent: MatDateRangeSelectionStrategy<unknown>,\n  adapter: DateAdapter<unknown>,\n) {\n  return parent || new DefaultMatCalendarRangeStrategy(adapter);\n}\n\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nexport const MAT_CALENDAR_RANGE_STRATEGY_PROVIDER: FactoryProvider = {\n  provide: MAT_DATE_RANGE_SELECTION_STRATEGY,\n  deps: [[new Optional(), new SkipSelf(), MAT_DATE_RANGE_SELECTION_STRATEGY], DateAdapter],\n  useFactory: MAT_CALENDAR_RANGE_STRATEGY_PROVIDER_FACTORY,\n};\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  DOWN_ARROW,\n  END,\n  ENTER,\n  HOME,\n  LEFT_ARROW,\n  PAGE_DOWN,\n  PAGE_UP,\n  RIGHT_ARROW,\n  UP_ARROW,\n  SPACE,\n  ESCAPE,\n  hasModifierKey,\n} from '@angular/cdk/keycodes';\nimport {\n  AfterContentInit,\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  EventEmitter,\n  Input,\n  Output,\n  ViewEncapsulation,\n  ViewChild,\n  OnDestroy,\n  SimpleChanges,\n  OnChanges,\n  inject,\n} from '@angular/core';\nimport {DateAdapter, MAT_DATE_FORMATS, MatDateFormats} from '../core';\nimport {Directionality} from '@angular/cdk/bidi';\nimport {\n  MatCalendarBody,\n  MatCalendarCell,\n  MatCalendarUserEvent,\n  MatCalendarCellClassFunction,\n} from './calendar-body';\nimport {createMissingDateImplError} from './datepicker-errors';\nimport {Subscription} from 'rxjs';\nimport {startWith} from 'rxjs/operators';\nimport {DateRange} from './date-selection-model';\nimport {\n  MatDateRangeSelectionStrategy,\n  MAT_DATE_RANGE_SELECTION_STRATEGY,\n} from './date-range-selection-strategy';\nimport {_CdkPrivateStyleLoader, _VisuallyHiddenLoader} from '@angular/cdk/private';\n\nconst DAYS_PER_WEEK = 7;\n\nlet uniqueIdCounter = 0;\n\n/**\n * An internal component used to display a single month in the datepicker.\n * @docs-private\n */\n@Component({\n  selector: 'mat-month-view',\n  templateUrl: 'month-view.html',\n  exportAs: 'matMonthView',\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  imports: [MatCalendarBody],\n})\nexport class MatMonthView<D> implements AfterContentInit, OnChanges, OnDestroy {\n  readonly _changeDetectorRef = inject(ChangeDetectorRef);\n  private _dateFormats = inject<MatDateFormats>(MAT_DATE_FORMATS, {optional: true})!;\n  _dateAdapter = inject<DateAdapter<D>>(DateAdapter, {optional: true})!;\n  private _dir = inject(Directionality, {optional: true});\n  private _rangeStrategy = inject<MatDateRangeSelectionStrategy<D>>(\n    MAT_DATE_RANGE_SELECTION_STRATEGY,\n    {optional: true},\n  );\n\n  private _rerenderSubscription = Subscription.EMPTY;\n\n  /** Flag used to filter out space/enter keyup events that originated outside of the view. */\n  private _selectionKeyPressed: boolean;\n\n  /**\n   * The date to display in this month view (everything other than the month and year is ignored).\n   */\n  @Input()\n  get activeDate(): D {\n    return this._activeDate;\n  }\n  set activeDate(value: D) {\n    const oldActiveDate = this._activeDate;\n    const validDate =\n      this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(value)) ||\n      this._dateAdapter.today();\n    this._activeDate = this._dateAdapter.clampDate(validDate, this.minDate, this.maxDate);\n    if (!this._hasSameMonthAndYear(oldActiveDate, this._activeDate)) {\n      this._init();\n    }\n  }\n  private _activeDate: D;\n\n  /** The currently selected date. */\n  @Input()\n  get selected(): DateRange<D> | D | null {\n    return this._selected;\n  }\n  set selected(value: DateRange<D> | D | null) {\n    if (value instanceof DateRange) {\n      this._selected = value;\n    } else {\n      this._selected = this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(value));\n    }\n\n    this._setRanges(this._selected);\n  }\n  private _selected: DateRange<D> | D | null;\n\n  /** The minimum selectable date. */\n  @Input()\n  get minDate(): D | null {\n    return this._minDate;\n  }\n  set minDate(value: D | null) {\n    this._minDate = this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(value));\n  }\n  private _minDate: D | null;\n\n  /** The maximum selectable date. */\n  @Input()\n  get maxDate(): D | null {\n    return this._maxDate;\n  }\n  set maxDate(value: D | null) {\n    this._maxDate = this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(value));\n  }\n  private _maxDate: D | null;\n\n  /** Function used to filter which dates are selectable. */\n  @Input() dateFilter: (date: D) => boolean;\n\n  /** Function that can be used to add custom CSS classes to dates. */\n  @Input() dateClass: MatCalendarCellClassFunction<D>;\n\n  /** Start of the comparison range. */\n  @Input() comparisonStart: D | null;\n\n  /** End of the comparison range. */\n  @Input() comparisonEnd: D | null;\n\n  /** ARIA Accessible name of the `<input matStartDate/>` */\n  @Input() startDateAccessibleName: string | null;\n\n  /** ARIA Accessible name of the `<input matEndDate/>` */\n  @Input() endDateAccessibleName: string | null;\n\n  /** Origin of active drag, or null when dragging is not active. */\n  @Input() activeDrag: MatCalendarUserEvent<D> | null = null;\n\n  /** Emits when a new date is selected. */\n  @Output() readonly selectedChange: EventEmitter<D | null> = new EventEmitter<D | null>();\n\n  /** Emits when any date is selected. */\n  @Output() readonly _userSelection: EventEmitter<MatCalendarUserEvent<D | null>> =\n    new EventEmitter<MatCalendarUserEvent<D | null>>();\n\n  /** Emits when the user initiates a date range drag via mouse or touch. */\n  @Output() readonly dragStarted = new EventEmitter<MatCalendarUserEvent<D>>();\n\n  /**\n   * Emits when the user completes or cancels a date range drag.\n   * Emits null when the drag was canceled or the newly selected date range if completed.\n   */\n  @Output() readonly dragEnded = new EventEmitter<MatCalendarUserEvent<DateRange<D> | null>>();\n\n  /** Emits when any date is activated. */\n  @Output() readonly activeDateChange: EventEmitter<D> = new EventEmitter<D>();\n\n  /** The body of calendar table */\n  @ViewChild(MatCalendarBody) _matCalendarBody: MatCalendarBody;\n\n  /** The label for this month (e.g. \"January 2017\"). */\n  _monthLabel: string;\n\n  /** Grid of calendar cells representing the dates of the month. */\n  _weeks: MatCalendarCell[][];\n\n  /** The number of blank cells in the first row before the 1st of the month. */\n  _firstWeekOffset: number;\n\n  /** Start value of the currently-shown date range. */\n  _rangeStart: number | null;\n\n  /** End value of the currently-shown date range. */\n  _rangeEnd: number | null;\n\n  /** Start value of the currently-shown comparison date range. */\n  _comparisonRangeStart: number | null;\n\n  /** End value of the currently-shown comparison date range. */\n  _comparisonRangeEnd: number | null;\n\n  /** Start of the preview range. */\n  _previewStart: number | null;\n\n  /** End of the preview range. */\n  _previewEnd: number | null;\n\n  /** Whether the user is currently selecting a range of dates. */\n  _isRange: boolean;\n\n  /** The date of the month that today falls on. Null if today is in another month. */\n  _todayDate: number | null;\n\n  /** The names of the weekdays. */\n  _weekdays: {long: string; narrow: string; id: number}[];\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    inject(_CdkPrivateStyleLoader).load(_VisuallyHiddenLoader);\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (!this._dateAdapter) {\n        throw createMissingDateImplError('DateAdapter');\n      }\n      if (!this._dateFormats) {\n        throw createMissingDateImplError('MAT_DATE_FORMATS');\n      }\n    }\n\n    this._activeDate = this._dateAdapter.today();\n  }\n\n  ngAfterContentInit() {\n    this._rerenderSubscription = this._dateAdapter.localeChanges\n      .pipe(startWith(null))\n      .subscribe(() => this._init());\n  }\n\n  ngOnChanges(changes: SimpleChanges) {\n    const comparisonChange = changes['comparisonStart'] || changes['comparisonEnd'];\n\n    if (comparisonChange && !comparisonChange.firstChange) {\n      this._setRanges(this.selected);\n    }\n\n    if (changes['activeDrag'] && !this.activeDrag) {\n      this._clearPreview();\n    }\n  }\n\n  ngOnDestroy() {\n    this._rerenderSubscription.unsubscribe();\n  }\n\n  /** Handles when a new date is selected. */\n  _dateSelected(event: MatCalendarUserEvent<number>) {\n    const date = event.value;\n    const selectedDate = this._getDateFromDayOfMonth(date);\n    let rangeStartDate: number | null;\n    let rangeEndDate: number | null;\n\n    if (this._selected instanceof DateRange) {\n      rangeStartDate = this._getDateInCurrentMonth(this._selected.start);\n      rangeEndDate = this._getDateInCurrentMonth(this._selected.end);\n    } else {\n      rangeStartDate = rangeEndDate = this._getDateInCurrentMonth(this._selected);\n    }\n\n    if (rangeStartDate !== date || rangeEndDate !== date) {\n      this.selectedChange.emit(selectedDate);\n    }\n\n    this._userSelection.emit({value: selectedDate, event: event.event});\n    this._clearPreview();\n    this._changeDetectorRef.markForCheck();\n  }\n\n  /**\n   * Takes the index of a calendar body cell wrapped in an event as argument. For the date that\n   * corresponds to the given cell, set `activeDate` to that date and fire `activeDateChange` with\n   * that date.\n   *\n   * This function is used to match each component's model of the active date with the calendar\n   * body cell that was focused. It updates its value of `activeDate` synchronously and updates the\n   * parent's value asynchronously via the `activeDateChange` event. The child component receives an\n   * updated value asynchronously via the `activeCell` Input.\n   */\n  _updateActiveDate(event: MatCalendarUserEvent<number>) {\n    const month = event.value;\n    const oldActiveDate = this._activeDate;\n    this.activeDate = this._getDateFromDayOfMonth(month);\n\n    if (this._dateAdapter.compareDate(oldActiveDate, this.activeDate)) {\n      this.activeDateChange.emit(this._activeDate);\n    }\n  }\n\n  /** Handles keydown events on the calendar body when calendar is in month view. */\n  _handleCalendarBodyKeydown(event: KeyboardEvent): void {\n    // TODO(mmalerba): We currently allow keyboard navigation to disabled dates, but just prevent\n    // disabled ones from being selected. This may not be ideal, we should look into whether\n    // navigation should skip over disabled dates, and if so, how to implement that efficiently.\n\n    const oldActiveDate = this._activeDate;\n    const isRtl = this._isRtl();\n\n    switch (event.keyCode) {\n      case LEFT_ARROW:\n        this.activeDate = this._dateAdapter.addCalendarDays(this._activeDate, isRtl ? 1 : -1);\n        break;\n      case RIGHT_ARROW:\n        this.activeDate = this._dateAdapter.addCalendarDays(this._activeDate, isRtl ? -1 : 1);\n        break;\n      case UP_ARROW:\n        this.activeDate = this._dateAdapter.addCalendarDays(this._activeDate, -7);\n        break;\n      case DOWN_ARROW:\n        this.activeDate = this._dateAdapter.addCalendarDays(this._activeDate, 7);\n        break;\n      case HOME:\n        this.activeDate = this._dateAdapter.addCalendarDays(\n          this._activeDate,\n          1 - this._dateAdapter.getDate(this._activeDate),\n        );\n        break;\n      case END:\n        this.activeDate = this._dateAdapter.addCalendarDays(\n          this._activeDate,\n          this._dateAdapter.getNumDaysInMonth(this._activeDate) -\n            this._dateAdapter.getDate(this._activeDate),\n        );\n        break;\n      case PAGE_UP:\n        this.activeDate = event.altKey\n          ? this._dateAdapter.addCalendarYears(this._activeDate, -1)\n          : this._dateAdapter.addCalendarMonths(this._activeDate, -1);\n        break;\n      case PAGE_DOWN:\n        this.activeDate = event.altKey\n          ? this._dateAdapter.addCalendarYears(this._activeDate, 1)\n          : this._dateAdapter.addCalendarMonths(this._activeDate, 1);\n        break;\n      case ENTER:\n      case SPACE:\n        this._selectionKeyPressed = true;\n\n        if (this._canSelect(this._activeDate)) {\n          // Prevent unexpected default actions such as form submission.\n          // Note that we only prevent the default action here while the selection happens in\n          // `keyup` below. We can't do the selection here, because it can cause the calendar to\n          // reopen if focus is restored immediately. We also can't call `preventDefault` on `keyup`\n          // because it's too late (see #23305).\n          event.preventDefault();\n        }\n        return;\n      case ESCAPE:\n        // Abort the current range selection if the user presses escape mid-selection.\n        if (this._previewEnd != null && !hasModifierKey(event)) {\n          this._clearPreview();\n          // If a drag is in progress, cancel the drag without changing the\n          // current selection.\n          if (this.activeDrag) {\n            this.dragEnded.emit({value: null, event});\n          } else {\n            this.selectedChange.emit(null);\n            this._userSelection.emit({value: null, event});\n          }\n          event.preventDefault();\n          event.stopPropagation(); // Prevents the overlay from closing.\n        }\n        return;\n      default:\n        // Don't prevent default or focus active cell on keys that we don't explicitly handle.\n        return;\n    }\n\n    if (this._dateAdapter.compareDate(oldActiveDate, this.activeDate)) {\n      this.activeDateChange.emit(this.activeDate);\n\n      this._focusActiveCellAfterViewChecked();\n    }\n\n    // Prevent unexpected default actions such as form submission.\n    event.preventDefault();\n  }\n\n  /** Handles keyup events on the calendar body when calendar is in month view. */\n  _handleCalendarBodyKeyup(event: KeyboardEvent): void {\n    if (event.keyCode === SPACE || event.keyCode === ENTER) {\n      if (this._selectionKeyPressed && this._canSelect(this._activeDate)) {\n        this._dateSelected({value: this._dateAdapter.getDate(this._activeDate), event});\n      }\n\n      this._selectionKeyPressed = false;\n    }\n  }\n\n  /** Initializes this month view. */\n  _init() {\n    this._setRanges(this.selected);\n    this._todayDate = this._getCellCompareValue(this._dateAdapter.today());\n    this._monthLabel = this._dateFormats.display.monthLabel\n      ? this._dateAdapter.format(this.activeDate, this._dateFormats.display.monthLabel)\n      : this._dateAdapter\n          .getMonthNames('short')\n          [this._dateAdapter.getMonth(this.activeDate)].toLocaleUpperCase();\n\n    let firstOfMonth = this._dateAdapter.createDate(\n      this._dateAdapter.getYear(this.activeDate),\n      this._dateAdapter.getMonth(this.activeDate),\n      1,\n    );\n    this._firstWeekOffset =\n      (DAYS_PER_WEEK +\n        this._dateAdapter.getDayOfWeek(firstOfMonth) -\n        this._dateAdapter.getFirstDayOfWeek()) %\n      DAYS_PER_WEEK;\n\n    this._initWeekdays();\n    this._createWeekCells();\n    this._changeDetectorRef.markForCheck();\n  }\n\n  /** Focuses the active cell after the microtask queue is empty. */\n  _focusActiveCell(movePreview?: boolean) {\n    this._matCalendarBody._focusActiveCell(movePreview);\n  }\n\n  /** Focuses the active cell after change detection has run and the microtask queue is empty. */\n  _focusActiveCellAfterViewChecked() {\n    this._matCalendarBody._scheduleFocusActiveCellAfterViewChecked();\n  }\n\n  /** Called when the user has activated a new cell and the preview needs to be updated. */\n  _previewChanged({event, value: cell}: MatCalendarUserEvent<MatCalendarCell<D> | null>) {\n    if (this._rangeStrategy) {\n      // We can assume that this will be a range, because preview\n      // events aren't fired for single date selections.\n      const value = cell ? cell.rawValue! : null;\n      const previewRange = this._rangeStrategy.createPreview(\n        value,\n        this.selected as DateRange<D>,\n        event,\n      );\n      this._previewStart = this._getCellCompareValue(previewRange.start);\n      this._previewEnd = this._getCellCompareValue(previewRange.end);\n\n      if (this.activeDrag && value) {\n        const dragRange = this._rangeStrategy.createDrag?.(\n          this.activeDrag.value,\n          this.selected as DateRange<D>,\n          value,\n          event,\n        );\n\n        if (dragRange) {\n          this._previewStart = this._getCellCompareValue(dragRange.start);\n          this._previewEnd = this._getCellCompareValue(dragRange.end);\n        }\n      }\n\n      // Note that here we need to use `detectChanges`, rather than `markForCheck`, because\n      // the way `_focusActiveCell` is set up at the moment makes it fire at the wrong time\n      // when navigating one month back using the keyboard which will cause this handler\n      // to throw a \"changed after checked\" error when updating the preview state.\n      this._changeDetectorRef.detectChanges();\n    }\n  }\n\n  /**\n   * Called when the user has ended a drag. If the drag/drop was successful,\n   * computes and emits the new range selection.\n   */\n  protected _dragEnded(event: MatCalendarUserEvent<D | null>) {\n    if (!this.activeDrag) return;\n\n    if (event.value) {\n      // Propagate drag effect\n      const dragDropResult = this._rangeStrategy?.createDrag?.(\n        this.activeDrag.value,\n        this.selected as DateRange<D>,\n        event.value,\n        event.event,\n      );\n\n      this.dragEnded.emit({value: dragDropResult ?? null, event: event.event});\n    } else {\n      this.dragEnded.emit({value: null, event: event.event});\n    }\n  }\n\n  /**\n   * Takes a day of the month and returns a new date in the same month and year as the currently\n   *  active date. The returned date will have the same day of the month as the argument date.\n   */\n  private _getDateFromDayOfMonth(dayOfMonth: number): D {\n    return this._dateAdapter.createDate(\n      this._dateAdapter.getYear(this.activeDate),\n      this._dateAdapter.getMonth(this.activeDate),\n      dayOfMonth,\n    );\n  }\n\n  /** Initializes the weekdays. */\n  private _initWeekdays() {\n    const firstDayOfWeek = this._dateAdapter.getFirstDayOfWeek();\n    const narrowWeekdays = this._dateAdapter.getDayOfWeekNames('narrow');\n    const longWeekdays = this._dateAdapter.getDayOfWeekNames('long');\n\n    // Rotate the labels for days of the week based on the configured first day of the week.\n    let weekdays = longWeekdays.map((long, i) => {\n      return {long, narrow: narrowWeekdays[i], id: uniqueIdCounter++};\n    });\n    this._weekdays = weekdays.slice(firstDayOfWeek).concat(weekdays.slice(0, firstDayOfWeek));\n  }\n\n  /** Creates MatCalendarCells for the dates in this month. */\n  private _createWeekCells() {\n    const daysInMonth = this._dateAdapter.getNumDaysInMonth(this.activeDate);\n    const dateNames = this._dateAdapter.getDateNames();\n    this._weeks = [[]];\n    for (let i = 0, cell = this._firstWeekOffset; i < daysInMonth; i++, cell++) {\n      if (cell == DAYS_PER_WEEK) {\n        this._weeks.push([]);\n        cell = 0;\n      }\n      const date = this._dateAdapter.createDate(\n        this._dateAdapter.getYear(this.activeDate),\n        this._dateAdapter.getMonth(this.activeDate),\n        i + 1,\n      );\n      const enabled = this._shouldEnableDate(date);\n      const ariaLabel = this._dateAdapter.format(date, this._dateFormats.display.dateA11yLabel);\n      const cellClasses = this.dateClass ? this.dateClass(date, 'month') : undefined;\n\n      this._weeks[this._weeks.length - 1].push(\n        new MatCalendarCell<D>(\n          i + 1,\n          dateNames[i],\n          ariaLabel,\n          enabled,\n          cellClasses,\n          this._getCellCompareValue(date)!,\n          date,\n        ),\n      );\n    }\n  }\n\n  /** Date filter for the month */\n  private _shouldEnableDate(date: D): boolean {\n    return (\n      !!date &&\n      (!this.minDate || this._dateAdapter.compareDate(date, this.minDate) >= 0) &&\n      (!this.maxDate || this._dateAdapter.compareDate(date, this.maxDate) <= 0) &&\n      (!this.dateFilter || this.dateFilter(date))\n    );\n  }\n\n  /**\n   * Gets the date in this month that the given Date falls on.\n   * Returns null if the given Date is in another month.\n   */\n  private _getDateInCurrentMonth(date: D | null): number | null {\n    return date && this._hasSameMonthAndYear(date, this.activeDate)\n      ? this._dateAdapter.getDate(date)\n      : null;\n  }\n\n  /** Checks whether the 2 dates are non-null and fall within the same month of the same year. */\n  private _hasSameMonthAndYear(d1: D | null, d2: D | null): boolean {\n    return !!(\n      d1 &&\n      d2 &&\n      this._dateAdapter.getMonth(d1) == this._dateAdapter.getMonth(d2) &&\n      this._dateAdapter.getYear(d1) == this._dateAdapter.getYear(d2)\n    );\n  }\n\n  /** Gets the value that will be used to one cell to another. */\n  private _getCellCompareValue(date: D | null): number | null {\n    if (date) {\n      // We use the time since the Unix epoch to compare dates in this view, rather than the\n      // cell values, because we need to support ranges that span across multiple months/years.\n      const year = this._dateAdapter.getYear(date);\n      const month = this._dateAdapter.getMonth(date);\n      const day = this._dateAdapter.getDate(date);\n      return new Date(year, month, day).getTime();\n    }\n\n    return null;\n  }\n\n  /** Determines whether the user has the RTL layout direction. */\n  private _isRtl() {\n    return this._dir && this._dir.value === 'rtl';\n  }\n\n  /** Sets the current range based on a model value. */\n  private _setRanges(selectedValue: DateRange<D> | D | null) {\n    if (selectedValue instanceof DateRange) {\n      this._rangeStart = this._getCellCompareValue(selectedValue.start);\n      this._rangeEnd = this._getCellCompareValue(selectedValue.end);\n      this._isRange = true;\n    } else {\n      this._rangeStart = this._rangeEnd = this._getCellCompareValue(selectedValue);\n      this._isRange = false;\n    }\n\n    this._comparisonRangeStart = this._getCellCompareValue(this.comparisonStart);\n    this._comparisonRangeEnd = this._getCellCompareValue(this.comparisonEnd);\n  }\n\n  /** Gets whether a date can be selected in the month view. */\n  private _canSelect(date: D) {\n    return !this.dateFilter || this.dateFilter(date);\n  }\n\n  /** Clears out preview state. */\n  private _clearPreview() {\n    this._previewStart = this._previewEnd = null;\n  }\n}\n", "<table class=\"mat-calendar-table\" role=\"grid\">\n  <thead class=\"mat-calendar-table-header\">\n    <tr>\n      @for (day of _weekdays; track day.id) {\n        <th scope=\"col\">\n          <span class=\"cdk-visually-hidden\">{{day.long}}</span>\n          <span aria-hidden=\"true\">{{day.narrow}}</span>\n        </th>\n      }\n    </tr>\n    <tr aria-hidden=\"true\"><th class=\"mat-calendar-table-header-divider\" colspan=\"7\"></th></tr>\n  </thead>\n  <tbody mat-calendar-body\n         [label]=\"_monthLabel\"\n         [rows]=\"_weeks\"\n         [todayValue]=\"_todayDate!\"\n         [startValue]=\"_rangeStart!\"\n         [endValue]=\"_rangeEnd!\"\n         [comparisonStart]=\"_comparisonRangeStart\"\n         [comparisonEnd]=\"_comparisonRangeEnd\"\n         [previewStart]=\"_previewStart\"\n         [previewEnd]=\"_previewEnd\"\n         [isRange]=\"_isRange\"\n         [labelMinRequiredCells]=\"3\"\n         [activeCell]=\"_dateAdapter.getDate(activeDate) - 1\"\n         [startDateAccessibleName]=\"startDateAccessibleName\"\n         [endDateAccessibleName]=\"endDateAccessibleName\"\n         (selectedValueChange)=\"_dateSelected($event)\"\n         (activeDateChange)=\"_updateActiveDate($event)\"\n         (previewChange)=\"_previewChanged($event)\"\n         (dragStarted)=\"dragStarted.emit($event)\"\n         (dragEnded)=\"_dragEnded($event)\"\n         (keyup)=\"_handleCalendarBodyKeyup($event)\"\n         (keydown)=\"_handleCalendarBodyKeydown($event)\">\n  </tbody>\n</table>\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  DOWN_ARROW,\n  END,\n  ENTER,\n  HOME,\n  LEFT_ARROW,\n  PAGE_DOWN,\n  PAGE_UP,\n  RIGHT_ARROW,\n  UP_ARROW,\n  SPACE,\n} from '@angular/cdk/keycodes';\nimport {\n  AfterContentInit,\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  EventEmitter,\n  Input,\n  Output,\n  ViewChild,\n  ViewEncapsulation,\n  OnDestroy,\n  inject,\n} from '@angular/core';\nimport {DateAdapter} from '../core';\nimport {Directionality} from '@angular/cdk/bidi';\nimport {\n  MatCalendarBody,\n  MatCalendarCell,\n  MatCalendarUserEvent,\n  MatCalendarCellClassFunction,\n} from './calendar-body';\nimport {createMissingDateImplError} from './datepicker-errors';\nimport {Subscription} from 'rxjs';\nimport {startWith} from 'rxjs/operators';\nimport {DateRange} from './date-selection-model';\n\nexport const yearsPerPage = 24;\n\nexport const yearsPerRow = 4;\n\n/**\n * An internal component used to display a year selector in the datepicker.\n * @docs-private\n */\n@Component({\n  selector: 'mat-multi-year-view',\n  templateUrl: 'multi-year-view.html',\n  exportAs: 'matMultiYearView',\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  imports: [MatCalendarBody],\n})\nexport class MatMultiYearView<D> implements AfterContentInit, OnDestroy {\n  private _changeDetectorRef = inject(ChangeDetectorRef);\n  _dateAdapter = inject<DateAdapter<D>>(DateAdapter, {optional: true})!;\n  private _dir = inject(Directionality, {optional: true});\n  private _rerenderSubscription = Subscription.EMPTY;\n\n  /** Flag used to filter out space/enter keyup events that originated outside of the view. */\n  private _selectionKeyPressed: boolean;\n\n  /** The date to display in this multi-year view (everything other than the year is ignored). */\n  @Input()\n  get activeDate(): D {\n    return this._activeDate;\n  }\n  set activeDate(value: D) {\n    let oldActiveDate = this._activeDate;\n    const validDate =\n      this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(value)) ||\n      this._dateAdapter.today();\n    this._activeDate = this._dateAdapter.clampDate(validDate, this.minDate, this.maxDate);\n\n    if (\n      !isSameMultiYearView(\n        this._dateAdapter,\n        oldActiveDate,\n        this._activeDate,\n        this.minDate,\n        this.maxDate,\n      )\n    ) {\n      this._init();\n    }\n  }\n  private _activeDate: D;\n\n  /** The currently selected date. */\n  @Input()\n  get selected(): DateRange<D> | D | null {\n    return this._selected;\n  }\n  set selected(value: DateRange<D> | D | null) {\n    if (value instanceof DateRange) {\n      this._selected = value;\n    } else {\n      this._selected = this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(value));\n    }\n\n    this._setSelectedYear(value);\n  }\n  private _selected: DateRange<D> | D | null;\n\n  /** The minimum selectable date. */\n  @Input()\n  get minDate(): D | null {\n    return this._minDate;\n  }\n  set minDate(value: D | null) {\n    this._minDate = this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(value));\n  }\n  private _minDate: D | null;\n\n  /** The maximum selectable date. */\n  @Input()\n  get maxDate(): D | null {\n    return this._maxDate;\n  }\n  set maxDate(value: D | null) {\n    this._maxDate = this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(value));\n  }\n  private _maxDate: D | null;\n\n  /** A function used to filter which dates are selectable. */\n  @Input() dateFilter: (date: D) => boolean;\n\n  /** Function that can be used to add custom CSS classes to date cells. */\n  @Input() dateClass: MatCalendarCellClassFunction<D>;\n\n  /** Emits when a new year is selected. */\n  @Output() readonly selectedChange: EventEmitter<D> = new EventEmitter<D>();\n\n  /** Emits the selected year. This doesn't imply a change on the selected date */\n  @Output() readonly yearSelected: EventEmitter<D> = new EventEmitter<D>();\n\n  /** Emits when any date is activated. */\n  @Output() readonly activeDateChange: EventEmitter<D> = new EventEmitter<D>();\n\n  /** The body of calendar table */\n  @ViewChild(MatCalendarBody) _matCalendarBody: MatCalendarBody;\n\n  /** Grid of calendar cells representing the currently displayed years. */\n  _years: MatCalendarCell[][];\n\n  /** The year that today falls on. */\n  _todayYear: number;\n\n  /** The year of the selected date. Null if the selected date is null. */\n  _selectedYear: number | null;\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    if (!this._dateAdapter && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw createMissingDateImplError('DateAdapter');\n    }\n\n    this._activeDate = this._dateAdapter.today();\n  }\n\n  ngAfterContentInit() {\n    this._rerenderSubscription = this._dateAdapter.localeChanges\n      .pipe(startWith(null))\n      .subscribe(() => this._init());\n  }\n\n  ngOnDestroy() {\n    this._rerenderSubscription.unsubscribe();\n  }\n\n  /** Initializes this multi-year view. */\n  _init() {\n    this._todayYear = this._dateAdapter.getYear(this._dateAdapter.today());\n\n    // We want a range years such that we maximize the number of\n    // enabled dates visible at once. This prevents issues where the minimum year\n    // is the last item of a page OR the maximum year is the first item of a page.\n\n    // The offset from the active year to the \"slot\" for the starting year is the\n    // *actual* first rendered year in the multi-year view.\n    const activeYear = this._dateAdapter.getYear(this._activeDate);\n    const minYearOfPage =\n      activeYear - getActiveOffset(this._dateAdapter, this.activeDate, this.minDate, this.maxDate);\n\n    this._years = [];\n    for (let i = 0, row: number[] = []; i < yearsPerPage; i++) {\n      row.push(minYearOfPage + i);\n      if (row.length == yearsPerRow) {\n        this._years.push(row.map(year => this._createCellForYear(year)));\n        row = [];\n      }\n    }\n    this._changeDetectorRef.markForCheck();\n  }\n\n  /** Handles when a new year is selected. */\n  _yearSelected(event: MatCalendarUserEvent<number>) {\n    const year = event.value;\n    const selectedYear = this._dateAdapter.createDate(year, 0, 1);\n    const selectedDate = this._getDateFromYear(year);\n\n    this.yearSelected.emit(selectedYear);\n    this.selectedChange.emit(selectedDate);\n  }\n\n  /**\n   * Takes the index of a calendar body cell wrapped in an event as argument. For the date that\n   * corresponds to the given cell, set `activeDate` to that date and fire `activeDateChange` with\n   * that date.\n   *\n   * This function is used to match each component's model of the active date with the calendar\n   * body cell that was focused. It updates its value of `activeDate` synchronously and updates the\n   * parent's value asynchronously via the `activeDateChange` event. The child component receives an\n   * updated value asynchronously via the `activeCell` Input.\n   */\n  _updateActiveDate(event: MatCalendarUserEvent<number>) {\n    const year = event.value;\n    const oldActiveDate = this._activeDate;\n\n    this.activeDate = this._getDateFromYear(year);\n    if (this._dateAdapter.compareDate(oldActiveDate, this.activeDate)) {\n      this.activeDateChange.emit(this.activeDate);\n    }\n  }\n\n  /** Handles keydown events on the calendar body when calendar is in multi-year view. */\n  _handleCalendarBodyKeydown(event: KeyboardEvent): void {\n    const oldActiveDate = this._activeDate;\n    const isRtl = this._isRtl();\n\n    switch (event.keyCode) {\n      case LEFT_ARROW:\n        this.activeDate = this._dateAdapter.addCalendarYears(this._activeDate, isRtl ? 1 : -1);\n        break;\n      case RIGHT_ARROW:\n        this.activeDate = this._dateAdapter.addCalendarYears(this._activeDate, isRtl ? -1 : 1);\n        break;\n      case UP_ARROW:\n        this.activeDate = this._dateAdapter.addCalendarYears(this._activeDate, -yearsPerRow);\n        break;\n      case DOWN_ARROW:\n        this.activeDate = this._dateAdapter.addCalendarYears(this._activeDate, yearsPerRow);\n        break;\n      case HOME:\n        this.activeDate = this._dateAdapter.addCalendarYears(\n          this._activeDate,\n          -getActiveOffset(this._dateAdapter, this.activeDate, this.minDate, this.maxDate),\n        );\n        break;\n      case END:\n        this.activeDate = this._dateAdapter.addCalendarYears(\n          this._activeDate,\n          yearsPerPage -\n            getActiveOffset(this._dateAdapter, this.activeDate, this.minDate, this.maxDate) -\n            1,\n        );\n        break;\n      case PAGE_UP:\n        this.activeDate = this._dateAdapter.addCalendarYears(\n          this._activeDate,\n          event.altKey ? -yearsPerPage * 10 : -yearsPerPage,\n        );\n        break;\n      case PAGE_DOWN:\n        this.activeDate = this._dateAdapter.addCalendarYears(\n          this._activeDate,\n          event.altKey ? yearsPerPage * 10 : yearsPerPage,\n        );\n        break;\n      case ENTER:\n      case SPACE:\n        // Note that we only prevent the default action here while the selection happens in\n        // `keyup` below. We can't do the selection here, because it can cause the calendar to\n        // reopen if focus is restored immediately. We also can't call `preventDefault` on `keyup`\n        // because it's too late (see #23305).\n        this._selectionKeyPressed = true;\n        break;\n      default:\n        // Don't prevent default or focus active cell on keys that we don't explicitly handle.\n        return;\n    }\n    if (this._dateAdapter.compareDate(oldActiveDate, this.activeDate)) {\n      this.activeDateChange.emit(this.activeDate);\n    }\n\n    this._focusActiveCellAfterViewChecked();\n    // Prevent unexpected default actions such as form submission.\n    event.preventDefault();\n  }\n\n  /** Handles keyup events on the calendar body when calendar is in multi-year view. */\n  _handleCalendarBodyKeyup(event: KeyboardEvent): void {\n    if (event.keyCode === SPACE || event.keyCode === ENTER) {\n      if (this._selectionKeyPressed) {\n        this._yearSelected({value: this._dateAdapter.getYear(this._activeDate), event});\n      }\n\n      this._selectionKeyPressed = false;\n    }\n  }\n\n  _getActiveCell(): number {\n    return getActiveOffset(this._dateAdapter, this.activeDate, this.minDate, this.maxDate);\n  }\n\n  /** Focuses the active cell after the microtask queue is empty. */\n  _focusActiveCell() {\n    this._matCalendarBody._focusActiveCell();\n  }\n\n  /** Focuses the active cell after change detection has run and the microtask queue is empty. */\n  _focusActiveCellAfterViewChecked() {\n    this._matCalendarBody._scheduleFocusActiveCellAfterViewChecked();\n  }\n\n  /**\n   * Takes a year and returns a new date on the same day and month as the currently active date\n   *  The returned date will have the same year as the argument date.\n   */\n  private _getDateFromYear(year: number) {\n    const activeMonth = this._dateAdapter.getMonth(this.activeDate);\n    const daysInMonth = this._dateAdapter.getNumDaysInMonth(\n      this._dateAdapter.createDate(year, activeMonth, 1),\n    );\n    const normalizedDate = this._dateAdapter.createDate(\n      year,\n      activeMonth,\n      Math.min(this._dateAdapter.getDate(this.activeDate), daysInMonth),\n    );\n    return normalizedDate;\n  }\n\n  /** Creates an MatCalendarCell for the given year. */\n  private _createCellForYear(year: number) {\n    const date = this._dateAdapter.createDate(year, 0, 1);\n    const yearName = this._dateAdapter.getYearName(date);\n    const cellClasses = this.dateClass ? this.dateClass(date, 'multi-year') : undefined;\n\n    return new MatCalendarCell(year, yearName, yearName, this._shouldEnableYear(year), cellClasses);\n  }\n\n  /** Whether the given year is enabled. */\n  private _shouldEnableYear(year: number) {\n    // disable if the year is greater than maxDate lower than minDate\n    if (\n      year === undefined ||\n      year === null ||\n      (this.maxDate && year > this._dateAdapter.getYear(this.maxDate)) ||\n      (this.minDate && year < this._dateAdapter.getYear(this.minDate))\n    ) {\n      return false;\n    }\n\n    // enable if it reaches here and there's no filter defined\n    if (!this.dateFilter) {\n      return true;\n    }\n\n    const firstOfYear = this._dateAdapter.createDate(year, 0, 1);\n\n    // If any date in the year is enabled count the year as enabled.\n    for (\n      let date = firstOfYear;\n      this._dateAdapter.getYear(date) == year;\n      date = this._dateAdapter.addCalendarDays(date, 1)\n    ) {\n      if (this.dateFilter(date)) {\n        return true;\n      }\n    }\n\n    return false;\n  }\n\n  /** Determines whether the user has the RTL layout direction. */\n  private _isRtl() {\n    return this._dir && this._dir.value === 'rtl';\n  }\n\n  /** Sets the currently-highlighted year based on a model value. */\n  private _setSelectedYear(value: DateRange<D> | D | null) {\n    this._selectedYear = null;\n\n    if (value instanceof DateRange) {\n      const displayValue = value.start || value.end;\n\n      if (displayValue) {\n        this._selectedYear = this._dateAdapter.getYear(displayValue);\n      }\n    } else if (value) {\n      this._selectedYear = this._dateAdapter.getYear(value);\n    }\n  }\n}\n\nexport function isSameMultiYearView<D>(\n  dateAdapter: DateAdapter<D>,\n  date1: D,\n  date2: D,\n  minDate: D | null,\n  maxDate: D | null,\n): boolean {\n  const year1 = dateAdapter.getYear(date1);\n  const year2 = dateAdapter.getYear(date2);\n  const startingYear = getStartingYear(dateAdapter, minDate, maxDate);\n  return (\n    Math.floor((year1 - startingYear) / yearsPerPage) ===\n    Math.floor((year2 - startingYear) / yearsPerPage)\n  );\n}\n\n/**\n * When the multi-year view is first opened, the active year will be in view.\n * So we compute how many years are between the active year and the *slot* where our\n * \"startingYear\" will render when paged into view.\n */\nexport function getActiveOffset<D>(\n  dateAdapter: DateAdapter<D>,\n  activeDate: D,\n  minDate: D | null,\n  maxDate: D | null,\n): number {\n  const activeYear = dateAdapter.getYear(activeDate);\n  return euclideanModulo(activeYear - getStartingYear(dateAdapter, minDate, maxDate), yearsPerPage);\n}\n\n/**\n * We pick a \"starting\" year such that either the maximum year would be at the end\n * or the minimum year would be at the beginning of a page.\n */\nfunction getStartingYear<D>(\n  dateAdapter: DateAdapter<D>,\n  minDate: D | null,\n  maxDate: D | null,\n): number {\n  let startingYear = 0;\n  if (maxDate) {\n    const maxYear = dateAdapter.getYear(maxDate);\n    startingYear = maxYear - yearsPerPage + 1;\n  } else if (minDate) {\n    startingYear = dateAdapter.getYear(minDate);\n  }\n  return startingYear;\n}\n\n/** Gets remainder that is non-negative, even if first number is negative */\nfunction euclideanModulo(a: number, b: number): number {\n  return ((a % b) + b) % b;\n}\n", "<table class=\"mat-calendar-table\" role=\"grid\">\n  <thead aria-hidden=\"true\" class=\"mat-calendar-table-header\">\n    <tr><th class=\"mat-calendar-table-header-divider\" colspan=\"4\"></th></tr>\n  </thead>\n  <tbody mat-calendar-body\n         [rows]=\"_years\"\n         [todayValue]=\"_todayYear\"\n         [startValue]=\"_selectedYear!\"\n         [endValue]=\"_selectedYear!\"\n         [numCols]=\"4\"\n         [cellAspectRatio]=\"4 / 7\"\n         [activeCell]=\"_getActiveCell()\"\n         (selectedValueChange)=\"_yearSelected($event)\"\n         (activeDateChange)=\"_updateActiveDate($event)\"\n         (keyup)=\"_handleCalendarBodyKeyup($event)\"\n         (keydown)=\"_handleCalendarBodyKeydown($event)\">\n  </tbody>\n</table>\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  DOWN_ARROW,\n  END,\n  ENTER,\n  HOME,\n  LEFT_ARROW,\n  PAGE_DOWN,\n  PAGE_UP,\n  RIGHT_ARROW,\n  UP_ARROW,\n  SPACE,\n} from '@angular/cdk/keycodes';\nimport {\n  AfterContentInit,\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  EventEmitter,\n  Input,\n  Output,\n  ViewChild,\n  ViewEncapsulation,\n  OnDestroy,\n  inject,\n} from '@angular/core';\nimport {DateAdapter, MAT_DATE_FORMATS, MatDateFormats} from '../core';\nimport {Directionality} from '@angular/cdk/bidi';\nimport {\n  MatCalendarBody,\n  MatCalendarCell,\n  MatCalendarUserEvent,\n  MatCalendarCellClassFunction,\n} from './calendar-body';\nimport {createMissingDateImplError} from './datepicker-errors';\nimport {Subscription} from 'rxjs';\nimport {startWith} from 'rxjs/operators';\nimport {DateRange} from './date-selection-model';\n\n/**\n * An internal component used to display a single year in the datepicker.\n * @docs-private\n */\n@Component({\n  selector: 'mat-year-view',\n  templateUrl: 'year-view.html',\n  exportAs: 'matYearView',\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  imports: [MatCalendarBody],\n})\nexport class MatYearView<D> implements AfterContentInit, OnDestroy {\n  readonly _changeDetectorRef = inject(ChangeDetectorRef);\n  private _dateFormats = inject<MatDateFormats>(MAT_DATE_FORMATS, {optional: true})!;\n  _dateAdapter = inject<DateAdapter<D>>(DateAdapter, {optional: true})!;\n  private _dir = inject(Directionality, {optional: true});\n\n  private _rerenderSubscription = Subscription.EMPTY;\n\n  /** Flag used to filter out space/enter keyup events that originated outside of the view. */\n  private _selectionKeyPressed: boolean;\n\n  /** The date to display in this year view (everything other than the year is ignored). */\n  @Input()\n  get activeDate(): D {\n    return this._activeDate;\n  }\n  set activeDate(value: D) {\n    let oldActiveDate = this._activeDate;\n    const validDate =\n      this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(value)) ||\n      this._dateAdapter.today();\n    this._activeDate = this._dateAdapter.clampDate(validDate, this.minDate, this.maxDate);\n    if (this._dateAdapter.getYear(oldActiveDate) !== this._dateAdapter.getYear(this._activeDate)) {\n      this._init();\n    }\n  }\n  private _activeDate: D;\n\n  /** The currently selected date. */\n  @Input()\n  get selected(): DateRange<D> | D | null {\n    return this._selected;\n  }\n  set selected(value: DateRange<D> | D | null) {\n    if (value instanceof DateRange) {\n      this._selected = value;\n    } else {\n      this._selected = this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(value));\n    }\n\n    this._setSelectedMonth(value);\n  }\n  private _selected: DateRange<D> | D | null;\n\n  /** The minimum selectable date. */\n  @Input()\n  get minDate(): D | null {\n    return this._minDate;\n  }\n  set minDate(value: D | null) {\n    this._minDate = this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(value));\n  }\n  private _minDate: D | null;\n\n  /** The maximum selectable date. */\n  @Input()\n  get maxDate(): D | null {\n    return this._maxDate;\n  }\n  set maxDate(value: D | null) {\n    this._maxDate = this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(value));\n  }\n  private _maxDate: D | null;\n\n  /** A function used to filter which dates are selectable. */\n  @Input() dateFilter: (date: D) => boolean;\n\n  /** Function that can be used to add custom CSS classes to date cells. */\n  @Input() dateClass: MatCalendarCellClassFunction<D>;\n\n  /** Emits when a new month is selected. */\n  @Output() readonly selectedChange: EventEmitter<D> = new EventEmitter<D>();\n\n  /** Emits the selected month. This doesn't imply a change on the selected date */\n  @Output() readonly monthSelected: EventEmitter<D> = new EventEmitter<D>();\n\n  /** Emits when any date is activated. */\n  @Output() readonly activeDateChange: EventEmitter<D> = new EventEmitter<D>();\n\n  /** The body of calendar table */\n  @ViewChild(MatCalendarBody) _matCalendarBody: MatCalendarBody;\n\n  /** Grid of calendar cells representing the months of the year. */\n  _months: MatCalendarCell[][];\n\n  /** The label for this year (e.g. \"2017\"). */\n  _yearLabel: string;\n\n  /** The month in this year that today falls on. Null if today is in a different year. */\n  _todayMonth: number | null;\n\n  /**\n   * The month in this year that the selected Date falls on.\n   * Null if the selected Date is in a different year.\n   */\n  _selectedMonth: number | null;\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (!this._dateAdapter) {\n        throw createMissingDateImplError('DateAdapter');\n      }\n      if (!this._dateFormats) {\n        throw createMissingDateImplError('MAT_DATE_FORMATS');\n      }\n    }\n\n    this._activeDate = this._dateAdapter.today();\n  }\n\n  ngAfterContentInit() {\n    this._rerenderSubscription = this._dateAdapter.localeChanges\n      .pipe(startWith(null))\n      .subscribe(() => this._init());\n  }\n\n  ngOnDestroy() {\n    this._rerenderSubscription.unsubscribe();\n  }\n\n  /** Handles when a new month is selected. */\n  _monthSelected(event: MatCalendarUserEvent<number>) {\n    const month = event.value;\n\n    const selectedMonth = this._dateAdapter.createDate(\n      this._dateAdapter.getYear(this.activeDate),\n      month,\n      1,\n    );\n    this.monthSelected.emit(selectedMonth);\n\n    const selectedDate = this._getDateFromMonth(month);\n    this.selectedChange.emit(selectedDate);\n  }\n\n  /**\n   * Takes the index of a calendar body cell wrapped in an event as argument. For the date that\n   * corresponds to the given cell, set `activeDate` to that date and fire `activeDateChange` with\n   * that date.\n   *\n   * This function is used to match each component's model of the active date with the calendar\n   * body cell that was focused. It updates its value of `activeDate` synchronously and updates the\n   * parent's value asynchronously via the `activeDateChange` event. The child component receives an\n   * updated value asynchronously via the `activeCell` Input.\n   */\n  _updateActiveDate(event: MatCalendarUserEvent<number>) {\n    const month = event.value;\n    const oldActiveDate = this._activeDate;\n\n    this.activeDate = this._getDateFromMonth(month);\n\n    if (this._dateAdapter.compareDate(oldActiveDate, this.activeDate)) {\n      this.activeDateChange.emit(this.activeDate);\n    }\n  }\n\n  /** Handles keydown events on the calendar body when calendar is in year view. */\n  _handleCalendarBodyKeydown(event: KeyboardEvent): void {\n    // TODO(mmalerba): We currently allow keyboard navigation to disabled dates, but just prevent\n    // disabled ones from being selected. This may not be ideal, we should look into whether\n    // navigation should skip over disabled dates, and if so, how to implement that efficiently.\n\n    const oldActiveDate = this._activeDate;\n    const isRtl = this._isRtl();\n\n    switch (event.keyCode) {\n      case LEFT_ARROW:\n        this.activeDate = this._dateAdapter.addCalendarMonths(this._activeDate, isRtl ? 1 : -1);\n        break;\n      case RIGHT_ARROW:\n        this.activeDate = this._dateAdapter.addCalendarMonths(this._activeDate, isRtl ? -1 : 1);\n        break;\n      case UP_ARROW:\n        this.activeDate = this._dateAdapter.addCalendarMonths(this._activeDate, -4);\n        break;\n      case DOWN_ARROW:\n        this.activeDate = this._dateAdapter.addCalendarMonths(this._activeDate, 4);\n        break;\n      case HOME:\n        this.activeDate = this._dateAdapter.addCalendarMonths(\n          this._activeDate,\n          -this._dateAdapter.getMonth(this._activeDate),\n        );\n        break;\n      case END:\n        this.activeDate = this._dateAdapter.addCalendarMonths(\n          this._activeDate,\n          11 - this._dateAdapter.getMonth(this._activeDate),\n        );\n        break;\n      case PAGE_UP:\n        this.activeDate = this._dateAdapter.addCalendarYears(\n          this._activeDate,\n          event.altKey ? -10 : -1,\n        );\n        break;\n      case PAGE_DOWN:\n        this.activeDate = this._dateAdapter.addCalendarYears(\n          this._activeDate,\n          event.altKey ? 10 : 1,\n        );\n        break;\n      case ENTER:\n      case SPACE:\n        // Note that we only prevent the default action here while the selection happens in\n        // `keyup` below. We can't do the selection here, because it can cause the calendar to\n        // reopen if focus is restored immediately. We also can't call `preventDefault` on `keyup`\n        // because it's too late (see #23305).\n        this._selectionKeyPressed = true;\n        break;\n      default:\n        // Don't prevent default or focus active cell on keys that we don't explicitly handle.\n        return;\n    }\n\n    if (this._dateAdapter.compareDate(oldActiveDate, this.activeDate)) {\n      this.activeDateChange.emit(this.activeDate);\n      this._focusActiveCellAfterViewChecked();\n    }\n\n    // Prevent unexpected default actions such as form submission.\n    event.preventDefault();\n  }\n\n  /** Handles keyup events on the calendar body when calendar is in year view. */\n  _handleCalendarBodyKeyup(event: KeyboardEvent): void {\n    if (event.keyCode === SPACE || event.keyCode === ENTER) {\n      if (this._selectionKeyPressed) {\n        this._monthSelected({value: this._dateAdapter.getMonth(this._activeDate), event});\n      }\n\n      this._selectionKeyPressed = false;\n    }\n  }\n\n  /** Initializes this year view. */\n  _init() {\n    this._setSelectedMonth(this.selected);\n    this._todayMonth = this._getMonthInCurrentYear(this._dateAdapter.today());\n    this._yearLabel = this._dateAdapter.getYearName(this.activeDate);\n\n    let monthNames = this._dateAdapter.getMonthNames('short');\n    // First row of months only contains 5 elements so we can fit the year label on the same row.\n    this._months = [\n      [0, 1, 2, 3],\n      [4, 5, 6, 7],\n      [8, 9, 10, 11],\n    ].map(row => row.map(month => this._createCellForMonth(month, monthNames[month])));\n    this._changeDetectorRef.markForCheck();\n  }\n\n  /** Focuses the active cell after the microtask queue is empty. */\n  _focusActiveCell() {\n    this._matCalendarBody._focusActiveCell();\n  }\n\n  /** Schedules the matCalendarBody to focus the active cell after change detection has run */\n  _focusActiveCellAfterViewChecked() {\n    this._matCalendarBody._scheduleFocusActiveCellAfterViewChecked();\n  }\n\n  /**\n   * Gets the month in this year that the given Date falls on.\n   * Returns null if the given Date is in another year.\n   */\n  private _getMonthInCurrentYear(date: D | null) {\n    return date && this._dateAdapter.getYear(date) == this._dateAdapter.getYear(this.activeDate)\n      ? this._dateAdapter.getMonth(date)\n      : null;\n  }\n\n  /**\n   * Takes a month and returns a new date in the same day and year as the currently active date.\n   *  The returned date will have the same month as the argument date.\n   */\n  private _getDateFromMonth(month: number) {\n    const normalizedDate = this._dateAdapter.createDate(\n      this._dateAdapter.getYear(this.activeDate),\n      month,\n      1,\n    );\n\n    const daysInMonth = this._dateAdapter.getNumDaysInMonth(normalizedDate);\n\n    return this._dateAdapter.createDate(\n      this._dateAdapter.getYear(this.activeDate),\n      month,\n      Math.min(this._dateAdapter.getDate(this.activeDate), daysInMonth),\n    );\n  }\n\n  /** Creates an MatCalendarCell for the given month. */\n  private _createCellForMonth(month: number, monthName: string) {\n    const date = this._dateAdapter.createDate(this._dateAdapter.getYear(this.activeDate), month, 1);\n    const ariaLabel = this._dateAdapter.format(date, this._dateFormats.display.monthYearA11yLabel);\n    const cellClasses = this.dateClass ? this.dateClass(date, 'year') : undefined;\n\n    return new MatCalendarCell(\n      month,\n      monthName.toLocaleUpperCase(),\n      ariaLabel,\n      this._shouldEnableMonth(month),\n      cellClasses,\n    );\n  }\n\n  /** Whether the given month is enabled. */\n  private _shouldEnableMonth(month: number) {\n    const activeYear = this._dateAdapter.getYear(this.activeDate);\n\n    if (\n      month === undefined ||\n      month === null ||\n      this._isYearAndMonthAfterMaxDate(activeYear, month) ||\n      this._isYearAndMonthBeforeMinDate(activeYear, month)\n    ) {\n      return false;\n    }\n\n    if (!this.dateFilter) {\n      return true;\n    }\n\n    const firstOfMonth = this._dateAdapter.createDate(activeYear, month, 1);\n\n    // If any date in the month is enabled count the month as enabled.\n    for (\n      let date = firstOfMonth;\n      this._dateAdapter.getMonth(date) == month;\n      date = this._dateAdapter.addCalendarDays(date, 1)\n    ) {\n      if (this.dateFilter(date)) {\n        return true;\n      }\n    }\n\n    return false;\n  }\n\n  /**\n   * Tests whether the combination month/year is after this.maxDate, considering\n   * just the month and year of this.maxDate\n   */\n  private _isYearAndMonthAfterMaxDate(year: number, month: number) {\n    if (this.maxDate) {\n      const maxYear = this._dateAdapter.getYear(this.maxDate);\n      const maxMonth = this._dateAdapter.getMonth(this.maxDate);\n\n      return year > maxYear || (year === maxYear && month > maxMonth);\n    }\n\n    return false;\n  }\n\n  /**\n   * Tests whether the combination month/year is before this.minDate, considering\n   * just the month and year of this.minDate\n   */\n  private _isYearAndMonthBeforeMinDate(year: number, month: number) {\n    if (this.minDate) {\n      const minYear = this._dateAdapter.getYear(this.minDate);\n      const minMonth = this._dateAdapter.getMonth(this.minDate);\n\n      return year < minYear || (year === minYear && month < minMonth);\n    }\n\n    return false;\n  }\n\n  /** Determines whether the user has the RTL layout direction. */\n  private _isRtl() {\n    return this._dir && this._dir.value === 'rtl';\n  }\n\n  /** Sets the currently-selected month based on a model value. */\n  private _setSelectedMonth(value: DateRange<D> | D | null) {\n    if (value instanceof DateRange) {\n      this._selectedMonth =\n        this._getMonthInCurrentYear(value.start) || this._getMonthInCurrentYear(value.end);\n    } else {\n      this._selectedMonth = this._getMonthInCurrentYear(value);\n    }\n  }\n}\n", "<table class=\"mat-calendar-table\" role=\"grid\">\n  <thead aria-hidden=\"true\" class=\"mat-calendar-table-header\">\n    <tr><th class=\"mat-calendar-table-header-divider\" colspan=\"4\"></th></tr>\n  </thead>\n  <tbody mat-calendar-body\n         [label]=\"_yearLabel\"\n         [rows]=\"_months\"\n         [todayValue]=\"_todayMonth!\"\n         [startValue]=\"_selectedMonth!\"\n         [endValue]=\"_selectedMonth!\"\n         [labelMinRequiredCells]=\"2\"\n         [numCols]=\"4\"\n         [cellAspectRatio]=\"4 / 7\"\n         [activeCell]=\"_dateAdapter.getMonth(activeDate)\"\n         (selectedValueChange)=\"_monthSelected($event)\"\n         (activeDateChange)=\"_updateActiveDate($event)\"\n         (keyup)=\"_handleCalendarBodyKeyup($event)\"\n         (keydown)=\"_handleCalendarBodyKeydown($event)\">\n  </tbody>\n</table>\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {CdkPortalOutlet, ComponentPortal, ComponentType, Portal} from '@angular/cdk/portal';\nimport {\n  AfterContentInit,\n  AfterViewChecked,\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  ElementRef,\n  EventEmitter,\n  Input,\n  OnChanges,\n  OnDestroy,\n  Output,\n  SimpleChange,\n  SimpleChanges,\n  ViewChild,\n  ViewEncapsulation,\n  inject,\n} from '@angular/core';\nimport {DateAdapter, MAT_DATE_FORMATS, MatDateFormats} from '../core';\nimport {Subject, Subscription} from 'rxjs';\nimport {MatCalendarUserEvent, MatCalendarCellClassFunction} from './calendar-body';\nimport {createMissingDateImplError} from './datepicker-errors';\nimport {MatDatepickerIntl} from './datepicker-intl';\nimport {MatMonthView} from './month-view';\nimport {\n  getActiveOffset,\n  isSameMultiYearView,\n  MatMultiYearView,\n  yearsPerPage,\n} from './multi-year-view';\nimport {MatYearView} from './year-view';\nimport {MAT_SINGLE_DATE_SELECTION_MODEL_PROVIDER, DateRange} from './date-selection-model';\nimport {MatIconButton, MatButton} from '../button';\nimport {_IdGenerator, CdkMonitorFocus} from '@angular/cdk/a11y';\nimport {_CdkPrivateStyleLoader, _VisuallyHiddenLoader} from '@angular/cdk/private';\nimport {_getFocusedElementPierceShadowDom} from '@angular/cdk/platform';\n\n/**\n * Possible views for the calendar.\n * @docs-private\n */\nexport type MatCalendarView = 'month' | 'year' | 'multi-year';\n\n/** Default header for MatCalendar */\n@Component({\n  selector: 'mat-calendar-header',\n  templateUrl: 'calendar-header.html',\n  exportAs: 'matCalendarHeader',\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  imports: [MatButton, MatIconButton],\n})\nexport class MatCalendarHeader<D> {\n  private _intl = inject(MatDatepickerIntl);\n  calendar = inject<MatCalendar<D>>(MatCalendar);\n  private _dateAdapter = inject<DateAdapter<D>>(DateAdapter, {optional: true})!;\n  private _dateFormats = inject<MatDateFormats>(MAT_DATE_FORMATS, {optional: true})!;\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    inject(_CdkPrivateStyleLoader).load(_VisuallyHiddenLoader);\n    const changeDetectorRef = inject(ChangeDetectorRef);\n    this.calendar.stateChanges.subscribe(() => changeDetectorRef.markForCheck());\n  }\n\n  /** The display text for the current calendar view. */\n  get periodButtonText(): string {\n    if (this.calendar.currentView == 'month') {\n      return this._dateAdapter\n        .format(this.calendar.activeDate, this._dateFormats.display.monthYearLabel)\n        .toLocaleUpperCase();\n    }\n    if (this.calendar.currentView == 'year') {\n      return this._dateAdapter.getYearName(this.calendar.activeDate);\n    }\n\n    return this._intl.formatYearRange(...this._formatMinAndMaxYearLabels());\n  }\n\n  /** The aria description for the current calendar view. */\n  get periodButtonDescription(): string {\n    if (this.calendar.currentView == 'month') {\n      return this._dateAdapter\n        .format(this.calendar.activeDate, this._dateFormats.display.monthYearLabel)\n        .toLocaleUpperCase();\n    }\n    if (this.calendar.currentView == 'year') {\n      return this._dateAdapter.getYearName(this.calendar.activeDate);\n    }\n\n    // Format a label for the window of years displayed in the multi-year calendar view. Use\n    // `formatYearRangeLabel` because it is TTS friendly.\n    return this._intl.formatYearRangeLabel(...this._formatMinAndMaxYearLabels());\n  }\n\n  /** The `aria-label` for changing the calendar view. */\n  get periodButtonLabel(): string {\n    return this.calendar.currentView == 'month'\n      ? this._intl.switchToMultiYearViewLabel\n      : this._intl.switchToMonthViewLabel;\n  }\n\n  /** The label for the previous button. */\n  get prevButtonLabel(): string {\n    return {\n      'month': this._intl.prevMonthLabel,\n      'year': this._intl.prevYearLabel,\n      'multi-year': this._intl.prevMultiYearLabel,\n    }[this.calendar.currentView];\n  }\n\n  /** The label for the next button. */\n  get nextButtonLabel(): string {\n    return {\n      'month': this._intl.nextMonthLabel,\n      'year': this._intl.nextYearLabel,\n      'multi-year': this._intl.nextMultiYearLabel,\n    }[this.calendar.currentView];\n  }\n\n  /** Handles user clicks on the period label. */\n  currentPeriodClicked(): void {\n    this.calendar.currentView = this.calendar.currentView == 'month' ? 'multi-year' : 'month';\n  }\n\n  /** Handles user clicks on the previous button. */\n  previousClicked(): void {\n    this.calendar.activeDate =\n      this.calendar.currentView == 'month'\n        ? this._dateAdapter.addCalendarMonths(this.calendar.activeDate, -1)\n        : this._dateAdapter.addCalendarYears(\n            this.calendar.activeDate,\n            this.calendar.currentView == 'year' ? -1 : -yearsPerPage,\n          );\n  }\n\n  /** Handles user clicks on the next button. */\n  nextClicked(): void {\n    this.calendar.activeDate =\n      this.calendar.currentView == 'month'\n        ? this._dateAdapter.addCalendarMonths(this.calendar.activeDate, 1)\n        : this._dateAdapter.addCalendarYears(\n            this.calendar.activeDate,\n            this.calendar.currentView == 'year' ? 1 : yearsPerPage,\n          );\n  }\n\n  /** Whether the previous period button is enabled. */\n  previousEnabled(): boolean {\n    if (!this.calendar.minDate) {\n      return true;\n    }\n    return (\n      !this.calendar.minDate || !this._isSameView(this.calendar.activeDate, this.calendar.minDate)\n    );\n  }\n\n  /** Whether the next period button is enabled. */\n  nextEnabled(): boolean {\n    return (\n      !this.calendar.maxDate || !this._isSameView(this.calendar.activeDate, this.calendar.maxDate)\n    );\n  }\n\n  /** Whether the two dates represent the same view in the current view mode (month or year). */\n  private _isSameView(date1: D, date2: D): boolean {\n    if (this.calendar.currentView == 'month') {\n      return (\n        this._dateAdapter.getYear(date1) == this._dateAdapter.getYear(date2) &&\n        this._dateAdapter.getMonth(date1) == this._dateAdapter.getMonth(date2)\n      );\n    }\n    if (this.calendar.currentView == 'year') {\n      return this._dateAdapter.getYear(date1) == this._dateAdapter.getYear(date2);\n    }\n    // Otherwise we are in 'multi-year' view.\n    return isSameMultiYearView(\n      this._dateAdapter,\n      date1,\n      date2,\n      this.calendar.minDate,\n      this.calendar.maxDate,\n    );\n  }\n\n  /**\n   * Format two individual labels for the minimum year and maximum year available in the multi-year\n   * calendar view. Returns an array of two strings where the first string is the formatted label\n   * for the minimum year, and the second string is the formatted label for the maximum year.\n   */\n  private _formatMinAndMaxYearLabels(): [minYearLabel: string, maxYearLabel: string] {\n    // The offset from the active year to the \"slot\" for the starting year is the\n    // *actual* first rendered year in the multi-year view, and the last year is\n    // just yearsPerPage - 1 away.\n    const activeYear = this._dateAdapter.getYear(this.calendar.activeDate);\n    const minYearOfPage =\n      activeYear -\n      getActiveOffset(\n        this._dateAdapter,\n        this.calendar.activeDate,\n        this.calendar.minDate,\n        this.calendar.maxDate,\n      );\n    const maxYearOfPage = minYearOfPage + yearsPerPage - 1;\n    const minYearLabel = this._dateAdapter.getYearName(\n      this._dateAdapter.createDate(minYearOfPage, 0, 1),\n    );\n    const maxYearLabel = this._dateAdapter.getYearName(\n      this._dateAdapter.createDate(maxYearOfPage, 0, 1),\n    );\n\n    return [minYearLabel, maxYearLabel];\n  }\n\n  _periodButtonLabelId = inject(_IdGenerator).getId('mat-calendar-period-label-');\n}\n\n/** A calendar that is used as part of the datepicker. */\n@Component({\n  selector: 'mat-calendar',\n  templateUrl: 'calendar.html',\n  styleUrl: 'calendar.css',\n  host: {\n    'class': 'mat-calendar',\n  },\n  exportAs: 'matCalendar',\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  providers: [MAT_SINGLE_DATE_SELECTION_MODEL_PROVIDER],\n  imports: [CdkPortalOutlet, CdkMonitorFocus, MatMonthView, MatYearView, MatMultiYearView],\n})\nexport class MatCalendar<D> implements AfterContentInit, AfterViewChecked, OnDestroy, OnChanges {\n  private _dateAdapter = inject<DateAdapter<D>>(DateAdapter, {optional: true})!;\n  private _dateFormats = inject<MatDateFormats>(MAT_DATE_FORMATS, {optional: true});\n  private _changeDetectorRef = inject(ChangeDetectorRef);\n  private _elementRef = inject<ElementRef<HTMLElement>>(ElementRef);\n\n  /** An input indicating the type of the header component, if set. */\n  @Input() headerComponent: ComponentType<any>;\n\n  /** A portal containing the header component type for this calendar. */\n  _calendarHeaderPortal: Portal<any>;\n\n  private _intlChanges: Subscription;\n\n  /**\n   * Used for scheduling that focus should be moved to the active cell on the next tick.\n   * We need to schedule it, rather than do it immediately, because we have to wait\n   * for Angular to re-evaluate the view children.\n   */\n  private _moveFocusOnNextTick = false;\n\n  /** A date representing the period (month or year) to start the calendar in. */\n  @Input()\n  get startAt(): D | null {\n    return this._startAt;\n  }\n  set startAt(value: D | null) {\n    this._startAt = this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(value));\n  }\n  private _startAt: D | null;\n\n  /** Whether the calendar should be started in month or year view. */\n  @Input() startView: MatCalendarView = 'month';\n\n  /** The currently selected date. */\n  @Input()\n  get selected(): DateRange<D> | D | null {\n    return this._selected;\n  }\n  set selected(value: DateRange<D> | D | null) {\n    if (value instanceof DateRange) {\n      this._selected = value;\n    } else {\n      this._selected = this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(value));\n    }\n  }\n  private _selected: DateRange<D> | D | null;\n\n  /** The minimum selectable date. */\n  @Input()\n  get minDate(): D | null {\n    return this._minDate;\n  }\n  set minDate(value: D | null) {\n    this._minDate = this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(value));\n  }\n  private _minDate: D | null;\n\n  /** The maximum selectable date. */\n  @Input()\n  get maxDate(): D | null {\n    return this._maxDate;\n  }\n  set maxDate(value: D | null) {\n    this._maxDate = this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(value));\n  }\n  private _maxDate: D | null;\n\n  /** Function used to filter which dates are selectable. */\n  @Input() dateFilter: (date: D) => boolean;\n\n  /** Function that can be used to add custom CSS classes to dates. */\n  @Input() dateClass: MatCalendarCellClassFunction<D>;\n\n  /** Start of the comparison range. */\n  @Input() comparisonStart: D | null;\n\n  /** End of the comparison range. */\n  @Input() comparisonEnd: D | null;\n\n  /** ARIA Accessible name of the `<input matStartDate/>` */\n  @Input() startDateAccessibleName: string | null;\n\n  /** ARIA Accessible name of the `<input matEndDate/>` */\n  @Input() endDateAccessibleName: string | null;\n\n  /** Emits when the currently selected date changes. */\n  @Output() readonly selectedChange: EventEmitter<D | null> = new EventEmitter<D | null>();\n\n  /**\n   * Emits the year chosen in multiyear view.\n   * This doesn't imply a change on the selected date.\n   */\n  @Output() readonly yearSelected: EventEmitter<D> = new EventEmitter<D>();\n\n  /**\n   * Emits the month chosen in year view.\n   * This doesn't imply a change on the selected date.\n   */\n  @Output() readonly monthSelected: EventEmitter<D> = new EventEmitter<D>();\n\n  /**\n   * Emits when the current view changes.\n   */\n  @Output() readonly viewChanged: EventEmitter<MatCalendarView> = new EventEmitter<MatCalendarView>(\n    true,\n  );\n\n  /** Emits when any date is selected. */\n  @Output() readonly _userSelection: EventEmitter<MatCalendarUserEvent<D | null>> =\n    new EventEmitter<MatCalendarUserEvent<D | null>>();\n\n  /** Emits a new date range value when the user completes a drag drop operation. */\n  @Output() readonly _userDragDrop = new EventEmitter<MatCalendarUserEvent<DateRange<D>>>();\n\n  /** Reference to the current month view component. */\n  @ViewChild(MatMonthView) monthView: MatMonthView<D>;\n\n  /** Reference to the current year view component. */\n  @ViewChild(MatYearView) yearView: MatYearView<D>;\n\n  /** Reference to the current multi-year view component. */\n  @ViewChild(MatMultiYearView) multiYearView: MatMultiYearView<D>;\n\n  /**\n   * The current active date. This determines which time period is shown and which date is\n   * highlighted when using keyboard navigation.\n   */\n  get activeDate(): D {\n    return this._clampedActiveDate;\n  }\n  set activeDate(value: D) {\n    this._clampedActiveDate = this._dateAdapter.clampDate(value, this.minDate, this.maxDate);\n    this.stateChanges.next();\n    this._changeDetectorRef.markForCheck();\n  }\n  private _clampedActiveDate: D;\n\n  /** Whether the calendar is in month view. */\n  get currentView(): MatCalendarView {\n    return this._currentView;\n  }\n  set currentView(value: MatCalendarView) {\n    const viewChangedResult = this._currentView !== value ? value : null;\n    this._currentView = value;\n    this._moveFocusOnNextTick = true;\n    this._changeDetectorRef.markForCheck();\n    if (viewChangedResult) {\n      this.viewChanged.emit(viewChangedResult);\n    }\n  }\n  private _currentView: MatCalendarView;\n\n  /** Origin of active drag, or null when dragging is not active. */\n  protected _activeDrag: MatCalendarUserEvent<D> | null = null;\n\n  /**\n   * Emits whenever there is a state change that the header may need to respond to.\n   */\n  readonly stateChanges = new Subject<void>();\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (!this._dateAdapter) {\n        throw createMissingDateImplError('DateAdapter');\n      }\n\n      if (!this._dateFormats) {\n        throw createMissingDateImplError('MAT_DATE_FORMATS');\n      }\n    }\n\n    this._intlChanges = inject(MatDatepickerIntl).changes.subscribe(() => {\n      this._changeDetectorRef.markForCheck();\n      this.stateChanges.next();\n    });\n  }\n\n  ngAfterContentInit() {\n    this._calendarHeaderPortal = new ComponentPortal(this.headerComponent || MatCalendarHeader);\n    this.activeDate = this.startAt || this._dateAdapter.today();\n\n    // Assign to the private property since we don't want to move focus on init.\n    this._currentView = this.startView;\n  }\n\n  ngAfterViewChecked() {\n    if (this._moveFocusOnNextTick) {\n      this._moveFocusOnNextTick = false;\n      this.focusActiveCell();\n    }\n  }\n\n  ngOnDestroy() {\n    this._intlChanges.unsubscribe();\n    this.stateChanges.complete();\n  }\n\n  ngOnChanges(changes: SimpleChanges) {\n    // Ignore date changes that are at a different time on the same day. This fixes issues where\n    // the calendar re-renders when there is no meaningful change to [minDate] or [maxDate]\n    // (#24435).\n    const minDateChange: SimpleChange | undefined =\n      changes['minDate'] &&\n      !this._dateAdapter.sameDate(changes['minDate'].previousValue, changes['minDate'].currentValue)\n        ? changes['minDate']\n        : undefined;\n    const maxDateChange: SimpleChange | undefined =\n      changes['maxDate'] &&\n      !this._dateAdapter.sameDate(changes['maxDate'].previousValue, changes['maxDate'].currentValue)\n        ? changes['maxDate']\n        : undefined;\n\n    const changeRequiringRerender = minDateChange || maxDateChange || changes['dateFilter'];\n\n    if (changeRequiringRerender && !changeRequiringRerender.firstChange) {\n      const view = this._getCurrentViewComponent();\n\n      if (view) {\n        // Schedule focus to be moved to the active date since re-rendering can blur the active\n        // cell (see #29265), however don't do so if focus is outside of the calendar, because it\n        // can steal away the user's attention (see #30635).\n        if (this._elementRef.nativeElement.contains(_getFocusedElementPierceShadowDom())) {\n          this._moveFocusOnNextTick = true;\n        }\n\n        // We need to `detectChanges` manually here, because the `minDate`, `maxDate` etc. are\n        // passed down to the view via data bindings which won't be up-to-date when we call `_init`.\n        this._changeDetectorRef.detectChanges();\n        view._init();\n      }\n    }\n\n    this.stateChanges.next();\n  }\n\n  /** Focuses the active date. */\n  focusActiveCell() {\n    this._getCurrentViewComponent()._focusActiveCell(false);\n  }\n\n  /** Updates today's date after an update of the active date */\n  updateTodaysDate() {\n    this._getCurrentViewComponent()._init();\n  }\n\n  /** Handles date selection in the month view. */\n  _dateSelected(event: MatCalendarUserEvent<D | null>): void {\n    const date = event.value;\n\n    if (\n      this.selected instanceof DateRange ||\n      (date && !this._dateAdapter.sameDate(date, this.selected))\n    ) {\n      this.selectedChange.emit(date);\n    }\n\n    this._userSelection.emit(event);\n  }\n\n  /** Handles year selection in the multiyear view. */\n  _yearSelectedInMultiYearView(normalizedYear: D) {\n    this.yearSelected.emit(normalizedYear);\n  }\n\n  /** Handles month selection in the year view. */\n  _monthSelectedInYearView(normalizedMonth: D) {\n    this.monthSelected.emit(normalizedMonth);\n  }\n\n  /** Handles year/month selection in the multi-year/year views. */\n  _goToDateInView(date: D, view: 'month' | 'year' | 'multi-year'): void {\n    this.activeDate = date;\n    this.currentView = view;\n  }\n\n  /** Called when the user starts dragging to change a date range. */\n  _dragStarted(event: MatCalendarUserEvent<D>) {\n    this._activeDrag = event;\n  }\n\n  /**\n   * Called when a drag completes. It may end in cancelation or in the selection\n   * of a new range.\n   */\n  _dragEnded(event: MatCalendarUserEvent<DateRange<D> | null>) {\n    if (!this._activeDrag) return;\n\n    if (event.value) {\n      this._userDragDrop.emit(event as MatCalendarUserEvent<DateRange<D>>);\n    }\n\n    this._activeDrag = null;\n  }\n\n  /** Returns the component instance that corresponds to the current calendar view. */\n  private _getCurrentViewComponent(): MatMonthView<D> | MatYearView<D> | MatMultiYearView<D> {\n    // The return type is explicitly written as a union to ensure that the Closure compiler does\n    // not optimize calls to _init(). Without the explicit return type, TypeScript narrows it to\n    // only the first component type. See https://github.com/angular/components/issues/22996.\n    return this.monthView || this.yearView || this.multiYearView;\n  }\n}\n", "<div class=\"mat-calendar-header\">\n  <div class=\"mat-calendar-controls\">\n    <!-- [Firefox Issue: https://bugzilla.mozilla.org/show_bug.cgi?id=1880533]\n      Relocated label next to related button and made visually hidden via cdk-visually-hidden\n      to enable label to appear in a11y tree for <PERSON> when using Firefox -->\n    <span [id]=\"_periodButtonLabelId\" class=\"cdk-visually-hidden\" aria-live=\"polite\">{{periodButtonDescription}}</span>\n    <button mat-button type=\"button\" class=\"mat-calendar-period-button\"\n            (click)=\"currentPeriodClicked()\" [attr.aria-label]=\"periodButtonLabel\"\n            [attr.aria-describedby]=\"_periodButtonLabelId\">\n      <span aria-hidden=\"true\">{{periodButtonText}}</span>\n      <svg class=\"mat-calendar-arrow\" [class.mat-calendar-invert]=\"calendar.currentView !== 'month'\"\n           viewBox=\"0 0 10 5\" focusable=\"false\" aria-hidden=\"true\">\n           <polygon points=\"0,0 5,5 10,0\"/>\n      </svg>\n    </button>\n\n    <div class=\"mat-calendar-spacer\"></div>\n\n    <ng-content></ng-content>\n\n    <button mat-icon-button type=\"button\" class=\"mat-calendar-previous-button\"\n            [disabled]=\"!previousEnabled()\" (click)=\"previousClicked()\"\n            [attr.aria-label]=\"prevButtonLabel\">\n      <svg viewBox=\"0 0 24 24\" focusable=\"false\" aria-hidden=\"true\">\n        <path d=\"M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z\"/>\n       </svg>\n    </button>\n\n    <button mat-icon-button type=\"button\" class=\"mat-calendar-next-button\"\n            [disabled]=\"!nextEnabled()\" (click)=\"nextClicked()\"\n            [attr.aria-label]=\"nextButtonLabel\">\n      <svg viewBox=\"0 0 24 24\" focusable=\"false\" aria-hidden=\"true\">\n        <path d=\"M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z\"/>\n      </svg>\n    </button>\n  </div>\n</div>\n", "<ng-template [cdkPortalOutlet]=\"_calendarHeaderPortal\"></ng-template>\n\n<div class=\"mat-calendar-content\" cdkMonitorSubtreeFocus tabindex=\"-1\">\n  @switch (currentView) {\n    @case ('month') {\n        <mat-month-view\n            [(activeDate)]=\"activeDate\"\n            [selected]=\"selected\"\n            [dateFilter]=\"dateFilter\"\n            [maxDate]=\"maxDate\"\n            [minDate]=\"minDate\"\n            [dateClass]=\"dateClass\"\n            [comparisonStart]=\"comparisonStart\"\n            [comparisonEnd]=\"comparisonEnd\"\n            [startDateAccessibleName]=\"startDateAccessibleName\"\n            [endDateAccessibleName]=\"endDateAccessibleName\"\n            (_userSelection)=\"_dateSelected($event)\"\n            (dragStarted)=\"_dragStarted($event)\"\n            (dragEnded)=\"_dragEnded($event)\"\n            [activeDrag]=\"_activeDrag\"></mat-month-view>\n    }\n\n    @case ('year') {\n        <mat-year-view\n            [(activeDate)]=\"activeDate\"\n            [selected]=\"selected\"\n            [dateFilter]=\"dateFilter\"\n            [maxDate]=\"maxDate\"\n            [minDate]=\"minDate\"\n            [dateClass]=\"dateClass\"\n            (monthSelected)=\"_monthSelectedInYearView($event)\"\n            (selectedChange)=\"_goToDateInView($event, 'month')\"></mat-year-view>\n    }\n\n    @case ('multi-year') {\n        <mat-multi-year-view\n            [(activeDate)]=\"activeDate\"\n            [selected]=\"selected\"\n            [dateFilter]=\"dateFilter\"\n            [maxDate]=\"maxDate\"\n            [minDate]=\"minDate\"\n            [dateClass]=\"dateClass\"\n            (yearSelected)=\"_yearSelectedInMultiYearView($event)\"\n            (selectedChange)=\"_goToDateInView($event, 'year')\"></mat-multi-year-view>\n    }\n  }\n</div>\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {_IdGenerator, CdkTrapFocus} from '@angular/cdk/a11y';\nimport {Directionality} from '@angular/cdk/bidi';\nimport {coerceStringArray} from '@angular/cdk/coercion';\nimport {\n  DOWN_ARROW,\n  ESCAPE,\n  hasModifierKey,\n  LEFT_ARROW,\n  ModifierKey,\n  PAGE_DOWN,\n  PAGE_UP,\n  RIGHT_ARROW,\n  UP_ARROW,\n} from '@angular/cdk/keycodes';\nimport {\n  FlexibleConnectedPositionStrategy,\n  Overlay,\n  OverlayConfig,\n  OverlayRef,\n  ScrollStrategy,\n} from '@angular/cdk/overlay';\nimport {_getFocusedElementPierceShadowDom} from '@angular/cdk/platform';\nimport {CdkPortalOutlet, ComponentPortal, ComponentType, TemplatePortal} from '@angular/cdk/portal';\nimport {DOCUMENT} from '@angular/common';\nimport {\n  afterNextRender,\n  AfterViewInit,\n  ANIMATION_MODULE_TYPE,\n  booleanAttribute,\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  ComponentRef,\n  Directive,\n  ElementRef,\n  EventEmitter,\n  inject,\n  InjectionToken,\n  Injector,\n  Input,\n  NgZone,\n  OnChanges,\n  OnDestroy,\n  Output,\n  Renderer2,\n  SimpleChanges,\n  ViewChild,\n  ViewContainerRef,\n  ViewEncapsulation,\n} from '@angular/core';\nimport {MatButton} from '../button';\nimport {DateAdapter, ThemePalette} from '../core';\nimport {merge, Observable, Subject, Subscription} from 'rxjs';\nimport {filter, take} from 'rxjs/operators';\nimport {MatCalendar, MatCalendarView} from './calendar';\nimport {MatCalendarCellClassFunction, MatCalendarUserEvent} from './calendar-body';\nimport {\n  MAT_DATE_RANGE_SELECTION_STRATEGY,\n  MatDateRangeSelectionStrategy,\n} from './date-range-selection-strategy';\nimport {\n  DateRange,\n  ExtractDateTypeFromSelection,\n  MatDateSelectionModel,\n} from './date-selection-model';\nimport {createMissingDateImplError} from './datepicker-errors';\nimport {DateFilterFn} from './datepicker-input-base';\nimport {MatDatepickerIntl} from './datepicker-intl';\nimport {_CdkPrivateStyleLoader, _VisuallyHiddenLoader} from '@angular/cdk/private';\n\n/** Injection token that determines the scroll handling while the calendar is open. */\nexport const MAT_DATEPICKER_SCROLL_STRATEGY = new InjectionToken<() => ScrollStrategy>(\n  'mat-datepicker-scroll-strategy',\n  {\n    providedIn: 'root',\n    factory: () => {\n      const overlay = inject(Overlay);\n      return () => overlay.scrollStrategies.reposition();\n    },\n  },\n);\n\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nexport function MAT_DATEPICKER_SCROLL_STRATEGY_FACTORY(overlay: Overlay): () => ScrollStrategy {\n  return () => overlay.scrollStrategies.reposition();\n}\n\n/** Possible positions for the datepicker dropdown along the X axis. */\nexport type DatepickerDropdownPositionX = 'start' | 'end';\n\n/** Possible positions for the datepicker dropdown along the Y axis. */\nexport type DatepickerDropdownPositionY = 'above' | 'below';\n\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nexport const MAT_DATEPICKER_SCROLL_STRATEGY_FACTORY_PROVIDER = {\n  provide: MAT_DATEPICKER_SCROLL_STRATEGY,\n  deps: [Overlay],\n  useFactory: MAT_DATEPICKER_SCROLL_STRATEGY_FACTORY,\n};\n\n/**\n * Component used as the content for the datepicker overlay. We use this instead of using\n * MatCalendar directly as the content so we can control the initial focus. This also gives us a\n * place to put additional features of the overlay that are not part of the calendar itself in the\n * future. (e.g. confirmation buttons).\n * @docs-private\n */\n@Component({\n  selector: 'mat-datepicker-content',\n  templateUrl: 'datepicker-content.html',\n  styleUrl: 'datepicker-content.css',\n  host: {\n    'class': 'mat-datepicker-content',\n    '[class]': 'color ? \"mat-\" + color : \"\"',\n    '[class.mat-datepicker-content-touch]': 'datepicker.touchUi',\n    '[class.mat-datepicker-content-animations-enabled]': '!_animationsDisabled',\n  },\n  exportAs: 'matDatepickerContent',\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  imports: [CdkTrapFocus, MatCalendar, CdkPortalOutlet, MatButton],\n})\nexport class MatDatepickerContent<S, D = ExtractDateTypeFromSelection<S>>\n  implements AfterViewInit, OnDestroy\n{\n  protected _elementRef = inject<ElementRef<HTMLElement>>(ElementRef);\n  protected _animationsDisabled =\n    inject(ANIMATION_MODULE_TYPE, {optional: true}) === 'NoopAnimations';\n  private _changeDetectorRef = inject(ChangeDetectorRef);\n  private _globalModel = inject<MatDateSelectionModel<S, D>>(MatDateSelectionModel);\n  private _dateAdapter = inject<DateAdapter<D>>(DateAdapter)!;\n  private _ngZone = inject(NgZone);\n  private _rangeSelectionStrategy = inject<MatDateRangeSelectionStrategy<D>>(\n    MAT_DATE_RANGE_SELECTION_STRATEGY,\n    {optional: true},\n  );\n\n  private _stateChanges: Subscription | undefined;\n  private _model: MatDateSelectionModel<S, D>;\n  private _eventCleanups: (() => void)[] | undefined;\n  private _animationFallback: ReturnType<typeof setTimeout> | undefined;\n\n  /** Reference to the internal calendar component. */\n  @ViewChild(MatCalendar) _calendar: MatCalendar<D>;\n\n  /**\n   * Theme color of the internal calendar. This API is supported in M2 themes\n   * only, it has no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/datepicker/styling.\n   *\n   * For information on applying color variants in M3, see\n   * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n   */\n  @Input() color: ThemePalette;\n\n  /** Reference to the datepicker that created the overlay. */\n  datepicker: MatDatepickerBase<any, S, D>;\n\n  /** Start of the comparison range. */\n  comparisonStart: D | null;\n\n  /** End of the comparison range. */\n  comparisonEnd: D | null;\n\n  /** ARIA Accessible name of the `<input matStartDate/>` */\n  startDateAccessibleName: string | null;\n\n  /** ARIA Accessible name of the `<input matEndDate/>` */\n  endDateAccessibleName: string | null;\n\n  /** Whether the datepicker is above or below the input. */\n  _isAbove: boolean;\n\n  /** Emits when an animation has finished. */\n  readonly _animationDone = new Subject<void>();\n\n  /** Whether there is an in-progress animation. */\n  _isAnimating = false;\n\n  /** Text for the close button. */\n  _closeButtonText: string;\n\n  /** Whether the close button currently has focus. */\n  _closeButtonFocused: boolean;\n\n  /** Portal with projected action buttons. */\n  _actionsPortal: TemplatePortal | null = null;\n\n  /** Id of the label for the `role=\"dialog\"` element. */\n  _dialogLabelId: string | null;\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    inject(_CdkPrivateStyleLoader).load(_VisuallyHiddenLoader);\n    this._closeButtonText = inject(MatDatepickerIntl).closeCalendarLabel;\n\n    if (!this._animationsDisabled) {\n      const element = this._elementRef.nativeElement;\n      const renderer = inject(Renderer2);\n\n      this._eventCleanups = this._ngZone.runOutsideAngular(() => [\n        renderer.listen(element, 'animationstart', this._handleAnimationEvent),\n        renderer.listen(element, 'animationend', this._handleAnimationEvent),\n        renderer.listen(element, 'animationcancel', this._handleAnimationEvent),\n      ]);\n    }\n  }\n\n  ngAfterViewInit() {\n    this._stateChanges = this.datepicker.stateChanges.subscribe(() => {\n      this._changeDetectorRef.markForCheck();\n    });\n    this._calendar.focusActiveCell();\n  }\n\n  ngOnDestroy() {\n    clearTimeout(this._animationFallback);\n    this._eventCleanups?.forEach(cleanup => cleanup());\n    this._stateChanges?.unsubscribe();\n    this._animationDone.complete();\n  }\n\n  _handleUserSelection(event: MatCalendarUserEvent<D | null>) {\n    const selection = this._model.selection;\n    const value = event.value;\n    const isRange = selection instanceof DateRange;\n\n    // If we're selecting a range and we have a selection strategy, always pass the value through\n    // there. Otherwise don't assign null values to the model, unless we're selecting a range.\n    // A null value when picking a range means that the user cancelled the selection (e.g. by\n    // pressing escape), whereas when selecting a single value it means that the value didn't\n    // change. This isn't very intuitive, but it's here for backwards-compatibility.\n    if (isRange && this._rangeSelectionStrategy) {\n      const newSelection = this._rangeSelectionStrategy.selectionFinished(\n        value,\n        selection as unknown as DateRange<D>,\n        event.event,\n      );\n      this._model.updateSelection(newSelection as unknown as S, this);\n    } else if (\n      value &&\n      (isRange || !this._dateAdapter.sameDate(value, selection as unknown as D))\n    ) {\n      this._model.add(value);\n    }\n\n    // Delegate closing the overlay to the actions.\n    if ((!this._model || this._model.isComplete()) && !this._actionsPortal) {\n      this.datepicker.close();\n    }\n  }\n\n  _handleUserDragDrop(event: MatCalendarUserEvent<DateRange<D>>) {\n    this._model.updateSelection(event.value as unknown as S, this);\n  }\n\n  _startExitAnimation() {\n    this._elementRef.nativeElement.classList.add('mat-datepicker-content-exit');\n\n    if (this._animationsDisabled) {\n      this._animationDone.next();\n    } else {\n      // Some internal apps disable animations in tests using `* {animation: none !important}`.\n      // If that happens, the animation events won't fire and we'll never clean up the overlay.\n      // Add a fallback that will fire if the animation doesn't run in a certain amount of time.\n      clearTimeout(this._animationFallback);\n      this._animationFallback = setTimeout(() => {\n        if (!this._isAnimating) {\n          this._animationDone.next();\n        }\n      }, 200);\n    }\n  }\n\n  private _handleAnimationEvent = (event: AnimationEvent) => {\n    const element = this._elementRef.nativeElement;\n\n    if (event.target !== element || !event.animationName.startsWith('_mat-datepicker-content')) {\n      return;\n    }\n\n    clearTimeout(this._animationFallback);\n    this._isAnimating = event.type === 'animationstart';\n    element.classList.toggle('mat-datepicker-content-animating', this._isAnimating);\n\n    if (!this._isAnimating) {\n      this._animationDone.next();\n    }\n  };\n\n  _getSelected() {\n    return this._model.selection as unknown as D | DateRange<D> | null;\n  }\n\n  /** Applies the current pending selection to the global model. */\n  _applyPendingSelection() {\n    if (this._model !== this._globalModel) {\n      this._globalModel.updateSelection(this._model.selection, this);\n    }\n  }\n\n  /**\n   * Assigns a new portal containing the datepicker actions.\n   * @param portal Portal with the actions to be assigned.\n   * @param forceRerender Whether a re-render of the portal should be triggered. This isn't\n   * necessary if the portal is assigned during initialization, but it may be required if it's\n   * added at a later point.\n   */\n  _assignActions(portal: TemplatePortal<any> | null, forceRerender: boolean) {\n    // If we have actions, clone the model so that we have the ability to cancel the selection,\n    // otherwise update the global model directly. Note that we want to assign this as soon as\n    // possible, but `_actionsPortal` isn't available in the constructor so we do it in `ngOnInit`.\n    this._model = portal ? this._globalModel.clone() : this._globalModel;\n    this._actionsPortal = portal;\n\n    if (forceRerender) {\n      this._changeDetectorRef.detectChanges();\n    }\n  }\n}\n\n/** Form control that can be associated with a datepicker. */\nexport interface MatDatepickerControl<D> {\n  getStartValue(): D | null;\n  getThemePalette(): ThemePalette;\n  min: D | null;\n  max: D | null;\n  disabled: boolean;\n  dateFilter: DateFilterFn<D>;\n  getConnectedOverlayOrigin(): ElementRef;\n  getOverlayLabelId(): string | null;\n  stateChanges: Observable<void>;\n}\n\n/** A datepicker that can be attached to a {@link MatDatepickerControl}. */\nexport interface MatDatepickerPanel<\n  C extends MatDatepickerControl<D>,\n  S,\n  D = ExtractDateTypeFromSelection<S>,\n> {\n  /** Stream that emits whenever the date picker is closed. */\n  closedStream: EventEmitter<void>;\n  /**\n   * Color palette to use on the datepicker's calendar. This API is supported in M2 themes only, it\n   * has no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/datepicker/styling.\n   *\n   * For information on applying color variants in M3, see\n   * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n   */\n  color: ThemePalette;\n  /** The input element the datepicker is associated with. */\n  datepickerInput: C;\n  /** Whether the datepicker pop-up should be disabled. */\n  disabled: boolean;\n  /** The id for the datepicker's calendar. */\n  id: string;\n  /** Whether the datepicker is open. */\n  opened: boolean;\n  /** Stream that emits whenever the date picker is opened. */\n  openedStream: EventEmitter<void>;\n  /** Emits when the datepicker's state changes. */\n  stateChanges: Subject<void>;\n  /** Opens the datepicker. */\n  open(): void;\n  /** Register an input with the datepicker. */\n  registerInput(input: C): MatDateSelectionModel<S, D>;\n}\n\n/** Base class for a datepicker. */\n@Directive()\nexport abstract class MatDatepickerBase<\n    C extends MatDatepickerControl<D>,\n    S,\n    D = ExtractDateTypeFromSelection<S>,\n  >\n  implements MatDatepickerPanel<C, S, D>, OnDestroy, OnChanges\n{\n  private _overlay = inject(Overlay);\n  private _viewContainerRef = inject(ViewContainerRef);\n  private _dateAdapter = inject<DateAdapter<D>>(DateAdapter, {optional: true})!;\n  private _dir = inject(Directionality, {optional: true});\n  private _model = inject<MatDateSelectionModel<S, D>>(MatDateSelectionModel);\n\n  private _scrollStrategy = inject(MAT_DATEPICKER_SCROLL_STRATEGY);\n  private _inputStateChanges = Subscription.EMPTY;\n  private _document = inject(DOCUMENT);\n\n  /** An input indicating the type of the custom header component for the calendar, if set. */\n  @Input() calendarHeaderComponent: ComponentType<any>;\n\n  /** The date to open the calendar to initially. */\n  @Input()\n  get startAt(): D | null {\n    // If an explicit startAt is set we start there, otherwise we start at whatever the currently\n    // selected value is.\n    return this._startAt || (this.datepickerInput ? this.datepickerInput.getStartValue() : null);\n  }\n  set startAt(value: D | null) {\n    this._startAt = this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(value));\n  }\n  private _startAt: D | null;\n\n  /** The view that the calendar should start in. */\n  @Input() startView: 'month' | 'year' | 'multi-year' = 'month';\n\n  /**\n   * Theme color of the datepicker's calendar. This API is supported in M2 themes only, it\n   * has no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/datepicker/styling.\n   *\n   * For information on applying color variants in M3, see\n   * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n   */\n  @Input()\n  get color(): ThemePalette {\n    return (\n      this._color || (this.datepickerInput ? this.datepickerInput.getThemePalette() : undefined)\n    );\n  }\n  set color(value: ThemePalette) {\n    this._color = value;\n  }\n  _color: ThemePalette;\n\n  /**\n   * Whether the calendar UI is in touch mode. In touch mode the calendar opens in a dialog rather\n   * than a dropdown and elements have more padding to allow for bigger touch targets.\n   */\n  @Input({transform: booleanAttribute})\n  touchUi: boolean = false;\n\n  /** Whether the datepicker pop-up should be disabled. */\n  @Input({transform: booleanAttribute})\n  get disabled(): boolean {\n    return this._disabled === undefined && this.datepickerInput\n      ? this.datepickerInput.disabled\n      : !!this._disabled;\n  }\n  set disabled(value: boolean) {\n    if (value !== this._disabled) {\n      this._disabled = value;\n      this.stateChanges.next(undefined);\n    }\n  }\n  private _disabled: boolean;\n\n  /** Preferred position of the datepicker in the X axis. */\n  @Input()\n  xPosition: DatepickerDropdownPositionX = 'start';\n\n  /** Preferred position of the datepicker in the Y axis. */\n  @Input()\n  yPosition: DatepickerDropdownPositionY = 'below';\n\n  /**\n   * Whether to restore focus to the previously-focused element when the calendar is closed.\n   * Note that automatic focus restoration is an accessibility feature and it is recommended that\n   * you provide your own equivalent, if you decide to turn it off.\n   */\n  @Input({transform: booleanAttribute})\n  restoreFocus: boolean = true;\n\n  /**\n   * Emits selected year in multiyear view.\n   * This doesn't imply a change on the selected date.\n   */\n  @Output() readonly yearSelected: EventEmitter<D> = new EventEmitter<D>();\n\n  /**\n   * Emits selected month in year view.\n   * This doesn't imply a change on the selected date.\n   */\n  @Output() readonly monthSelected: EventEmitter<D> = new EventEmitter<D>();\n\n  /**\n   * Emits when the current view changes.\n   */\n  @Output() readonly viewChanged: EventEmitter<MatCalendarView> = new EventEmitter<MatCalendarView>(\n    true,\n  );\n\n  /** Function that can be used to add custom CSS classes to dates. */\n  @Input() dateClass: MatCalendarCellClassFunction<D>;\n\n  /** Emits when the datepicker has been opened. */\n  @Output('opened') readonly openedStream = new EventEmitter<void>();\n\n  /** Emits when the datepicker has been closed. */\n  @Output('closed') readonly closedStream = new EventEmitter<void>();\n\n  /** Classes to be passed to the date picker panel. */\n  @Input()\n  get panelClass(): string | string[] {\n    return this._panelClass;\n  }\n  set panelClass(value: string | string[]) {\n    this._panelClass = coerceStringArray(value);\n  }\n  private _panelClass: string[];\n\n  /** Whether the calendar is open. */\n  @Input({transform: booleanAttribute})\n  get opened(): boolean {\n    return this._opened;\n  }\n  set opened(value: boolean) {\n    if (value) {\n      this.open();\n    } else {\n      this.close();\n    }\n  }\n  private _opened = false;\n\n  /** The id for the datepicker calendar. */\n  id: string = inject(_IdGenerator).getId('mat-datepicker-');\n\n  /** The minimum selectable date. */\n  _getMinDate(): D | null {\n    return this.datepickerInput && this.datepickerInput.min;\n  }\n\n  /** The maximum selectable date. */\n  _getMaxDate(): D | null {\n    return this.datepickerInput && this.datepickerInput.max;\n  }\n\n  _getDateFilter(): DateFilterFn<D> {\n    return this.datepickerInput && this.datepickerInput.dateFilter;\n  }\n\n  /** A reference to the overlay into which we've rendered the calendar. */\n  private _overlayRef: OverlayRef | null;\n\n  /** Reference to the component instance rendered in the overlay. */\n  private _componentRef: ComponentRef<MatDatepickerContent<S, D>> | null;\n\n  /** The element that was focused before the datepicker was opened. */\n  private _focusedElementBeforeOpen: HTMLElement | null = null;\n\n  /** Unique class that will be added to the backdrop so that the test harnesses can look it up. */\n  private _backdropHarnessClass = `${this.id}-backdrop`;\n\n  /** Currently-registered actions portal. */\n  private _actionsPortal: TemplatePortal | null;\n\n  /** The input element this datepicker is associated with. */\n  datepickerInput: C;\n\n  /** Emits when the datepicker's state changes. */\n  readonly stateChanges = new Subject<void>();\n\n  private _injector = inject(Injector);\n\n  private readonly _changeDetectorRef = inject(ChangeDetectorRef);\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    if (!this._dateAdapter && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw createMissingDateImplError('DateAdapter');\n    }\n\n    this._model.selectionChanged.subscribe(() => {\n      this._changeDetectorRef.markForCheck();\n    });\n  }\n\n  ngOnChanges(changes: SimpleChanges) {\n    const positionChange = changes['xPosition'] || changes['yPosition'];\n\n    if (positionChange && !positionChange.firstChange && this._overlayRef) {\n      const positionStrategy = this._overlayRef.getConfig().positionStrategy;\n\n      if (positionStrategy instanceof FlexibleConnectedPositionStrategy) {\n        this._setConnectedPositions(positionStrategy);\n\n        if (this.opened) {\n          this._overlayRef.updatePosition();\n        }\n      }\n    }\n\n    this.stateChanges.next(undefined);\n  }\n\n  ngOnDestroy() {\n    this._destroyOverlay();\n    this.close();\n    this._inputStateChanges.unsubscribe();\n    this.stateChanges.complete();\n  }\n\n  /** Selects the given date */\n  select(date: D): void {\n    this._model.add(date);\n  }\n\n  /** Emits the selected year in multiyear view */\n  _selectYear(normalizedYear: D): void {\n    this.yearSelected.emit(normalizedYear);\n  }\n\n  /** Emits selected month in year view */\n  _selectMonth(normalizedMonth: D): void {\n    this.monthSelected.emit(normalizedMonth);\n  }\n\n  /** Emits changed view */\n  _viewChanged(view: MatCalendarView): void {\n    this.viewChanged.emit(view);\n  }\n\n  /**\n   * Register an input with this datepicker.\n   * @param input The datepicker input to register with this datepicker.\n   * @returns Selection model that the input should hook itself up to.\n   */\n  registerInput(input: C): MatDateSelectionModel<S, D> {\n    if (this.datepickerInput && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error('A MatDatepicker can only be associated with a single input.');\n    }\n    this._inputStateChanges.unsubscribe();\n    this.datepickerInput = input;\n    this._inputStateChanges = input.stateChanges.subscribe(() => this.stateChanges.next(undefined));\n    return this._model;\n  }\n\n  /**\n   * Registers a portal containing action buttons with the datepicker.\n   * @param portal Portal to be registered.\n   */\n  registerActions(portal: TemplatePortal): void {\n    if (this._actionsPortal && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error('A MatDatepicker can only be associated with a single actions row.');\n    }\n    this._actionsPortal = portal;\n    this._componentRef?.instance._assignActions(portal, true);\n  }\n\n  /**\n   * Removes a portal containing action buttons from the datepicker.\n   * @param portal Portal to be removed.\n   */\n  removeActions(portal: TemplatePortal): void {\n    if (portal === this._actionsPortal) {\n      this._actionsPortal = null;\n      this._componentRef?.instance._assignActions(null, true);\n    }\n  }\n\n  /** Open the calendar. */\n  open(): void {\n    // Skip reopening if there's an in-progress animation to avoid overlapping\n    // sequences which can cause \"changed after checked\" errors. See #25837.\n    if (this._opened || this.disabled || this._componentRef?.instance._isAnimating) {\n      return;\n    }\n\n    if (!this.datepickerInput && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error('Attempted to open an MatDatepicker with no associated input.');\n    }\n\n    this._focusedElementBeforeOpen = _getFocusedElementPierceShadowDom();\n    this._openOverlay();\n    this._opened = true;\n    this.openedStream.emit();\n  }\n\n  /** Close the calendar. */\n  close(): void {\n    // Skip reopening if there's an in-progress animation to avoid overlapping\n    // sequences which can cause \"changed after checked\" errors. See #25837.\n    if (!this._opened || this._componentRef?.instance._isAnimating) {\n      return;\n    }\n\n    const canRestoreFocus =\n      this.restoreFocus &&\n      this._focusedElementBeforeOpen &&\n      typeof this._focusedElementBeforeOpen.focus === 'function';\n\n    const completeClose = () => {\n      // The `_opened` could've been reset already if\n      // we got two events in quick succession.\n      if (this._opened) {\n        this._opened = false;\n        this.closedStream.emit();\n      }\n    };\n\n    if (this._componentRef) {\n      const {instance, location} = this._componentRef;\n      instance._animationDone.pipe(take(1)).subscribe(() => {\n        const activeElement = this._document.activeElement;\n\n        // Since we restore focus after the exit animation, we have to check that\n        // the user didn't move focus themselves inside the `close` handler.\n        if (\n          canRestoreFocus &&\n          (!activeElement ||\n            activeElement === this._document.activeElement ||\n            location.nativeElement.contains(activeElement))\n        ) {\n          this._focusedElementBeforeOpen!.focus();\n        }\n\n        this._focusedElementBeforeOpen = null;\n        this._destroyOverlay();\n      });\n      instance._startExitAnimation();\n    }\n\n    if (canRestoreFocus) {\n      // Because IE moves focus asynchronously, we can't count on it being restored before we've\n      // marked the datepicker as closed. If the event fires out of sequence and the element that\n      // we're refocusing opens the datepicker on focus, the user could be stuck with not being\n      // able to close the calendar at all. We work around it by making the logic, that marks\n      // the datepicker as closed, async as well.\n      setTimeout(completeClose);\n    } else {\n      completeClose();\n    }\n  }\n\n  /** Applies the current pending selection on the overlay to the model. */\n  _applyPendingSelection() {\n    this._componentRef?.instance?._applyPendingSelection();\n  }\n\n  /** Forwards relevant values from the datepicker to the datepicker content inside the overlay. */\n  protected _forwardContentValues(instance: MatDatepickerContent<S, D>) {\n    instance.datepicker = this;\n    instance.color = this.color;\n    instance._dialogLabelId = this.datepickerInput.getOverlayLabelId();\n    instance._assignActions(this._actionsPortal, false);\n  }\n\n  /** Opens the overlay with the calendar. */\n  private _openOverlay(): void {\n    this._destroyOverlay();\n\n    const isDialog = this.touchUi;\n    const portal = new ComponentPortal<MatDatepickerContent<S, D>>(\n      MatDatepickerContent,\n      this._viewContainerRef,\n    );\n    const overlayRef = (this._overlayRef = this._overlay.create(\n      new OverlayConfig({\n        positionStrategy: isDialog ? this._getDialogStrategy() : this._getDropdownStrategy(),\n        hasBackdrop: true,\n        backdropClass: [\n          isDialog ? 'cdk-overlay-dark-backdrop' : 'mat-overlay-transparent-backdrop',\n          this._backdropHarnessClass,\n        ],\n        direction: this._dir || 'ltr',\n        scrollStrategy: isDialog ? this._overlay.scrollStrategies.block() : this._scrollStrategy(),\n        panelClass: `mat-datepicker-${isDialog ? 'dialog' : 'popup'}`,\n      }),\n    ));\n\n    this._getCloseStream(overlayRef).subscribe(event => {\n      if (event) {\n        event.preventDefault();\n      }\n      this.close();\n    });\n\n    // The `preventDefault` call happens inside the calendar as well, however focus moves into\n    // it inside a timeout which can give browsers a chance to fire off a keyboard event in-between\n    // that can scroll the page (see #24969). Always block default actions of arrow keys for the\n    // entire overlay so the page doesn't get scrolled by accident.\n    overlayRef.keydownEvents().subscribe(event => {\n      const keyCode = event.keyCode;\n\n      if (\n        keyCode === UP_ARROW ||\n        keyCode === DOWN_ARROW ||\n        keyCode === LEFT_ARROW ||\n        keyCode === RIGHT_ARROW ||\n        keyCode === PAGE_UP ||\n        keyCode === PAGE_DOWN\n      ) {\n        event.preventDefault();\n      }\n    });\n\n    this._componentRef = overlayRef.attach(portal);\n    this._forwardContentValues(this._componentRef.instance);\n\n    // Update the position once the calendar has rendered. Only relevant in dropdown mode.\n    if (!isDialog) {\n      afterNextRender(\n        () => {\n          overlayRef.updatePosition();\n        },\n        {injector: this._injector},\n      );\n    }\n  }\n\n  /** Destroys the current overlay. */\n  private _destroyOverlay() {\n    if (this._overlayRef) {\n      this._overlayRef.dispose();\n      this._overlayRef = this._componentRef = null;\n    }\n  }\n\n  /** Gets a position strategy that will open the calendar as a dropdown. */\n  private _getDialogStrategy() {\n    return this._overlay.position().global().centerHorizontally().centerVertically();\n  }\n\n  /** Gets a position strategy that will open the calendar as a dropdown. */\n  private _getDropdownStrategy() {\n    const strategy = this._overlay\n      .position()\n      .flexibleConnectedTo(this.datepickerInput.getConnectedOverlayOrigin())\n      .withTransformOriginOn('.mat-datepicker-content')\n      .withFlexibleDimensions(false)\n      .withViewportMargin(8)\n      .withLockedPosition();\n\n    return this._setConnectedPositions(strategy);\n  }\n\n  /** Sets the positions of the datepicker in dropdown mode based on the current configuration. */\n  private _setConnectedPositions(strategy: FlexibleConnectedPositionStrategy) {\n    const primaryX = this.xPosition === 'end' ? 'end' : 'start';\n    const secondaryX = primaryX === 'start' ? 'end' : 'start';\n    const primaryY = this.yPosition === 'above' ? 'bottom' : 'top';\n    const secondaryY = primaryY === 'top' ? 'bottom' : 'top';\n\n    return strategy.withPositions([\n      {\n        originX: primaryX,\n        originY: secondaryY,\n        overlayX: primaryX,\n        overlayY: primaryY,\n      },\n      {\n        originX: primaryX,\n        originY: primaryY,\n        overlayX: primaryX,\n        overlayY: secondaryY,\n      },\n      {\n        originX: secondaryX,\n        originY: secondaryY,\n        overlayX: secondaryX,\n        overlayY: primaryY,\n      },\n      {\n        originX: secondaryX,\n        originY: primaryY,\n        overlayX: secondaryX,\n        overlayY: secondaryY,\n      },\n    ]);\n  }\n\n  /** Gets an observable that will emit when the overlay is supposed to be closed. */\n  private _getCloseStream(overlayRef: OverlayRef) {\n    const ctrlShiftMetaModifiers: ModifierKey[] = ['ctrlKey', 'shiftKey', 'metaKey'];\n    return merge(\n      overlayRef.backdropClick(),\n      overlayRef.detachments(),\n      overlayRef.keydownEvents().pipe(\n        filter(event => {\n          // Closing on alt + up is only valid when there's an input associated with the datepicker.\n          return (\n            (event.keyCode === ESCAPE && !hasModifierKey(event)) ||\n            (this.datepickerInput &&\n              hasModifierKey(event, 'altKey') &&\n              event.keyCode === UP_ARROW &&\n              ctrlShiftMetaModifiers.every(\n                (modifier: ModifierKey) => !hasModifierKey(event, modifier),\n              ))\n          );\n        }),\n      ),\n    );\n  }\n}\n", "<div\n  cdkTrapFocus\n  role=\"dialog\"\n  [attr.aria-modal]=\"true\"\n  [attr.aria-labelledby]=\"_dialogLabelId ?? undefined\"\n  class=\"mat-datepicker-content-container\"\n  [class.mat-datepicker-content-container-with-custom-header]=\"datepicker.calendarHeaderComponent\"\n  [class.mat-datepicker-content-container-with-actions]=\"_actionsPortal\">\n  <mat-calendar\n    [id]=\"datepicker.id\"\n    [class]=\"datepicker.panelClass\"\n    [startAt]=\"datepicker.startAt\"\n    [startView]=\"datepicker.startView\"\n    [minDate]=\"datepicker._getMinDate()\"\n    [maxDate]=\"datepicker._getMaxDate()\"\n    [dateFilter]=\"datepicker._getDateFilter()\"\n    [headerComponent]=\"datepicker.calendarHeaderComponent\"\n    [selected]=\"_getSelected()\"\n    [dateClass]=\"datepicker.dateClass\"\n    [comparisonStart]=\"comparisonStart\"\n    [comparisonEnd]=\"comparisonEnd\"\n    [startDateAccessibleName]=\"startDateAccessibleName\"\n    [endDateAccessibleName]=\"endDateAccessibleName\"\n    (yearSelected)=\"datepicker._selectYear($event)\"\n    (monthSelected)=\"datepicker._selectMonth($event)\"\n    (viewChanged)=\"datepicker._viewChanged($event)\"\n    (_userSelection)=\"_handleUserSelection($event)\"\n    (_userDragDrop)=\"_handleUserDragDrop($event)\"></mat-calendar>\n\n  <ng-template [cdkPortalOutlet]=\"_actionsPortal\"></ng-template>\n\n  <!-- Invisible close button for screen reader users. -->\n  <button\n    type=\"button\"\n    mat-raised-button\n    [color]=\"color || 'primary'\"\n    class=\"mat-datepicker-close-button\"\n    [class.cdk-visually-hidden]=\"!_closeButtonFocused\"\n    (focus)=\"_closeButtonFocused = true\"\n    (blur)=\"_closeButtonFocused = false\"\n    (click)=\"datepicker.close()\">{{ _closeButtonText }}</button>\n</div>\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {ChangeDetectionStrategy, Component, ViewEncapsulation} from '@angular/core';\nimport {MatDatepickerBase, MatDatepickerControl} from './datepicker-base';\nimport {MAT_SINGLE_DATE_SELECTION_MODEL_PROVIDER} from './date-selection-model';\n\n// TODO(mmalerba): We use a component instead of a directive here so the user can use implicit\n// template reference variables (e.g. #d vs #d=\"matDatepicker\"). We can change this to a directive\n// if angular adds support for `exportAs: '$implicit'` on directives.\n/** Component responsible for managing the datepicker popup/dialog. */\n@Component({\n  selector: 'mat-datepicker',\n  template: '',\n  exportAs: 'matDatepicker',\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  encapsulation: ViewEncapsulation.None,\n  providers: [\n    MAT_SINGLE_DATE_SELECTION_MODEL_PROVIDER,\n    {provide: MatDatepickerBase, useExisting: MatDatepicker},\n  ],\n})\nexport class MatDatepicker<D> extends MatDatepickerBase<MatDatepickerControl<D>, D | null, D> {}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {DOWN_ARROW, hasModifier<PERSON><PERSON>, Modifier<PERSON>ey} from '@angular/cdk/keycodes';\nimport {\n  Directive,\n  ElementRef,\n  EventEmitter,\n  Input,\n  OnDestroy,\n  Output,\n  AfterViewInit,\n  OnChanges,\n  SimpleChanges,\n  booleanAttribute,\n  inject,\n} from '@angular/core';\nimport {\n  AbstractControl,\n  ControlValueAccessor,\n  ValidationErrors,\n  Validator,\n  ValidatorFn,\n} from '@angular/forms';\nimport {DateAdapter, MAT_DATE_FORMATS, MatDateFormats, ThemePalette} from '../core';\nimport {Subscription, Subject} from 'rxjs';\nimport {createMissingDateImplError} from './datepicker-errors';\nimport {\n  ExtractDateTypeFromSelection,\n  MatDateSelectionModel,\n  DateSelectionModelChange,\n} from './date-selection-model';\n\n/**\n * An event used for datepicker input and change events. We don't always have access to a native\n * input or change event because the event may have been triggered by the user clicking on the\n * calendar popup. For consistency, we always use MatDatepickerInputEvent instead.\n */\nexport class MatDatepickerInputEvent<D, S = unknown> {\n  /** The new value for the target datepicker input. */\n  value: D | null;\n\n  constructor(\n    /** Reference to the datepicker input component that emitted the event. */\n    public target: MatDatepickerInputBase<S, D>,\n    /** Reference to the native input element associated with the datepicker input. */\n    public targetElement: HTMLElement,\n  ) {\n    this.value = this.target.value;\n  }\n}\n\n/**\n * Function that can be used to filter out dates from a calendar.\n * Datepicker can sometimes receive a null value as input for the date argument.\n * This doesn't represent a \"null date\" but rather signifies that no date has been selected yet in the calendar.\n */\nexport type DateFilterFn<D> = (date: D | null) => boolean;\n\n/**\n * Partial representation of `MatFormField` that is used for backwards-compatibility\n * between the legacy and non-legacy variants.\n */\nexport interface _MatFormFieldPartial {\n  getConnectedOverlayOrigin(): ElementRef;\n  getLabelId(): string | null;\n  color: ThemePalette;\n  _elementRef: ElementRef;\n  _shouldLabelFloat(): boolean;\n  _hasFloatingLabel(): boolean;\n  _labelId: string;\n}\n\n/** Base class for datepicker inputs. */\n@Directive()\nexport abstract class MatDatepickerInputBase<S, D = ExtractDateTypeFromSelection<S>>\n  implements ControlValueAccessor, AfterViewInit, OnChanges, OnDestroy, Validator\n{\n  protected _elementRef = inject<ElementRef<HTMLInputElement>>(ElementRef);\n  _dateAdapter = inject<DateAdapter<D>>(DateAdapter, {optional: true})!;\n  private _dateFormats = inject<MatDateFormats>(MAT_DATE_FORMATS, {optional: true})!;\n\n  /** Whether the component has been initialized. */\n  private _isInitialized: boolean;\n\n  /** The value of the input. */\n  @Input()\n  get value(): D | null {\n    return this._model ? this._getValueFromModel(this._model.selection) : this._pendingValue;\n  }\n  set value(value: any) {\n    this._assignValueProgrammatically(value);\n  }\n  protected _model: MatDateSelectionModel<S, D> | undefined;\n\n  /** Whether the datepicker-input is disabled. */\n  @Input({transform: booleanAttribute})\n  get disabled(): boolean {\n    return !!this._disabled || this._parentDisabled();\n  }\n  set disabled(value: boolean) {\n    const newValue = value;\n    const element = this._elementRef.nativeElement;\n\n    if (this._disabled !== newValue) {\n      this._disabled = newValue;\n      this.stateChanges.next(undefined);\n    }\n\n    // We need to null check the `blur` method, because it's undefined during SSR.\n    // In Ivy static bindings are invoked earlier, before the element is attached to the DOM.\n    // This can cause an error to be thrown in some browsers (IE/Edge) which assert that the\n    // element has been inserted.\n    if (newValue && this._isInitialized && element.blur) {\n      // Normally, native input elements automatically blur if they turn disabled. This behavior\n      // is problematic, because it would mean that it triggers another change detection cycle,\n      // which then causes a changed after checked error if the input element was focused before.\n      element.blur();\n    }\n  }\n  private _disabled: boolean;\n\n  /** Emits when a `change` event is fired on this `<input>`. */\n  @Output() readonly dateChange: EventEmitter<MatDatepickerInputEvent<D, S>> = new EventEmitter<\n    MatDatepickerInputEvent<D, S>\n  >();\n\n  /** Emits when an `input` event is fired on this `<input>`. */\n  @Output() readonly dateInput: EventEmitter<MatDatepickerInputEvent<D, S>> = new EventEmitter<\n    MatDatepickerInputEvent<D, S>\n  >();\n\n  /** Emits when the internal state has changed */\n  readonly stateChanges = new Subject<void>();\n\n  _onTouched = () => {};\n  _validatorOnChange = () => {};\n\n  private _cvaOnChange: (value: any) => void = () => {};\n  private _valueChangesSubscription = Subscription.EMPTY;\n  private _localeSubscription = Subscription.EMPTY;\n\n  /**\n   * Since the value is kept on the model which is assigned in an Input,\n   * we might get a value before we have a model. This property keeps track\n   * of the value until we have somewhere to assign it.\n   */\n  private _pendingValue: D | null;\n\n  /** The form control validator for whether the input parses. */\n  private _parseValidator: ValidatorFn = (): ValidationErrors | null => {\n    return this._lastValueValid\n      ? null\n      : {'matDatepickerParse': {'text': this._elementRef.nativeElement.value}};\n  };\n\n  /** The form control validator for the date filter. */\n  private _filterValidator: ValidatorFn = (control: AbstractControl): ValidationErrors | null => {\n    const controlValue = this._dateAdapter.getValidDateOrNull(\n      this._dateAdapter.deserialize(control.value),\n    );\n    return !controlValue || this._matchesFilter(controlValue)\n      ? null\n      : {'matDatepickerFilter': true};\n  };\n\n  /** The form control validator for the min date. */\n  private _minValidator: ValidatorFn = (control: AbstractControl): ValidationErrors | null => {\n    const controlValue = this._dateAdapter.getValidDateOrNull(\n      this._dateAdapter.deserialize(control.value),\n    );\n    const min = this._getMinDate();\n    return !min || !controlValue || this._dateAdapter.compareDate(min, controlValue) <= 0\n      ? null\n      : {'matDatepickerMin': {'min': min, 'actual': controlValue}};\n  };\n\n  /** The form control validator for the max date. */\n  private _maxValidator: ValidatorFn = (control: AbstractControl): ValidationErrors | null => {\n    const controlValue = this._dateAdapter.getValidDateOrNull(\n      this._dateAdapter.deserialize(control.value),\n    );\n    const max = this._getMaxDate();\n    return !max || !controlValue || this._dateAdapter.compareDate(max, controlValue) >= 0\n      ? null\n      : {'matDatepickerMax': {'max': max, 'actual': controlValue}};\n  };\n\n  /** Gets the base validator functions. */\n  protected _getValidators(): ValidatorFn[] {\n    return [this._parseValidator, this._minValidator, this._maxValidator, this._filterValidator];\n  }\n\n  /** Gets the minimum date for the input. Used for validation. */\n  abstract _getMinDate(): D | null;\n\n  /** Gets the maximum date for the input. Used for validation. */\n  abstract _getMaxDate(): D | null;\n\n  /** Gets the date filter function. Used for validation. */\n  protected abstract _getDateFilter(): DateFilterFn<D> | undefined;\n\n  /** Registers a date selection model with the input. */\n  _registerModel(model: MatDateSelectionModel<S, D>): void {\n    this._model = model;\n    this._valueChangesSubscription.unsubscribe();\n\n    if (this._pendingValue) {\n      this._assignValue(this._pendingValue);\n    }\n\n    this._valueChangesSubscription = this._model.selectionChanged.subscribe(event => {\n      if (this._shouldHandleChangeEvent(event)) {\n        const value = this._getValueFromModel(event.selection);\n        this._lastValueValid = this._isValidValue(value);\n        this._cvaOnChange(value);\n        this._onTouched();\n        this._formatValue(value);\n        this.dateInput.emit(new MatDatepickerInputEvent(this, this._elementRef.nativeElement));\n        this.dateChange.emit(new MatDatepickerInputEvent(this, this._elementRef.nativeElement));\n      }\n    });\n  }\n\n  /** Opens the popup associated with the input. */\n  protected abstract _openPopup(): void;\n\n  /** Assigns a value to the input's model. */\n  protected abstract _assignValueToModel(model: D | null): void;\n\n  /** Converts a value from the model into a native value for the input. */\n  protected abstract _getValueFromModel(modelValue: S): D | null;\n\n  /** Combined form control validator for this input. */\n  protected abstract _validator: ValidatorFn | null;\n\n  /** Predicate that determines whether the input should handle a particular change event. */\n  protected abstract _shouldHandleChangeEvent(event: DateSelectionModelChange<S>): boolean;\n\n  /** Whether the last value set on the input was valid. */\n  protected _lastValueValid = false;\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (!this._dateAdapter) {\n        throw createMissingDateImplError('DateAdapter');\n      }\n      if (!this._dateFormats) {\n        throw createMissingDateImplError('MAT_DATE_FORMATS');\n      }\n    }\n\n    // Update the displayed date when the locale changes.\n    this._localeSubscription = this._dateAdapter.localeChanges.subscribe(() => {\n      this._assignValueProgrammatically(this.value);\n    });\n  }\n\n  ngAfterViewInit() {\n    this._isInitialized = true;\n  }\n\n  ngOnChanges(changes: SimpleChanges) {\n    if (dateInputsHaveChanged(changes, this._dateAdapter)) {\n      this.stateChanges.next(undefined);\n    }\n  }\n\n  ngOnDestroy() {\n    this._valueChangesSubscription.unsubscribe();\n    this._localeSubscription.unsubscribe();\n    this.stateChanges.complete();\n  }\n\n  /** @docs-private */\n  registerOnValidatorChange(fn: () => void): void {\n    this._validatorOnChange = fn;\n  }\n\n  /** @docs-private */\n  validate(c: AbstractControl): ValidationErrors | null {\n    return this._validator ? this._validator(c) : null;\n  }\n\n  // Implemented as part of ControlValueAccessor.\n  writeValue(value: D): void {\n    this._assignValueProgrammatically(value);\n  }\n\n  // Implemented as part of ControlValueAccessor.\n  registerOnChange(fn: (value: any) => void): void {\n    this._cvaOnChange = fn;\n  }\n\n  // Implemented as part of ControlValueAccessor.\n  registerOnTouched(fn: () => void): void {\n    this._onTouched = fn;\n  }\n\n  // Implemented as part of ControlValueAccessor.\n  setDisabledState(isDisabled: boolean): void {\n    this.disabled = isDisabled;\n  }\n\n  _onKeydown(event: KeyboardEvent) {\n    const ctrlShiftMetaModifiers: ModifierKey[] = ['ctrlKey', 'shiftKey', 'metaKey'];\n    const isAltDownArrow =\n      hasModifierKey(event, 'altKey') &&\n      event.keyCode === DOWN_ARROW &&\n      ctrlShiftMetaModifiers.every((modifier: ModifierKey) => !hasModifierKey(event, modifier));\n\n    if (isAltDownArrow && !this._elementRef.nativeElement.readOnly) {\n      this._openPopup();\n      event.preventDefault();\n    }\n  }\n\n  _onInput(value: string) {\n    const lastValueWasValid = this._lastValueValid;\n    let date = this._dateAdapter.parse(value, this._dateFormats.parse.dateInput);\n    this._lastValueValid = this._isValidValue(date);\n    date = this._dateAdapter.getValidDateOrNull(date);\n    const hasChanged = !this._dateAdapter.sameDate(date, this.value);\n\n    // We need to fire the CVA change event for all\n    // nulls, otherwise the validators won't run.\n    if (!date || hasChanged) {\n      this._cvaOnChange(date);\n    } else {\n      // Call the CVA change handler for invalid values\n      // since this is what marks the control as dirty.\n      if (value && !this.value) {\n        this._cvaOnChange(date);\n      }\n\n      if (lastValueWasValid !== this._lastValueValid) {\n        this._validatorOnChange();\n      }\n    }\n\n    if (hasChanged) {\n      this._assignValue(date);\n      this.dateInput.emit(new MatDatepickerInputEvent(this, this._elementRef.nativeElement));\n    }\n  }\n\n  _onChange() {\n    this.dateChange.emit(new MatDatepickerInputEvent(this, this._elementRef.nativeElement));\n  }\n\n  /** Handles blur events on the input. */\n  _onBlur() {\n    // Reformat the input only if we have a valid value.\n    if (this.value) {\n      this._formatValue(this.value);\n    }\n\n    this._onTouched();\n  }\n\n  /** Formats a value and sets it on the input element. */\n  protected _formatValue(value: D | null) {\n    this._elementRef.nativeElement.value =\n      value != null ? this._dateAdapter.format(value, this._dateFormats.display.dateInput) : '';\n  }\n\n  /** Assigns a value to the model. */\n  private _assignValue(value: D | null) {\n    // We may get some incoming values before the model was\n    // assigned. Save the value so that we can assign it later.\n    if (this._model) {\n      this._assignValueToModel(value);\n      this._pendingValue = null;\n    } else {\n      this._pendingValue = value;\n    }\n  }\n\n  /** Whether a value is considered valid. */\n  private _isValidValue(value: D | null): boolean {\n    return !value || this._dateAdapter.isValid(value);\n  }\n\n  /**\n   * Checks whether a parent control is disabled. This is in place so that it can be overridden\n   * by inputs extending this one which can be placed inside of a group that can be disabled.\n   */\n  protected _parentDisabled() {\n    return false;\n  }\n\n  /** Programmatically assigns a value to the input. */\n  protected _assignValueProgrammatically(value: D | null) {\n    value = this._dateAdapter.deserialize(value);\n    this._lastValueValid = this._isValidValue(value);\n    value = this._dateAdapter.getValidDateOrNull(value);\n    this._assignValue(value);\n    this._formatValue(value);\n  }\n\n  /** Gets whether a value matches the current date filter. */\n  _matchesFilter(value: D | null): boolean {\n    const filter = this._getDateFilter();\n    return !filter || filter(value);\n  }\n}\n\n/**\n * Checks whether the `SimpleChanges` object from an `ngOnChanges`\n * callback has any changes, accounting for date objects.\n */\nexport function dateInputsHaveChanged(\n  changes: SimpleChanges,\n  adapter: DateAdapter<unknown>,\n): boolean {\n  const keys = Object.keys(changes);\n\n  for (let key of keys) {\n    const {previousValue, currentValue} = changes[key];\n\n    if (adapter.isDateInstance(previousValue) && adapter.isDateInstance(currentValue)) {\n      if (!adapter.sameDate(previousValue, currentValue)) {\n        return true;\n      }\n    } else {\n      return true;\n    }\n  }\n\n  return false;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Directive, ElementRef, forwardRef, Input, OnDestroy, signal, inject} from '@angular/core';\nimport {NG_VALIDATORS, NG_VALUE_ACCESSOR, ValidatorFn, Validators} from '@angular/forms';\nimport {ThemePalette} from '../core';\nimport {MAT_FORM_FIELD} from '../form-field';\nimport {MAT_INPUT_VALUE_ACCESSOR} from '../input';\nimport {Subscription} from 'rxjs';\nimport {DateSelectionModelChange} from './date-selection-model';\nimport {MatDatepickerControl, MatDatepickerPanel} from './datepicker-base';\nimport {_MatFormFieldPartial, DateFilterFn, MatDatepickerInputBase} from './datepicker-input-base';\n\n/** @docs-private */\nexport const MAT_DATEPICKER_VALUE_ACCESSOR: any = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => MatDatepickerInput),\n  multi: true,\n};\n\n/** @docs-private */\nexport const MAT_DATEPICKER_VALIDATORS: any = {\n  provide: NG_VALIDATORS,\n  useExisting: forwardRef(() => MatDatepickerInput),\n  multi: true,\n};\n\n/** Directive used to connect an input to a MatDatepicker. */\n@Directive({\n  selector: 'input[matDatepicker]',\n  providers: [\n    MAT_DATEPICKER_VALUE_ACCESSOR,\n    MAT_DATEPICKER_VALIDATORS,\n    {provide: MAT_INPUT_VALUE_ACCESSOR, useExisting: MatDatepickerInput},\n  ],\n  host: {\n    'class': 'mat-datepicker-input',\n    '[attr.aria-haspopup]': '_datepicker ? \"dialog\" : null',\n    '[attr.aria-owns]': '_ariaOwns()',\n    '[attr.min]': 'min ? _dateAdapter.toIso8601(min) : null',\n    '[attr.max]': 'max ? _dateAdapter.toIso8601(max) : null',\n    // Used by the test harness to tie this input to its calendar. We can't depend on\n    // `aria-owns` for this, because it's only defined while the calendar is open.\n    '[attr.data-mat-calendar]': '_datepicker ? _datepicker.id : null',\n    '[disabled]': 'disabled',\n    '(input)': '_onInput($event.target.value)',\n    '(change)': '_onChange()',\n    '(blur)': '_onBlur()',\n    '(keydown)': '_onKeydown($event)',\n  },\n  exportAs: 'matDatepickerInput',\n})\nexport class MatDatepickerInput<D>\n  extends MatDatepickerInputBase<D | null, D>\n  implements MatDatepickerControl<D | null>, OnDestroy\n{\n  private _formField = inject<_MatFormFieldPartial>(MAT_FORM_FIELD, {optional: true});\n  private _closedSubscription = Subscription.EMPTY;\n  private _openedSubscription = Subscription.EMPTY;\n\n  /** The datepicker that this input is associated with. */\n  @Input()\n  set matDatepicker(datepicker: MatDatepickerPanel<MatDatepickerControl<D>, D | null, D>) {\n    if (datepicker) {\n      this._datepicker = datepicker;\n      this._ariaOwns.set(datepicker.opened ? datepicker.id : null);\n      this._closedSubscription = datepicker.closedStream.subscribe(() => {\n        this._onTouched();\n        this._ariaOwns.set(null);\n      });\n      this._openedSubscription = datepicker.openedStream.subscribe(() => {\n        this._ariaOwns.set(datepicker.id);\n      });\n      this._registerModel(datepicker.registerInput(this));\n    }\n  }\n  _datepicker: MatDatepickerPanel<MatDatepickerControl<D>, D | null, D>;\n\n  /** The id of the panel owned by this input. */\n  protected _ariaOwns = signal<string | null>(null);\n\n  /** The minimum valid date. */\n  @Input()\n  get min(): D | null {\n    return this._min;\n  }\n  set min(value: D | null) {\n    const validValue = this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(value));\n\n    if (!this._dateAdapter.sameDate(validValue, this._min)) {\n      this._min = validValue;\n      this._validatorOnChange();\n    }\n  }\n  private _min: D | null;\n\n  /** The maximum valid date. */\n  @Input()\n  get max(): D | null {\n    return this._max;\n  }\n  set max(value: D | null) {\n    const validValue = this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(value));\n\n    if (!this._dateAdapter.sameDate(validValue, this._max)) {\n      this._max = validValue;\n      this._validatorOnChange();\n    }\n  }\n  private _max: D | null;\n\n  /** Function that can be used to filter out dates within the datepicker. */\n  @Input('matDatepickerFilter')\n  get dateFilter() {\n    return this._dateFilter;\n  }\n  set dateFilter(value: DateFilterFn<D | null>) {\n    const wasMatchingValue = this._matchesFilter(this.value);\n    this._dateFilter = value;\n\n    if (this._matchesFilter(this.value) !== wasMatchingValue) {\n      this._validatorOnChange();\n    }\n  }\n  private _dateFilter: DateFilterFn<D | null>;\n\n  /** The combined form control validator for this input. */\n  protected _validator: ValidatorFn | null;\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    super();\n    this._validator = Validators.compose(super._getValidators());\n  }\n\n  /**\n   * Gets the element that the datepicker popup should be connected to.\n   * @return The element to connect the popup to.\n   */\n  getConnectedOverlayOrigin(): ElementRef {\n    return this._formField ? this._formField.getConnectedOverlayOrigin() : this._elementRef;\n  }\n\n  /** Gets the ID of an element that should be used a description for the calendar overlay. */\n  getOverlayLabelId(): string | null {\n    if (this._formField) {\n      return this._formField.getLabelId();\n    }\n\n    return this._elementRef.nativeElement.getAttribute('aria-labelledby');\n  }\n\n  /** Returns the palette used by the input's form field, if any. */\n  getThemePalette(): ThemePalette {\n    return this._formField ? this._formField.color : undefined;\n  }\n\n  /** Gets the value at which the calendar should start. */\n  getStartValue(): D | null {\n    return this.value;\n  }\n\n  override ngOnDestroy() {\n    super.ngOnDestroy();\n    this._closedSubscription.unsubscribe();\n    this._openedSubscription.unsubscribe();\n  }\n\n  /** Opens the associated datepicker. */\n  protected _openPopup(): void {\n    if (this._datepicker) {\n      this._datepicker.open();\n    }\n  }\n\n  protected _getValueFromModel(modelValue: D | null): D | null {\n    return modelValue;\n  }\n\n  protected _assignValueToModel(value: D | null): void {\n    if (this._model) {\n      this._model.updateSelection(value, this);\n    }\n  }\n\n  /** Gets the input's minimum date. */\n  _getMinDate() {\n    return this._min;\n  }\n\n  /** Gets the input's maximum date. */\n  _getMaxDate() {\n    return this._max;\n  }\n\n  /** Gets the input's date filtering function. */\n  protected _getDateFilter() {\n    return this._dateFilter;\n  }\n\n  protected _shouldHandleChangeEvent(event: DateSelectionModelChange<D>) {\n    return event.source !== this;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  AfterContentInit,\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  ContentChild,\n  Directive,\n  Input,\n  OnChanges,\n  OnDestroy,\n  SimpleChanges,\n  ViewEncapsulation,\n  ViewChild,\n  booleanAttribute,\n  inject,\n  HostAttributeToken,\n} from '@angular/core';\nimport {MatButton, MatIconButton} from '../button';\nimport {merge, Observable, of as observableOf, Subscription} from 'rxjs';\nimport {MatDatepickerIntl} from './datepicker-intl';\nimport {MatDatepickerControl, MatDatepickerPanel} from './datepicker-base';\n\n/** Can be used to override the icon of a `matDatepickerToggle`. */\n@Directive({\n  selector: '[matDatepickerToggleIcon]',\n})\nexport class MatDatepickerToggleIcon {}\n\n@Component({\n  selector: 'mat-datepicker-toggle',\n  templateUrl: 'datepicker-toggle.html',\n  styleUrl: 'datepicker-toggle.css',\n  host: {\n    'class': 'mat-datepicker-toggle',\n    '[attr.tabindex]': 'null',\n    '[class.mat-datepicker-toggle-active]': 'datepicker && datepicker.opened',\n    '[class.mat-accent]': 'datepicker && datepicker.color === \"accent\"',\n    '[class.mat-warn]': 'datepicker && datepicker.color === \"warn\"',\n    // Used by the test harness to tie this toggle to its datepicker.\n    '[attr.data-mat-calendar]': 'datepicker ? datepicker.id : null',\n    // Bind the `click` on the host, rather than the inner `button`, so that we can call\n    // `stopPropagation` on it without affecting the user's `click` handlers. We need to stop\n    // it so that the input doesn't get focused automatically by the form field (See #21836).\n    '(click)': '_open($event)',\n  },\n  exportAs: 'matDatepickerToggle',\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  imports: [MatIconButton],\n})\nexport class MatDatepickerToggle<D> implements AfterContentInit, OnChanges, OnDestroy {\n  _intl = inject(MatDatepickerIntl);\n  private _changeDetectorRef = inject(ChangeDetectorRef);\n  private _stateChanges = Subscription.EMPTY;\n\n  /** Datepicker instance that the button will toggle. */\n  @Input('for') datepicker: MatDatepickerPanel<MatDatepickerControl<any>, D>;\n\n  /** Tabindex for the toggle. */\n  @Input() tabIndex: number | null;\n\n  /** Screen-reader label for the button. */\n  @Input('aria-label') ariaLabel: string;\n\n  /** Whether the toggle button is disabled. */\n  @Input({transform: booleanAttribute})\n  get disabled(): boolean {\n    if (this._disabled === undefined && this.datepicker) {\n      return this.datepicker.disabled;\n    }\n\n    return !!this._disabled;\n  }\n  set disabled(value: boolean) {\n    this._disabled = value;\n  }\n  private _disabled: boolean;\n\n  /** Whether ripples on the toggle should be disabled. */\n  @Input() disableRipple: boolean;\n\n  /** Custom icon set by the consumer. */\n  @ContentChild(MatDatepickerToggleIcon) _customIcon: MatDatepickerToggleIcon;\n\n  /** Underlying button element. */\n  @ViewChild('button') _button: MatButton;\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    const defaultTabIndex = inject(new HostAttributeToken('tabindex'), {optional: true});\n    const parsedTabIndex = Number(defaultTabIndex);\n    this.tabIndex = parsedTabIndex || parsedTabIndex === 0 ? parsedTabIndex : null;\n  }\n\n  ngOnChanges(changes: SimpleChanges) {\n    if (changes['datepicker']) {\n      this._watchStateChanges();\n    }\n  }\n\n  ngOnDestroy() {\n    this._stateChanges.unsubscribe();\n  }\n\n  ngAfterContentInit() {\n    this._watchStateChanges();\n  }\n\n  _open(event: Event): void {\n    if (this.datepicker && !this.disabled) {\n      this.datepicker.open();\n      event.stopPropagation();\n    }\n  }\n\n  private _watchStateChanges() {\n    const datepickerStateChanged = this.datepicker ? this.datepicker.stateChanges : observableOf();\n    const inputStateChanged =\n      this.datepicker && this.datepicker.datepickerInput\n        ? this.datepicker.datepickerInput.stateChanges\n        : observableOf();\n    const datepickerToggled = this.datepicker\n      ? merge(this.datepicker.openedStream, this.datepicker.closedStream)\n      : observableOf();\n\n    this._stateChanges.unsubscribe();\n    this._stateChanges = merge(\n      this._intl.changes,\n      datepickerStateChanged as Observable<void>,\n      inputStateChanged,\n      datepickerToggled,\n    ).subscribe(() => this._changeDetectorRef.markForCheck());\n  }\n}\n", "<button\n  #button\n  mat-icon-button\n  type=\"button\"\n  [attr.aria-haspopup]=\"datepicker ? 'dialog' : null\"\n  [attr.aria-label]=\"ariaLabel || _intl.openCalendarLabel\"\n  [attr.tabindex]=\"disabled ? -1 : tabIndex\"\n  [attr.aria-expanded]=\"datepicker ? datepicker.opened : null\"\n  [disabled]=\"disabled\"\n  [disableRipple]=\"disableRipple\">\n\n  @if (!_customIcon) {\n    <svg\n      class=\"mat-datepicker-toggle-default-icon\"\n      viewBox=\"0 0 24 24\"\n      width=\"24px\"\n      height=\"24px\"\n      fill=\"currentColor\"\n      focusable=\"false\"\n      aria-hidden=\"true\">\n      <path d=\"M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7z\"/>\n    </svg>\n  }\n\n  <ng-content select=\"[matDatepickerToggleIcon]\"></ng-content>\n</button>\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {_IdGenerator, CdkMonitorFocus, FocusOrigin} from '@angular/cdk/a11y';\nimport {\n  AfterContentInit,\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  ElementRef,\n  Input,\n  OnChanges,\n  OnDestroy,\n  SimpleChanges,\n  ViewEncapsulation,\n  booleanAttribute,\n  signal,\n  inject,\n} from '@angular/core';\nimport {ControlContainer, NgControl, Validators} from '@angular/forms';\nimport {DateAdapter, ThemePalette} from '../core';\nimport {MAT_FORM_FIELD, MatFormFieldControl} from '../form-field';\nimport {Subject, Subscription, merge} from 'rxjs';\nimport type {MatEndDate, MatStartDate} from './date-range-input-parts';\nimport {MatDateRangePickerInput} from './date-range-picker';\nimport {DateRange, MatDateSelectionModel} from './date-selection-model';\nimport {MatDatepickerControl, MatDatepickerPanel} from './datepicker-base';\nimport {createMissingDateImplError} from './datepicker-errors';\nimport {DateFilterFn, _MatFormFieldPartial, dateInputsHaveChanged} from './datepicker-input-base';\n\n@Component({\n  selector: 'mat-date-range-input',\n  templateUrl: 'date-range-input.html',\n  styleUrl: 'date-range-input.css',\n  exportAs: 'matDateRangeInput',\n  host: {\n    'class': 'mat-date-range-input',\n    '[class.mat-date-range-input-hide-placeholders]': '_shouldHidePlaceholders()',\n    '[class.mat-date-range-input-required]': 'required',\n    '[attr.id]': 'id',\n    'role': 'group',\n    '[attr.aria-labelledby]': '_getAriaLabelledby()',\n    '[attr.aria-describedby]': '_ariaDescribedBy',\n    // Used by the test harness to tie this input to its calendar. We can't depend on\n    // `aria-owns` for this, because it's only defined while the calendar is open.\n    '[attr.data-mat-calendar]': 'rangePicker ? rangePicker.id : null',\n  },\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  encapsulation: ViewEncapsulation.None,\n  providers: [{provide: MatFormFieldControl, useExisting: MatDateRangeInput}],\n  imports: [CdkMonitorFocus],\n})\nexport class MatDateRangeInput<D>\n  implements\n    MatFormFieldControl<DateRange<D>>,\n    MatDatepickerControl<D>,\n    MatDateRangePickerInput<D>,\n    AfterContentInit,\n    OnChanges,\n    OnDestroy\n{\n  private _changeDetectorRef = inject(ChangeDetectorRef);\n  private _elementRef = inject<ElementRef<HTMLElement>>(ElementRef);\n  private _dateAdapter = inject<DateAdapter<D>>(DateAdapter, {optional: true})!;\n  private _formField = inject<_MatFormFieldPartial>(MAT_FORM_FIELD, {optional: true});\n\n  private _closedSubscription = Subscription.EMPTY;\n  private _openedSubscription = Subscription.EMPTY;\n\n  _startInput: MatStartDate<D>;\n  _endInput: MatEndDate<D>;\n\n  /** Current value of the range input. */\n  get value() {\n    return this._model ? this._model.selection : null;\n  }\n\n  /** Unique ID for the group. */\n  id: string = inject(_IdGenerator).getId('mat-date-range-input-');\n\n  /** Whether the control is focused. */\n  focused = false;\n\n  /** Whether the control's label should float. */\n  get shouldLabelFloat(): boolean {\n    return this.focused || !this.empty;\n  }\n\n  /** Name of the form control. */\n  controlType = 'mat-date-range-input';\n\n  /**\n   * Implemented as a part of `MatFormFieldControl`.\n   * Set the placeholder attribute on `matStartDate` and `matEndDate`.\n   * @docs-private\n   */\n  get placeholder() {\n    const start = this._startInput?._getPlaceholder() || '';\n    const end = this._endInput?._getPlaceholder() || '';\n    return start || end ? `${start} ${this.separator} ${end}` : '';\n  }\n\n  /** The range picker that this input is associated with. */\n  @Input()\n  get rangePicker() {\n    return this._rangePicker;\n  }\n  set rangePicker(rangePicker: MatDatepickerPanel<MatDatepickerControl<D>, DateRange<D>, D>) {\n    if (rangePicker) {\n      this._model = rangePicker.registerInput(this);\n      this._rangePicker = rangePicker;\n      this._closedSubscription.unsubscribe();\n      this._openedSubscription.unsubscribe();\n      this._ariaOwns.set(this.rangePicker.opened ? rangePicker.id : null);\n      this._closedSubscription = rangePicker.closedStream.subscribe(() => {\n        this._startInput?._onTouched();\n        this._endInput?._onTouched();\n        this._ariaOwns.set(null);\n      });\n      this._openedSubscription = rangePicker.openedStream.subscribe(() => {\n        this._ariaOwns.set(rangePicker.id);\n      });\n      this._registerModel(this._model!);\n    }\n  }\n  private _rangePicker: MatDatepickerPanel<MatDatepickerControl<D>, DateRange<D>, D>;\n\n  /** The id of the panel owned by this input. */\n  _ariaOwns = signal<string | null>(null);\n\n  /** Whether the input is required. */\n  @Input({transform: booleanAttribute})\n  get required(): boolean {\n    return (\n      this._required ??\n      (this._isTargetRequired(this) ||\n        this._isTargetRequired(this._startInput) ||\n        this._isTargetRequired(this._endInput)) ??\n      false\n    );\n  }\n  set required(value: boolean) {\n    this._required = value;\n  }\n  private _required: boolean | undefined;\n\n  /** Function that can be used to filter out dates within the date range picker. */\n  @Input()\n  get dateFilter() {\n    return this._dateFilter;\n  }\n  set dateFilter(value: DateFilterFn<D>) {\n    const start = this._startInput;\n    const end = this._endInput;\n    const wasMatchingStart = start && start._matchesFilter(start.value);\n    const wasMatchingEnd = end && end._matchesFilter(start.value);\n    this._dateFilter = value;\n\n    if (start && start._matchesFilter(start.value) !== wasMatchingStart) {\n      start._validatorOnChange();\n    }\n\n    if (end && end._matchesFilter(end.value) !== wasMatchingEnd) {\n      end._validatorOnChange();\n    }\n  }\n  private _dateFilter: DateFilterFn<D>;\n\n  /** The minimum valid date. */\n  @Input()\n  get min(): D | null {\n    return this._min;\n  }\n  set min(value: D | null) {\n    const validValue = this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(value));\n\n    if (!this._dateAdapter.sameDate(validValue, this._min)) {\n      this._min = validValue;\n      this._revalidate();\n    }\n  }\n  private _min: D | null;\n\n  /** The maximum valid date. */\n  @Input()\n  get max(): D | null {\n    return this._max;\n  }\n  set max(value: D | null) {\n    const validValue = this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(value));\n\n    if (!this._dateAdapter.sameDate(validValue, this._max)) {\n      this._max = validValue;\n      this._revalidate();\n    }\n  }\n  private _max: D | null;\n\n  /** Whether the input is disabled. */\n  @Input({transform: booleanAttribute})\n  get disabled(): boolean {\n    return this._startInput && this._endInput\n      ? this._startInput.disabled && this._endInput.disabled\n      : this._groupDisabled;\n  }\n  set disabled(value: boolean) {\n    if (value !== this._groupDisabled) {\n      this._groupDisabled = value;\n      this.stateChanges.next(undefined);\n    }\n  }\n  _groupDisabled = false;\n\n  /** Whether the input is in an error state. */\n  get errorState(): boolean {\n    if (this._startInput && this._endInput) {\n      return this._startInput.errorState || this._endInput.errorState;\n    }\n\n    return false;\n  }\n\n  /** Whether the datepicker input is empty. */\n  get empty(): boolean {\n    const startEmpty = this._startInput ? this._startInput.isEmpty() : false;\n    const endEmpty = this._endInput ? this._endInput.isEmpty() : false;\n    return startEmpty && endEmpty;\n  }\n\n  /** Value for the `aria-describedby` attribute of the inputs. */\n  _ariaDescribedBy: string | null = null;\n\n  /** Date selection model currently registered with the input. */\n  private _model: MatDateSelectionModel<DateRange<D>> | undefined;\n\n  /** Separator text to be shown between the inputs. */\n  @Input() separator = '–';\n\n  /** Start of the comparison range that should be shown in the calendar. */\n  @Input() comparisonStart: D | null = null;\n\n  /** End of the comparison range that should be shown in the calendar. */\n  @Input() comparisonEnd: D | null = null;\n\n  /**\n   * Implemented as a part of `MatFormFieldControl`.\n   * TODO(crisbeto): change type to `AbstractControlDirective` after #18206 lands.\n   * @docs-private\n   */\n  ngControl: NgControl | null;\n\n  /** Emits when the input's state has changed. */\n  readonly stateChanges = new Subject<void>();\n\n  /**\n   * Disable the automatic labeling to avoid issues like #27241.\n   * @docs-private\n   */\n  readonly disableAutomaticLabeling = true;\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    if (!this._dateAdapter && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw createMissingDateImplError('DateAdapter');\n    }\n\n    // The datepicker module can be used both with MDC and non-MDC form fields. We have\n    // to conditionally add the MDC input class so that the range picker looks correctly.\n    if (this._formField?._elementRef.nativeElement.classList.contains('mat-mdc-form-field')) {\n      this._elementRef.nativeElement.classList.add(\n        'mat-mdc-input-element',\n        'mat-mdc-form-field-input-control',\n        'mdc-text-field__input',\n      );\n    }\n\n    // TODO(crisbeto): remove `as any` after #18206 lands.\n    this.ngControl = inject(ControlContainer, {optional: true, self: true}) as any;\n  }\n\n  /**\n   * Implemented as a part of `MatFormFieldControl`.\n   * @docs-private\n   */\n  setDescribedByIds(ids: string[]): void {\n    this._ariaDescribedBy = ids.length ? ids.join(' ') : null;\n  }\n\n  /**\n   * Implemented as a part of `MatFormFieldControl`.\n   * @docs-private\n   */\n  onContainerClick(): void {\n    if (!this.focused && !this.disabled) {\n      if (!this._model || !this._model.selection.start) {\n        this._startInput.focus();\n      } else {\n        this._endInput.focus();\n      }\n    }\n  }\n\n  ngAfterContentInit() {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (!this._startInput) {\n        throw Error('mat-date-range-input must contain a matStartDate input');\n      }\n\n      if (!this._endInput) {\n        throw Error('mat-date-range-input must contain a matEndDate input');\n      }\n    }\n\n    if (this._model) {\n      this._registerModel(this._model);\n    }\n\n    // We don't need to unsubscribe from this, because we\n    // know that the input streams will be completed on destroy.\n    merge(this._startInput.stateChanges, this._endInput.stateChanges).subscribe(() => {\n      this.stateChanges.next(undefined);\n    });\n  }\n\n  ngOnChanges(changes: SimpleChanges) {\n    if (dateInputsHaveChanged(changes, this._dateAdapter)) {\n      this.stateChanges.next(undefined);\n    }\n  }\n\n  ngOnDestroy() {\n    this._closedSubscription.unsubscribe();\n    this._openedSubscription.unsubscribe();\n    this.stateChanges.complete();\n  }\n\n  /** Gets the date at which the calendar should start. */\n  getStartValue(): D | null {\n    return this.value ? this.value.start : null;\n  }\n\n  /** Gets the input's theme palette. */\n  getThemePalette(): ThemePalette {\n    return this._formField ? this._formField.color : undefined;\n  }\n\n  /** Gets the element to which the calendar overlay should be attached. */\n  getConnectedOverlayOrigin(): ElementRef {\n    return this._formField ? this._formField.getConnectedOverlayOrigin() : this._elementRef;\n  }\n\n  /** Gets the ID of an element that should be used a description for the calendar overlay. */\n  getOverlayLabelId(): string | null {\n    return this._formField ? this._formField.getLabelId() : null;\n  }\n\n  /** Gets the value that is used to mirror the state input. */\n  _getInputMirrorValue(part: 'start' | 'end') {\n    const input = part === 'start' ? this._startInput : this._endInput;\n    return input ? input.getMirrorValue() : '';\n  }\n\n  /** Whether the input placeholders should be hidden. */\n  _shouldHidePlaceholders() {\n    return this._startInput ? !this._startInput.isEmpty() : false;\n  }\n\n  /** Handles the value in one of the child inputs changing. */\n  _handleChildValueChange() {\n    this.stateChanges.next(undefined);\n    this._changeDetectorRef.markForCheck();\n  }\n\n  /** Opens the date range picker associated with the input. */\n  _openDatepicker() {\n    if (this._rangePicker) {\n      this._rangePicker.open();\n    }\n  }\n\n  /** Whether the separate text should be hidden. */\n  _shouldHideSeparator() {\n    return (\n      (!this._formField ||\n        (this._formField.getLabelId() && !this._formField._shouldLabelFloat())) &&\n      this.empty\n    );\n  }\n\n  /** Gets the value for the `aria-labelledby` attribute of the inputs. */\n  _getAriaLabelledby() {\n    const formField = this._formField;\n    return formField && formField._hasFloatingLabel() ? formField._labelId : null;\n  }\n\n  _getStartDateAccessibleName(): string {\n    return this._startInput._getAccessibleName();\n  }\n\n  _getEndDateAccessibleName(): string {\n    return this._endInput._getAccessibleName();\n  }\n\n  /** Updates the focused state of the range input. */\n  _updateFocus(origin: FocusOrigin) {\n    this.focused = origin !== null;\n    this.stateChanges.next();\n  }\n\n  /** Re-runs the validators on the start/end inputs. */\n  private _revalidate() {\n    if (this._startInput) {\n      this._startInput._validatorOnChange();\n    }\n\n    if (this._endInput) {\n      this._endInput._validatorOnChange();\n    }\n  }\n\n  /** Registers the current date selection model with the start/end inputs. */\n  private _registerModel(model: MatDateSelectionModel<DateRange<D>>) {\n    if (this._startInput) {\n      this._startInput._registerModel(model);\n    }\n\n    if (this._endInput) {\n      this._endInput._registerModel(model);\n    }\n  }\n\n  /** Checks whether a specific range input directive is required. */\n  private _isTargetRequired(target: {ngControl: NgControl | null} | null): boolean | undefined {\n    return target?.ngControl?.control?.hasValidator(Validators.required);\n  }\n}\n", "<div\n  class=\"mat-date-range-input-container\"\n  cdkMonitorSubtreeFocus\n  (cdkFocusChange)=\"_updateFocus($event)\">\n  <div class=\"mat-date-range-input-wrapper\">\n    <ng-content select=\"input[matStartDate]\"></ng-content>\n    <span\n      class=\"mat-date-range-input-mirror\"\n      aria-hidden=\"true\">{{_getInputMirrorValue('start')}}</span>\n  </div>\n\n  <span\n    class=\"mat-date-range-input-separator\"\n    [class.mat-date-range-input-separator-hidden]=\"_shouldHideSeparator()\">{{separator}}</span>\n\n  <div class=\"mat-date-range-input-wrapper mat-date-range-input-end-wrapper\">\n    <ng-content select=\"input[matEndDate]\"></ng-content>\n    <span\n      class=\"mat-date-range-input-mirror\"\n      aria-hidden=\"true\">{{_getInputMirrorValue('end')}}</span>\n  </div>\n</div>\n\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// This file contains the `_computeAriaAccessibleName` function, which computes what the *expected*\n// ARIA accessible name would be for a given element. Implements a subset of ARIA specification\n// [Accessible Name and Description Computation 1.2](https://www.w3.org/TR/accname-1.2/).\n//\n// Specification accname-1.2 can be summarized by returning the result of the first method\n// available.\n//\n//  1. `aria-labelledby` attribute\n//     ```\n//       <!-- example using aria-labelledby-->\n//       <label id='label-id'>Start Date</label>\n//       <input aria-labelledby='label-id'/>\n//     ```\n//  2. `aria-label` attribute (e.g. `<input aria-label=\"Departure\"/>`)\n//  3. Label with `for`/`id`\n//     ```\n//       <!-- example using for/id -->\n//       <label for=\"current-node\">Label</label>\n//       <input id=\"current-node\"/>\n//     ```\n//  4. `placeholder` attribute (e.g. `<input placeholder=\"06/03/1990\"/>`)\n//  5. `title` attribute (e.g. `<input title=\"Check-In\"/>`)\n//  6. text content\n//     ```\n//       <!-- example using text content -->\n//       <label for=\"current-node\"><span>Departure</span> Date</label>\n//       <input id=\"current-node\"/>\n//     ```\n\n/**\n * Computes the *expected* ARIA accessible name for argument element based on [accname-1.2\n * specification](https://www.w3.org/TR/accname-1.2/). Implements a subset of accname-1.2,\n * and should only be used for the Datepicker's specific use case.\n *\n * Intended use:\n * This is not a general use implementation. Only implements the parts of accname-1.2 that are\n * required for the Datepicker's specific use case. This function is not intended for any other\n * use.\n *\n * Limitations:\n *  - Only covers the needs of `matStartDate` and `matEndDate`. Does not support other use cases.\n *  - See NOTES's in implementation for specific details on what parts of the accname-1.2\n *  specification are not implemented.\n *\n *  @param element {HTMLInputElement} native &lt;input/&gt; element of `matStartDate` or\n *  `matEndDate` component. Corresponds to the 'Root Element' from accname-1.2\n *\n *  @return expected ARIA accessible name of argument &lt;input/&gt;\n */\nexport function _computeAriaAccessibleName(\n  element: HTMLInputElement | HTMLTextAreaElement,\n): string {\n  return _computeAriaAccessibleNameInternal(element, true);\n}\n\n/**\n * Determine if argument node is an Element based on `nodeType` property. This function is safe to\n * use with server-side rendering.\n */\nfunction ssrSafeIsElement(node: Node): node is Element {\n  return node.nodeType === Node.ELEMENT_NODE;\n}\n\n/**\n * Determine if argument node is an HTMLInputElement based on `nodeName` property. This funciton is\n * safe to use with server-side rendering.\n */\nfunction ssrSafeIsHTMLInputElement(node: Node): node is HTMLInputElement {\n  return node.nodeName === 'INPUT';\n}\n\n/**\n * Determine if argument node is an HTMLTextAreaElement based on `nodeName` property. This\n * funciton is safe to use with server-side rendering.\n */\nfunction ssrSafeIsHTMLTextAreaElement(node: Node): node is HTMLTextAreaElement {\n  return node.nodeName === 'TEXTAREA';\n}\n\n/**\n * Calculate the expected ARIA accessible name for given DOM Node. Given DOM Node may be either the\n * \"Root node\" passed to `_computeAriaAccessibleName` or \"Current node\" as result of recursion.\n *\n * @return the accessible name of argument DOM Node\n *\n * @param currentNode node to determine accessible name of\n * @param isDirectlyReferenced true if `currentNode` is the root node to calculate ARIA accessible\n * name of. False if it is a result of recursion.\n */\nfunction _computeAriaAccessibleNameInternal(\n  currentNode: Node,\n  isDirectlyReferenced: boolean,\n): string {\n  // NOTE: this differs from accname-1.2 specification.\n  //  - Does not implement Step 1. of accname-1.2: '''If `currentNode`'s role prohibits naming,\n  //    return the empty string (\"\")'''.\n  //  - Does not implement Step 2.A. of accname-1.2: '''if current node is hidden and not directly\n  //    referenced by aria-labelledby... return the empty string.'''\n\n  // acc-name-1.2 Step 2.B.: aria-labelledby\n  if (ssrSafeIsElement(currentNode) && isDirectlyReferenced) {\n    const labelledbyIds: string[] =\n      currentNode.getAttribute?.('aria-labelledby')?.split(/\\s+/g) || [];\n    const validIdRefs: HTMLElement[] = labelledbyIds.reduce((validIds, id) => {\n      const elem = document.getElementById(id);\n      if (elem) {\n        validIds.push(elem);\n      }\n      return validIds;\n    }, [] as HTMLElement[]);\n\n    if (validIdRefs.length) {\n      return validIdRefs\n        .map(idRef => {\n          return _computeAriaAccessibleNameInternal(idRef, false);\n        })\n        .join(' ');\n    }\n  }\n\n  // acc-name-1.2 Step 2.C.: aria-label\n  if (ssrSafeIsElement(currentNode)) {\n    const ariaLabel = currentNode.getAttribute('aria-label')?.trim();\n\n    if (ariaLabel) {\n      return ariaLabel;\n    }\n  }\n\n  // acc-name-1.2 Step 2.D. attribute or element that defines a text alternative\n  //\n  // NOTE: this differs from accname-1.2 specification.\n  // Only implements Step 2.D. for `<label>`,`<input/>`, and `<textarea/>` element. Does not\n  // implement other elements that have an attribute or element that defines a text alternative.\n  if (ssrSafeIsHTMLInputElement(currentNode) || ssrSafeIsHTMLTextAreaElement(currentNode)) {\n    // use label with a `for` attribute referencing the current node\n    if (currentNode.labels?.length) {\n      return Array.from(currentNode.labels)\n        .map(x => _computeAriaAccessibleNameInternal(x, false))\n        .join(' ');\n    }\n\n    // use placeholder if available\n    const placeholder = currentNode.getAttribute('placeholder')?.trim();\n    if (placeholder) {\n      return placeholder;\n    }\n\n    // use title if available\n    const title = currentNode.getAttribute('title')?.trim();\n    if (title) {\n      return title;\n    }\n  }\n\n  // NOTE: this differs from accname-1.2 specification.\n  //  - does not implement acc-name-1.2 Step 2.E.: '''if the current node is a control embedded\n  //     within the label... then include the embedded control as part of the text alternative in\n  //     the following manner...'''. Step 2E applies to embedded controls such as textbox, listbox,\n  //     range, etc.\n  //  - does not implement acc-name-1.2 step 2.F.: check that '''role allows name from content''',\n  //    which applies to `currentNode` and its children.\n  //  - does not implement acc-name-1.2 Step 2.F.ii.: '''Check for CSS generated textual content'''\n  //    (e.g. :before and :after).\n  //  - does not implement acc-name-1.2 Step 2.I.: '''if the current node has a Tooltip attribute,\n  //    return its value'''\n\n  // Return text content with whitespace collapsed into a single space character. Accomplish\n  // acc-name-1.2 steps 2F, 2G, and 2H.\n  return (currentNode.textContent || '').replace(/\\s+/g, ' ').trim();\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Directionality} from '@angular/cdk/bidi';\nimport {BACKSPACE, LEFT_ARROW, RIGHT_ARROW} from '@angular/cdk/keycodes';\nimport {\n  AfterContentInit,\n  Directive,\n  DoCheck,\n  ElementRef,\n  Injector,\n  Input,\n  OnInit,\n  inject,\n} from '@angular/core';\nimport {\n  AbstractControl,\n  FormGroupDirective,\n  NG_VALIDATORS,\n  NG_VALUE_ACCESSOR,\n  NgControl,\n  NgForm,\n  ValidationErrors,\n  ValidatorFn,\n  Validators,\n} from '@angular/forms';\nimport {ErrorStateMatcher, _ErrorStateTracker} from '../core';\nimport {_computeAriaAccessibleName} from './aria-accessible-name';\nimport {DateRange, DateSelectionModelChange} from './date-selection-model';\nimport {MatDatepickerInputBase} from './datepicker-input-base';\nimport {MatDateRangeInput} from './date-range-input';\n\n/**\n * Base class for the individual inputs that can be projected inside a `mat-date-range-input`.\n */\n@Directive()\nabstract class MatDateRangeInputPartBase<D>\n  extends MatDatepickerInputBase<DateRange<D>>\n  implements OnInit, AfterContentInit, DoCheck\n{\n  _rangeInput = inject<MatDateRangeInput<D>>(MatDateRangeInput);\n  override _elementRef = inject<ElementRef<HTMLInputElement>>(ElementRef);\n  _defaultErrorStateMatcher = inject(ErrorStateMatcher);\n  private _injector = inject(Injector);\n  _parentForm = inject(NgForm, {optional: true});\n  _parentFormGroup = inject(FormGroupDirective, {optional: true});\n\n  /**\n   * Form control bound to this input part.\n   * @docs-private\n   */\n  ngControl: NgControl;\n\n  protected abstract override _validator: ValidatorFn | null;\n  protected abstract override _assignValueToModel(value: D | null): void;\n  protected abstract override _getValueFromModel(modelValue: DateRange<D>): D | null;\n  protected abstract _register(): void;\n  protected readonly _dir = inject(Directionality, {optional: true});\n  private _errorStateTracker: _ErrorStateTracker;\n\n  /** Object used to control when error messages are shown. */\n  @Input()\n  get errorStateMatcher() {\n    return this._errorStateTracker.matcher;\n  }\n  set errorStateMatcher(value: ErrorStateMatcher) {\n    this._errorStateTracker.matcher = value;\n  }\n\n  /** Whether the input is in an error state. */\n  get errorState() {\n    return this._errorStateTracker.errorState;\n  }\n  set errorState(value: boolean) {\n    this._errorStateTracker.errorState = value;\n  }\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    super();\n\n    this._errorStateTracker = new _ErrorStateTracker(\n      this._defaultErrorStateMatcher,\n      null,\n      this._parentFormGroup,\n      this._parentForm,\n      this.stateChanges,\n    );\n  }\n\n  ngOnInit() {\n    // We need the date input to provide itself as a `ControlValueAccessor` and a `Validator`, while\n    // injecting its `NgControl` so that the error state is handled correctly. This introduces a\n    // circular dependency, because both `ControlValueAccessor` and `Validator` depend on the input\n    // itself. Usually we can work around it for the CVA, but there's no API to do it for the\n    // validator. We work around it here by injecting the `NgControl` in `ngOnInit`, after\n    // everything has been resolved.\n    const ngControl = this._injector.get(NgControl, null, {optional: true, self: true});\n\n    if (ngControl) {\n      this.ngControl = ngControl;\n      this._errorStateTracker.ngControl = ngControl;\n    }\n  }\n\n  ngAfterContentInit(): void {\n    this._register();\n  }\n\n  ngDoCheck() {\n    if (this.ngControl) {\n      // We need to re-evaluate this on every change detection cycle, because there are some\n      // error triggers that we can't subscribe to (e.g. parent form submissions). This means\n      // that whatever logic is in here has to be super lean or we risk destroying the performance.\n      this.updateErrorState();\n    }\n  }\n\n  /** Gets whether the input is empty. */\n  isEmpty(): boolean {\n    return this._elementRef.nativeElement.value.length === 0;\n  }\n\n  /** Gets the placeholder of the input. */\n  _getPlaceholder() {\n    return this._elementRef.nativeElement.placeholder;\n  }\n\n  /** Focuses the input. */\n  focus(): void {\n    this._elementRef.nativeElement.focus();\n  }\n\n  /** Gets the value that should be used when mirroring the input's size. */\n  getMirrorValue(): string {\n    const element = this._elementRef.nativeElement;\n    const value = element.value;\n    return value.length > 0 ? value : element.placeholder;\n  }\n\n  /** Refreshes the error state of the input. */\n  updateErrorState() {\n    this._errorStateTracker.updateErrorState();\n  }\n\n  /** Handles `input` events on the input element. */\n  override _onInput(value: string) {\n    super._onInput(value);\n    this._rangeInput._handleChildValueChange();\n  }\n\n  /** Opens the datepicker associated with the input. */\n  protected _openPopup(): void {\n    this._rangeInput._openDatepicker();\n  }\n\n  /** Gets the minimum date from the range input. */\n  _getMinDate() {\n    return this._rangeInput.min;\n  }\n\n  /** Gets the maximum date from the range input. */\n  _getMaxDate() {\n    return this._rangeInput.max;\n  }\n\n  /** Gets the date filter function from the range input. */\n  protected _getDateFilter() {\n    return this._rangeInput.dateFilter;\n  }\n\n  protected override _parentDisabled() {\n    return this._rangeInput._groupDisabled;\n  }\n\n  protected _shouldHandleChangeEvent({source}: DateSelectionModelChange<DateRange<D>>): boolean {\n    return source !== this._rangeInput._startInput && source !== this._rangeInput._endInput;\n  }\n\n  protected override _assignValueProgrammatically(value: D | null) {\n    super._assignValueProgrammatically(value);\n    const opposite = (\n      this === (this._rangeInput._startInput as MatDateRangeInputPartBase<D>)\n        ? this._rangeInput._endInput\n        : this._rangeInput._startInput\n    ) as MatDateRangeInputPartBase<D> | undefined;\n    opposite?._validatorOnChange();\n  }\n\n  protected override _formatValue(value: D | null) {\n    super._formatValue(value);\n    // Any time the input value is reformatted we need to tell the parent.\n    this._rangeInput._handleChildValueChange();\n  }\n\n  /** return the ARIA accessible name of the input element */\n  _getAccessibleName(): string {\n    return _computeAriaAccessibleName(this._elementRef.nativeElement);\n  }\n}\n\n/** Input for entering the start date in a `mat-date-range-input`. */\n@Directive({\n  selector: 'input[matStartDate]',\n  host: {\n    'class': 'mat-start-date mat-date-range-input-inner',\n    '[disabled]': 'disabled',\n    '(input)': '_onInput($event.target.value)',\n    '(change)': '_onChange()',\n    '(keydown)': '_onKeydown($event)',\n    '[attr.aria-haspopup]': '_rangeInput.rangePicker ? \"dialog\" : null',\n    '[attr.aria-owns]': `_rangeInput._ariaOwns\n        ? _rangeInput._ariaOwns()\n        : (_rangeInput.rangePicker?.opened && _rangeInput.rangePicker.id) || null`,\n    '[attr.min]': '_getMinDate() ? _dateAdapter.toIso8601(_getMinDate()) : null',\n    '[attr.max]': '_getMaxDate() ? _dateAdapter.toIso8601(_getMaxDate()) : null',\n    '(blur)': '_onBlur()',\n    'type': 'text',\n  },\n  providers: [\n    {provide: NG_VALUE_ACCESSOR, useExisting: MatStartDate, multi: true},\n    {provide: NG_VALIDATORS, useExisting: MatStartDate, multi: true},\n  ],\n  // These need to be specified explicitly, because some tooling doesn't\n  // seem to pick them up from the base class. See #20932.\n  outputs: ['dateChange', 'dateInput'],\n})\nexport class MatStartDate<D> extends MatDateRangeInputPartBase<D> {\n  /** Validator that checks that the start date isn't after the end date. */\n  private _startValidator: ValidatorFn = (control: AbstractControl): ValidationErrors | null => {\n    const start = this._dateAdapter.getValidDateOrNull(\n      this._dateAdapter.deserialize(control.value),\n    );\n    const end = this._model ? this._model.selection.end : null;\n    return !start || !end || this._dateAdapter.compareDate(start, end) <= 0\n      ? null\n      : {'matStartDateInvalid': {'end': end, 'actual': start}};\n  };\n\n  protected _validator = Validators.compose([...super._getValidators(), this._startValidator]);\n\n  protected override _register(): void {\n    this._rangeInput._startInput = this;\n  }\n\n  protected _getValueFromModel(modelValue: DateRange<D>) {\n    return modelValue.start;\n  }\n\n  protected override _shouldHandleChangeEvent(\n    change: DateSelectionModelChange<DateRange<D>>,\n  ): boolean {\n    if (!super._shouldHandleChangeEvent(change)) {\n      return false;\n    } else {\n      return !change.oldValue?.start\n        ? !!change.selection.start\n        : !change.selection.start ||\n            !!this._dateAdapter.compareDate(change.oldValue.start, change.selection.start);\n    }\n  }\n\n  protected _assignValueToModel(value: D | null) {\n    if (this._model) {\n      const range = new DateRange(value, this._model.selection.end);\n      this._model.updateSelection(range, this);\n      this._rangeInput._handleChildValueChange();\n    }\n  }\n\n  override _onKeydown(event: KeyboardEvent) {\n    const endInput = this._rangeInput._endInput;\n    const element = this._elementRef.nativeElement;\n    const isLtr = this._dir?.value !== 'rtl';\n\n    // If the user hits RIGHT (LTR) when at the end of the input (and no\n    // selection), move the cursor to the start of the end input.\n    if (\n      ((event.keyCode === RIGHT_ARROW && isLtr) || (event.keyCode === LEFT_ARROW && !isLtr)) &&\n      element.selectionStart === element.value.length &&\n      element.selectionEnd === element.value.length\n    ) {\n      event.preventDefault();\n      endInput._elementRef.nativeElement.setSelectionRange(0, 0);\n      endInput.focus();\n    } else {\n      super._onKeydown(event);\n    }\n  }\n}\n\n/** Input for entering the end date in a `mat-date-range-input`. */\n@Directive({\n  selector: 'input[matEndDate]',\n  host: {\n    'class': 'mat-end-date mat-date-range-input-inner',\n    '[disabled]': 'disabled',\n    '(input)': '_onInput($event.target.value)',\n    '(change)': '_onChange()',\n    '(keydown)': '_onKeydown($event)',\n    '[attr.aria-haspopup]': '_rangeInput.rangePicker ? \"dialog\" : null',\n    '[attr.aria-owns]': `_rangeInput._ariaOwns\n        ? _rangeInput._ariaOwns()\n        : (_rangeInput.rangePicker?.opened && _rangeInput.rangePicker.id) || null`,\n    '[attr.min]': '_getMinDate() ? _dateAdapter.toIso8601(_getMinDate()) : null',\n    '[attr.max]': '_getMaxDate() ? _dateAdapter.toIso8601(_getMaxDate()) : null',\n    '(blur)': '_onBlur()',\n    'type': 'text',\n  },\n  providers: [\n    {provide: NG_VALUE_ACCESSOR, useExisting: MatEndDate, multi: true},\n    {provide: NG_VALIDATORS, useExisting: MatEndDate, multi: true},\n  ],\n  // These need to be specified explicitly, because some tooling doesn't\n  // seem to pick them up from the base class. See #20932.\n  outputs: ['dateChange', 'dateInput'],\n})\nexport class MatEndDate<D> extends MatDateRangeInputPartBase<D> {\n  /** Validator that checks that the end date isn't before the start date. */\n  private _endValidator: ValidatorFn = (control: AbstractControl): ValidationErrors | null => {\n    const end = this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(control.value));\n    const start = this._model ? this._model.selection.start : null;\n    return !end || !start || this._dateAdapter.compareDate(end, start) >= 0\n      ? null\n      : {'matEndDateInvalid': {'start': start, 'actual': end}};\n  };\n\n  protected override _register(): void {\n    this._rangeInput._endInput = this;\n  }\n\n  protected _validator = Validators.compose([...super._getValidators(), this._endValidator]);\n\n  protected _getValueFromModel(modelValue: DateRange<D>) {\n    return modelValue.end;\n  }\n\n  protected override _shouldHandleChangeEvent(\n    change: DateSelectionModelChange<DateRange<D>>,\n  ): boolean {\n    if (!super._shouldHandleChangeEvent(change)) {\n      return false;\n    } else {\n      return !change.oldValue?.end\n        ? !!change.selection.end\n        : !change.selection.end ||\n            !!this._dateAdapter.compareDate(change.oldValue.end, change.selection.end);\n    }\n  }\n\n  protected _assignValueToModel(value: D | null) {\n    if (this._model) {\n      const range = new DateRange(this._model.selection.start, value);\n      this._model.updateSelection(range, this);\n    }\n  }\n\n  private _moveCaretToEndOfStartInput() {\n    const startInput = this._rangeInput._startInput._elementRef.nativeElement;\n    const value = startInput.value;\n\n    if (value.length > 0) {\n      startInput.setSelectionRange(value.length, value.length);\n    }\n\n    startInput.focus();\n  }\n\n  override _onKeydown(event: KeyboardEvent) {\n    const element = this._elementRef.nativeElement;\n    const isLtr = this._dir?.value !== 'rtl';\n\n    // If the user is pressing backspace on an empty end input, move focus back to the start.\n    if (event.keyCode === BACKSPACE && !element.value) {\n      this._moveCaretToEndOfStartInput();\n    }\n    // If the user hits LEFT (LTR) when at the start of the input (and no\n    // selection), move the cursor to the end of the start input.\n    else if (\n      ((event.keyCode === LEFT_ARROW && isLtr) || (event.keyCode === RIGHT_ARROW && !isLtr)) &&\n      element.selectionStart === 0 &&\n      element.selectionEnd === 0\n    ) {\n      event.preventDefault();\n      this._moveCaretToEndOfStartInput();\n    } else {\n      super._onKeydown(event);\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {ChangeDetectionStrategy, Component, ViewEncapsulation} from '@angular/core';\nimport {MatD<PERSON>pickerBase, MatDatepickerContent, MatDatepickerControl} from './datepicker-base';\nimport {MAT_RANGE_DATE_SELECTION_MODEL_PROVIDER, DateRange} from './date-selection-model';\nimport {MAT_CALENDAR_RANGE_STRATEGY_PROVIDER} from './date-range-selection-strategy';\n\n/**\n * Input that can be associated with a date range picker.\n * @docs-private\n */\nexport interface MatDateRangePickerInput<D> extends MatDatepickerControl<D> {\n  _getEndDateAccessibleName(): string | null;\n  _getStartDateAccessibleName(): string | null;\n  comparisonStart: D | null;\n  comparisonEnd: D | null;\n}\n\n// TODO(mmalerba): We use a component instead of a directive here so the user can use implicit\n// template reference variables (e.g. #d vs #d=\"matDateRangePicker\"). We can change this to a\n// directive if angular adds support for `exportAs: '$implicit'` on directives.\n/** Component responsible for managing the date range picker popup/dialog. */\n@Component({\n  selector: 'mat-date-range-picker',\n  template: '',\n  exportAs: 'matDateRangePicker',\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  encapsulation: ViewEncapsulation.None,\n  providers: [\n    MAT_RANGE_DATE_SELECTION_MODEL_PROVIDER,\n    MAT_CALENDAR_RANGE_STRATEGY_PROVIDER,\n    {provide: MatDatepickerBase, useExisting: MatDateRangePicker},\n  ],\n})\nexport class MatDateRangePicker<D> extends MatDatepickerBase<\n  MatDateRangePickerInput<D>,\n  DateRange<D>,\n  D\n> {\n  protected override _forwardContentValues(instance: MatDatepickerContent<DateRange<D>, D>) {\n    super._forwardContentValues(instance);\n\n    const input = this.datepickerInput;\n\n    if (input) {\n      instance.comparisonStart = input.comparisonStart;\n      instance.comparisonEnd = input.comparisonEnd;\n      instance.startDateAccessibleName = input._getStartDateAccessibleName();\n      instance.endDateAccessibleName = input._getEndDateAccessibleName();\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  AfterViewInit,\n  ChangeDetectionStrategy,\n  Component,\n  Directive,\n  OnDestroy,\n  TemplateRef,\n  ViewChild,\n  ViewContainerRef,\n  ViewEncapsulation,\n  inject,\n} from '@angular/core';\nimport {TemplatePortal} from '@angular/cdk/portal';\nimport {MatDatepickerBase, MatDatepickerControl} from './datepicker-base';\n\n/** Button that will close the datepicker and assign the current selection to the data model. */\n@Directive({\n  selector: '[matDatepickerApply], [matDateRangePickerApply]',\n  host: {'(click)': '_applySelection()'},\n})\nexport class MatDatepickerApply {\n  private _datepicker =\n    inject<MatDatepickerBase<MatDatepickerControl<any>, unknown>>(MatDatepickerBase);\n\n  constructor(...args: unknown[]);\n\n  constructor() {}\n\n  _applySelection() {\n    this._datepicker._applyPendingSelection();\n    this._datepicker.close();\n  }\n}\n\n/** Button that will close the datepicker and discard the current selection. */\n@Directive({\n  selector: '[matDatepickerCancel], [matDateRangePickerCancel]',\n  host: {'(click)': '_datepicker.close()'},\n})\nexport class MatDatepickerCancel {\n  _datepicker = inject<MatDatepickerBase<MatDatepickerControl<any>, unknown>>(MatDatepickerBase);\n\n  constructor(...args: unknown[]);\n  constructor() {}\n}\n\n/**\n * Container that can be used to project a row of action buttons\n * to the bottom of a datepicker or date range picker.\n */\n@Component({\n  selector: 'mat-datepicker-actions, mat-date-range-picker-actions',\n  styleUrl: 'datepicker-actions.css',\n  template: `\n    <ng-template>\n      <div class=\"mat-datepicker-actions\">\n        <ng-content></ng-content>\n      </div>\n    </ng-template>\n  `,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  encapsulation: ViewEncapsulation.None,\n})\nexport class MatDatepickerActions implements AfterViewInit, OnDestroy {\n  private _datepicker =\n    inject<MatDatepickerBase<MatDatepickerControl<any>, unknown>>(MatDatepickerBase);\n  private _viewContainerRef = inject(ViewContainerRef);\n\n  @ViewChild(TemplateRef) _template: TemplateRef<unknown>;\n  private _portal: TemplatePortal;\n\n  constructor(...args: unknown[]);\n  constructor() {}\n\n  ngAfterViewInit() {\n    this._portal = new TemplatePortal(this._template, this._viewContainerRef);\n    this._datepicker.registerActions(this._portal);\n  }\n\n  ngOnDestroy() {\n    this._datepicker.removeActions(this._portal);\n\n    // Needs to be null checked since we initialize it in `ngAfterViewInit`.\n    if (this._portal && this._portal.isAttached) {\n      this._portal?.detach();\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {A11yModule} from '@angular/cdk/a11y';\nimport {OverlayModule} from '@angular/cdk/overlay';\nimport {PortalModule} from '@angular/cdk/portal';\nimport {NgModule} from '@angular/core';\nimport {MatButtonModule} from '../button';\nimport {CdkScrollableModule} from '@angular/cdk/scrolling';\nimport {MatCommonModule} from '../core';\nimport {MatCalendar, MatCalendarHeader} from './calendar';\nimport {MatCalendarBody} from './calendar-body';\nimport {MatDatepicker} from './datepicker';\nimport {\n  MatDatepickerContent,\n  MAT_DATEPICKER_SCROLL_STRATEGY_FACTORY_PROVIDER,\n} from './datepicker-base';\nimport {MatDatepickerInput} from './datepicker-input';\nimport {MatDatepickerIntl} from './datepicker-intl';\nimport {MatDatepickerToggle, MatDatepickerToggleIcon} from './datepicker-toggle';\nimport {MatMonthView} from './month-view';\nimport {MatMultiYearView} from './multi-year-view';\nimport {MatYearView} from './year-view';\nimport {MatDateRangeInput} from './date-range-input';\nimport {MatStartDate, MatEndDate} from './date-range-input-parts';\nimport {MatDateRangePicker} from './date-range-picker';\nimport {MatDatepickerActions, MatDatepickerApply, MatDatepickerCancel} from './datepicker-actions';\n\n@NgModule({\n  imports: [\n    MatButtonModule,\n    OverlayModule,\n    A11yModule,\n    PortalModule,\n    MatCommonModule,\n    MatCalendar,\n    MatCalendarBody,\n    MatDatepicker,\n    MatDatepickerContent,\n    MatDatepickerInput,\n    MatDatepickerToggle,\n    MatDatepickerToggleIcon,\n    MatMonthView,\n    MatYearView,\n    MatMultiYearView,\n    MatCalendarHeader,\n    MatDateRangeInput,\n    MatStartDate,\n    MatEndDate,\n    MatDateRangePicker,\n    MatDatepickerActions,\n    MatDatepickerCancel,\n    MatDatepickerApply,\n  ],\n  exports: [\n    CdkScrollableModule,\n    MatCalendar,\n    MatCalendarBody,\n    MatDatepicker,\n    MatDatepickerContent,\n    MatDatepickerInput,\n    MatDatepickerToggle,\n    MatDatepickerToggleIcon,\n    MatMonthView,\n    MatYearView,\n    MatMultiYearView,\n    MatCalendarHeader,\n    MatDateRangeInput,\n    MatStartDate,\n    MatEndDate,\n    MatDateRangePicker,\n    MatDatepickerActions,\n    MatDatepickerCancel,\n    MatDatepickerApply,\n  ],\n  providers: [MatDatepickerIntl, MAT_DATEPICKER_SCROLL_STRATEGY_FACTORY_PROVIDER],\n})\nexport class MatDatepickerModule {}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/**\n * Animations used by the Material datepicker.\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nexport const matDatepickerAnimations: {\n  readonly transformPanel: any;\n  readonly fadeInCalendar: any;\n} = {\n  // Represents:\n  // trigger('transformPanel', [\n  //   transition(\n  //     'void => enter-dropdown',\n  //     animate(\n  //       '120ms cubic-bezier(0, 0, 0.2, 1)',\n  //       keyframes([\n  //         style({opacity: 0, transform: 'scale(1, 0.8)'}),\n  //         style({opacity: 1, transform: 'scale(1, 1)'}),\n  //       ]),\n  //     ),\n  //   ),\n  //   transition(\n  //     'void => enter-dialog',\n  //     animate(\n  //       '150ms cubic-bezier(0, 0, 0.2, 1)',\n  //       keyframes([\n  //         style({opacity: 0, transform: 'scale(0.7)'}),\n  //         style({transform: 'none', opacity: 1}),\n  //       ]),\n  //     ),\n  //   ),\n  //   transition('* => void', animate('100ms linear', style({opacity: 0}))),\n  // ])\n\n  /** Transforms the height of the datepicker's calendar. */\n  transformPanel: {\n    type: 7,\n    name: 'transformPanel',\n    definitions: [\n      {\n        type: 1,\n        expr: 'void => enter-dropdown',\n        animation: {\n          type: 4,\n          styles: {\n            type: 5,\n            steps: [\n              {type: 6, styles: {opacity: 0, transform: 'scale(1, 0.8)'}, offset: null},\n              {type: 6, styles: {opacity: 1, transform: 'scale(1, 1)'}, offset: null},\n            ],\n          },\n          timings: '120ms cubic-bezier(0, 0, 0.2, 1)',\n        },\n        options: null,\n      },\n      {\n        type: 1,\n        expr: 'void => enter-dialog',\n        animation: {\n          type: 4,\n          styles: {\n            type: 5,\n            steps: [\n              {type: 6, styles: {opacity: 0, transform: 'scale(0.7)'}, offset: null},\n              {type: 6, styles: {transform: 'none', opacity: 1}, offset: null},\n            ],\n          },\n          timings: '150ms cubic-bezier(0, 0, 0.2, 1)',\n        },\n        options: null,\n      },\n      {\n        type: 1,\n        expr: '* => void',\n        animation: {\n          type: 4,\n          styles: {type: 6, styles: {opacity: 0}, offset: null},\n          timings: '100ms linear',\n        },\n        options: null,\n      },\n    ],\n    options: {},\n  },\n\n  // Represents:\n  // trigger('fadeInCalendar', [\n  //   state('void', style({opacity: 0})),\n  //   state('enter', style({opacity: 1})),\n\n  //   // TODO(crisbeto): this animation should be removed since it isn't quite on spec, but we\n  //   // need to keep it until #12440 gets in, otherwise the exit animation will look glitchy.\n  //   transition('void => *', animate('120ms 100ms cubic-bezier(0.55, 0, 0.55, 0.2)')),\n  // ])\n\n  /** Fades in the content of the calendar. */\n  fadeInCalendar: {\n    type: 7,\n    name: 'fadeInCalendar',\n    definitions: [\n      {type: 0, name: 'void', styles: {type: 6, styles: {opacity: 0}, offset: null}},\n      {type: 0, name: 'enter', styles: {type: 6, styles: {opacity: 1}, offset: null}},\n      {\n        type: 1,\n        expr: 'void => *',\n        animation: {\n          type: 4,\n          styles: null,\n          timings: '120ms 100ms cubic-bezier(0.55, 0, 0.55, 0.2)',\n        },\n        options: null,\n      },\n    ],\n    options: {},\n  },\n};\n"], "names": ["uniqueIdCounter", "i1.DateAdapter", "observableOf"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQA;AACM,SAAU,0BAA0B,CAAC,QAAgB,EAAA;AACzD,IAAA,OAAO,KAAK,CACV,CAAwC,qCAAA,EAAA,QAAQ,CAAsC,oCAAA,CAAA;QACpF,CAAuE,qEAAA,CAAA;AACvE,QAAA,CAAA,sFAAA,CAAwF,CAC3F;AACH;;ACJA;MAEa,iBAAiB,CAAA;AAC5B;;;AAGG;AACM,IAAA,OAAO,GAAkB,IAAI,OAAO,EAAQ;;IAGrD,aAAa,GAAG,UAAU;;IAG1B,iBAAiB,GAAG,eAAe;;IAGnC,kBAAkB,GAAG,gBAAgB;;IAGrC,cAAc,GAAG,gBAAgB;;IAGjC,cAAc,GAAG,YAAY;;IAG7B,aAAa,GAAG,eAAe;;IAG/B,aAAa,GAAG,WAAW;;IAG3B,kBAAkB,GAAG,mBAAmB;;IAGxC,kBAAkB,GAAG,eAAe;;IAGpC,sBAAsB,GAAG,aAAa;;IAGtC,0BAA0B,GAAG,uBAAuB;AAEpD;;;;AAIG;IACH,cAAc,GAAG,YAAY;AAE7B;;;;AAIG;IACH,YAAY,GAAG,UAAU;AAEzB;;AAEG;IACH,mBAAmB,GAAG,kBAAkB;;IAGxC,eAAe,CAAC,KAAa,EAAE,GAAW,EAAA;AACxC,QAAA,OAAO,CAAG,EAAA,KAAK,CAAW,QAAA,EAAA,GAAG,EAAE;;;IAIjC,oBAAoB,CAAC,KAAa,EAAE,GAAW,EAAA;AAC7C,QAAA,OAAO,CAAG,EAAA,KAAK,CAAO,IAAA,EAAA,GAAG,EAAE;;uGAlElB,iBAAiB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;AAAjB,IAAA,OAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,iBAAiB,cADL,MAAM,EAAA,CAAA;;2FAClB,iBAAiB,EAAA,UAAA,EAAA,CAAA;kBAD7B,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC;;;AC8BhC,IAAIA,iBAAe,GAAG,CAAC;AAEvB;;;AAGG;MACU,eAAe,CAAA;AAIjB,IAAA,KAAA;AACA,IAAA,YAAA;AACA,IAAA,SAAA;AACA,IAAA,OAAA;AACA,IAAA,UAAA;AACA,IAAA,YAAA;AACA,IAAA,QAAA;IATA,EAAE,GAAGA,iBAAe,EAAE;AAE/B,IAAA,WAAA,CACS,KAAa,EACb,YAAoB,EACpB,SAAiB,EACjB,OAAgB,EAChB,UAAA,GAAwC,EAAE,EAC1C,YAAe,GAAA,KAAK,EACpB,QAAY,EAAA;QANZ,IAAK,CAAA,KAAA,GAAL,KAAK;QACL,IAAY,CAAA,YAAA,GAAZ,YAAY;QACZ,IAAS,CAAA,SAAA,GAAT,SAAS;QACT,IAAO,CAAA,OAAA,GAAP,OAAO;QACP,IAAU,CAAA,UAAA,GAAV,UAAU;QACV,IAAY,CAAA,YAAA,GAAZ,YAAY;QACZ,IAAQ,CAAA,QAAA,GAAR,QAAQ;;AAElB;AAQD;AACA,MAAM,2BAA2B,GAAG;AAClC,IAAA,OAAO,EAAE,KAAK;AACd,IAAA,OAAO,EAAE,IAAI;CACd;AAED;AACA,MAAM,4BAA4B,GAAG;AACnC,IAAA,OAAO,EAAE,IAAI;AACb,IAAA,OAAO,EAAE,IAAI;CACd;AAED;AACA,MAAM,mBAAmB,GAAG,EAAC,OAAO,EAAE,IAAI,EAAC;AAE3C;;;AAGG;MAaU,eAAe,CAAA;AAClB,IAAA,WAAW,GAAG,MAAM,CAA0B,UAAU,CAAC;AACzD,IAAA,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;AACxB,IAAA,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC;AAC5B,IAAA,KAAK,GAAG,MAAM,CAAC,iBAAiB,CAAC;AACjC,IAAA,cAAc;AAEtB;;;AAGG;AACK,IAAA,cAAc;AAEtB;;AAEG;IACK,gCAAgC,GAAG,KAAK;;AAGvC,IAAA,KAAK;;AAGL,IAAA,IAAI;;AAGJ,IAAA,UAAU;;AAGV,IAAA,UAAU;;AAGV,IAAA,QAAQ;;AAGR,IAAA,qBAAqB;;IAGrB,OAAO,GAAW,CAAC;;IAGnB,UAAU,GAAW,CAAC;IAE/B,kBAAkB,GAAA;AAChB,QAAA,IAAI,IAAI,CAAC,gCAAgC,EAAE;YACzC,IAAI,CAAC,gBAAgB,EAAE;AACvB,YAAA,IAAI,CAAC,gCAAgC,GAAG,KAAK;;;;IAKxC,OAAO,GAAY,KAAK;AAEjC;;;AAGG;IACM,eAAe,GAAW,CAAC;;AAG3B,IAAA,eAAe;;AAGf,IAAA,aAAa;;IAGb,YAAY,GAAkB,IAAI;;IAGlC,UAAU,GAAkB,IAAI;;AAGhC,IAAA,uBAAuB;;AAGvB,IAAA,qBAAqB;;AAGX,IAAA,mBAAmB,GAAG,IAAI,YAAY,EAAgC;;AAGtE,IAAA,aAAa,GAAG,IAAI,YAAY,EAEhD;AAEgB,IAAA,gBAAgB,GAAG,IAAI,YAAY,EAAgC;;AAGnE,IAAA,WAAW,GAAG,IAAI,YAAY,EAA2B;;AAGzD,IAAA,SAAS,GAAG,IAAI,YAAY,EAAkC;;AAGjF,IAAA,eAAe;;AAGf,IAAA,YAAY;;AAGZ,IAAA,UAAU;;AAGV,IAAA,iBAAiB;;AAGjB,IAAA,eAAe;;AAGf,IAAA,2BAA2B;;AAG3B,IAAA,yBAAyB;IAEjB,sBAAsB,GAAG,KAAK;AAE9B,IAAA,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC;AAEpC,IAAA,4BAA4B,GAAG,IAAI,CAAC,KAAK,CAAC,mBAAmB;AAE7D;;;;AAIG;AACH,IAAA,SAAS,GAAG,CAAC,GAAsB,KAAK,GAAG;AAI3C,IAAA,WAAA,GAAA;AACE,QAAA,MAAM,QAAQ,GAAG,MAAM,CAAC,SAAS,CAAC;AAClC,QAAA,MAAM,WAAW,GAAG,MAAM,CAAC,YAAY,CAAC;QACxC,IAAI,CAAC,iBAAiB,GAAG,WAAW,CAAC,KAAK,CAAC,0BAA0B,CAAC;QACtE,IAAI,CAAC,eAAe,GAAG,WAAW,CAAC,KAAK,CAAC,wBAAwB,CAAC;QAClE,IAAI,CAAC,2BAA2B,GAAG,WAAW,CAAC,KAAK,CAAC,qCAAqC,CAAC;QAC3F,IAAI,CAAC,yBAAyB,GAAG,WAAW,CAAC,KAAK,CAAC,mCAAmC,CAAC;QAEvF,MAAM,CAAC,sBAAsB,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC;AAE5D,QAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;AAClC,YAAA,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa;AAC9C,YAAA,MAAM,QAAQ,GAAG;;AAEf,gBAAA,qBAAqB,CACnB,QAAQ,EACR,OAAO,EACP,WAAW,EACX,IAAI,CAAC,iBAAiB,EACtB,2BAA2B,CAC5B;AACD,gBAAA,qBAAqB,CACnB,QAAQ,EACR,OAAO,EACP,YAAY,EACZ,IAAI,CAAC,aAAa,EAClB,4BAA4B,CAC7B;AACD,gBAAA,qBAAqB,CACnB,QAAQ,EACR,OAAO,EACP,OAAO,EACP,IAAI,CAAC,aAAa,EAClB,4BAA4B,CAC7B;AACD,gBAAA,qBAAqB,CACnB,QAAQ,EACR,OAAO,EACP,YAAY,EACZ,IAAI,CAAC,aAAa,EAClB,4BAA4B,CAC7B;AACD,gBAAA,qBAAqB,CACnB,QAAQ,EACR,OAAO,EACP,MAAM,EACN,IAAI,CAAC,aAAa,EAClB,4BAA4B,CAC7B;AACD,gBAAA,qBAAqB,CACnB,QAAQ,EACR,OAAO,EACP,WAAW,EACX,IAAI,CAAC,iBAAiB,EACtB,mBAAmB,CACpB;AACD,gBAAA,qBAAqB,CACnB,QAAQ,EACR,OAAO,EACP,YAAY,EACZ,IAAI,CAAC,iBAAiB,EACtB,mBAAmB,CACpB;aACF;AAED,YAAA,IAAI,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE;AAC5B,gBAAA,QAAQ,CAAC,IAAI,CACX,QAAQ,CAAC,MAAM,CAAC,QAAQ,EAAE,SAAS,EAAE,IAAI,CAAC,eAAe,CAAC,EAC1D,QAAQ,CAAC,MAAM,CAAC,QAAQ,EAAE,UAAU,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAC7D;;AAGH,YAAA,IAAI,CAAC,cAAc,GAAG,QAAQ;AAChC,SAAC,CAAC;;;IAIJ,YAAY,CAAC,IAAqB,EAAE,KAAiB,EAAA;;;AAGnD,QAAA,IAAI,IAAI,CAAC,sBAAsB,EAAE;YAC/B;;AAGF,QAAA,IAAI,IAAI,CAAC,OAAO,EAAE;AAChB,YAAA,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,EAAC,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,KAAK,EAAC,CAAC;;;IAI7D,qBAAqB,CAAC,IAAqB,EAAE,KAAiB,EAAA;AAC5D,QAAA,IAAI,IAAI,CAAC,OAAO,EAAE;AAChB,YAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAC,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,KAAK,EAAC,CAAC;;;;AAK1D,IAAA,WAAW,CAAC,KAAa,EAAA;QACvB,OAAO,IAAI,CAAC,UAAU,KAAK,KAAK,IAAI,IAAI,CAAC,QAAQ,KAAK,KAAK;;AAG7D,IAAA,WAAW,CAAC,OAAsB,EAAA;AAChC,QAAA,MAAM,aAAa,GAAG,OAAO,CAAC,SAAS,CAAC;AACxC,QAAA,MAAM,EAAC,IAAI,EAAE,OAAO,EAAC,GAAG,IAAI;AAE5B,QAAA,IAAI,OAAO,CAAC,MAAM,CAAC,IAAI,aAAa,EAAE;AACpC,YAAA,IAAI,CAAC,eAAe,GAAG,IAAI,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,OAAO,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC;;AAG7F,QAAA,IAAI,OAAO,CAAC,iBAAiB,CAAC,IAAI,aAAa,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;AACrE,YAAA,IAAI,CAAC,YAAY,GAAG,CAAA,EAAG,CAAC,EAAE,GAAG,IAAI,CAAC,eAAe,IAAI,OAAO,GAAG;;AAGjE,QAAA,IAAI,aAAa,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YACrC,IAAI,CAAC,UAAU,GAAG,CAAA,EAAG,GAAG,GAAG,OAAO,GAAG;;;IAIzC,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,OAAO,IAAI,OAAO,EAAE,CAAC;;;IAInD,aAAa,CAAC,QAAgB,EAAE,QAAgB,EAAA;QAC9C,IAAI,UAAU,GAAG,QAAQ,GAAG,IAAI,CAAC,OAAO,GAAG,QAAQ;;QAGnD,IAAI,QAAQ,EAAE;AACZ,YAAA,UAAU,IAAI,IAAI,CAAC,eAAe;;AAGpC,QAAA,OAAO,UAAU,IAAI,IAAI,CAAC,UAAU;;AAGtC;;;;;;;;;;;;;;;;;;;;;AAqBG;IACH,gBAAgB,CAAC,WAAW,GAAG,IAAI,EAAA;QACjC,eAAe,CACb,MAAK;YACH,UAAU,CAAC,MAAK;AACd,gBAAA,MAAM,UAAU,GAAuB,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,aAAa,CACjF,2BAA2B,CAC5B;gBAED,IAAI,UAAU,EAAE;oBACd,IAAI,CAAC,WAAW,EAAE;AAChB,wBAAA,IAAI,CAAC,cAAc,GAAG,IAAI;;oBAG5B,UAAU,CAAC,KAAK,EAAE;;AAEtB,aAAC,CAAC;SACH,EACD,EAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,EAAC,CAC3B;;;IAIH,wCAAwC,GAAA;AACtC,QAAA,IAAI,CAAC,gCAAgC,GAAG,IAAI;;;AAI9C,IAAA,aAAa,CAAC,KAAa,EAAA;AACzB,QAAA,OAAO,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,QAAQ,CAAC;;;AAIvD,IAAA,WAAW,CAAC,KAAa,EAAA;AACvB,QAAA,OAAO,KAAK,CAAC,KAAK,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,QAAQ,CAAC;;;AAIrD,IAAA,UAAU,CAAC,KAAa,EAAA;AACtB,QAAA,OAAO,SAAS,CAAC,KAAK,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC;;;AAIvE,IAAA,kBAAkB,CAAC,KAAa,EAAA;AAC9B,QAAA,OAAO,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,aAAa,CAAC;;;AAIjE,IAAA,wBAAwB,CAAC,KAAa,EAAE,QAAgB,EAAE,QAAgB,EAAA;QACxE,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE;AAC3F,YAAA,OAAO,KAAK;;AAGd,QAAA,IAAI,YAAY,GAAgC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC;QAEjF,IAAI,CAAC,YAAY,EAAE;YACjB,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;YAC3C,YAAY,GAAG,WAAW,IAAI,WAAW,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC;;QAGnE,OAAO,YAAY,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,YAAY,CAAC;;;AAIrE,IAAA,sBAAsB,CAAC,KAAa,EAAE,QAAgB,EAAE,QAAgB,EAAA;QACtE,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE;AACvF,YAAA,OAAO,KAAK;;AAGd,QAAA,IAAI,QAAQ,GAAgC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC;QAE7E,IAAI,CAAC,QAAQ,EAAE;YACb,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;AACvC,YAAA,QAAQ,GAAG,OAAO,IAAI,OAAO,CAAC,CAAC,CAAC;;QAGlC,OAAO,QAAQ,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,YAAY,CAAC;;;AAI/D,IAAA,gBAAgB,CAAC,KAAa,EAAA;AAC5B,QAAA,OAAO,KAAK,CAAC,KAAK,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,aAAa,CAAC;;;AAI/D,IAAA,oBAAoB,CAAC,KAAa,EAAA;AAChC,QAAA,OAAO,SAAS,CAAC,KAAK,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,OAAO,CAAC;;AAGjF;;;;;;;;;AASG;AACH,IAAA,sBAAsB,CAAC,KAAa,EAAA;;;AAGlC,QAAA,OAAO,IAAI,CAAC,eAAe,KAAK,IAAI,CAAC,aAAa,IAAI,KAAK,KAAK,IAAI,CAAC,eAAe;;;AAItF,IAAA,eAAe,CAAC,KAAa,EAAA;AAC3B,QAAA,OAAO,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,UAAU,CAAC;;;AAI3D,IAAA,aAAa,CAAC,KAAa,EAAA;AACzB,QAAA,OAAO,KAAK,CAAC,KAAK,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,UAAU,CAAC;;;AAIzD,IAAA,YAAY,CAAC,KAAa,EAAA;AACxB,QAAA,OAAO,SAAS,CAAC,KAAK,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,OAAO,CAAC;;;AAI3E,IAAA,eAAe,CAAC,KAAa,EAAA;AAC3B,QAAA,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;AACjB,YAAA,OAAO,IAAI;;AAGb,QAAA,IAAI,IAAI,CAAC,UAAU,KAAK,KAAK,IAAI,IAAI,CAAC,QAAQ,KAAK,KAAK,EAAE;YACxD,OAAO,CAAA,EAAG,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,eAAe,CAAA,CAAE;;AACrD,aAAA,IAAI,IAAI,CAAC,UAAU,KAAK,KAAK,EAAE;YACpC,OAAO,IAAI,CAAC,iBAAiB;;AACxB,aAAA,IAAI,IAAI,CAAC,QAAQ,KAAK,KAAK,EAAE;YAClC,OAAO,IAAI,CAAC,eAAe;;AAG7B,QAAA,IAAI,IAAI,CAAC,eAAe,KAAK,IAAI,IAAI,IAAI,CAAC,aAAa,KAAK,IAAI,EAAE;AAChE,YAAA,IAAI,KAAK,KAAK,IAAI,CAAC,eAAe,IAAI,KAAK,KAAK,IAAI,CAAC,aAAa,EAAE;gBAClE,OAAO,CAAA,EAAG,IAAI,CAAC,2BAA2B,IAAI,IAAI,CAAC,yBAAyB,CAAA,CAAE;;AACzE,iBAAA,IAAI,KAAK,KAAK,IAAI,CAAC,eAAe,EAAE;gBACzC,OAAO,IAAI,CAAC,2BAA2B;;AAClC,iBAAA,IAAI,KAAK,KAAK,IAAI,CAAC,aAAa,EAAE;gBACvC,OAAO,IAAI,CAAC,yBAAyB;;;AAIzC,QAAA,OAAO,IAAI;;AAGb;;;AAGG;AACK,IAAA,aAAa,GAAG,CAAC,KAAY,KAAI;QACvC,IAAI,IAAI,CAAC,cAAc,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,EAAE;AACjD,YAAA,IAAI,CAAC,cAAc,GAAG,KAAK;YAC3B;;;QAIF,IAAI,KAAK,CAAC,MAAM,IAAI,IAAI,CAAC,OAAO,EAAE;YAChC,MAAM,IAAI,GAAG,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,MAAqB,CAAC;YAElE,IAAI,IAAI,EAAE;AACR,gBAAA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,EAAC,KAAK,EAAE,IAAI,CAAC,OAAO,GAAG,IAAI,GAAG,IAAI,EAAE,KAAK,EAAC,CAAC,CAAC;;;AAGjG,KAAC;AAEO,IAAA,iBAAiB,GAAG,CAAC,KAAiB,KAAI;QAChD,IAAI,CAAC,IAAI,CAAC,OAAO;YAAE;AAEnB,QAAA,MAAM,MAAM,GAAG,oBAAoB,CAAC,KAAK,CAAC;AAC1C,QAAA,MAAM,IAAI,GAAG,MAAM,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAqB,CAAC,GAAG,IAAI;AAE5E,QAAA,IAAI,MAAM,KAAK,KAAK,CAAC,MAAM,EAAE;AAC3B,YAAA,IAAI,CAAC,sBAAsB,GAAG,IAAI;;;;AAKpC,QAAA,IAAI,cAAc,CAAC,KAAK,CAAC,MAAqB,CAAC,EAAE;YAC/C,KAAK,CAAC,cAAc,EAAE;;AAGxB,QAAA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,EAAC,KAAK,EAAE,IAAI,EAAE,OAAO,GAAG,IAAI,GAAG,IAAI,EAAE,KAAK,EAAC,CAAC,CAAC;AAC9F,KAAC;AAED;;;AAGG;AACK,IAAA,aAAa,GAAG,CAAC,KAAY,KAAI;;QAEvC,IAAI,IAAI,CAAC,UAAU,KAAK,IAAI,IAAI,IAAI,CAAC,OAAO,EAAE;AAC5C,YAAA,IAAI,KAAK,CAAC,IAAI,KAAK,MAAM,EAAE;AACzB,gBAAA,IAAI,CAAC,sBAAsB,GAAG,IAAI;;;;;YAMpC,IACE,KAAK,CAAC,MAAM;AACZ,gBAAA,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,MAAqB,CAAC;gBACrD,EACG,KAAoB,CAAC,aAAa;oBACnC,IAAI,CAAC,mBAAmB,CAAE,KAAoB,CAAC,aAA4B,CAAC,CAC7E,EACD;gBACA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,EAAC,KAAK,EAAE,IAAI,EAAE,KAAK,EAAC,CAAC,CAAC;;;AAG3E,KAAC;AAED;;;AAGG;AACK,IAAA,iBAAiB,GAAG,CAAC,KAAY,KAAI;QAC3C,IAAI,CAAC,IAAI,CAAC,OAAO;YAAE;AAEnB,QAAA,IAAI,CAAC,sBAAsB,GAAG,KAAK;;AAEnC,QAAA,MAAM,IAAI,GAAG,KAAK,CAAC,MAAM,IAAI,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,MAAqB,CAAC;AAClF,QAAA,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE;YAChD;;AAGF,QAAA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAK;AACpB,YAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;gBACpB,KAAK,EAAE,IAAI,CAAC,QAAQ;gBACpB,KAAK;AACN,aAAA,CAAC;AACJ,SAAC,CAAC;AACJ,KAAC;;AAGO,IAAA,eAAe,GAAG,CAAC,KAAY,KAAI;QACzC,IAAI,CAAC,IAAI,CAAC,OAAO;YAAE;QAEnB,MAAM,WAAW,GAAG,cAAc,CAAC,KAAK,CAAC,MAAqB,CAAC;QAC/D,IAAI,CAAC,WAAW,EAAE;;AAEhB,YAAA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAK;AACpB,gBAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAC,KAAK,EAAE,IAAI,EAAE,KAAK,EAAC,CAAC;AAC3C,aAAC,CAAC;YACF;;AAGF,QAAA,IAAI,WAAW,CAAC,OAAO,CAAC,oBAAoB,CAAC,KAAK,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE;;;YAGhF;;AAGF,QAAA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAK;YACpB,MAAM,IAAI,GAAG,IAAI,CAAC,mBAAmB,CAAC,WAAW,CAAC;AAClD,YAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAC,KAAK,EAAE,IAAI,EAAE,QAAQ,IAAI,IAAI,EAAE,KAAK,EAAC,CAAC;AAC7D,SAAC,CAAC;AACJ,KAAC;;AAGO,IAAA,gBAAgB,GAAG,CAAC,KAAiB,KAAI;AAC/C,QAAA,MAAM,MAAM,GAAG,oBAAoB,CAAC,KAAK,CAAC;QAE1C,IAAI,MAAM,EAAE;AACV,YAAA,IAAI,CAAC,eAAe,CAAC,EAAC,MAAM,EAAqB,CAAC;;AAEtD,KAAC;;AAGO,IAAA,mBAAmB,CAAC,OAAoB,EAAA;AAC9C,QAAA,MAAM,IAAI,GAAG,cAAc,CAAC,OAAO,CAAC;QAEpC,IAAI,IAAI,EAAE;YACR,MAAM,GAAG,GAAG,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC;YAC7C,MAAM,GAAG,GAAG,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC;AAE7C,YAAA,IAAI,GAAG,IAAI,GAAG,EAAE;AACd,gBAAA,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;;;AAIlD,QAAA,OAAO,IAAI;;uGApjBF,eAAe,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAf,eAAe,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,qBAAA,EAAA,MAAA,EAAA,EAAA,KAAA,EAAA,OAAA,EAAA,IAAA,EAAA,MAAA,EAAA,UAAA,EAAA,YAAA,EAAA,UAAA,EAAA,YAAA,EAAA,QAAA,EAAA,UAAA,EAAA,qBAAA,EAAA,uBAAA,EAAA,OAAA,EAAA,SAAA,EAAA,UAAA,EAAA,YAAA,EAAA,OAAA,EAAA,SAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,aAAA,EAAA,eAAA,EAAA,YAAA,EAAA,cAAA,EAAA,UAAA,EAAA,YAAA,EAAA,uBAAA,EAAA,yBAAA,EAAA,qBAAA,EAAA,uBAAA,EAAA,EAAA,OAAA,EAAA,EAAA,mBAAA,EAAA,qBAAA,EAAA,aAAA,EAAA,eAAA,EAAA,gBAAA,EAAA,kBAAA,EAAA,WAAA,EAAA,aAAA,EAAA,SAAA,EAAA,WAAA,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,mBAAA,EAAA,EAAA,QAAA,EAAA,CAAA,iBAAA,CAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,ECnG5B,05JAkGA,EAAA,MAAA,EAAA,CAAA,02TAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EDDY,OAAO,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAEN,eAAe,EAAA,UAAA,EAAA,CAAA;kBAZ3B,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,qBAAqB,EAGzB,IAAA,EAAA;AACJ,wBAAA,OAAO,EAAE,mBAAmB;AAC7B,qBAAA,EAAA,QAAA,EACS,iBAAiB,EAAA,aAAA,EACZ,iBAAiB,CAAC,IAAI,EAAA,eAAA,EACpB,uBAAuB,CAAC,MAAM,EAAA,OAAA,EACtC,CAAC,OAAO,CAAC,EAAA,QAAA,EAAA,05JAAA,EAAA,MAAA,EAAA,CAAA,02TAAA,CAAA,EAAA;wDAqBT,KAAK,EAAA,CAAA;sBAAb;gBAGQ,IAAI,EAAA,CAAA;sBAAZ;gBAGQ,UAAU,EAAA,CAAA;sBAAlB;gBAGQ,UAAU,EAAA,CAAA;sBAAlB;gBAGQ,QAAQ,EAAA,CAAA;sBAAhB;gBAGQ,qBAAqB,EAAA,CAAA;sBAA7B;gBAGQ,OAAO,EAAA,CAAA;sBAAf;gBAGQ,UAAU,EAAA,CAAA;sBAAlB;gBAUQ,OAAO,EAAA,CAAA;sBAAf;gBAMQ,eAAe,EAAA,CAAA;sBAAvB;gBAGQ,eAAe,EAAA,CAAA;sBAAvB;gBAGQ,aAAa,EAAA,CAAA;sBAArB;gBAGQ,YAAY,EAAA,CAAA;sBAApB;gBAGQ,UAAU,EAAA,CAAA;sBAAlB;gBAGQ,uBAAuB,EAAA,CAAA;sBAA/B;gBAGQ,qBAAqB,EAAA,CAAA;sBAA7B;gBAGkB,mBAAmB,EAAA,CAAA;sBAArC;gBAGkB,aAAa,EAAA,CAAA;sBAA/B;gBAIkB,gBAAgB,EAAA,CAAA;sBAAlC;gBAGkB,WAAW,EAAA,CAAA;sBAA7B;gBAGkB,SAAS,EAAA,CAAA;sBAA3B;;AA8dH;AACA,SAAS,WAAW,CAAC,IAA6B,EAAA;AAChD,IAAA,OAAO,IAAI,EAAE,QAAQ,KAAK,IAAI;AAChC;AAEA;;;AAGG;AACH,SAAS,cAAc,CAAC,OAAoB,EAAA;AAC1C,IAAA,IAAI,IAA6B;AACjC,IAAA,IAAI,WAAW,CAAC,OAAO,CAAC,EAAE;QACxB,IAAI,GAAG,OAAO;;AACT,SAAA,IAAI,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE;AAC1C,QAAA,IAAI,GAAG,OAAO,CAAC,UAAyB;;SACnC,IAAI,WAAW,CAAC,OAAO,CAAC,UAAU,EAAE,UAAU,CAAC,EAAE;AACtD,QAAA,IAAI,GAAG,OAAO,CAAC,UAAW,CAAC,UAAyB;;AAGtD,IAAA,OAAO,IAAI,EAAE,YAAY,CAAC,cAAc,CAAC,IAAI,IAAI,GAAG,IAAI,GAAG,IAAI;AACjE;AAEA;AACA,SAAS,OAAO,CAAC,KAAa,EAAE,KAAoB,EAAE,GAAkB,EAAA;AACtE,IAAA,OAAO,GAAG,KAAK,IAAI,IAAI,KAAK,KAAK,GAAG,IAAI,KAAK,GAAG,GAAG,IAAI,KAAK,KAAK,KAAK;AACxE;AAEA;AACA,SAAS,KAAK,CAAC,KAAa,EAAE,KAAoB,EAAE,GAAkB,EAAA;AACpE,IAAA,OAAO,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,GAAG,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,KAAK,GAAG;AAC3E;AAEA;AACA,SAAS,SAAS,CAChB,KAAa,EACb,KAAoB,EACpB,GAAkB,EAClB,YAAqB,EAAA;AAErB,IAAA,QACE,YAAY;AACZ,QAAA,KAAK,KAAK,IAAI;AACd,QAAA,GAAG,KAAK,IAAI;AACZ,QAAA,KAAK,KAAK,GAAG;AACb,QAAA,KAAK,IAAI,KAAK;QACd,KAAK,IAAI,GAAG;AAEhB;AAEA;;;AAGG;AACH,SAAS,oBAAoB,CAAC,KAAiB,EAAA;IAC7C,MAAM,aAAa,GAAG,KAAK,CAAC,cAAc,CAAC,CAAC,CAAC;AAC7C,IAAA,OAAO,QAAQ,CAAC,gBAAgB,CAAC,aAAa,CAAC,OAAO,EAAE,aAAa,CAAC,OAAO,CAAC;AAChF;;AEvsBA;MACa,SAAS,CAAA;AAUT,IAAA,KAAA;AAEA,IAAA,GAAA;AAXX;;;AAGG;;AAEK,IAAA,6BAA6B;AAErC,IAAA,WAAA;;IAEW,KAAe;;IAEf,GAAa,EAAA;QAFb,IAAK,CAAA,KAAA,GAAL,KAAK;QAEL,IAAG,CAAA,GAAA,GAAH,GAAG;;AAEf;AAuBD;;;AAGG;MAEmB,qBAAqB,CAAA;AAU9B,IAAA,SAAA;AACC,IAAA,QAAA;AARK,IAAA,iBAAiB,GAAG,IAAI,OAAO,EAA+B;;AAG/E,IAAA,gBAAgB,GAA4C,IAAI,CAAC,iBAAiB;AAElF,IAAA,WAAA;;AAEW,IAAA,SAAY,EACX,QAAwB,EAAA;QADzB,IAAS,CAAA,SAAA,GAAT,SAAS;QACR,IAAQ,CAAA,QAAA,GAAR,QAAQ;AAElB,QAAA,IAAI,CAAC,SAAS,GAAG,SAAS;;AAG5B;;;;AAIG;IACH,eAAe,CAAC,KAAQ,EAAE,MAAe,EAAA;AACvC,QAAA,MAAM,QAAQ,GAAI,IAAuB,CAAC,SAAS;AAClD,QAAA,IAAuB,CAAC,SAAS,GAAG,KAAK;AAC1C,QAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAC,SAAS,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAC,CAAC;;IAGnE,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE;;AAGzB,IAAA,oBAAoB,CAAC,IAAO,EAAA;AACpC,QAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC;;uGAhCtD,qBAAqB,EAAA,IAAA,EAAA,SAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;2GAArB,qBAAqB,EAAA,CAAA;;2FAArB,qBAAqB,EAAA,UAAA,EAAA,CAAA;kBAD1C;;AAiDD;;;AAGG;AAEG,MAAO,2BAA+B,SAAQ,qBAAkC,CAAA;AACpF,IAAA,WAAA,CAAY,OAAuB,EAAA;AACjC,QAAA,KAAK,CAAC,IAAI,EAAE,OAAO,CAAC;;AAGtB;;;AAGG;AACH,IAAA,GAAG,CAAC,IAAc,EAAA;AAChB,QAAA,KAAK,CAAC,eAAe,CAAC,IAAI,EAAE,IAAI,CAAC;;;IAInC,OAAO,GAAA;AACL,QAAA,OAAO,IAAI,CAAC,SAAS,IAAI,IAAI,IAAI,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,SAAS,CAAC;;AAG5E;;;AAGG;IACH,UAAU,GAAA;AACR,QAAA,OAAO,IAAI,CAAC,SAAS,IAAI,IAAI;;;IAI/B,KAAK,GAAA;QACH,MAAM,KAAK,GAAG,IAAI,2BAA2B,CAAI,IAAI,CAAC,QAAQ,CAAC;QAC/D,KAAK,CAAC,eAAe,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC;AAC3C,QAAA,OAAO,KAAK;;uGA9BH,2BAA2B,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAAC,WAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;2GAA3B,2BAA2B,EAAA,CAAA;;2FAA3B,2BAA2B,EAAA,UAAA,EAAA,CAAA;kBADvC;;AAmCD;;;AAGG;AAEG,MAAO,0BAA8B,SAAQ,qBAAsC,CAAA;AACvF,IAAA,WAAA,CAAY,OAAuB,EAAA;QACjC,KAAK,CAAC,IAAI,SAAS,CAAI,IAAI,EAAE,IAAI,CAAC,EAAE,OAAO,CAAC;;AAG9C;;;;AAIG;AACH,IAAA,GAAG,CAAC,IAAc,EAAA;QAChB,IAAI,EAAC,KAAK,EAAE,GAAG,EAAC,GAAG,IAAI,CAAC,SAAS;AAEjC,QAAA,IAAI,KAAK,IAAI,IAAI,EAAE;YACjB,KAAK,GAAG,IAAI;;AACP,aAAA,IAAI,GAAG,IAAI,IAAI,EAAE;YACtB,GAAG,GAAG,IAAI;;aACL;YACL,KAAK,GAAG,IAAI;YACZ,GAAG,GAAG,IAAI;;AAGZ,QAAA,KAAK,CAAC,eAAe,CAAC,IAAI,SAAS,CAAI,KAAK,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC;;;IAI3D,OAAO,GAAA;QACL,MAAM,EAAC,KAAK,EAAE,GAAG,EAAC,GAAG,IAAI,CAAC,SAAS;;QAGnC,IAAI,KAAK,IAAI,IAAI,IAAI,GAAG,IAAI,IAAI,EAAE;AAChC,YAAA,OAAO,IAAI;;;QAIb,IAAI,KAAK,IAAI,IAAI,IAAI,GAAG,IAAI,IAAI,EAAE;AAChC,YAAA,QACE,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC;AAChC,gBAAA,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC;AAC9B,gBAAA,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,KAAK,EAAE,GAAG,CAAC,IAAI,CAAC;;;AAK9C,QAAA,QACE,CAAC,KAAK,IAAI,IAAI,IAAI,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC;AAClD,aAAC,GAAG,IAAI,IAAI,IAAI,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC;;AAInD;;;AAGG;IACH,UAAU,GAAA;AACR,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,IAAI,IAAI,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,IAAI,IAAI;;;IAInE,KAAK,GAAA;QACH,MAAM,KAAK,GAAG,IAAI,0BAA0B,CAAI,IAAI,CAAC,QAAQ,CAAC;QAC9D,KAAK,CAAC,eAAe,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC;AAC3C,QAAA,OAAO,KAAK;;uGA9DH,0BAA0B,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAAA,WAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;2GAA1B,0BAA0B,EAAA,CAAA;;2FAA1B,0BAA0B,EAAA,UAAA,EAAA,CAAA;kBADtC;;AAmED;;;;AAIG;AACa,SAAA,uCAAuC,CACrD,MAA4C,EAC5C,OAA6B,EAAA;AAE7B,IAAA,OAAO,MAAM,IAAI,IAAI,2BAA2B,CAAC,OAAO,CAAC;AAC3D;AAEA;;;;;AAKG;AACU,MAAA,wCAAwC,GAAoB;AACvE,IAAA,OAAO,EAAE,qBAAqB;AAC9B,IAAA,IAAI,EAAE,CAAC,CAAC,IAAI,QAAQ,EAAE,EAAE,IAAI,QAAQ,EAAE,EAAE,qBAAqB,CAAC,EAAE,WAAW,CAAC;AAC5E,IAAA,UAAU,EAAE,uCAAuC;;AAGrD;;;;AAIG;AACa,SAAA,sCAAsC,CACpD,MAA4C,EAC5C,OAA6B,EAAA;AAE7B,IAAA,OAAO,MAAM,IAAI,IAAI,0BAA0B,CAAC,OAAO,CAAC;AAC1D;AAEA;;;;;AAKG;AACU,MAAA,uCAAuC,GAAoB;AACtE,IAAA,OAAO,EAAE,qBAAqB;AAC9B,IAAA,IAAI,EAAE,CAAC,CAAC,IAAI,QAAQ,EAAE,EAAE,IAAI,QAAQ,EAAE,EAAE,qBAAqB,CAAC,EAAE,WAAW,CAAC;AAC5E,IAAA,UAAU,EAAE,sCAAsC;;;ACtPpD;MACa,iCAAiC,GAAG,IAAI,cAAc,CAEjE,mCAAmC;AA2CrC;MAEa,+BAA+B,CAAA;AACtB,IAAA,YAAA;AAApB,IAAA,WAAA,CAAoB,YAA4B,EAAA;QAA5B,IAAY,CAAA,YAAA,GAAZ,YAAY;;IAEhC,iBAAiB,CAAC,IAAO,EAAE,YAA0B,EAAA;AACnD,QAAA,IAAI,EAAC,KAAK,EAAE,GAAG,EAAC,GAAG,YAAY;AAE/B,QAAA,IAAI,KAAK,IAAI,IAAI,EAAE;YACjB,KAAK,GAAG,IAAI;;AACP,aAAA,IAAI,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,EAAE;YACjF,GAAG,GAAG,IAAI;;aACL;YACL,KAAK,GAAG,IAAI;YACZ,GAAG,GAAG,IAAI;;AAGZ,QAAA,OAAO,IAAI,SAAS,CAAI,KAAK,EAAE,GAAG,CAAC;;IAGrC,aAAa,CAAC,UAAoB,EAAE,YAA0B,EAAA;QAC5D,IAAI,KAAK,GAAa,IAAI;QAC1B,IAAI,GAAG,GAAa,IAAI;QAExB,IAAI,YAAY,CAAC,KAAK,IAAI,CAAC,YAAY,CAAC,GAAG,IAAI,UAAU,EAAE;AACzD,YAAA,KAAK,GAAG,YAAY,CAAC,KAAK;YAC1B,GAAG,GAAG,UAAU;;AAGlB,QAAA,OAAO,IAAI,SAAS,CAAI,KAAK,EAAE,GAAG,CAAC;;AAGrC,IAAA,UAAU,CAAC,UAAa,EAAE,aAA2B,EAAE,OAAU,EAAA;AAC/D,QAAA,IAAI,KAAK,GAAG,aAAa,CAAC,KAAK;AAC/B,QAAA,IAAI,GAAG,GAAG,aAAa,CAAC,GAAG;AAE3B,QAAA,IAAI,CAAC,KAAK,IAAI,CAAC,GAAG,EAAE;;AAElB,YAAA,OAAO,IAAI;;AAGb,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,YAAY;AAEjC,QAAA,MAAM,OAAO,GAAG,OAAO,CAAC,WAAW,CAAC,KAAK,EAAE,GAAG,CAAC,KAAK,CAAC;AACrD,QAAA,MAAM,SAAS,GAAG,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC;AACxE,QAAA,MAAM,UAAU,GAAG,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC;AAC3E,QAAA,MAAM,QAAQ,GAAG,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC;AAEvE,QAAA,IAAI,OAAO,IAAI,OAAO,CAAC,QAAQ,CAAC,UAAU,EAAE,aAAa,CAAC,KAAK,CAAC,EAAE;YAChE,KAAK,GAAG,OAAO;YACf,IAAI,OAAO,CAAC,WAAW,CAAC,OAAO,EAAE,GAAG,CAAC,GAAG,CAAC,EAAE;gBACzC,GAAG,GAAG,OAAO,CAAC,gBAAgB,CAAC,GAAG,EAAE,SAAS,CAAC;gBAC9C,GAAG,GAAG,OAAO,CAAC,iBAAiB,CAAC,GAAG,EAAE,UAAU,CAAC;gBAChD,GAAG,GAAG,OAAO,CAAC,eAAe,CAAC,GAAG,EAAE,QAAQ,CAAC;;;AAEzC,aAAA,IAAI,OAAO,IAAI,OAAO,CAAC,QAAQ,CAAC,UAAU,EAAE,aAAa,CAAC,GAAG,CAAC,EAAE;YACrE,GAAG,GAAG,OAAO;YACb,IAAI,OAAO,CAAC,WAAW,CAAC,OAAO,EAAE,KAAK,CAAC,GAAG,CAAC,EAAE;gBAC3C,KAAK,GAAG,OAAO,CAAC,gBAAgB,CAAC,KAAK,EAAE,SAAS,CAAC;gBAClD,KAAK,GAAG,OAAO,CAAC,iBAAiB,CAAC,KAAK,EAAE,UAAU,CAAC;gBACpD,KAAK,GAAG,OAAO,CAAC,eAAe,CAAC,KAAK,EAAE,QAAQ,CAAC;;;aAE7C;YACL,KAAK,GAAG,OAAO,CAAC,gBAAgB,CAAC,KAAK,EAAE,SAAS,CAAC;YAClD,KAAK,GAAG,OAAO,CAAC,iBAAiB,CAAC,KAAK,EAAE,UAAU,CAAC;YACpD,KAAK,GAAG,OAAO,CAAC,eAAe,CAAC,KAAK,EAAE,QAAQ,CAAC;YAChD,GAAG,GAAG,OAAO,CAAC,gBAAgB,CAAC,GAAG,EAAE,SAAS,CAAC;YAC9C,GAAG,GAAG,OAAO,CAAC,iBAAiB,CAAC,GAAG,EAAE,UAAU,CAAC;YAChD,GAAG,GAAG,OAAO,CAAC,eAAe,CAAC,GAAG,EAAE,QAAQ,CAAC;;AAG9C,QAAA,OAAO,IAAI,SAAS,CAAI,KAAK,EAAE,GAAG,CAAC;;uGArE1B,+BAA+B,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAAA,WAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;2GAA/B,+BAA+B,EAAA,CAAA;;2FAA/B,+BAA+B,EAAA,UAAA,EAAA,CAAA;kBAD3C;;AA0ED;;;;AAIG;AACa,SAAA,4CAA4C,CAC1D,MAA8C,EAC9C,OAA6B,EAAA;AAE7B,IAAA,OAAO,MAAM,IAAI,IAAI,+BAA+B,CAAC,OAAO,CAAC;AAC/D;AAEA;;;;AAIG;AACI,MAAM,oCAAoC,GAAoB;AACnE,IAAA,OAAO,EAAE,iCAAiC;AAC1C,IAAA,IAAI,EAAE,CAAC,CAAC,IAAI,QAAQ,EAAE,EAAE,IAAI,QAAQ,EAAE,EAAE,iCAAiC,CAAC,EAAE,WAAW,CAAC;AACxF,IAAA,UAAU,EAAE,4CAA4C;CACzD;;ACnGD,MAAM,aAAa,GAAG,CAAC;AAEvB,IAAI,eAAe,GAAG,CAAC;AAEvB;;;AAGG;MASU,YAAY,CAAA;AACd,IAAA,kBAAkB,GAAG,MAAM,CAAC,iBAAiB,CAAC;IAC/C,YAAY,GAAG,MAAM,CAAiB,gBAAgB,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAE;IAClF,YAAY,GAAG,MAAM,CAAiB,WAAW,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAE;IAC7D,IAAI,GAAG,MAAM,CAAC,cAAc,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;IAC/C,cAAc,GAAG,MAAM,CAC7B,iCAAiC,EACjC,EAAC,QAAQ,EAAE,IAAI,EAAC,CACjB;AAEO,IAAA,qBAAqB,GAAG,YAAY,CAAC,KAAK;;AAG1C,IAAA,oBAAoB;AAE5B;;AAEG;AACH,IAAA,IACI,UAAU,GAAA;QACZ,OAAO,IAAI,CAAC,WAAW;;IAEzB,IAAI,UAAU,CAAC,KAAQ,EAAA;AACrB,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,WAAW;AACtC,QAAA,MAAM,SAAS,GACb,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;AAC1E,YAAA,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE;AAC3B,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC;AACrF,QAAA,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,aAAa,EAAE,IAAI,CAAC,WAAW,CAAC,EAAE;YAC/D,IAAI,CAAC,KAAK,EAAE;;;AAGR,IAAA,WAAW;;AAGnB,IAAA,IACI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,SAAS;;IAEvB,IAAI,QAAQ,CAAC,KAA8B,EAAA;AACzC,QAAA,IAAI,KAAK,YAAY,SAAS,EAAE;AAC9B,YAAA,IAAI,CAAC,SAAS,GAAG,KAAK;;aACjB;AACL,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;;AAG7F,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC;;AAEzB,IAAA,SAAS;;AAGjB,IAAA,IACI,OAAO,GAAA;QACT,OAAO,IAAI,CAAC,QAAQ;;IAEtB,IAAI,OAAO,CAAC,KAAe,EAAA;AACzB,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;;AAEpF,IAAA,QAAQ;;AAGhB,IAAA,IACI,OAAO,GAAA;QACT,OAAO,IAAI,CAAC,QAAQ;;IAEtB,IAAI,OAAO,CAAC,KAAe,EAAA;AACzB,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;;AAEpF,IAAA,QAAQ;;AAGP,IAAA,UAAU;;AAGV,IAAA,SAAS;;AAGT,IAAA,eAAe;;AAGf,IAAA,aAAa;;AAGb,IAAA,uBAAuB;;AAGvB,IAAA,qBAAqB;;IAGrB,UAAU,GAAmC,IAAI;;AAGvC,IAAA,cAAc,GAA2B,IAAI,YAAY,EAAY;;AAGrE,IAAA,cAAc,GAC/B,IAAI,YAAY,EAAkC;;AAGjC,IAAA,WAAW,GAAG,IAAI,YAAY,EAA2B;AAE5E;;;AAGG;AACgB,IAAA,SAAS,GAAG,IAAI,YAAY,EAA6C;;AAGzE,IAAA,gBAAgB,GAAoB,IAAI,YAAY,EAAK;;AAGhD,IAAA,gBAAgB;;AAG5C,IAAA,WAAW;;AAGX,IAAA,MAAM;;AAGN,IAAA,gBAAgB;;AAGhB,IAAA,WAAW;;AAGX,IAAA,SAAS;;AAGT,IAAA,qBAAqB;;AAGrB,IAAA,mBAAmB;;AAGnB,IAAA,aAAa;;AAGb,IAAA,WAAW;;AAGX,IAAA,QAAQ;;AAGR,IAAA,UAAU;;AAGV,IAAA,SAAS;AAIT,IAAA,WAAA,GAAA;QACE,MAAM,CAAC,sBAAsB,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC;AAC1D,QAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;AACjD,YAAA,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;AACtB,gBAAA,MAAM,0BAA0B,CAAC,aAAa,CAAC;;AAEjD,YAAA,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;AACtB,gBAAA,MAAM,0BAA0B,CAAC,kBAAkB,CAAC;;;QAIxD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE;;IAG9C,kBAAkB,GAAA;AAChB,QAAA,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,YAAY,CAAC;AAC5C,aAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;aACpB,SAAS,CAAC,MAAM,IAAI,CAAC,KAAK,EAAE,CAAC;;AAGlC,IAAA,WAAW,CAAC,OAAsB,EAAA;QAChC,MAAM,gBAAgB,GAAG,OAAO,CAAC,iBAAiB,CAAC,IAAI,OAAO,CAAC,eAAe,CAAC;AAE/E,QAAA,IAAI,gBAAgB,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE;AACrD,YAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC;;QAGhC,IAAI,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YAC7C,IAAI,CAAC,aAAa,EAAE;;;IAIxB,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,qBAAqB,CAAC,WAAW,EAAE;;;AAI1C,IAAA,aAAa,CAAC,KAAmC,EAAA;AAC/C,QAAA,MAAM,IAAI,GAAG,KAAK,CAAC,KAAK;QACxB,MAAM,YAAY,GAAG,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC;AACtD,QAAA,IAAI,cAA6B;AACjC,QAAA,IAAI,YAA2B;AAE/B,QAAA,IAAI,IAAI,CAAC,SAAS,YAAY,SAAS,EAAE;YACvC,cAAc,GAAG,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;YAClE,YAAY,GAAG,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC;;aACzD;YACL,cAAc,GAAG,YAAY,GAAG,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,SAAS,CAAC;;QAG7E,IAAI,cAAc,KAAK,IAAI,IAAI,YAAY,KAAK,IAAI,EAAE;AACpD,YAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,YAAY,CAAC;;AAGxC,QAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAC,KAAK,EAAE,YAAY,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,EAAC,CAAC;QACnE,IAAI,CAAC,aAAa,EAAE;AACpB,QAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE;;AAGxC;;;;;;;;;AASG;AACH,IAAA,iBAAiB,CAAC,KAAmC,EAAA;AACnD,QAAA,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK;AACzB,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,WAAW;QACtC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC;AAEpD,QAAA,IAAI,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,aAAa,EAAE,IAAI,CAAC,UAAU,CAAC,EAAE;YACjE,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC;;;;AAKhD,IAAA,0BAA0B,CAAC,KAAoB,EAAA;;;;AAK7C,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,WAAW;AACtC,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE;AAE3B,QAAA,QAAQ,KAAK,CAAC,OAAO;AACnB,YAAA,KAAK,UAAU;gBACb,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;gBACrF;AACF,YAAA,KAAK,WAAW;gBACd,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;gBACrF;AACF,YAAA,KAAK,QAAQ;AACX,gBAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;gBACzE;AACF,YAAA,KAAK,UAAU;AACb,gBAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;gBACxE;AACF,YAAA,KAAK,IAAI;gBACP,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,eAAe,CACjD,IAAI,CAAC,WAAW,EAChB,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAChD;gBACD;AACF,YAAA,KAAK,GAAG;gBACN,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,eAAe,CACjD,IAAI,CAAC,WAAW,EAChB,IAAI,CAAC,YAAY,CAAC,iBAAiB,CAAC,IAAI,CAAC,WAAW,CAAC;oBACnD,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAC9C;gBACD;AACF,YAAA,KAAK,OAAO;AACV,gBAAA,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;AACtB,sBAAE,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;AACzD,sBAAE,IAAI,CAAC,YAAY,CAAC,iBAAiB,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;gBAC7D;AACF,YAAA,KAAK,SAAS;AACZ,gBAAA,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;AACtB,sBAAE,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;AACxD,sBAAE,IAAI,CAAC,YAAY,CAAC,iBAAiB,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;gBAC5D;AACF,YAAA,KAAK,KAAK;AACV,YAAA,KAAK,KAAK;AACR,gBAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI;gBAEhC,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE;;;;;;oBAMrC,KAAK,CAAC,cAAc,EAAE;;gBAExB;AACF,YAAA,KAAK,MAAM;;AAET,gBAAA,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE;oBACtD,IAAI,CAAC,aAAa,EAAE;;;AAGpB,oBAAA,IAAI,IAAI,CAAC,UAAU,EAAE;AACnB,wBAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAC,KAAK,EAAE,IAAI,EAAE,KAAK,EAAC,CAAC;;yBACpC;AACL,wBAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC;AAC9B,wBAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAC,KAAK,EAAE,IAAI,EAAE,KAAK,EAAC,CAAC;;oBAEhD,KAAK,CAAC,cAAc,EAAE;AACtB,oBAAA,KAAK,CAAC,eAAe,EAAE,CAAC;;gBAE1B;AACF,YAAA;;gBAEE;;AAGJ,QAAA,IAAI,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,aAAa,EAAE,IAAI,CAAC,UAAU,CAAC,EAAE;YACjE,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC;YAE3C,IAAI,CAAC,gCAAgC,EAAE;;;QAIzC,KAAK,CAAC,cAAc,EAAE;;;AAIxB,IAAA,wBAAwB,CAAC,KAAoB,EAAA;AAC3C,QAAA,IAAI,KAAK,CAAC,OAAO,KAAK,KAAK,IAAI,KAAK,CAAC,OAAO,KAAK,KAAK,EAAE;AACtD,YAAA,IAAI,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE;gBAClE,IAAI,CAAC,aAAa,CAAC,EAAC,KAAK,EAAE,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,KAAK,EAAC,CAAC;;AAGjF,YAAA,IAAI,CAAC,oBAAoB,GAAG,KAAK;;;;IAKrC,KAAK,GAAA;AACH,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC;AAC9B,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;QACtE,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC;AAC3C,cAAE,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,UAAU;cAC9E,IAAI,CAAC;AACF,iBAAA,aAAa,CAAC,OAAO,CAAC,CACtB,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,iBAAiB,EAAE;AAEvE,QAAA,IAAI,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,UAAU,CAC7C,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,EAC1C,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,EAC3C,CAAC,CACF;AACD,QAAA,IAAI,CAAC,gBAAgB;AACnB,YAAA,CAAC,aAAa;AACZ,gBAAA,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,YAAY,CAAC;AAC5C,gBAAA,IAAI,CAAC,YAAY,CAAC,iBAAiB,EAAE;AACvC,gBAAA,aAAa;QAEf,IAAI,CAAC,aAAa,EAAE;QACpB,IAAI,CAAC,gBAAgB,EAAE;AACvB,QAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE;;;AAIxC,IAAA,gBAAgB,CAAC,WAAqB,EAAA;AACpC,QAAA,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,WAAW,CAAC;;;IAIrD,gCAAgC,GAAA;AAC9B,QAAA,IAAI,CAAC,gBAAgB,CAAC,wCAAwC,EAAE;;;AAIlE,IAAA,eAAe,CAAC,EAAC,KAAK,EAAE,KAAK,EAAE,IAAI,EAAkD,EAAA;AACnF,QAAA,IAAI,IAAI,CAAC,cAAc,EAAE;;;AAGvB,YAAA,MAAM,KAAK,GAAG,IAAI,GAAG,IAAI,CAAC,QAAS,GAAG,IAAI;AAC1C,YAAA,MAAM,YAAY,GAAG,IAAI,CAAC,cAAc,CAAC,aAAa,CACpD,KAAK,EACL,IAAI,CAAC,QAAwB,EAC7B,KAAK,CACN;YACD,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,oBAAoB,CAAC,YAAY,CAAC,KAAK,CAAC;YAClE,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,oBAAoB,CAAC,YAAY,CAAC,GAAG,CAAC;AAE9D,YAAA,IAAI,IAAI,CAAC,UAAU,IAAI,KAAK,EAAE;gBAC5B,MAAM,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,UAAU,GAC9C,IAAI,CAAC,UAAU,CAAC,KAAK,EACrB,IAAI,CAAC,QAAwB,EAC7B,KAAK,EACL,KAAK,CACN;gBAED,IAAI,SAAS,EAAE;oBACb,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,oBAAoB,CAAC,SAAS,CAAC,KAAK,CAAC;oBAC/D,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,oBAAoB,CAAC,SAAS,CAAC,GAAG,CAAC;;;;;;;AAQ/D,YAAA,IAAI,CAAC,kBAAkB,CAAC,aAAa,EAAE;;;AAI3C;;;AAGG;AACO,IAAA,UAAU,CAAC,KAAqC,EAAA;QACxD,IAAI,CAAC,IAAI,CAAC,UAAU;YAAE;AAEtB,QAAA,IAAI,KAAK,CAAC,KAAK,EAAE;;YAEf,MAAM,cAAc,GAAG,IAAI,CAAC,cAAc,EAAE,UAAU,GACpD,IAAI,CAAC,UAAU,CAAC,KAAK,EACrB,IAAI,CAAC,QAAwB,EAC7B,KAAK,CAAC,KAAK,EACX,KAAK,CAAC,KAAK,CACZ;AAED,YAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAC,KAAK,EAAE,cAAc,IAAI,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,EAAC,CAAC;;aACnE;AACL,YAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAC,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,EAAC,CAAC;;;AAI1D;;;AAGG;AACK,IAAA,sBAAsB,CAAC,UAAkB,EAAA;AAC/C,QAAA,OAAO,IAAI,CAAC,YAAY,CAAC,UAAU,CACjC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,EAC1C,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,EAC3C,UAAU,CACX;;;IAIK,aAAa,GAAA;QACnB,MAAM,cAAc,GAAG,IAAI,CAAC,YAAY,CAAC,iBAAiB,EAAE;QAC5D,MAAM,cAAc,GAAG,IAAI,CAAC,YAAY,CAAC,iBAAiB,CAAC,QAAQ,CAAC;QACpE,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,iBAAiB,CAAC,MAAM,CAAC;;QAGhE,IAAI,QAAQ,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,KAAI;AAC1C,YAAA,OAAO,EAAC,IAAI,EAAE,MAAM,EAAE,cAAc,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,eAAe,EAAE,EAAC;AACjE,SAAC,CAAC;QACF,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,cAAc,CAAC,CAAC;;;IAInF,gBAAgB,GAAA;AACtB,QAAA,MAAM,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC;QACxE,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE;AAClD,QAAA,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QAClB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,IAAI,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC,GAAG,WAAW,EAAE,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE;AAC1E,YAAA,IAAI,IAAI,IAAI,aAAa,EAAE;AACzB,gBAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;gBACpB,IAAI,GAAG,CAAC;;AAEV,YAAA,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,UAAU,CACvC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,EAC1C,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,EAC3C,CAAC,GAAG,CAAC,CACN;YACD,MAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC;AAC5C,YAAA,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,aAAa,CAAC;YACzF,MAAM,WAAW,GAAG,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,OAAO,CAAC,GAAG,SAAS;AAE9E,YAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,IAAI,CACtC,IAAI,eAAe,CACjB,CAAC,GAAG,CAAC,EACL,SAAS,CAAC,CAAC,CAAC,EACZ,SAAS,EACT,OAAO,EACP,WAAW,EACX,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAE,EAChC,IAAI,CACL,CACF;;;;AAKG,IAAA,iBAAiB,CAAC,IAAO,EAAA;QAC/B,QACE,CAAC,CAAC,IAAI;AACN,aAAC,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AACzE,aAAC,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AACzE,aAAC,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;;AAI/C;;;AAGG;AACK,IAAA,sBAAsB,CAAC,IAAc,EAAA;QAC3C,OAAO,IAAI,IAAI,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,IAAI,CAAC,UAAU;cAC1D,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI;cAC9B,IAAI;;;IAIF,oBAAoB,CAAC,EAAY,EAAE,EAAY,EAAA;QACrD,OAAO,CAAC,EACN,EAAE;YACF,EAAE;AACF,YAAA,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,EAAE,CAAC;AAChE,YAAA,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,EAAE,CAAC,CAC/D;;;AAIK,IAAA,oBAAoB,CAAC,IAAc,EAAA;QACzC,IAAI,IAAI,EAAE;;;YAGR,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC;YAC5C,MAAM,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC;YAC9C,MAAM,GAAG,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC;AAC3C,YAAA,OAAO,IAAI,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC,OAAO,EAAE;;AAG7C,QAAA,OAAO,IAAI;;;IAIL,MAAM,GAAA;QACZ,OAAO,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,KAAK;;;AAIvC,IAAA,UAAU,CAAC,aAAsC,EAAA;AACvD,QAAA,IAAI,aAAa,YAAY,SAAS,EAAE;YACtC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,oBAAoB,CAAC,aAAa,CAAC,KAAK,CAAC;YACjE,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,oBAAoB,CAAC,aAAa,CAAC,GAAG,CAAC;AAC7D,YAAA,IAAI,CAAC,QAAQ,GAAG,IAAI;;aACf;AACL,YAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,oBAAoB,CAAC,aAAa,CAAC;AAC5E,YAAA,IAAI,CAAC,QAAQ,GAAG,KAAK;;QAGvB,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,eAAe,CAAC;QAC5E,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,aAAa,CAAC;;;AAIlE,IAAA,UAAU,CAAC,IAAO,EAAA;QACxB,OAAO,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;;;IAI1C,aAAa,GAAA;QACnB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,WAAW,GAAG,IAAI;;uGAziBnC,YAAY,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAZ,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,YAAY,EA+GZ,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,gBAAA,EAAA,MAAA,EAAA,EAAA,UAAA,EAAA,YAAA,EAAA,QAAA,EAAA,UAAA,EAAA,OAAA,EAAA,SAAA,EAAA,OAAA,EAAA,SAAA,EAAA,UAAA,EAAA,YAAA,EAAA,SAAA,EAAA,WAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,aAAA,EAAA,eAAA,EAAA,uBAAA,EAAA,yBAAA,EAAA,qBAAA,EAAA,uBAAA,EAAA,UAAA,EAAA,YAAA,EAAA,EAAA,OAAA,EAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,WAAA,EAAA,aAAA,EAAA,SAAA,EAAA,WAAA,EAAA,gBAAA,EAAA,kBAAA,EAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,kBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,eAAe,ECtL5B,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,cAAA,CAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,i/CAoCA,4CDiCY,eAAe,EAAA,QAAA,EAAA,qBAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,MAAA,EAAA,YAAA,EAAA,YAAA,EAAA,UAAA,EAAA,uBAAA,EAAA,SAAA,EAAA,YAAA,EAAA,SAAA,EAAA,iBAAA,EAAA,iBAAA,EAAA,eAAA,EAAA,cAAA,EAAA,YAAA,EAAA,yBAAA,EAAA,uBAAA,CAAA,EAAA,OAAA,EAAA,CAAA,qBAAA,EAAA,eAAA,EAAA,kBAAA,EAAA,aAAA,EAAA,WAAA,CAAA,EAAA,QAAA,EAAA,CAAA,iBAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAEd,YAAY,EAAA,UAAA,EAAA,CAAA;kBARxB,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,gBAAgB,EAEhB,QAAA,EAAA,cAAc,EACT,aAAA,EAAA,iBAAiB,CAAC,IAAI,EACpB,eAAA,EAAA,uBAAuB,CAAC,MAAM,EACtC,OAAA,EAAA,CAAC,eAAe,CAAC,EAAA,QAAA,EAAA,i/CAAA,EAAA;wDAqBtB,UAAU,EAAA,CAAA;sBADb;gBAkBG,QAAQ,EAAA,CAAA;sBADX;gBAiBG,OAAO,EAAA,CAAA;sBADV;gBAWG,OAAO,EAAA,CAAA;sBADV;gBAUQ,UAAU,EAAA,CAAA;sBAAlB;gBAGQ,SAAS,EAAA,CAAA;sBAAjB;gBAGQ,eAAe,EAAA,CAAA;sBAAvB;gBAGQ,aAAa,EAAA,CAAA;sBAArB;gBAGQ,uBAAuB,EAAA,CAAA;sBAA/B;gBAGQ,qBAAqB,EAAA,CAAA;sBAA7B;gBAGQ,UAAU,EAAA,CAAA;sBAAlB;gBAGkB,cAAc,EAAA,CAAA;sBAAhC;gBAGkB,cAAc,EAAA,CAAA;sBAAhC;gBAIkB,WAAW,EAAA,CAAA;sBAA7B;gBAMkB,SAAS,EAAA,CAAA;sBAA3B;gBAGkB,gBAAgB,EAAA,CAAA;sBAAlC;gBAG2B,gBAAgB,EAAA,CAAA;sBAA3C,SAAS;uBAAC,eAAe;;;AExIrB,MAAM,YAAY,GAAG;AAErB,MAAM,WAAW,GAAG;AAE3B;;;AAGG;MASU,gBAAgB,CAAA;AACnB,IAAA,kBAAkB,GAAG,MAAM,CAAC,iBAAiB,CAAC;IACtD,YAAY,GAAG,MAAM,CAAiB,WAAW,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAE;IAC7D,IAAI,GAAG,MAAM,CAAC,cAAc,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;AAC/C,IAAA,qBAAqB,GAAG,YAAY,CAAC,KAAK;;AAG1C,IAAA,oBAAoB;;AAG5B,IAAA,IACI,UAAU,GAAA;QACZ,OAAO,IAAI,CAAC,WAAW;;IAEzB,IAAI,UAAU,CAAC,KAAQ,EAAA;AACrB,QAAA,IAAI,aAAa,GAAG,IAAI,CAAC,WAAW;AACpC,QAAA,MAAM,SAAS,GACb,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;AAC1E,YAAA,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE;AAC3B,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC;QAErF,IACE,CAAC,mBAAmB,CAClB,IAAI,CAAC,YAAY,EACjB,aAAa,EACb,IAAI,CAAC,WAAW,EAChB,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,OAAO,CACb,EACD;YACA,IAAI,CAAC,KAAK,EAAE;;;AAGR,IAAA,WAAW;;AAGnB,IAAA,IACI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,SAAS;;IAEvB,IAAI,QAAQ,CAAC,KAA8B,EAAA;AACzC,QAAA,IAAI,KAAK,YAAY,SAAS,EAAE;AAC9B,YAAA,IAAI,CAAC,SAAS,GAAG,KAAK;;aACjB;AACL,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;;AAG7F,QAAA,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC;;AAEtB,IAAA,SAAS;;AAGjB,IAAA,IACI,OAAO,GAAA;QACT,OAAO,IAAI,CAAC,QAAQ;;IAEtB,IAAI,OAAO,CAAC,KAAe,EAAA;AACzB,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;;AAEpF,IAAA,QAAQ;;AAGhB,IAAA,IACI,OAAO,GAAA;QACT,OAAO,IAAI,CAAC,QAAQ;;IAEtB,IAAI,OAAO,CAAC,KAAe,EAAA;AACzB,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;;AAEpF,IAAA,QAAQ;;AAGP,IAAA,UAAU;;AAGV,IAAA,SAAS;;AAGC,IAAA,cAAc,GAAoB,IAAI,YAAY,EAAK;;AAGvD,IAAA,YAAY,GAAoB,IAAI,YAAY,EAAK;;AAGrD,IAAA,gBAAgB,GAAoB,IAAI,YAAY,EAAK;;AAGhD,IAAA,gBAAgB;;AAG5C,IAAA,MAAM;;AAGN,IAAA,UAAU;;AAGV,IAAA,aAAa;AAIb,IAAA,WAAA,GAAA;AACE,QAAA,IAAI,CAAC,IAAI,CAAC,YAAY,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAAE;AACzE,YAAA,MAAM,0BAA0B,CAAC,aAAa,CAAC;;QAGjD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE;;IAG9C,kBAAkB,GAAA;AAChB,QAAA,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,YAAY,CAAC;AAC5C,aAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;aACpB,SAAS,CAAC,MAAM,IAAI,CAAC,KAAK,EAAE,CAAC;;IAGlC,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,qBAAqB,CAAC,WAAW,EAAE;;;IAI1C,KAAK,GAAA;AACH,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;;;;;;AAQtE,QAAA,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC;QAC9D,MAAM,aAAa,GACjB,UAAU,GAAG,eAAe,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC;AAE9F,QAAA,IAAI,CAAC,MAAM,GAAG,EAAE;AAChB,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAa,EAAE,EAAE,CAAC,GAAG,YAAY,EAAE,CAAC,EAAE,EAAE;AACzD,YAAA,GAAG,CAAC,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;AAC3B,YAAA,IAAI,GAAG,CAAC,MAAM,IAAI,WAAW,EAAE;gBAC7B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,IAAI,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC;gBAChE,GAAG,GAAG,EAAE;;;AAGZ,QAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE;;;AAIxC,IAAA,aAAa,CAAC,KAAmC,EAAA;AAC/C,QAAA,MAAM,IAAI,GAAG,KAAK,CAAC,KAAK;AACxB,QAAA,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC;QAC7D,MAAM,YAAY,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC;AAEhD,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,YAAY,CAAC;AACpC,QAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,YAAY,CAAC;;AAGxC;;;;;;;;;AASG;AACH,IAAA,iBAAiB,CAAC,KAAmC,EAAA;AACnD,QAAA,MAAM,IAAI,GAAG,KAAK,CAAC,KAAK;AACxB,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,WAAW;QAEtC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC;AAC7C,QAAA,IAAI,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,aAAa,EAAE,IAAI,CAAC,UAAU,CAAC,EAAE;YACjE,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC;;;;AAK/C,IAAA,0BAA0B,CAAC,KAAoB,EAAA;AAC7C,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,WAAW;AACtC,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE;AAE3B,QAAA,QAAQ,KAAK,CAAC,OAAO;AACnB,YAAA,KAAK,UAAU;gBACb,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;gBACtF;AACF,YAAA,KAAK,WAAW;gBACd,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;gBACtF;AACF,YAAA,KAAK,QAAQ;AACX,gBAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,WAAW,CAAC;gBACpF;AACF,YAAA,KAAK,UAAU;AACb,gBAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,IAAI,CAAC,WAAW,EAAE,WAAW,CAAC;gBACnF;AACF,YAAA,KAAK,IAAI;AACP,gBAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAClD,IAAI,CAAC,WAAW,EAChB,CAAC,eAAe,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CACjF;gBACD;AACF,YAAA,KAAK,GAAG;AACN,gBAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAClD,IAAI,CAAC,WAAW,EAChB,YAAY;AACV,oBAAA,eAAe,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC;AAC/E,oBAAA,CAAC,CACJ;gBACD;AACF,YAAA,KAAK,OAAO;AACV,gBAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAClD,IAAI,CAAC,WAAW,EAChB,KAAK,CAAC,MAAM,GAAG,CAAC,YAAY,GAAG,EAAE,GAAG,CAAC,YAAY,CAClD;gBACD;AACF,YAAA,KAAK,SAAS;gBACZ,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAClD,IAAI,CAAC,WAAW,EAChB,KAAK,CAAC,MAAM,GAAG,YAAY,GAAG,EAAE,GAAG,YAAY,CAChD;gBACD;AACF,YAAA,KAAK,KAAK;AACV,YAAA,KAAK,KAAK;;;;;AAKR,gBAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI;gBAChC;AACF,YAAA;;gBAEE;;AAEJ,QAAA,IAAI,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,aAAa,EAAE,IAAI,CAAC,UAAU,CAAC,EAAE;YACjE,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC;;QAG7C,IAAI,CAAC,gCAAgC,EAAE;;QAEvC,KAAK,CAAC,cAAc,EAAE;;;AAIxB,IAAA,wBAAwB,CAAC,KAAoB,EAAA;AAC3C,QAAA,IAAI,KAAK,CAAC,OAAO,KAAK,KAAK,IAAI,KAAK,CAAC,OAAO,KAAK,KAAK,EAAE;AACtD,YAAA,IAAI,IAAI,CAAC,oBAAoB,EAAE;gBAC7B,IAAI,CAAC,aAAa,CAAC,EAAC,KAAK,EAAE,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,KAAK,EAAC,CAAC;;AAGjF,YAAA,IAAI,CAAC,oBAAoB,GAAG,KAAK;;;IAIrC,cAAc,GAAA;AACZ,QAAA,OAAO,eAAe,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC;;;IAIxF,gBAAgB,GAAA;AACd,QAAA,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,EAAE;;;IAI1C,gCAAgC,GAAA;AAC9B,QAAA,IAAI,CAAC,gBAAgB,CAAC,wCAAwC,EAAE;;AAGlE;;;AAGG;AACK,IAAA,gBAAgB,CAAC,IAAY,EAAA;AACnC,QAAA,MAAM,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC;QAC/D,MAAM,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,iBAAiB,CACrD,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC,CAAC,CACnD;AACD,QAAA,MAAM,cAAc,GAAG,IAAI,CAAC,YAAY,CAAC,UAAU,CACjD,IAAI,EACJ,WAAW,EACX,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,WAAW,CAAC,CAClE;AACD,QAAA,OAAO,cAAc;;;AAIf,IAAA,kBAAkB,CAAC,IAAY,EAAA;AACrC,QAAA,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC;QACrD,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,IAAI,CAAC;QACpD,MAAM,WAAW,GAAG,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,YAAY,CAAC,GAAG,SAAS;AAEnF,QAAA,OAAO,IAAI,eAAe,CAAC,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAE,WAAW,CAAC;;;AAIzF,IAAA,iBAAiB,CAAC,IAAY,EAAA;;QAEpC,IACE,IAAI,KAAK,SAAS;AAClB,YAAA,IAAI,KAAK,IAAI;AACb,aAAC,IAAI,CAAC,OAAO,IAAI,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAChE,aAAC,IAAI,CAAC,OAAO,IAAI,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,EAChE;AACA,YAAA,OAAO,KAAK;;;AAId,QAAA,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;AACpB,YAAA,OAAO,IAAI;;AAGb,QAAA,MAAM,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC;;AAG5D,QAAA,KACE,IAAI,IAAI,GAAG,WAAW,EACtB,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI,EACvC,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,IAAI,EAAE,CAAC,CAAC,EACjD;AACA,YAAA,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;AACzB,gBAAA,OAAO,IAAI;;;AAIf,QAAA,OAAO,KAAK;;;IAIN,MAAM,GAAA;QACZ,OAAO,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,KAAK;;;AAIvC,IAAA,gBAAgB,CAAC,KAA8B,EAAA;AACrD,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI;AAEzB,QAAA,IAAI,KAAK,YAAY,SAAS,EAAE;YAC9B,MAAM,YAAY,GAAG,KAAK,CAAC,KAAK,IAAI,KAAK,CAAC,GAAG;YAE7C,IAAI,YAAY,EAAE;gBAChB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,YAAY,CAAC;;;aAEzD,IAAI,KAAK,EAAE;YAChB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,KAAK,CAAC;;;uGAlV9C,gBAAgB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAhB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,gBAAgB,EAuFhB,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,qBAAA,EAAA,MAAA,EAAA,EAAA,UAAA,EAAA,YAAA,EAAA,QAAA,EAAA,UAAA,EAAA,OAAA,EAAA,SAAA,EAAA,OAAA,EAAA,SAAA,EAAA,UAAA,EAAA,YAAA,EAAA,SAAA,EAAA,WAAA,EAAA,EAAA,OAAA,EAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,YAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,kBAAA,EAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,kBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,eAAe,ECrJ5B,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,kvBAkBA,4CD0CY,eAAe,EAAA,QAAA,EAAA,qBAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,MAAA,EAAA,YAAA,EAAA,YAAA,EAAA,UAAA,EAAA,uBAAA,EAAA,SAAA,EAAA,YAAA,EAAA,SAAA,EAAA,iBAAA,EAAA,iBAAA,EAAA,eAAA,EAAA,cAAA,EAAA,YAAA,EAAA,yBAAA,EAAA,uBAAA,CAAA,EAAA,OAAA,EAAA,CAAA,qBAAA,EAAA,eAAA,EAAA,kBAAA,EAAA,aAAA,EAAA,WAAA,CAAA,EAAA,QAAA,EAAA,CAAA,iBAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAEd,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAR5B,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,qBAAqB,EAErB,QAAA,EAAA,kBAAkB,EACb,aAAA,EAAA,iBAAiB,CAAC,IAAI,EACpB,eAAA,EAAA,uBAAuB,CAAC,MAAM,EACtC,OAAA,EAAA,CAAC,eAAe,CAAC,EAAA,QAAA,EAAA,kvBAAA,EAAA;wDAatB,UAAU,EAAA,CAAA;sBADb;gBA2BG,QAAQ,EAAA,CAAA;sBADX;gBAiBG,OAAO,EAAA,CAAA;sBADV;gBAWG,OAAO,EAAA,CAAA;sBADV;gBAUQ,UAAU,EAAA,CAAA;sBAAlB;gBAGQ,SAAS,EAAA,CAAA;sBAAjB;gBAGkB,cAAc,EAAA,CAAA;sBAAhC;gBAGkB,YAAY,EAAA,CAAA;sBAA9B;gBAGkB,gBAAgB,EAAA,CAAA;sBAAlC;gBAG2B,gBAAgB,EAAA,CAAA;sBAA3C,SAAS;uBAAC,eAAe;;AAgQtB,SAAU,mBAAmB,CACjC,WAA2B,EAC3B,KAAQ,EACR,KAAQ,EACR,OAAiB,EACjB,OAAiB,EAAA;IAEjB,MAAM,KAAK,GAAG,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC;IACxC,MAAM,KAAK,GAAG,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC;IACxC,MAAM,YAAY,GAAG,eAAe,CAAC,WAAW,EAAE,OAAO,EAAE,OAAO,CAAC;AACnE,IAAA,QACE,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,GAAG,YAAY,IAAI,YAAY,CAAC;AACjD,QAAA,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,GAAG,YAAY,IAAI,YAAY,CAAC;AAErD;AAEA;;;;AAIG;AACG,SAAU,eAAe,CAC7B,WAA2B,EAC3B,UAAa,EACb,OAAiB,EACjB,OAAiB,EAAA;IAEjB,MAAM,UAAU,GAAG,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC;AAClD,IAAA,OAAO,eAAe,CAAC,UAAU,GAAG,eAAe,CAAC,WAAW,EAAE,OAAO,EAAE,OAAO,CAAC,EAAE,YAAY,CAAC;AACnG;AAEA;;;AAGG;AACH,SAAS,eAAe,CACtB,WAA2B,EAC3B,OAAiB,EACjB,OAAiB,EAAA;IAEjB,IAAI,YAAY,GAAG,CAAC;IACpB,IAAI,OAAO,EAAE;QACX,MAAM,OAAO,GAAG,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC;AAC5C,QAAA,YAAY,GAAG,OAAO,GAAG,YAAY,GAAG,CAAC;;SACpC,IAAI,OAAO,EAAE;AAClB,QAAA,YAAY,GAAG,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC;;AAE7C,IAAA,OAAO,YAAY;AACrB;AAEA;AACA,SAAS,eAAe,CAAC,CAAS,EAAE,CAAS,EAAA;IAC3C,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;AAC1B;;AE5ZA;;;AAGG;MASU,WAAW,CAAA;AACb,IAAA,kBAAkB,GAAG,MAAM,CAAC,iBAAiB,CAAC;IAC/C,YAAY,GAAG,MAAM,CAAiB,gBAAgB,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAE;IAClF,YAAY,GAAG,MAAM,CAAiB,WAAW,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAE;IAC7D,IAAI,GAAG,MAAM,CAAC,cAAc,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;AAE/C,IAAA,qBAAqB,GAAG,YAAY,CAAC,KAAK;;AAG1C,IAAA,oBAAoB;;AAG5B,IAAA,IACI,UAAU,GAAA;QACZ,OAAO,IAAI,CAAC,WAAW;;IAEzB,IAAI,UAAU,CAAC,KAAQ,EAAA;AACrB,QAAA,IAAI,aAAa,GAAG,IAAI,CAAC,WAAW;AACpC,QAAA,MAAM,SAAS,GACb,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;AAC1E,YAAA,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE;AAC3B,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC;QACrF,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,aAAa,CAAC,KAAK,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE;YAC5F,IAAI,CAAC,KAAK,EAAE;;;AAGR,IAAA,WAAW;;AAGnB,IAAA,IACI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,SAAS;;IAEvB,IAAI,QAAQ,CAAC,KAA8B,EAAA;AACzC,QAAA,IAAI,KAAK,YAAY,SAAS,EAAE;AAC9B,YAAA,IAAI,CAAC,SAAS,GAAG,KAAK;;aACjB;AACL,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;;AAG7F,QAAA,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC;;AAEvB,IAAA,SAAS;;AAGjB,IAAA,IACI,OAAO,GAAA;QACT,OAAO,IAAI,CAAC,QAAQ;;IAEtB,IAAI,OAAO,CAAC,KAAe,EAAA;AACzB,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;;AAEpF,IAAA,QAAQ;;AAGhB,IAAA,IACI,OAAO,GAAA;QACT,OAAO,IAAI,CAAC,QAAQ;;IAEtB,IAAI,OAAO,CAAC,KAAe,EAAA;AACzB,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;;AAEpF,IAAA,QAAQ;;AAGP,IAAA,UAAU;;AAGV,IAAA,SAAS;;AAGC,IAAA,cAAc,GAAoB,IAAI,YAAY,EAAK;;AAGvD,IAAA,aAAa,GAAoB,IAAI,YAAY,EAAK;;AAGtD,IAAA,gBAAgB,GAAoB,IAAI,YAAY,EAAK;;AAGhD,IAAA,gBAAgB;;AAG5C,IAAA,OAAO;;AAGP,IAAA,UAAU;;AAGV,IAAA,WAAW;AAEX;;;AAGG;AACH,IAAA,cAAc;AAId,IAAA,WAAA,GAAA;AACE,QAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;AACjD,YAAA,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;AACtB,gBAAA,MAAM,0BAA0B,CAAC,aAAa,CAAC;;AAEjD,YAAA,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;AACtB,gBAAA,MAAM,0BAA0B,CAAC,kBAAkB,CAAC;;;QAIxD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE;;IAG9C,kBAAkB,GAAA;AAChB,QAAA,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,YAAY,CAAC;AAC5C,aAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;aACpB,SAAS,CAAC,MAAM,IAAI,CAAC,KAAK,EAAE,CAAC;;IAGlC,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,qBAAqB,CAAC,WAAW,EAAE;;;AAI1C,IAAA,cAAc,CAAC,KAAmC,EAAA;AAChD,QAAA,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK;QAEzB,MAAM,aAAa,GAAG,IAAI,CAAC,YAAY,CAAC,UAAU,CAChD,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,EAC1C,KAAK,EACL,CAAC,CACF;AACD,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,aAAa,CAAC;QAEtC,MAAM,YAAY,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC;AAClD,QAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,YAAY,CAAC;;AAGxC;;;;;;;;;AASG;AACH,IAAA,iBAAiB,CAAC,KAAmC,EAAA;AACnD,QAAA,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK;AACzB,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,WAAW;QAEtC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC;AAE/C,QAAA,IAAI,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,aAAa,EAAE,IAAI,CAAC,UAAU,CAAC,EAAE;YACjE,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC;;;;AAK/C,IAAA,0BAA0B,CAAC,KAAoB,EAAA;;;;AAK7C,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,WAAW;AACtC,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE;AAE3B,QAAA,QAAQ,KAAK,CAAC,OAAO;AACnB,YAAA,KAAK,UAAU;gBACb,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,iBAAiB,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;gBACvF;AACF,YAAA,KAAK,WAAW;gBACd,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,iBAAiB,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;gBACvF;AACF,YAAA,KAAK,QAAQ;AACX,gBAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,iBAAiB,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;gBAC3E;AACF,YAAA,KAAK,UAAU;AACb,gBAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,iBAAiB,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;gBAC1E;AACF,YAAA,KAAK,IAAI;gBACP,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,iBAAiB,CACnD,IAAI,CAAC,WAAW,EAChB,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAC9C;gBACD;AACF,YAAA,KAAK,GAAG;gBACN,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,iBAAiB,CACnD,IAAI,CAAC,WAAW,EAChB,EAAE,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAClD;gBACD;AACF,YAAA,KAAK,OAAO;gBACV,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAClD,IAAI,CAAC,WAAW,EAChB,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC,CACxB;gBACD;AACF,YAAA,KAAK,SAAS;gBACZ,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAClD,IAAI,CAAC,WAAW,EAChB,KAAK,CAAC,MAAM,GAAG,EAAE,GAAG,CAAC,CACtB;gBACD;AACF,YAAA,KAAK,KAAK;AACV,YAAA,KAAK,KAAK;;;;;AAKR,gBAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI;gBAChC;AACF,YAAA;;gBAEE;;AAGJ,QAAA,IAAI,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,aAAa,EAAE,IAAI,CAAC,UAAU,CAAC,EAAE;YACjE,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC;YAC3C,IAAI,CAAC,gCAAgC,EAAE;;;QAIzC,KAAK,CAAC,cAAc,EAAE;;;AAIxB,IAAA,wBAAwB,CAAC,KAAoB,EAAA;AAC3C,QAAA,IAAI,KAAK,CAAC,OAAO,KAAK,KAAK,IAAI,KAAK,CAAC,OAAO,KAAK,KAAK,EAAE;AACtD,YAAA,IAAI,IAAI,CAAC,oBAAoB,EAAE;gBAC7B,IAAI,CAAC,cAAc,CAAC,EAAC,KAAK,EAAE,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,KAAK,EAAC,CAAC;;AAGnF,YAAA,IAAI,CAAC,oBAAoB,GAAG,KAAK;;;;IAKrC,KAAK,GAAA;AACH,QAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,QAAQ,CAAC;AACrC,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;AACzE,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC;QAEhE,IAAI,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,OAAO,CAAC;;QAEzD,IAAI,CAAC,OAAO,GAAG;AACb,YAAA,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AACZ,YAAA,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AACZ,YAAA,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;SACf,CAAC,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,KAAK,IAAI,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAClF,QAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE;;;IAIxC,gBAAgB,GAAA;AACd,QAAA,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,EAAE;;;IAI1C,gCAAgC,GAAA;AAC9B,QAAA,IAAI,CAAC,gBAAgB,CAAC,wCAAwC,EAAE;;AAGlE;;;AAGG;AACK,IAAA,sBAAsB,CAAC,IAAc,EAAA;QAC3C,OAAO,IAAI,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU;cACvF,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI;cAC/B,IAAI;;AAGV;;;AAGG;AACK,IAAA,iBAAiB,CAAC,KAAa,EAAA;QACrC,MAAM,cAAc,GAAG,IAAI,CAAC,YAAY,CAAC,UAAU,CACjD,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,EAC1C,KAAK,EACL,CAAC,CACF;QAED,MAAM,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,iBAAiB,CAAC,cAAc,CAAC;AAEvE,QAAA,OAAO,IAAI,CAAC,YAAY,CAAC,UAAU,CACjC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,EAC1C,KAAK,EACL,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,WAAW,CAAC,CAClE;;;IAIK,mBAAmB,CAAC,KAAa,EAAE,SAAiB,EAAA;QAC1D,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC;AAC/F,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,kBAAkB,CAAC;QAC9F,MAAM,WAAW,GAAG,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,SAAS;QAE7E,OAAO,IAAI,eAAe,CACxB,KAAK,EACL,SAAS,CAAC,iBAAiB,EAAE,EAC7B,SAAS,EACT,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,EAC9B,WAAW,CACZ;;;AAIK,IAAA,kBAAkB,CAAC,KAAa,EAAA;AACtC,QAAA,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC;QAE7D,IACE,KAAK,KAAK,SAAS;AACnB,YAAA,KAAK,KAAK,IAAI;AACd,YAAA,IAAI,CAAC,2BAA2B,CAAC,UAAU,EAAE,KAAK,CAAC;YACnD,IAAI,CAAC,4BAA4B,CAAC,UAAU,EAAE,KAAK,CAAC,EACpD;AACA,YAAA,OAAO,KAAK;;AAGd,QAAA,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;AACpB,YAAA,OAAO,IAAI;;AAGb,QAAA,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,UAAU,EAAE,KAAK,EAAE,CAAC,CAAC;;AAGvE,QAAA,KACE,IAAI,IAAI,GAAG,YAAY,EACvB,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,KAAK,EACzC,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,IAAI,EAAE,CAAC,CAAC,EACjD;AACA,YAAA,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;AACzB,gBAAA,OAAO,IAAI;;;AAIf,QAAA,OAAO,KAAK;;AAGd;;;AAGG;IACK,2BAA2B,CAAC,IAAY,EAAE,KAAa,EAAA;AAC7D,QAAA,IAAI,IAAI,CAAC,OAAO,EAAE;AAChB,YAAA,MAAM,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC;AACvD,YAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC;AAEzD,YAAA,OAAO,IAAI,GAAG,OAAO,KAAK,IAAI,KAAK,OAAO,IAAI,KAAK,GAAG,QAAQ,CAAC;;AAGjE,QAAA,OAAO,KAAK;;AAGd;;;AAGG;IACK,4BAA4B,CAAC,IAAY,EAAE,KAAa,EAAA;AAC9D,QAAA,IAAI,IAAI,CAAC,OAAO,EAAE;AAChB,YAAA,MAAM,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC;AACvD,YAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC;AAEzD,YAAA,OAAO,IAAI,GAAG,OAAO,KAAK,IAAI,KAAK,OAAO,IAAI,KAAK,GAAG,QAAQ,CAAC;;AAGjE,QAAA,OAAO,KAAK;;;IAIN,MAAM,GAAA;QACZ,OAAO,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,KAAK;;;AAIvC,IAAA,iBAAiB,CAAC,KAA8B,EAAA;AACtD,QAAA,IAAI,KAAK,YAAY,SAAS,EAAE;AAC9B,YAAA,IAAI,CAAC,cAAc;AACjB,gBAAA,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,GAAG,CAAC;;aAC/E;YACL,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC;;;uGA9XjD,WAAW,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAX,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,WAAW,EAgFX,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,eAAA,EAAA,MAAA,EAAA,EAAA,UAAA,EAAA,YAAA,EAAA,QAAA,EAAA,UAAA,EAAA,OAAA,EAAA,SAAA,EAAA,OAAA,EAAA,SAAA,EAAA,UAAA,EAAA,YAAA,EAAA,SAAA,EAAA,WAAA,EAAA,EAAA,OAAA,EAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,aAAA,EAAA,eAAA,EAAA,gBAAA,EAAA,kBAAA,EAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,kBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,eAAe,EC1I5B,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,aAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,k1BAoBA,4CDoCY,eAAe,EAAA,QAAA,EAAA,qBAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,MAAA,EAAA,YAAA,EAAA,YAAA,EAAA,UAAA,EAAA,uBAAA,EAAA,SAAA,EAAA,YAAA,EAAA,SAAA,EAAA,iBAAA,EAAA,iBAAA,EAAA,eAAA,EAAA,cAAA,EAAA,YAAA,EAAA,yBAAA,EAAA,uBAAA,CAAA,EAAA,OAAA,EAAA,CAAA,qBAAA,EAAA,eAAA,EAAA,kBAAA,EAAA,aAAA,EAAA,WAAA,CAAA,EAAA,QAAA,EAAA,CAAA,iBAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAEd,WAAW,EAAA,UAAA,EAAA,CAAA;kBARvB,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,eAAe,EAEf,QAAA,EAAA,aAAa,EACR,aAAA,EAAA,iBAAiB,CAAC,IAAI,EACpB,eAAA,EAAA,uBAAuB,CAAC,MAAM,EACtC,OAAA,EAAA,CAAC,eAAe,CAAC,EAAA,QAAA,EAAA,k1BAAA,EAAA;wDAetB,UAAU,EAAA,CAAA;sBADb;gBAkBG,QAAQ,EAAA,CAAA;sBADX;gBAiBG,OAAO,EAAA,CAAA;sBADV;gBAWG,OAAO,EAAA,CAAA;sBADV;gBAUQ,UAAU,EAAA,CAAA;sBAAlB;gBAGQ,SAAS,EAAA,CAAA;sBAAjB;gBAGkB,cAAc,EAAA,CAAA;sBAAhC;gBAGkB,aAAa,EAAA,CAAA;sBAA/B;gBAGkB,gBAAgB,EAAA,CAAA;sBAAlC;gBAG2B,gBAAgB,EAAA,CAAA;sBAA3C,SAAS;uBAAC,eAAe;;;AEtF5B;MASa,iBAAiB,CAAA;AACpB,IAAA,KAAK,GAAG,MAAM,CAAC,iBAAiB,CAAC;AACzC,IAAA,QAAQ,GAAG,MAAM,CAAiB,WAAW,CAAC;IACtC,YAAY,GAAG,MAAM,CAAiB,WAAW,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAE;IACrE,YAAY,GAAG,MAAM,CAAiB,gBAAgB,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAE;AAIlF,IAAA,WAAA,GAAA;QACE,MAAM,CAAC,sBAAsB,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC;AAC1D,QAAA,MAAM,iBAAiB,GAAG,MAAM,CAAC,iBAAiB,CAAC;AACnD,QAAA,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,SAAS,CAAC,MAAM,iBAAiB,CAAC,YAAY,EAAE,CAAC;;;AAI9E,IAAA,IAAI,gBAAgB,GAAA;QAClB,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,IAAI,OAAO,EAAE;YACxC,OAAO,IAAI,CAAC;AACT,iBAAA,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,cAAc;AACzE,iBAAA,iBAAiB,EAAE;;QAExB,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,IAAI,MAAM,EAAE;AACvC,YAAA,OAAO,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC;;AAGhE,QAAA,OAAO,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,GAAG,IAAI,CAAC,0BAA0B,EAAE,CAAC;;;AAIzE,IAAA,IAAI,uBAAuB,GAAA;QACzB,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,IAAI,OAAO,EAAE;YACxC,OAAO,IAAI,CAAC;AACT,iBAAA,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,cAAc;AACzE,iBAAA,iBAAiB,EAAE;;QAExB,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,IAAI,MAAM,EAAE;AACvC,YAAA,OAAO,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC;;;;AAKhE,QAAA,OAAO,IAAI,CAAC,KAAK,CAAC,oBAAoB,CAAC,GAAG,IAAI,CAAC,0BAA0B,EAAE,CAAC;;;AAI9E,IAAA,IAAI,iBAAiB,GAAA;AACnB,QAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,WAAW,IAAI;AAClC,cAAE,IAAI,CAAC,KAAK,CAAC;AACb,cAAE,IAAI,CAAC,KAAK,CAAC,sBAAsB;;;AAIvC,IAAA,IAAI,eAAe,GAAA;QACjB,OAAO;AACL,YAAA,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,cAAc;AAClC,YAAA,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,aAAa;AAChC,YAAA,YAAY,EAAE,IAAI,CAAC,KAAK,CAAC,kBAAkB;AAC5C,SAAA,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC;;;AAI9B,IAAA,IAAI,eAAe,GAAA;QACjB,OAAO;AACL,YAAA,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,cAAc;AAClC,YAAA,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,aAAa;AAChC,YAAA,YAAY,EAAE,IAAI,CAAC,KAAK,CAAC,kBAAkB;AAC5C,SAAA,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC;;;IAI9B,oBAAoB,GAAA;QAClB,IAAI,CAAC,QAAQ,CAAC,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAW,IAAI,OAAO,GAAG,YAAY,GAAG,OAAO;;;IAI3F,eAAe,GAAA;QACb,IAAI,CAAC,QAAQ,CAAC,UAAU;AACtB,YAAA,IAAI,CAAC,QAAQ,CAAC,WAAW,IAAI;AAC3B,kBAAE,IAAI,CAAC,YAAY,CAAC,iBAAiB,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,CAAC,CAAC;AAClE,kBAAE,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAChC,IAAI,CAAC,QAAQ,CAAC,UAAU,EACxB,IAAI,CAAC,QAAQ,CAAC,WAAW,IAAI,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,YAAY,CACzD;;;IAIT,WAAW,GAAA;QACT,IAAI,CAAC,QAAQ,CAAC,UAAU;AACtB,YAAA,IAAI,CAAC,QAAQ,CAAC,WAAW,IAAI;AAC3B,kBAAE,IAAI,CAAC,YAAY,CAAC,iBAAiB,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,CAAC;AACjE,kBAAE,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAChC,IAAI,CAAC,QAAQ,CAAC,UAAU,EACxB,IAAI,CAAC,QAAQ,CAAC,WAAW,IAAI,MAAM,GAAG,CAAC,GAAG,YAAY,CACvD;;;IAIT,eAAe,GAAA;AACb,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE;AAC1B,YAAA,OAAO,IAAI;;QAEb,QACE,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;;;IAKhG,WAAW,GAAA;QACT,QACE,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;;;IAKxF,WAAW,CAAC,KAAQ,EAAE,KAAQ,EAAA;QACpC,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,IAAI,OAAO,EAAE;AACxC,YAAA,QACE,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,KAAK,CAAC;AACpE,gBAAA,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,KAAK,CAAC;;QAG1E,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,IAAI,MAAM,EAAE;AACvC,YAAA,OAAO,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,KAAK,CAAC;;;QAG7E,OAAO,mBAAmB,CACxB,IAAI,CAAC,YAAY,EACjB,KAAK,EACL,KAAK,EACL,IAAI,CAAC,QAAQ,CAAC,OAAO,EACrB,IAAI,CAAC,QAAQ,CAAC,OAAO,CACtB;;AAGH;;;;AAIG;IACK,0BAA0B,GAAA;;;;AAIhC,QAAA,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC;QACtE,MAAM,aAAa,GACjB,UAAU;YACV,eAAe,CACb,IAAI,CAAC,YAAY,EACjB,IAAI,CAAC,QAAQ,CAAC,UAAU,EACxB,IAAI,CAAC,QAAQ,CAAC,OAAO,EACrB,IAAI,CAAC,QAAQ,CAAC,OAAO,CACtB;AACH,QAAA,MAAM,aAAa,GAAG,aAAa,GAAG,YAAY,GAAG,CAAC;QACtD,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,WAAW,CAChD,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,aAAa,EAAE,CAAC,EAAE,CAAC,CAAC,CAClD;QACD,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,WAAW,CAChD,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,aAAa,EAAE,CAAC,EAAE,CAAC,CAAC,CAClD;AAED,QAAA,OAAO,CAAC,YAAY,EAAE,YAAY,CAAC;;IAGrC,oBAAoB,GAAG,MAAM,CAAC,YAAY,CAAC,CAAC,KAAK,CAAC,4BAA4B,CAAC;uGAnKpE,iBAAiB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAjB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,iBAAiB,EC7D9B,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,qBAAA,EAAA,QAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,i1DAqCA,EDsBY,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,SAAS,iLAAE,aAAa,EAAA,QAAA,EAAA,yBAAA,EAAA,QAAA,EAAA,CAAA,WAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAEvB,iBAAiB,EAAA,UAAA,EAAA,CAAA;kBAR7B,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,qBAAqB,EAErB,QAAA,EAAA,mBAAmB,EACd,aAAA,EAAA,iBAAiB,CAAC,IAAI,EAAA,eAAA,EACpB,uBAAuB,CAAC,MAAM,EACtC,OAAA,EAAA,CAAC,SAAS,EAAE,aAAa,CAAC,EAAA,QAAA,EAAA,i1DAAA,EAAA;;AAwKrC;MAca,WAAW,CAAA;IACd,YAAY,GAAG,MAAM,CAAiB,WAAW,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAE;IACrE,YAAY,GAAG,MAAM,CAAiB,gBAAgB,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;AACzE,IAAA,kBAAkB,GAAG,MAAM,CAAC,iBAAiB,CAAC;AAC9C,IAAA,WAAW,GAAG,MAAM,CAA0B,UAAU,CAAC;;AAGxD,IAAA,eAAe;;AAGxB,IAAA,qBAAqB;AAEb,IAAA,YAAY;AAEpB;;;;AAIG;IACK,oBAAoB,GAAG,KAAK;;AAGpC,IAAA,IACI,OAAO,GAAA;QACT,OAAO,IAAI,CAAC,QAAQ;;IAEtB,IAAI,OAAO,CAAC,KAAe,EAAA;AACzB,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;;AAEpF,IAAA,QAAQ;;IAGP,SAAS,GAAoB,OAAO;;AAG7C,IAAA,IACI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,SAAS;;IAEvB,IAAI,QAAQ,CAAC,KAA8B,EAAA;AACzC,QAAA,IAAI,KAAK,YAAY,SAAS,EAAE;AAC9B,YAAA,IAAI,CAAC,SAAS,GAAG,KAAK;;aACjB;AACL,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;;;AAGvF,IAAA,SAAS;;AAGjB,IAAA,IACI,OAAO,GAAA;QACT,OAAO,IAAI,CAAC,QAAQ;;IAEtB,IAAI,OAAO,CAAC,KAAe,EAAA;AACzB,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;;AAEpF,IAAA,QAAQ;;AAGhB,IAAA,IACI,OAAO,GAAA;QACT,OAAO,IAAI,CAAC,QAAQ;;IAEtB,IAAI,OAAO,CAAC,KAAe,EAAA;AACzB,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;;AAEpF,IAAA,QAAQ;;AAGP,IAAA,UAAU;;AAGV,IAAA,SAAS;;AAGT,IAAA,eAAe;;AAGf,IAAA,aAAa;;AAGb,IAAA,uBAAuB;;AAGvB,IAAA,qBAAqB;;AAGX,IAAA,cAAc,GAA2B,IAAI,YAAY,EAAY;AAExF;;;AAGG;AACgB,IAAA,YAAY,GAAoB,IAAI,YAAY,EAAK;AAExE;;;AAGG;AACgB,IAAA,aAAa,GAAoB,IAAI,YAAY,EAAK;AAEzE;;AAEG;AACgB,IAAA,WAAW,GAAkC,IAAI,YAAY,CAC9E,IAAI,CACL;;AAGkB,IAAA,cAAc,GAC/B,IAAI,YAAY,EAAkC;;AAGjC,IAAA,aAAa,GAAG,IAAI,YAAY,EAAsC;;AAGhE,IAAA,SAAS;;AAGV,IAAA,QAAQ;;AAGH,IAAA,aAAa;AAE1C;;;AAGG;AACH,IAAA,IAAI,UAAU,GAAA;QACZ,OAAO,IAAI,CAAC,kBAAkB;;IAEhC,IAAI,UAAU,CAAC,KAAQ,EAAA;AACrB,QAAA,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC;AACxF,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE;AACxB,QAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE;;AAEhC,IAAA,kBAAkB;;AAG1B,IAAA,IAAI,WAAW,GAAA;QACb,OAAO,IAAI,CAAC,YAAY;;IAE1B,IAAI,WAAW,CAAC,KAAsB,EAAA;AACpC,QAAA,MAAM,iBAAiB,GAAG,IAAI,CAAC,YAAY,KAAK,KAAK,GAAG,KAAK,GAAG,IAAI;AACpE,QAAA,IAAI,CAAC,YAAY,GAAG,KAAK;AACzB,QAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI;AAChC,QAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE;QACtC,IAAI,iBAAiB,EAAE;AACrB,YAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,iBAAiB,CAAC;;;AAGpC,IAAA,YAAY;;IAGV,WAAW,GAAmC,IAAI;AAE5D;;AAEG;AACM,IAAA,YAAY,GAAG,IAAI,OAAO,EAAQ;AAI3C,IAAA,WAAA,GAAA;AACE,QAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;AACjD,YAAA,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;AACtB,gBAAA,MAAM,0BAA0B,CAAC,aAAa,CAAC;;AAGjD,YAAA,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;AACtB,gBAAA,MAAM,0BAA0B,CAAC,kBAAkB,CAAC;;;AAIxD,QAAA,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC,iBAAiB,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,MAAK;AACnE,YAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE;AACtC,YAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE;AAC1B,SAAC,CAAC;;IAGJ,kBAAkB,GAAA;AAChB,QAAA,IAAI,CAAC,qBAAqB,GAAG,IAAI,eAAe,CAAC,IAAI,CAAC,eAAe,IAAI,iBAAiB,CAAC;AAC3F,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE;;AAG3D,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,SAAS;;IAGpC,kBAAkB,GAAA;AAChB,QAAA,IAAI,IAAI,CAAC,oBAAoB,EAAE;AAC7B,YAAA,IAAI,CAAC,oBAAoB,GAAG,KAAK;YACjC,IAAI,CAAC,eAAe,EAAE;;;IAI1B,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE;AAC/B,QAAA,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE;;AAG9B,IAAA,WAAW,CAAC,OAAsB,EAAA;;;;AAIhC,QAAA,MAAM,aAAa,GACjB,OAAO,CAAC,SAAS,CAAC;AAClB,YAAA,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,aAAa,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC,YAAY;AAC3F,cAAE,OAAO,CAAC,SAAS;cACjB,SAAS;AACf,QAAA,MAAM,aAAa,GACjB,OAAO,CAAC,SAAS,CAAC;AAClB,YAAA,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,aAAa,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC,YAAY;AAC3F,cAAE,OAAO,CAAC,SAAS;cACjB,SAAS;QAEf,MAAM,uBAAuB,GAAG,aAAa,IAAI,aAAa,IAAI,OAAO,CAAC,YAAY,CAAC;AAEvF,QAAA,IAAI,uBAAuB,IAAI,CAAC,uBAAuB,CAAC,WAAW,EAAE;AACnE,YAAA,MAAM,IAAI,GAAG,IAAI,CAAC,wBAAwB,EAAE;YAE5C,IAAI,IAAI,EAAE;;;;AAIR,gBAAA,IAAI,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,QAAQ,CAAC,iCAAiC,EAAE,CAAC,EAAE;AAChF,oBAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI;;;;AAKlC,gBAAA,IAAI,CAAC,kBAAkB,CAAC,aAAa,EAAE;gBACvC,IAAI,CAAC,KAAK,EAAE;;;AAIhB,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE;;;IAI1B,eAAe,GAAA;QACb,IAAI,CAAC,wBAAwB,EAAE,CAAC,gBAAgB,CAAC,KAAK,CAAC;;;IAIzD,gBAAgB,GAAA;AACd,QAAA,IAAI,CAAC,wBAAwB,EAAE,CAAC,KAAK,EAAE;;;AAIzC,IAAA,aAAa,CAAC,KAAqC,EAAA;AACjD,QAAA,MAAM,IAAI,GAAG,KAAK,CAAC,KAAK;AAExB,QAAA,IACE,IAAI,CAAC,QAAQ,YAAY,SAAS;AAClC,aAAC,IAAI,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,EAC1D;AACA,YAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC;;AAGhC,QAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC;;;AAIjC,IAAA,4BAA4B,CAAC,cAAiB,EAAA;AAC5C,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC;;;AAIxC,IAAA,wBAAwB,CAAC,eAAkB,EAAA;AACzC,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,eAAe,CAAC;;;IAI1C,eAAe,CAAC,IAAO,EAAE,IAAqC,EAAA;AAC5D,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI;AACtB,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI;;;AAIzB,IAAA,YAAY,CAAC,KAA8B,EAAA;AACzC,QAAA,IAAI,CAAC,WAAW,GAAG,KAAK;;AAG1B;;;AAGG;AACH,IAAA,UAAU,CAAC,KAAgD,EAAA;QACzD,IAAI,CAAC,IAAI,CAAC,WAAW;YAAE;AAEvB,QAAA,IAAI,KAAK,CAAC,KAAK,EAAE;AACf,YAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,KAA2C,CAAC;;AAGtE,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI;;;IAIjB,wBAAwB,GAAA;;;;QAI9B,OAAO,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,aAAa;;uGA9SnD,WAAW,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAX,WAAW,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,cAAA,EAAA,MAAA,EAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,OAAA,EAAA,SAAA,EAAA,SAAA,EAAA,WAAA,EAAA,QAAA,EAAA,UAAA,EAAA,OAAA,EAAA,SAAA,EAAA,OAAA,EAAA,SAAA,EAAA,UAAA,EAAA,YAAA,EAAA,SAAA,EAAA,WAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,aAAA,EAAA,eAAA,EAAA,uBAAA,EAAA,yBAAA,EAAA,qBAAA,EAAA,uBAAA,EAAA,EAAA,OAAA,EAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,YAAA,EAAA,cAAA,EAAA,aAAA,EAAA,eAAA,EAAA,WAAA,EAAA,aAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,aAAA,EAAA,eAAA,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,cAAA,EAAA,EAAA,SAAA,EAHX,CAAC,wCAAwC,CAAC,qEAuH1C,YAAY,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,UAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAGZ,WAAW,EAGX,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,eAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,gBAAgB,gGE3W7B,qxDA+CA,EAAA,MAAA,EAAA,CAAA,y3EAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EFgMY,eAAe,EAAE,QAAA,EAAA,mBAAA,EAAA,MAAA,EAAA,CAAA,iBAAA,CAAA,EAAA,OAAA,EAAA,CAAA,UAAA,CAAA,EAAA,QAAA,EAAA,CAAA,iBAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,eAAe,2JAAE,YAAY,EAAA,QAAA,EAAA,gBAAA,EAAA,MAAA,EAAA,CAAA,YAAA,EAAA,UAAA,EAAA,SAAA,EAAA,SAAA,EAAA,YAAA,EAAA,WAAA,EAAA,iBAAA,EAAA,eAAA,EAAA,yBAAA,EAAA,uBAAA,EAAA,YAAA,CAAA,EAAA,OAAA,EAAA,CAAA,gBAAA,EAAA,gBAAA,EAAA,aAAA,EAAA,WAAA,EAAA,kBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,cAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAE,WAAW,EAAA,QAAA,EAAA,eAAA,EAAA,MAAA,EAAA,CAAA,YAAA,EAAA,UAAA,EAAA,SAAA,EAAA,SAAA,EAAA,YAAA,EAAA,WAAA,CAAA,EAAA,OAAA,EAAA,CAAA,gBAAA,EAAA,eAAA,EAAA,kBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,aAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAE,gBAAgB,EAAA,QAAA,EAAA,qBAAA,EAAA,MAAA,EAAA,CAAA,YAAA,EAAA,UAAA,EAAA,SAAA,EAAA,SAAA,EAAA,YAAA,EAAA,WAAA,CAAA,EAAA,OAAA,EAAA,CAAA,gBAAA,EAAA,cAAA,EAAA,kBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAE5E,WAAW,EAAA,UAAA,EAAA,CAAA;kBAbvB,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,cAAc,EAGlB,IAAA,EAAA;AACJ,wBAAA,OAAO,EAAE,cAAc;qBACxB,EACS,QAAA,EAAA,aAAa,EACR,aAAA,EAAA,iBAAiB,CAAC,IAAI,mBACpB,uBAAuB,CAAC,MAAM,EAAA,SAAA,EACpC,CAAC,wCAAwC,CAAC,EAC5C,OAAA,EAAA,CAAC,eAAe,EAAE,eAAe,EAAE,YAAY,EAAE,WAAW,EAAE,gBAAgB,CAAC,EAAA,QAAA,EAAA,qxDAAA,EAAA,MAAA,EAAA,CAAA,y3EAAA,CAAA,EAAA;wDAS/E,eAAe,EAAA,CAAA;sBAAvB;gBAgBG,OAAO,EAAA,CAAA;sBADV;gBAUQ,SAAS,EAAA,CAAA;sBAAjB;gBAIG,QAAQ,EAAA,CAAA;sBADX;gBAeG,OAAO,EAAA,CAAA;sBADV;gBAWG,OAAO,EAAA,CAAA;sBADV;gBAUQ,UAAU,EAAA,CAAA;sBAAlB;gBAGQ,SAAS,EAAA,CAAA;sBAAjB;gBAGQ,eAAe,EAAA,CAAA;sBAAvB;gBAGQ,aAAa,EAAA,CAAA;sBAArB;gBAGQ,uBAAuB,EAAA,CAAA;sBAA/B;gBAGQ,qBAAqB,EAAA,CAAA;sBAA7B;gBAGkB,cAAc,EAAA,CAAA;sBAAhC;gBAMkB,YAAY,EAAA,CAAA;sBAA9B;gBAMkB,aAAa,EAAA,CAAA;sBAA/B;gBAKkB,WAAW,EAAA,CAAA;sBAA7B;gBAKkB,cAAc,EAAA,CAAA;sBAAhC;gBAIkB,aAAa,EAAA,CAAA;sBAA/B;gBAGwB,SAAS,EAAA,CAAA;sBAAjC,SAAS;uBAAC,YAAY;gBAGC,QAAQ,EAAA,CAAA;sBAA/B,SAAS;uBAAC,WAAW;gBAGO,aAAa,EAAA,CAAA;sBAAzC,SAAS;uBAAC,gBAAgB;;;AG7R7B;MACa,8BAA8B,GAAG,IAAI,cAAc,CAC9D,gCAAgC,EAChC;AACE,IAAA,UAAU,EAAE,MAAM;IAClB,OAAO,EAAE,MAAK;AACZ,QAAA,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC;QAC/B,OAAO,MAAM,OAAO,CAAC,gBAAgB,CAAC,UAAU,EAAE;KACnD;AACF,CAAA;AAGH;;;;AAIG;AACG,SAAU,sCAAsC,CAAC,OAAgB,EAAA;IACrE,OAAO,MAAM,OAAO,CAAC,gBAAgB,CAAC,UAAU,EAAE;AACpD;AAQA;;;;AAIG;AACU,MAAA,+CAA+C,GAAG;AAC7D,IAAA,OAAO,EAAE,8BAA8B;IACvC,IAAI,EAAE,CAAC,OAAO,CAAC;AACf,IAAA,UAAU,EAAE,sCAAsC;;AAGpD;;;;;;AAMG;MAgBU,oBAAoB,CAAA;AAGrB,IAAA,WAAW,GAAG,MAAM,CAA0B,UAAU,CAAC;AACzD,IAAA,mBAAmB,GAC3B,MAAM,CAAC,qBAAqB,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC,KAAK,gBAAgB;AAC9D,IAAA,kBAAkB,GAAG,MAAM,CAAC,iBAAiB,CAAC;AAC9C,IAAA,YAAY,GAAG,MAAM,CAA8B,qBAAqB,CAAC;AACzE,IAAA,YAAY,GAAG,MAAM,CAAiB,WAAW,CAAE;AACnD,IAAA,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;IACxB,uBAAuB,GAAG,MAAM,CACtC,iCAAiC,EACjC,EAAC,QAAQ,EAAE,IAAI,EAAC,CACjB;AAEO,IAAA,aAAa;AACb,IAAA,MAAM;AACN,IAAA,cAAc;AACd,IAAA,kBAAkB;;AAGF,IAAA,SAAS;AAEjC;;;;;;AAMG;AACM,IAAA,KAAK;;AAGd,IAAA,UAAU;;AAGV,IAAA,eAAe;;AAGf,IAAA,aAAa;;AAGb,IAAA,uBAAuB;;AAGvB,IAAA,qBAAqB;;AAGrB,IAAA,QAAQ;;AAGC,IAAA,cAAc,GAAG,IAAI,OAAO,EAAQ;;IAG7C,YAAY,GAAG,KAAK;;AAGpB,IAAA,gBAAgB;;AAGhB,IAAA,mBAAmB;;IAGnB,cAAc,GAA0B,IAAI;;AAG5C,IAAA,cAAc;AAId,IAAA,WAAA,GAAA;QACE,MAAM,CAAC,sBAAsB,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC;QAC1D,IAAI,CAAC,gBAAgB,GAAG,MAAM,CAAC,iBAAiB,CAAC,CAAC,kBAAkB;AAEpE,QAAA,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE;AAC7B,YAAA,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa;AAC9C,YAAA,MAAM,QAAQ,GAAG,MAAM,CAAC,SAAS,CAAC;YAElC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAM;gBACzD,QAAQ,CAAC,MAAM,CAAC,OAAO,EAAE,gBAAgB,EAAE,IAAI,CAAC,qBAAqB,CAAC;gBACtE,QAAQ,CAAC,MAAM,CAAC,OAAO,EAAE,cAAc,EAAE,IAAI,CAAC,qBAAqB,CAAC;gBACpE,QAAQ,CAAC,MAAM,CAAC,OAAO,EAAE,iBAAiB,EAAE,IAAI,CAAC,qBAAqB,CAAC;AACxE,aAAA,CAAC;;;IAIN,eAAe,GAAA;AACb,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,SAAS,CAAC,MAAK;AAC/D,YAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE;AACxC,SAAC,CAAC;AACF,QAAA,IAAI,CAAC,SAAS,CAAC,eAAe,EAAE;;IAGlC,WAAW,GAAA;AACT,QAAA,YAAY,CAAC,IAAI,CAAC,kBAAkB,CAAC;AACrC,QAAA,IAAI,CAAC,cAAc,EAAE,OAAO,CAAC,OAAO,IAAI,OAAO,EAAE,CAAC;AAClD,QAAA,IAAI,CAAC,aAAa,EAAE,WAAW,EAAE;AACjC,QAAA,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE;;AAGhC,IAAA,oBAAoB,CAAC,KAAqC,EAAA;AACxD,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS;AACvC,QAAA,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK;AACzB,QAAA,MAAM,OAAO,GAAG,SAAS,YAAY,SAAS;;;;;;AAO9C,QAAA,IAAI,OAAO,IAAI,IAAI,CAAC,uBAAuB,EAAE;AAC3C,YAAA,MAAM,YAAY,GAAG,IAAI,CAAC,uBAAuB,CAAC,iBAAiB,CACjE,KAAK,EACL,SAAoC,EACpC,KAAK,CAAC,KAAK,CACZ;YACD,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,YAA4B,EAAE,IAAI,CAAC;;AAC1D,aAAA,IACL,KAAK;AACL,aAAC,OAAO,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,KAAK,EAAE,SAAyB,CAAC,CAAC,EAC1E;AACA,YAAA,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC;;;AAIxB,QAAA,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,KAAK,CAAC,IAAI,CAAC,cAAc,EAAE;AACtE,YAAA,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE;;;AAI3B,IAAA,mBAAmB,CAAC,KAAyC,EAAA;QAC3D,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,KAAK,CAAC,KAAqB,EAAE,IAAI,CAAC;;IAGhE,mBAAmB,GAAA;QACjB,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,SAAS,CAAC,GAAG,CAAC,6BAA6B,CAAC;AAE3E,QAAA,IAAI,IAAI,CAAC,mBAAmB,EAAE;AAC5B,YAAA,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE;;aACrB;;;;AAIL,YAAA,YAAY,CAAC,IAAI,CAAC,kBAAkB,CAAC;AACrC,YAAA,IAAI,CAAC,kBAAkB,GAAG,UAAU,CAAC,MAAK;AACxC,gBAAA,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;AACtB,oBAAA,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE;;aAE7B,EAAE,GAAG,CAAC;;;AAIH,IAAA,qBAAqB,GAAG,CAAC,KAAqB,KAAI;AACxD,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa;AAE9C,QAAA,IAAI,KAAK,CAAC,MAAM,KAAK,OAAO,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,UAAU,CAAC,yBAAyB,CAAC,EAAE;YAC1F;;AAGF,QAAA,YAAY,CAAC,IAAI,CAAC,kBAAkB,CAAC;QACrC,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC,IAAI,KAAK,gBAAgB;QACnD,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,kCAAkC,EAAE,IAAI,CAAC,YAAY,CAAC;AAE/E,QAAA,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;AACtB,YAAA,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE;;AAE9B,KAAC;IAED,YAAY,GAAA;AACV,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,SAA+C;;;IAIpE,sBAAsB,GAAA;QACpB,IAAI,IAAI,CAAC,MAAM,KAAK,IAAI,CAAC,YAAY,EAAE;AACrC,YAAA,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,IAAI,CAAC;;;AAIlE;;;;;;AAMG;IACH,cAAc,CAAC,MAAkC,EAAE,aAAsB,EAAA;;;;AAIvE,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,YAAY;AACpE,QAAA,IAAI,CAAC,cAAc,GAAG,MAAM;QAE5B,IAAI,aAAa,EAAE;AACjB,YAAA,IAAI,CAAC,kBAAkB,CAAC,aAAa,EAAE;;;uGAlMhC,oBAAoB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAApB,oBAAoB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,wBAAA,EAAA,MAAA,EAAA,EAAA,KAAA,EAAA,OAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,OAAA,EAAA,iCAAA,EAAA,oCAAA,EAAA,oBAAA,EAAA,iDAAA,EAAA,sBAAA,EAAA,EAAA,cAAA,EAAA,wBAAA,EAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,WAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAqBpB,WAAW,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,sBAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EC/JxB,4uDA0CA,EAAA,MAAA,EAAA,CAAA,ilFAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,ED8FY,YAAY,EAAA,QAAA,EAAA,gBAAA,EAAA,MAAA,EAAA,CAAA,cAAA,EAAA,yBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,cAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAE,WAAW,EAAA,QAAA,EAAA,cAAA,EAAA,MAAA,EAAA,CAAA,iBAAA,EAAA,SAAA,EAAA,WAAA,EAAA,UAAA,EAAA,SAAA,EAAA,SAAA,EAAA,YAAA,EAAA,WAAA,EAAA,iBAAA,EAAA,eAAA,EAAA,yBAAA,EAAA,uBAAA,CAAA,EAAA,OAAA,EAAA,CAAA,gBAAA,EAAA,cAAA,EAAA,eAAA,EAAA,aAAA,EAAA,gBAAA,EAAA,eAAA,CAAA,EAAA,QAAA,EAAA,CAAA,aAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAE,eAAe,EAAA,QAAA,EAAA,mBAAA,EAAA,MAAA,EAAA,CAAA,iBAAA,CAAA,EAAA,OAAA,EAAA,CAAA,UAAA,CAAA,EAAA,QAAA,EAAA,CAAA,iBAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAE,SAAS,EAAA,QAAA,EAAA,6GAAA,EAAA,QAAA,EAAA,CAAA,WAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAEpD,oBAAoB,EAAA,UAAA,EAAA,CAAA;kBAfhC,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,wBAAwB,EAG5B,IAAA,EAAA;AACJ,wBAAA,OAAO,EAAE,wBAAwB;AACjC,wBAAA,SAAS,EAAE,6BAA6B;AACxC,wBAAA,sCAAsC,EAAE,oBAAoB;AAC5D,wBAAA,mDAAmD,EAAE,sBAAsB;AAC5E,qBAAA,EAAA,QAAA,EACS,sBAAsB,EACjB,aAAA,EAAA,iBAAiB,CAAC,IAAI,EAAA,eAAA,EACpB,uBAAuB,CAAC,MAAM,EACtC,OAAA,EAAA,CAAC,YAAY,EAAE,WAAW,EAAE,eAAe,EAAE,SAAS,CAAC,EAAA,QAAA,EAAA,4uDAAA,EAAA,MAAA,EAAA,CAAA,ilFAAA,CAAA,EAAA;wDAuBxC,SAAS,EAAA,CAAA;sBAAhC,SAAS;uBAAC,WAAW;gBASb,KAAK,EAAA,CAAA;sBAAb;;AAwNH;MAEsB,iBAAiB,CAAA;AAO7B,IAAA,QAAQ,GAAG,MAAM,CAAC,OAAO,CAAC;AAC1B,IAAA,iBAAiB,GAAG,MAAM,CAAC,gBAAgB,CAAC;IAC5C,YAAY,GAAG,MAAM,CAAiB,WAAW,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAE;IACrE,IAAI,GAAG,MAAM,CAAC,cAAc,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;AAC/C,IAAA,MAAM,GAAG,MAAM,CAA8B,qBAAqB,CAAC;AAEnE,IAAA,eAAe,GAAG,MAAM,CAAC,8BAA8B,CAAC;AACxD,IAAA,kBAAkB,GAAG,YAAY,CAAC,KAAK;AACvC,IAAA,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC;;AAG3B,IAAA,uBAAuB;;AAGhC,IAAA,IACI,OAAO,GAAA;;;QAGT,OAAO,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC,aAAa,EAAE,GAAG,IAAI,CAAC;;IAE9F,IAAI,OAAO,CAAC,KAAe,EAAA;AACzB,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;;AAEpF,IAAA,QAAQ;;IAGP,SAAS,GAAoC,OAAO;AAE7D;;;;;;AAMG;AACH,IAAA,IACI,KAAK,GAAA;QACP,QACE,IAAI,CAAC,MAAM,KAAK,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC,eAAe,EAAE,GAAG,SAAS,CAAC;;IAG9F,IAAI,KAAK,CAAC,KAAmB,EAAA;AAC3B,QAAA,IAAI,CAAC,MAAM,GAAG,KAAK;;AAErB,IAAA,MAAM;AAEN;;;AAGG;IAEH,OAAO,GAAY,KAAK;;AAGxB,IAAA,IACI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,SAAS,KAAK,SAAS,IAAI,IAAI,CAAC;AAC1C,cAAE,IAAI,CAAC,eAAe,CAAC;AACvB,cAAE,CAAC,CAAC,IAAI,CAAC,SAAS;;IAEtB,IAAI,QAAQ,CAAC,KAAc,EAAA;AACzB,QAAA,IAAI,KAAK,KAAK,IAAI,CAAC,SAAS,EAAE;AAC5B,YAAA,IAAI,CAAC,SAAS,GAAG,KAAK;AACtB,YAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC;;;AAG7B,IAAA,SAAS;;IAIjB,SAAS,GAAgC,OAAO;;IAIhD,SAAS,GAAgC,OAAO;AAEhD;;;;AAIG;IAEH,YAAY,GAAY,IAAI;AAE5B;;;AAGG;AACgB,IAAA,YAAY,GAAoB,IAAI,YAAY,EAAK;AAExE;;;AAGG;AACgB,IAAA,aAAa,GAAoB,IAAI,YAAY,EAAK;AAEzE;;AAEG;AACgB,IAAA,WAAW,GAAkC,IAAI,YAAY,CAC9E,IAAI,CACL;;AAGQ,IAAA,SAAS;;AAGS,IAAA,YAAY,GAAG,IAAI,YAAY,EAAQ;;AAGvC,IAAA,YAAY,GAAG,IAAI,YAAY,EAAQ;;AAGlE,IAAA,IACI,UAAU,GAAA;QACZ,OAAO,IAAI,CAAC,WAAW;;IAEzB,IAAI,UAAU,CAAC,KAAwB,EAAA;AACrC,QAAA,IAAI,CAAC,WAAW,GAAG,iBAAiB,CAAC,KAAK,CAAC;;AAErC,IAAA,WAAW;;AAGnB,IAAA,IACI,MAAM,GAAA;QACR,OAAO,IAAI,CAAC,OAAO;;IAErB,IAAI,MAAM,CAAC,KAAc,EAAA;QACvB,IAAI,KAAK,EAAE;YACT,IAAI,CAAC,IAAI,EAAE;;aACN;YACL,IAAI,CAAC,KAAK,EAAE;;;IAGR,OAAO,GAAG,KAAK;;IAGvB,EAAE,GAAW,MAAM,CAAC,YAAY,CAAC,CAAC,KAAK,CAAC,iBAAiB,CAAC;;IAG1D,WAAW,GAAA;QACT,OAAO,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,eAAe,CAAC,GAAG;;;IAIzD,WAAW,GAAA;QACT,OAAO,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,eAAe,CAAC,GAAG;;IAGzD,cAAc,GAAA;QACZ,OAAO,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,eAAe,CAAC,UAAU;;;AAIxD,IAAA,WAAW;;AAGX,IAAA,aAAa;;IAGb,yBAAyB,GAAuB,IAAI;;AAGpD,IAAA,qBAAqB,GAAG,CAAG,EAAA,IAAI,CAAC,EAAE,WAAW;;AAG7C,IAAA,cAAc;;AAGtB,IAAA,eAAe;;AAGN,IAAA,YAAY,GAAG,IAAI,OAAO,EAAQ;AAEnC,IAAA,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC;AAEnB,IAAA,kBAAkB,GAAG,MAAM,CAAC,iBAAiB,CAAC;AAI/D,IAAA,WAAA,GAAA;AACE,QAAA,IAAI,CAAC,IAAI,CAAC,YAAY,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAAE;AACzE,YAAA,MAAM,0BAA0B,CAAC,aAAa,CAAC;;QAGjD,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,SAAS,CAAC,MAAK;AAC1C,YAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE;AACxC,SAAC,CAAC;;AAGJ,IAAA,WAAW,CAAC,OAAsB,EAAA;QAChC,MAAM,cAAc,GAAG,OAAO,CAAC,WAAW,CAAC,IAAI,OAAO,CAAC,WAAW,CAAC;QAEnE,IAAI,cAAc,IAAI,CAAC,cAAc,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,EAAE;YACrE,MAAM,gBAAgB,GAAG,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,CAAC,gBAAgB;AAEtE,YAAA,IAAI,gBAAgB,YAAY,iCAAiC,EAAE;AACjE,gBAAA,IAAI,CAAC,sBAAsB,CAAC,gBAAgB,CAAC;AAE7C,gBAAA,IAAI,IAAI,CAAC,MAAM,EAAE;AACf,oBAAA,IAAI,CAAC,WAAW,CAAC,cAAc,EAAE;;;;AAKvC,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC;;IAGnC,WAAW,GAAA;QACT,IAAI,CAAC,eAAe,EAAE;QACtB,IAAI,CAAC,KAAK,EAAE;AACZ,QAAA,IAAI,CAAC,kBAAkB,CAAC,WAAW,EAAE;AACrC,QAAA,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE;;;AAI9B,IAAA,MAAM,CAAC,IAAO,EAAA;AACZ,QAAA,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC;;;AAIvB,IAAA,WAAW,CAAC,cAAiB,EAAA;AAC3B,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC;;;AAIxC,IAAA,YAAY,CAAC,eAAkB,EAAA;AAC7B,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,eAAe,CAAC;;;AAI1C,IAAA,YAAY,CAAC,IAAqB,EAAA;AAChC,QAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC;;AAG7B;;;;AAIG;AACH,IAAA,aAAa,CAAC,KAAQ,EAAA;AACpB,QAAA,IAAI,IAAI,CAAC,eAAe,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAAE;AAC3E,YAAA,MAAM,KAAK,CAAC,6DAA6D,CAAC;;AAE5E,QAAA,IAAI,CAAC,kBAAkB,CAAC,WAAW,EAAE;AACrC,QAAA,IAAI,CAAC,eAAe,GAAG,KAAK;QAC5B,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC,YAAY,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC/F,OAAO,IAAI,CAAC,MAAM;;AAGpB;;;AAGG;AACH,IAAA,eAAe,CAAC,MAAsB,EAAA;AACpC,QAAA,IAAI,IAAI,CAAC,cAAc,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAAE;AAC1E,YAAA,MAAM,KAAK,CAAC,mEAAmE,CAAC;;AAElF,QAAA,IAAI,CAAC,cAAc,GAAG,MAAM;QAC5B,IAAI,CAAC,aAAa,EAAE,QAAQ,CAAC,cAAc,CAAC,MAAM,EAAE,IAAI,CAAC;;AAG3D;;;AAGG;AACH,IAAA,aAAa,CAAC,MAAsB,EAAA;AAClC,QAAA,IAAI,MAAM,KAAK,IAAI,CAAC,cAAc,EAAE;AAClC,YAAA,IAAI,CAAC,cAAc,GAAG,IAAI;YAC1B,IAAI,CAAC,aAAa,EAAE,QAAQ,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC;;;;IAK3D,IAAI,GAAA;;;AAGF,QAAA,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,aAAa,EAAE,QAAQ,CAAC,YAAY,EAAE;YAC9E;;AAGF,QAAA,IAAI,CAAC,IAAI,CAAC,eAAe,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAAE;AAC5E,YAAA,MAAM,KAAK,CAAC,8DAA8D,CAAC;;AAG7E,QAAA,IAAI,CAAC,yBAAyB,GAAG,iCAAiC,EAAE;QACpE,IAAI,CAAC,YAAY,EAAE;AACnB,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI;AACnB,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE;;;IAI1B,KAAK,GAAA;;;AAGH,QAAA,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,aAAa,EAAE,QAAQ,CAAC,YAAY,EAAE;YAC9D;;AAGF,QAAA,MAAM,eAAe,GACnB,IAAI,CAAC,YAAY;AACjB,YAAA,IAAI,CAAC,yBAAyB;AAC9B,YAAA,OAAO,IAAI,CAAC,yBAAyB,CAAC,KAAK,KAAK,UAAU;QAE5D,MAAM,aAAa,GAAG,MAAK;;;AAGzB,YAAA,IAAI,IAAI,CAAC,OAAO,EAAE;AAChB,gBAAA,IAAI,CAAC,OAAO,GAAG,KAAK;AACpB,gBAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE;;AAE5B,SAAC;AAED,QAAA,IAAI,IAAI,CAAC,aAAa,EAAE;YACtB,MAAM,EAAC,QAAQ,EAAE,QAAQ,EAAC,GAAG,IAAI,CAAC,aAAa;AAC/C,YAAA,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,MAAK;AACnD,gBAAA,MAAM,aAAa,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa;;;AAIlD,gBAAA,IACE,eAAe;AACf,qBAAC,CAAC,aAAa;AACb,wBAAA,aAAa,KAAK,IAAI,CAAC,SAAS,CAAC,aAAa;wBAC9C,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,EACjD;AACA,oBAAA,IAAI,CAAC,yBAA0B,CAAC,KAAK,EAAE;;AAGzC,gBAAA,IAAI,CAAC,yBAAyB,GAAG,IAAI;gBACrC,IAAI,CAAC,eAAe,EAAE;AACxB,aAAC,CAAC;YACF,QAAQ,CAAC,mBAAmB,EAAE;;QAGhC,IAAI,eAAe,EAAE;;;;;;YAMnB,UAAU,CAAC,aAAa,CAAC;;aACpB;AACL,YAAA,aAAa,EAAE;;;;IAKnB,sBAAsB,GAAA;AACpB,QAAA,IAAI,CAAC,aAAa,EAAE,QAAQ,EAAE,sBAAsB,EAAE;;;AAI9C,IAAA,qBAAqB,CAAC,QAAoC,EAAA;AAClE,QAAA,QAAQ,CAAC,UAAU,GAAG,IAAI;AAC1B,QAAA,QAAQ,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK;QAC3B,QAAQ,CAAC,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC,iBAAiB,EAAE;QAClE,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,cAAc,EAAE,KAAK,CAAC;;;IAI7C,YAAY,GAAA;QAClB,IAAI,CAAC,eAAe,EAAE;AAEtB,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO;QAC7B,MAAM,MAAM,GAAG,IAAI,eAAe,CAChC,oBAAoB,EACpB,IAAI,CAAC,iBAAiB,CACvB;AACD,QAAA,MAAM,UAAU,IAAI,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CACzD,IAAI,aAAa,CAAC;AAChB,YAAA,gBAAgB,EAAE,QAAQ,GAAG,IAAI,CAAC,kBAAkB,EAAE,GAAG,IAAI,CAAC,oBAAoB,EAAE;AACpF,YAAA,WAAW,EAAE,IAAI;AACjB,YAAA,aAAa,EAAE;AACb,gBAAA,QAAQ,GAAG,2BAA2B,GAAG,kCAAkC;AAC3E,gBAAA,IAAI,CAAC,qBAAqB;AAC3B,aAAA;AACD,YAAA,SAAS,EAAE,IAAI,CAAC,IAAI,IAAI,KAAK;AAC7B,YAAA,cAAc,EAAE,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,eAAe,EAAE;YAC1F,UAAU,EAAE,CAAkB,eAAA,EAAA,QAAQ,GAAG,QAAQ,GAAG,OAAO,CAAE,CAAA;SAC9D,CAAC,CACH,CAAC;QAEF,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC,SAAS,CAAC,KAAK,IAAG;YACjD,IAAI,KAAK,EAAE;gBACT,KAAK,CAAC,cAAc,EAAE;;YAExB,IAAI,CAAC,KAAK,EAAE;AACd,SAAC,CAAC;;;;;QAMF,UAAU,CAAC,aAAa,EAAE,CAAC,SAAS,CAAC,KAAK,IAAG;AAC3C,YAAA,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO;YAE7B,IACE,OAAO,KAAK,QAAQ;AACpB,gBAAA,OAAO,KAAK,UAAU;AACtB,gBAAA,OAAO,KAAK,UAAU;AACtB,gBAAA,OAAO,KAAK,WAAW;AACvB,gBAAA,OAAO,KAAK,OAAO;gBACnB,OAAO,KAAK,SAAS,EACrB;gBACA,KAAK,CAAC,cAAc,EAAE;;AAE1B,SAAC,CAAC;QAEF,IAAI,CAAC,aAAa,GAAG,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC;QAC9C,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC;;QAGvD,IAAI,CAAC,QAAQ,EAAE;YACb,eAAe,CACb,MAAK;gBACH,UAAU,CAAC,cAAc,EAAE;aAC5B,EACD,EAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,EAAC,CAC3B;;;;IAKG,eAAe,GAAA;AACrB,QAAA,IAAI,IAAI,CAAC,WAAW,EAAE;AACpB,YAAA,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE;YAC1B,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,aAAa,GAAG,IAAI;;;;IAKxC,kBAAkB,GAAA;AACxB,QAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC,MAAM,EAAE,CAAC,kBAAkB,EAAE,CAAC,gBAAgB,EAAE;;;IAI1E,oBAAoB,GAAA;AAC1B,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC;AACnB,aAAA,QAAQ;AACR,aAAA,mBAAmB,CAAC,IAAI,CAAC,eAAe,CAAC,yBAAyB,EAAE;aACpE,qBAAqB,CAAC,yBAAyB;aAC/C,sBAAsB,CAAC,KAAK;aAC5B,kBAAkB,CAAC,CAAC;AACpB,aAAA,kBAAkB,EAAE;AAEvB,QAAA,OAAO,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC;;;AAItC,IAAA,sBAAsB,CAAC,QAA2C,EAAA;AACxE,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,KAAK,KAAK,GAAG,KAAK,GAAG,OAAO;AAC3D,QAAA,MAAM,UAAU,GAAG,QAAQ,KAAK,OAAO,GAAG,KAAK,GAAG,OAAO;AACzD,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,KAAK,OAAO,GAAG,QAAQ,GAAG,KAAK;AAC9D,QAAA,MAAM,UAAU,GAAG,QAAQ,KAAK,KAAK,GAAG,QAAQ,GAAG,KAAK;QAExD,OAAO,QAAQ,CAAC,aAAa,CAAC;AAC5B,YAAA;AACE,gBAAA,OAAO,EAAE,QAAQ;AACjB,gBAAA,OAAO,EAAE,UAAU;AACnB,gBAAA,QAAQ,EAAE,QAAQ;AAClB,gBAAA,QAAQ,EAAE,QAAQ;AACnB,aAAA;AACD,YAAA;AACE,gBAAA,OAAO,EAAE,QAAQ;AACjB,gBAAA,OAAO,EAAE,QAAQ;AACjB,gBAAA,QAAQ,EAAE,QAAQ;AAClB,gBAAA,QAAQ,EAAE,UAAU;AACrB,aAAA;AACD,YAAA;AACE,gBAAA,OAAO,EAAE,UAAU;AACnB,gBAAA,OAAO,EAAE,UAAU;AACnB,gBAAA,QAAQ,EAAE,UAAU;AACpB,gBAAA,QAAQ,EAAE,QAAQ;AACnB,aAAA;AACD,YAAA;AACE,gBAAA,OAAO,EAAE,UAAU;AACnB,gBAAA,OAAO,EAAE,QAAQ;AACjB,gBAAA,QAAQ,EAAE,UAAU;AACpB,gBAAA,QAAQ,EAAE,UAAU;AACrB,aAAA;AACF,SAAA,CAAC;;;AAII,IAAA,eAAe,CAAC,UAAsB,EAAA;QAC5C,MAAM,sBAAsB,GAAkB,CAAC,SAAS,EAAE,UAAU,EAAE,SAAS,CAAC;QAChF,OAAO,KAAK,CACV,UAAU,CAAC,aAAa,EAAE,EAC1B,UAAU,CAAC,WAAW,EAAE,EACxB,UAAU,CAAC,aAAa,EAAE,CAAC,IAAI,CAC7B,MAAM,CAAC,KAAK,IAAG;;AAEb,YAAA,QACE,CAAC,KAAK,CAAC,OAAO,KAAK,MAAM,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC;iBAClD,IAAI,CAAC,eAAe;AACnB,oBAAA,cAAc,CAAC,KAAK,EAAE,QAAQ,CAAC;oBAC/B,KAAK,CAAC,OAAO,KAAK,QAAQ;AAC1B,oBAAA,sBAAsB,CAAC,KAAK,CAC1B,CAAC,QAAqB,KAAK,CAAC,cAAc,CAAC,KAAK,EAAE,QAAQ,CAAC,CAC5D,CAAC;SAEP,CAAC,CACH,CACF;;uGA/fiB,iBAAiB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAjB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,iBAAiB,gLAyDlB,gBAAgB,CAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAIhB,gBAAgB,CA2BhB,EAAA,SAAA,EAAA,WAAA,EAAA,SAAA,EAAA,WAAA,EAAA,YAAA,EAAA,CAAA,cAAA,EAAA,cAAA,EAAA,gBAAgB,kFA0ChB,gBAAgB,CAAA,EAAA,EAAA,OAAA,EAAA,EAAA,YAAA,EAAA,cAAA,EAAA,aAAA,EAAA,eAAA,EAAA,WAAA,EAAA,aAAA,EAAA,YAAA,EAAA,QAAA,EAAA,YAAA,EAAA,QAAA,EAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAlIf,iBAAiB,EAAA,UAAA,EAAA,CAAA;kBADtC;wDAmBU,uBAAuB,EAAA,CAAA;sBAA/B;gBAIG,OAAO,EAAA,CAAA;sBADV;gBAYQ,SAAS,EAAA,CAAA;sBAAjB;gBAUG,KAAK,EAAA,CAAA;sBADR;gBAgBD,OAAO,EAAA,CAAA;sBADN,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBAKhC,QAAQ,EAAA,CAAA;sBADX,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBAgBpC,SAAS,EAAA,CAAA;sBADR;gBAKD,SAAS,EAAA,CAAA;sBADR;gBASD,YAAY,EAAA,CAAA;sBADX,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBAOjB,YAAY,EAAA,CAAA;sBAA9B;gBAMkB,aAAa,EAAA,CAAA;sBAA/B;gBAKkB,WAAW,EAAA,CAAA;sBAA7B;gBAKQ,SAAS,EAAA,CAAA;sBAAjB;gBAG0B,YAAY,EAAA,CAAA;sBAAtC,MAAM;uBAAC,QAAQ;gBAGW,YAAY,EAAA,CAAA;sBAAtC,MAAM;uBAAC,QAAQ;gBAIZ,UAAU,EAAA,CAAA;sBADb;gBAWG,MAAM,EAAA,CAAA;sBADT,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;;;AExftC;AACA;AACA;AACA;AAYM,MAAO,aAAiB,SAAQ,iBAAuD,CAAA;uGAAhF,aAAa,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAb,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,aAAa,EALb,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,gBAAA,EAAA,SAAA,EAAA;YACT,wCAAwC;AACxC,YAAA,EAAC,OAAO,EAAE,iBAAiB,EAAE,WAAW,EAAE,aAAa,EAAC;AACzD,SAAA,EAAA,QAAA,EAAA,CAAA,eAAA,CAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAPS,EAAE,EAAA,QAAA,EAAA,IAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FASD,aAAa,EAAA,UAAA,EAAA,CAAA;kBAXzB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,gBAAgB;AAC1B,oBAAA,QAAQ,EAAE,EAAE;AACZ,oBAAA,QAAQ,EAAE,eAAe;oBACzB,eAAe,EAAE,uBAAuB,CAAC,MAAM;oBAC/C,aAAa,EAAE,iBAAiB,CAAC,IAAI;AACrC,oBAAA,SAAS,EAAE;wBACT,wCAAwC;AACxC,wBAAA,EAAC,OAAO,EAAE,iBAAiB,EAAE,WAAW,eAAe,EAAC;AACzD,qBAAA;AACF,iBAAA;;;ACYD;;;;AAIG;MACU,uBAAuB,CAAA;AAMzB,IAAA,MAAA;AAEA,IAAA,aAAA;;AANT,IAAA,KAAK;AAEL,IAAA,WAAA;;IAES,MAAoC;;IAEpC,aAA0B,EAAA;QAF1B,IAAM,CAAA,MAAA,GAAN,MAAM;QAEN,IAAa,CAAA,aAAA,GAAb,aAAa;QAEpB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK;;AAEjC;AAuBD;MAEsB,sBAAsB,CAAA;AAGhC,IAAA,WAAW,GAAG,MAAM,CAA+B,UAAU,CAAC;IACxE,YAAY,GAAG,MAAM,CAAiB,WAAW,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAE;IAC7D,YAAY,GAAG,MAAM,CAAiB,gBAAgB,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAE;;AAG1E,IAAA,cAAc;;AAGtB,IAAA,IACI,KAAK,GAAA;QACP,OAAO,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,aAAa;;IAE1F,IAAI,KAAK,CAAC,KAAU,EAAA;AAClB,QAAA,IAAI,CAAC,4BAA4B,CAAC,KAAK,CAAC;;AAEhC,IAAA,MAAM;;AAGhB,IAAA,IACI,QAAQ,GAAA;QACV,OAAO,CAAC,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,eAAe,EAAE;;IAEnD,IAAI,QAAQ,CAAC,KAAc,EAAA;QACzB,MAAM,QAAQ,GAAG,KAAK;AACtB,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa;AAE9C,QAAA,IAAI,IAAI,CAAC,SAAS,KAAK,QAAQ,EAAE;AAC/B,YAAA,IAAI,CAAC,SAAS,GAAG,QAAQ;AACzB,YAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC;;;;;;QAOnC,IAAI,QAAQ,IAAI,IAAI,CAAC,cAAc,IAAI,OAAO,CAAC,IAAI,EAAE;;;;YAInD,OAAO,CAAC,IAAI,EAAE;;;AAGV,IAAA,SAAS;;AAGE,IAAA,UAAU,GAAgD,IAAI,YAAY,EAE1F;;AAGgB,IAAA,SAAS,GAAgD,IAAI,YAAY,EAEzF;;AAGM,IAAA,YAAY,GAAG,IAAI,OAAO,EAAQ;AAE3C,IAAA,UAAU,GAAG,MAAK,GAAG;AACrB,IAAA,kBAAkB,GAAG,MAAK,GAAG;AAErB,IAAA,YAAY,GAAyB,MAAK,GAAG;AAC7C,IAAA,yBAAyB,GAAG,YAAY,CAAC,KAAK;AAC9C,IAAA,mBAAmB,GAAG,YAAY,CAAC,KAAK;AAEhD;;;;AAIG;AACK,IAAA,aAAa;;IAGb,eAAe,GAAgB,MAA8B;QACnE,OAAO,IAAI,CAAC;AACV,cAAE;AACF,cAAE,EAAC,oBAAoB,EAAE,EAAC,MAAM,EAAE,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,KAAK,EAAC,EAAC;AAC5E,KAAC;;AAGO,IAAA,gBAAgB,GAAgB,CAAC,OAAwB,KAA6B;AAC5F,QAAA,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,kBAAkB,CACvD,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC,CAC7C;QACD,OAAO,CAAC,YAAY,IAAI,IAAI,CAAC,cAAc,CAAC,YAAY;AACtD,cAAE;AACF,cAAE,EAAC,qBAAqB,EAAE,IAAI,EAAC;AACnC,KAAC;;AAGO,IAAA,aAAa,GAAgB,CAAC,OAAwB,KAA6B;AACzF,QAAA,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,kBAAkB,CACvD,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC,CAC7C;AACD,QAAA,MAAM,GAAG,GAAG,IAAI,CAAC,WAAW,EAAE;AAC9B,QAAA,OAAO,CAAC,GAAG,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,GAAG,EAAE,YAAY,CAAC,IAAI;AAClF,cAAE;AACF,cAAE,EAAC,kBAAkB,EAAE,EAAC,KAAK,EAAE,GAAG,EAAE,QAAQ,EAAE,YAAY,EAAC,EAAC;AAChE,KAAC;;AAGO,IAAA,aAAa,GAAgB,CAAC,OAAwB,KAA6B;AACzF,QAAA,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,kBAAkB,CACvD,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC,CAC7C;AACD,QAAA,MAAM,GAAG,GAAG,IAAI,CAAC,WAAW,EAAE;AAC9B,QAAA,OAAO,CAAC,GAAG,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,GAAG,EAAE,YAAY,CAAC,IAAI;AAClF,cAAE;AACF,cAAE,EAAC,kBAAkB,EAAE,EAAC,KAAK,EAAE,GAAG,EAAE,QAAQ,EAAE,YAAY,EAAC,EAAC;AAChE,KAAC;;IAGS,cAAc,GAAA;AACtB,QAAA,OAAO,CAAC,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,gBAAgB,CAAC;;;AAa9F,IAAA,cAAc,CAAC,KAAkC,EAAA;AAC/C,QAAA,IAAI,CAAC,MAAM,GAAG,KAAK;AACnB,QAAA,IAAI,CAAC,yBAAyB,CAAC,WAAW,EAAE;AAE5C,QAAA,IAAI,IAAI,CAAC,aAAa,EAAE;AACtB,YAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,aAAa,CAAC;;AAGvC,QAAA,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,SAAS,CAAC,KAAK,IAAG;AAC9E,YAAA,IAAI,IAAI,CAAC,wBAAwB,CAAC,KAAK,CAAC,EAAE;gBACxC,MAAM,KAAK,GAAG,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,SAAS,CAAC;gBACtD,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC;AAChD,gBAAA,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC;gBACxB,IAAI,CAAC,UAAU,EAAE;AACjB,gBAAA,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC;AACxB,gBAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,uBAAuB,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC;AACtF,gBAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,uBAAuB,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC;;AAE3F,SAAC,CAAC;;;IAmBM,eAAe,GAAG,KAAK;AAIjC,IAAA,WAAA,GAAA;AACE,QAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;AACjD,YAAA,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;AACtB,gBAAA,MAAM,0BAA0B,CAAC,aAAa,CAAC;;AAEjD,YAAA,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;AACtB,gBAAA,MAAM,0BAA0B,CAAC,kBAAkB,CAAC;;;;AAKxD,QAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,SAAS,CAAC,MAAK;AACxE,YAAA,IAAI,CAAC,4BAA4B,CAAC,IAAI,CAAC,KAAK,CAAC;AAC/C,SAAC,CAAC;;IAGJ,eAAe,GAAA;AACb,QAAA,IAAI,CAAC,cAAc,GAAG,IAAI;;AAG5B,IAAA,WAAW,CAAC,OAAsB,EAAA;QAChC,IAAI,qBAAqB,CAAC,OAAO,EAAE,IAAI,CAAC,YAAY,CAAC,EAAE;AACrD,YAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC;;;IAIrC,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,yBAAyB,CAAC,WAAW,EAAE;AAC5C,QAAA,IAAI,CAAC,mBAAmB,CAAC,WAAW,EAAE;AACtC,QAAA,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE;;;AAI9B,IAAA,yBAAyB,CAAC,EAAc,EAAA;AACtC,QAAA,IAAI,CAAC,kBAAkB,GAAG,EAAE;;;AAI9B,IAAA,QAAQ,CAAC,CAAkB,EAAA;AACzB,QAAA,OAAO,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,IAAI;;;AAIpD,IAAA,UAAU,CAAC,KAAQ,EAAA;AACjB,QAAA,IAAI,CAAC,4BAA4B,CAAC,KAAK,CAAC;;;AAI1C,IAAA,gBAAgB,CAAC,EAAwB,EAAA;AACvC,QAAA,IAAI,CAAC,YAAY,GAAG,EAAE;;;AAIxB,IAAA,iBAAiB,CAAC,EAAc,EAAA;AAC9B,QAAA,IAAI,CAAC,UAAU,GAAG,EAAE;;;AAItB,IAAA,gBAAgB,CAAC,UAAmB,EAAA;AAClC,QAAA,IAAI,CAAC,QAAQ,GAAG,UAAU;;AAG5B,IAAA,UAAU,CAAC,KAAoB,EAAA;QAC7B,MAAM,sBAAsB,GAAkB,CAAC,SAAS,EAAE,UAAU,EAAE,SAAS,CAAC;AAChF,QAAA,MAAM,cAAc,GAClB,cAAc,CAAC,KAAK,EAAE,QAAQ,CAAC;YAC/B,KAAK,CAAC,OAAO,KAAK,UAAU;AAC5B,YAAA,sBAAsB,CAAC,KAAK,CAAC,CAAC,QAAqB,KAAK,CAAC,cAAc,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;QAE3F,IAAI,cAAc,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,QAAQ,EAAE;YAC9D,IAAI,CAAC,UAAU,EAAE;YACjB,KAAK,CAAC,cAAc,EAAE;;;AAI1B,IAAA,QAAQ,CAAC,KAAa,EAAA;AACpB,QAAA,MAAM,iBAAiB,GAAG,IAAI,CAAC,eAAe;AAC9C,QAAA,IAAI,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,KAAK,EAAE,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,SAAS,CAAC;QAC5E,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC;QAC/C,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC,IAAI,CAAC;AACjD,QAAA,MAAM,UAAU,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC;;;AAIhE,QAAA,IAAI,CAAC,IAAI,IAAI,UAAU,EAAE;AACvB,YAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;;aAClB;;;AAGL,YAAA,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;AACxB,gBAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;;AAGzB,YAAA,IAAI,iBAAiB,KAAK,IAAI,CAAC,eAAe,EAAE;gBAC9C,IAAI,CAAC,kBAAkB,EAAE;;;QAI7B,IAAI,UAAU,EAAE;AACd,YAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;AACvB,YAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,uBAAuB,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC;;;IAI1F,SAAS,GAAA;AACP,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,uBAAuB,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC;;;IAIzF,OAAO,GAAA;;AAEL,QAAA,IAAI,IAAI,CAAC,KAAK,EAAE;AACd,YAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC;;QAG/B,IAAI,CAAC,UAAU,EAAE;;;AAIT,IAAA,YAAY,CAAC,KAAe,EAAA;AACpC,QAAA,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,KAAK;YAClC,KAAK,IAAI,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,EAAE;;;AAIrF,IAAA,YAAY,CAAC,KAAe,EAAA;;;AAGlC,QAAA,IAAI,IAAI,CAAC,MAAM,EAAE;AACf,YAAA,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC;AAC/B,YAAA,IAAI,CAAC,aAAa,GAAG,IAAI;;aACpB;AACL,YAAA,IAAI,CAAC,aAAa,GAAG,KAAK;;;;AAKtB,IAAA,aAAa,CAAC,KAAe,EAAA;QACnC,OAAO,CAAC,KAAK,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,KAAK,CAAC;;AAGnD;;;AAGG;IACO,eAAe,GAAA;AACvB,QAAA,OAAO,KAAK;;;AAIJ,IAAA,4BAA4B,CAAC,KAAe,EAAA;QACpD,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,KAAK,CAAC;QAC5C,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC;QAChD,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC,KAAK,CAAC;AACnD,QAAA,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC;AACxB,QAAA,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC;;;AAI1B,IAAA,cAAc,CAAC,KAAe,EAAA;AAC5B,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,EAAE;AACpC,QAAA,OAAO,CAAC,MAAM,IAAI,MAAM,CAAC,KAAK,CAAC;;uGA1Ub,sBAAsB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAtB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,sBAAsB,mFAqBvB,gBAAgB,CAAA,EAAA,EAAA,OAAA,EAAA,EAAA,UAAA,EAAA,YAAA,EAAA,SAAA,EAAA,WAAA,EAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FArBf,sBAAsB,EAAA,UAAA,EAAA,CAAA;kBAD3C;wDAaK,KAAK,EAAA,CAAA;sBADR;gBAWG,QAAQ,EAAA,CAAA;sBADX,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBA2BjB,UAAU,EAAA,CAAA;sBAA5B;gBAKkB,SAAS,EAAA,CAAA;sBAA3B;;AAyRH;;;AAGG;AACa,SAAA,qBAAqB,CACnC,OAAsB,EACtB,OAA6B,EAAA;IAE7B,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC;AAEjC,IAAA,KAAK,IAAI,GAAG,IAAI,IAAI,EAAE;QACpB,MAAM,EAAC,aAAa,EAAE,YAAY,EAAC,GAAG,OAAO,CAAC,GAAG,CAAC;AAElD,QAAA,IAAI,OAAO,CAAC,cAAc,CAAC,aAAa,CAAC,IAAI,OAAO,CAAC,cAAc,CAAC,YAAY,CAAC,EAAE;YACjF,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,aAAa,EAAE,YAAY,CAAC,EAAE;AAClD,gBAAA,OAAO,IAAI;;;aAER;AACL,YAAA,OAAO,IAAI;;;AAIf,IAAA,OAAO,KAAK;AACd;;ACnaA;AACa,MAAA,6BAA6B,GAAQ;AAChD,IAAA,OAAO,EAAE,iBAAiB;AAC1B,IAAA,WAAW,EAAE,UAAU,CAAC,MAAM,kBAAkB,CAAC;AACjD,IAAA,KAAK,EAAE,IAAI;;AAGb;AACa,MAAA,yBAAyB,GAAQ;AAC5C,IAAA,OAAO,EAAE,aAAa;AACtB,IAAA,WAAW,EAAE,UAAU,CAAC,MAAM,kBAAkB,CAAC;AACjD,IAAA,KAAK,EAAE,IAAI;;AAGb;AAyBM,MAAO,kBACX,SAAQ,sBAAmC,CAAA;IAGnC,UAAU,GAAG,MAAM,CAAuB,cAAc,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;AAC3E,IAAA,mBAAmB,GAAG,YAAY,CAAC,KAAK;AACxC,IAAA,mBAAmB,GAAG,YAAY,CAAC,KAAK;;IAGhD,IACI,aAAa,CAAC,UAAoE,EAAA;QACpF,IAAI,UAAU,EAAE;AACd,YAAA,IAAI,CAAC,WAAW,GAAG,UAAU;AAC7B,YAAA,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,MAAM,GAAG,UAAU,CAAC,EAAE,GAAG,IAAI,CAAC;YAC5D,IAAI,CAAC,mBAAmB,GAAG,UAAU,CAAC,YAAY,CAAC,SAAS,CAAC,MAAK;gBAChE,IAAI,CAAC,UAAU,EAAE;AACjB,gBAAA,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC;AAC1B,aAAC,CAAC;YACF,IAAI,CAAC,mBAAmB,GAAG,UAAU,CAAC,YAAY,CAAC,SAAS,CAAC,MAAK;gBAChE,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,CAAC;AACnC,aAAC,CAAC;YACF,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;;;AAGvD,IAAA,WAAW;;AAGD,IAAA,SAAS,GAAG,MAAM,CAAgB,IAAI,CAAC;;AAGjD,IAAA,IACI,GAAG,GAAA;QACL,OAAO,IAAI,CAAC,IAAI;;IAElB,IAAI,GAAG,CAAC,KAAe,EAAA;AACrB,QAAA,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;AAE7F,QAAA,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE;AACtD,YAAA,IAAI,CAAC,IAAI,GAAG,UAAU;YACtB,IAAI,CAAC,kBAAkB,EAAE;;;AAGrB,IAAA,IAAI;;AAGZ,IAAA,IACI,GAAG,GAAA;QACL,OAAO,IAAI,CAAC,IAAI;;IAElB,IAAI,GAAG,CAAC,KAAe,EAAA;AACrB,QAAA,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;AAE7F,QAAA,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE;AACtD,YAAA,IAAI,CAAC,IAAI,GAAG,UAAU;YACtB,IAAI,CAAC,kBAAkB,EAAE;;;AAGrB,IAAA,IAAI;;AAGZ,IAAA,IACI,UAAU,GAAA;QACZ,OAAO,IAAI,CAAC,WAAW;;IAEzB,IAAI,UAAU,CAAC,KAA6B,EAAA;QAC1C,MAAM,gBAAgB,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC;AACxD,QAAA,IAAI,CAAC,WAAW,GAAG,KAAK;QAExB,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,gBAAgB,EAAE;YACxD,IAAI,CAAC,kBAAkB,EAAE;;;AAGrB,IAAA,WAAW;;AAGT,IAAA,UAAU;AAIpB,IAAA,WAAA,GAAA;AACE,QAAA,KAAK,EAAE;AACP,QAAA,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC,OAAO,CAAC,KAAK,CAAC,cAAc,EAAE,CAAC;;AAG9D;;;AAGG;IACH,yBAAyB,GAAA;AACvB,QAAA,OAAO,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,yBAAyB,EAAE,GAAG,IAAI,CAAC,WAAW;;;IAIzF,iBAAiB,GAAA;AACf,QAAA,IAAI,IAAI,CAAC,UAAU,EAAE;AACnB,YAAA,OAAO,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE;;QAGrC,OAAO,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,YAAY,CAAC,iBAAiB,CAAC;;;IAIvE,eAAe,GAAA;AACb,QAAA,OAAO,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,GAAG,SAAS;;;IAI5D,aAAa,GAAA;QACX,OAAO,IAAI,CAAC,KAAK;;IAGV,WAAW,GAAA;QAClB,KAAK,CAAC,WAAW,EAAE;AACnB,QAAA,IAAI,CAAC,mBAAmB,CAAC,WAAW,EAAE;AACtC,QAAA,IAAI,CAAC,mBAAmB,CAAC,WAAW,EAAE;;;IAI9B,UAAU,GAAA;AAClB,QAAA,IAAI,IAAI,CAAC,WAAW,EAAE;AACpB,YAAA,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE;;;AAIjB,IAAA,kBAAkB,CAAC,UAAoB,EAAA;AAC/C,QAAA,OAAO,UAAU;;AAGT,IAAA,mBAAmB,CAAC,KAAe,EAAA;AAC3C,QAAA,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,KAAK,EAAE,IAAI,CAAC;;;;IAK5C,WAAW,GAAA;QACT,OAAO,IAAI,CAAC,IAAI;;;IAIlB,WAAW,GAAA;QACT,OAAO,IAAI,CAAC,IAAI;;;IAIR,cAAc,GAAA;QACtB,OAAO,IAAI,CAAC,WAAW;;AAGf,IAAA,wBAAwB,CAAC,KAAkC,EAAA;AACnE,QAAA,OAAO,KAAK,CAAC,MAAM,KAAK,IAAI;;uGAtJnB,kBAAkB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAlB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,kBAAkB,EAtBlB,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,sBAAA,EAAA,MAAA,EAAA,EAAA,aAAA,EAAA,eAAA,EAAA,GAAA,EAAA,KAAA,EAAA,GAAA,EAAA,KAAA,EAAA,UAAA,EAAA,CAAA,qBAAA,EAAA,YAAA,CAAA,EAAA,EAAA,IAAA,EAAA,EAAA,SAAA,EAAA,EAAA,OAAA,EAAA,+BAAA,EAAA,QAAA,EAAA,aAAA,EAAA,MAAA,EAAA,WAAA,EAAA,SAAA,EAAA,oBAAA,EAAA,EAAA,UAAA,EAAA,EAAA,oBAAA,EAAA,iCAAA,EAAA,gBAAA,EAAA,aAAA,EAAA,UAAA,EAAA,0CAAA,EAAA,UAAA,EAAA,0CAAA,EAAA,wBAAA,EAAA,qCAAA,EAAA,UAAA,EAAA,UAAA,EAAA,EAAA,cAAA,EAAA,sBAAA,EAAA,EAAA,SAAA,EAAA;YACT,6BAA6B;YAC7B,yBAAyB;AACzB,YAAA,EAAC,OAAO,EAAE,wBAAwB,EAAE,WAAW,EAAE,kBAAkB,EAAC;AACrE,SAAA,EAAA,QAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAkBU,kBAAkB,EAAA,UAAA,EAAA,CAAA;kBAxB9B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,sBAAsB;AAChC,oBAAA,SAAS,EAAE;wBACT,6BAA6B;wBAC7B,yBAAyB;AACzB,wBAAA,EAAC,OAAO,EAAE,wBAAwB,EAAE,WAAW,oBAAoB,EAAC;AACrE,qBAAA;AACD,oBAAA,IAAI,EAAE;AACJ,wBAAA,OAAO,EAAE,sBAAsB;AAC/B,wBAAA,sBAAsB,EAAE,+BAA+B;AACvD,wBAAA,kBAAkB,EAAE,aAAa;AACjC,wBAAA,YAAY,EAAE,0CAA0C;AACxD,wBAAA,YAAY,EAAE,0CAA0C;;;AAGxD,wBAAA,0BAA0B,EAAE,qCAAqC;AACjE,wBAAA,YAAY,EAAE,UAAU;AACxB,wBAAA,SAAS,EAAE,+BAA+B;AAC1C,wBAAA,UAAU,EAAE,aAAa;AACzB,wBAAA,QAAQ,EAAE,WAAW;AACrB,wBAAA,WAAW,EAAE,oBAAoB;AAClC,qBAAA;AACD,oBAAA,QAAQ,EAAE,oBAAoB;AAC/B,iBAAA;wDAWK,aAAa,EAAA,CAAA;sBADhB;gBAsBG,GAAG,EAAA,CAAA;sBADN;gBAgBG,GAAG,EAAA,CAAA;sBADN;gBAgBG,UAAU,EAAA,CAAA;sBADb,KAAK;uBAAC,qBAAqB;;;ACvF9B;MAIa,uBAAuB,CAAA;uGAAvB,uBAAuB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAvB,uBAAuB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,2BAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAAvB,uBAAuB,EAAA,UAAA,EAAA,CAAA;kBAHnC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,2BAA2B;AACtC,iBAAA;;MAyBY,mBAAmB,CAAA;AAC9B,IAAA,KAAK,GAAG,MAAM,CAAC,iBAAiB,CAAC;AACzB,IAAA,kBAAkB,GAAG,MAAM,CAAC,iBAAiB,CAAC;AAC9C,IAAA,aAAa,GAAG,YAAY,CAAC,KAAK;;AAG5B,IAAA,UAAU;;AAGf,IAAA,QAAQ;;AAGI,IAAA,SAAS;;AAG9B,IAAA,IACI,QAAQ,GAAA;QACV,IAAI,IAAI,CAAC,SAAS,KAAK,SAAS,IAAI,IAAI,CAAC,UAAU,EAAE;AACnD,YAAA,OAAO,IAAI,CAAC,UAAU,CAAC,QAAQ;;AAGjC,QAAA,OAAO,CAAC,CAAC,IAAI,CAAC,SAAS;;IAEzB,IAAI,QAAQ,CAAC,KAAc,EAAA;AACzB,QAAA,IAAI,CAAC,SAAS,GAAG,KAAK;;AAEhB,IAAA,SAAS;;AAGR,IAAA,aAAa;;AAGiB,IAAA,WAAW;;AAG7B,IAAA,OAAO;AAI5B,IAAA,WAAA,GAAA;AACE,QAAA,MAAM,eAAe,GAAG,MAAM,CAAC,IAAI,kBAAkB,CAAC,UAAU,CAAC,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;AACpF,QAAA,MAAM,cAAc,GAAG,MAAM,CAAC,eAAe,CAAC;AAC9C,QAAA,IAAI,CAAC,QAAQ,GAAG,cAAc,IAAI,cAAc,KAAK,CAAC,GAAG,cAAc,GAAG,IAAI;;AAGhF,IAAA,WAAW,CAAC,OAAsB,EAAA;AAChC,QAAA,IAAI,OAAO,CAAC,YAAY,CAAC,EAAE;YACzB,IAAI,CAAC,kBAAkB,EAAE;;;IAI7B,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE;;IAGlC,kBAAkB,GAAA;QAChB,IAAI,CAAC,kBAAkB,EAAE;;AAG3B,IAAA,KAAK,CAAC,KAAY,EAAA;QAChB,IAAI,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AACrC,YAAA,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE;YACtB,KAAK,CAAC,eAAe,EAAE;;;IAInB,kBAAkB,GAAA;AACxB,QAAA,MAAM,sBAAsB,GAAG,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,YAAY,GAAGC,EAAY,EAAE;QAC9F,MAAM,iBAAiB,GACrB,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC;AACjC,cAAE,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC;cAChCA,EAAY,EAAE;AACpB,QAAA,MAAM,iBAAiB,GAAG,IAAI,CAAC;AAC7B,cAAE,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE,IAAI,CAAC,UAAU,CAAC,YAAY;cAChEA,EAAY,EAAE;AAElB,QAAA,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE;AAChC,QAAA,IAAI,CAAC,aAAa,GAAG,KAAK,CACxB,IAAI,CAAC,KAAK,CAAC,OAAO,EAClB,sBAA0C,EAC1C,iBAAiB,EACjB,iBAAiB,CAClB,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;;uGAlFhD,mBAAmB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAnB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,mBAAmB,uMAeX,gBAAgB,CAAA,EAAA,aAAA,EAAA,eAAA,EAAA,EAAA,IAAA,EAAA,EAAA,SAAA,EAAA,EAAA,OAAA,EAAA,eAAA,EAAA,EAAA,UAAA,EAAA,EAAA,eAAA,EAAA,MAAA,EAAA,oCAAA,EAAA,iCAAA,EAAA,kBAAA,EAAA,+CAAA,EAAA,gBAAA,EAAA,6CAAA,EAAA,wBAAA,EAAA,mCAAA,EAAA,EAAA,cAAA,EAAA,uBAAA,EAAA,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,aAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAiBrB,uBAAuB,EC1FvC,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,SAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,QAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,s1BA0BA,wYD8BY,aAAa,EAAA,QAAA,EAAA,yBAAA,EAAA,QAAA,EAAA,CAAA,WAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAEZ,mBAAmB,EAAA,UAAA,EAAA,CAAA;kBAtB/B,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,uBAAuB,EAG3B,IAAA,EAAA;AACJ,wBAAA,OAAO,EAAE,uBAAuB;AAChC,wBAAA,iBAAiB,EAAE,MAAM;AACzB,wBAAA,sCAAsC,EAAE,iCAAiC;AACzE,wBAAA,oBAAoB,EAAE,6CAA6C;AACnE,wBAAA,kBAAkB,EAAE,2CAA2C;;AAE/D,wBAAA,0BAA0B,EAAE,mCAAmC;;;;AAI/D,wBAAA,SAAS,EAAE,eAAe;AAC3B,qBAAA,EAAA,QAAA,EACS,qBAAqB,EAAA,aAAA,EAChB,iBAAiB,CAAC,IAAI,EAAA,eAAA,EACpB,uBAAuB,CAAC,MAAM,EAAA,OAAA,EACtC,CAAC,aAAa,CAAC,EAAA,QAAA,EAAA,s1BAAA,EAAA,MAAA,EAAA,CAAA,gVAAA,CAAA,EAAA;wDAQV,UAAU,EAAA,CAAA;sBAAvB,KAAK;uBAAC,KAAK;gBAGH,QAAQ,EAAA,CAAA;sBAAhB;gBAGoB,SAAS,EAAA,CAAA;sBAA7B,KAAK;uBAAC,YAAY;gBAIf,QAAQ,EAAA,CAAA;sBADX,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBAc3B,aAAa,EAAA,CAAA;sBAArB;gBAGsC,WAAW,EAAA,CAAA;sBAAjD,YAAY;uBAAC,uBAAuB;gBAGhB,OAAO,EAAA,CAAA;sBAA3B,SAAS;uBAAC,QAAQ;;;MEpCR,iBAAiB,CAAA;AASpB,IAAA,kBAAkB,GAAG,MAAM,CAAC,iBAAiB,CAAC;AAC9C,IAAA,WAAW,GAAG,MAAM,CAA0B,UAAU,CAAC;IACzD,YAAY,GAAG,MAAM,CAAiB,WAAW,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAE;IACrE,UAAU,GAAG,MAAM,CAAuB,cAAc,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;AAE3E,IAAA,mBAAmB,GAAG,YAAY,CAAC,KAAK;AACxC,IAAA,mBAAmB,GAAG,YAAY,CAAC,KAAK;AAEhD,IAAA,WAAW;AACX,IAAA,SAAS;;AAGT,IAAA,IAAI,KAAK,GAAA;AACP,QAAA,OAAO,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG,IAAI;;;IAInD,EAAE,GAAW,MAAM,CAAC,YAAY,CAAC,CAAC,KAAK,CAAC,uBAAuB,CAAC;;IAGhE,OAAO,GAAG,KAAK;;AAGf,IAAA,IAAI,gBAAgB,GAAA;QAClB,OAAO,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK;;;IAIpC,WAAW,GAAG,sBAAsB;AAEpC;;;;AAIG;AACH,IAAA,IAAI,WAAW,GAAA;QACb,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,EAAE,eAAe,EAAE,IAAI,EAAE;QACvD,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,EAAE,eAAe,EAAE,IAAI,EAAE;AACnD,QAAA,OAAO,KAAK,IAAI,GAAG,GAAG,GAAG,KAAK,CAAA,CAAA,EAAI,IAAI,CAAC,SAAS,IAAI,GAAG,CAAA,CAAE,GAAG,EAAE;;;AAIhE,IAAA,IACI,WAAW,GAAA;QACb,OAAO,IAAI,CAAC,YAAY;;IAE1B,IAAI,WAAW,CAAC,WAAyE,EAAA;QACvF,IAAI,WAAW,EAAE;YACf,IAAI,CAAC,MAAM,GAAG,WAAW,CAAC,aAAa,CAAC,IAAI,CAAC;AAC7C,YAAA,IAAI,CAAC,YAAY,GAAG,WAAW;AAC/B,YAAA,IAAI,CAAC,mBAAmB,CAAC,WAAW,EAAE;AACtC,YAAA,IAAI,CAAC,mBAAmB,CAAC,WAAW,EAAE;YACtC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,WAAW,CAAC,EAAE,GAAG,IAAI,CAAC;YACnE,IAAI,CAAC,mBAAmB,GAAG,WAAW,CAAC,YAAY,CAAC,SAAS,CAAC,MAAK;AACjE,gBAAA,IAAI,CAAC,WAAW,EAAE,UAAU,EAAE;AAC9B,gBAAA,IAAI,CAAC,SAAS,EAAE,UAAU,EAAE;AAC5B,gBAAA,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC;AAC1B,aAAC,CAAC;YACF,IAAI,CAAC,mBAAmB,GAAG,WAAW,CAAC,YAAY,CAAC,SAAS,CAAC,MAAK;gBACjE,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE,CAAC;AACpC,aAAC,CAAC;AACF,YAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAO,CAAC;;;AAG7B,IAAA,YAAY;;AAGpB,IAAA,SAAS,GAAG,MAAM,CAAgB,IAAI,CAAC;;AAGvC,IAAA,IACI,QAAQ,GAAA;QACV,QACE,IAAI,CAAC,SAAS;AACd,aAAC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC;AAC3B,gBAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,WAAW,CAAC;AACxC,gBAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AACzC,YAAA,KAAK;;IAGT,IAAI,QAAQ,CAAC,KAAc,EAAA;AACzB,QAAA,IAAI,CAAC,SAAS,GAAG,KAAK;;AAEhB,IAAA,SAAS;;AAGjB,IAAA,IACI,UAAU,GAAA;QACZ,OAAO,IAAI,CAAC,WAAW;;IAEzB,IAAI,UAAU,CAAC,KAAsB,EAAA;AACnC,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW;AAC9B,QAAA,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS;AAC1B,QAAA,MAAM,gBAAgB,GAAG,KAAK,IAAI,KAAK,CAAC,cAAc,CAAC,KAAK,CAAC,KAAK,CAAC;AACnE,QAAA,MAAM,cAAc,GAAG,GAAG,IAAI,GAAG,CAAC,cAAc,CAAC,KAAK,CAAC,KAAK,CAAC;AAC7D,QAAA,IAAI,CAAC,WAAW,GAAG,KAAK;AAExB,QAAA,IAAI,KAAK,IAAI,KAAK,CAAC,cAAc,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,gBAAgB,EAAE;YACnE,KAAK,CAAC,kBAAkB,EAAE;;AAG5B,QAAA,IAAI,GAAG,IAAI,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,cAAc,EAAE;YAC3D,GAAG,CAAC,kBAAkB,EAAE;;;AAGpB,IAAA,WAAW;;AAGnB,IAAA,IACI,GAAG,GAAA;QACL,OAAO,IAAI,CAAC,IAAI;;IAElB,IAAI,GAAG,CAAC,KAAe,EAAA;AACrB,QAAA,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;AAE7F,QAAA,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE;AACtD,YAAA,IAAI,CAAC,IAAI,GAAG,UAAU;YACtB,IAAI,CAAC,WAAW,EAAE;;;AAGd,IAAA,IAAI;;AAGZ,IAAA,IACI,GAAG,GAAA;QACL,OAAO,IAAI,CAAC,IAAI;;IAElB,IAAI,GAAG,CAAC,KAAe,EAAA;AACrB,QAAA,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;AAE7F,QAAA,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE;AACtD,YAAA,IAAI,CAAC,IAAI,GAAG,UAAU;YACtB,IAAI,CAAC,WAAW,EAAE;;;AAGd,IAAA,IAAI;;AAGZ,IAAA,IACI,QAAQ,GAAA;AACV,QAAA,OAAO,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC;cAC5B,IAAI,CAAC,WAAW,CAAC,QAAQ,IAAI,IAAI,CAAC,SAAS,CAAC;AAC9C,cAAE,IAAI,CAAC,cAAc;;IAEzB,IAAI,QAAQ,CAAC,KAAc,EAAA;AACzB,QAAA,IAAI,KAAK,KAAK,IAAI,CAAC,cAAc,EAAE;AACjC,YAAA,IAAI,CAAC,cAAc,GAAG,KAAK;AAC3B,YAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC;;;IAGrC,cAAc,GAAG,KAAK;;AAGtB,IAAA,IAAI,UAAU,GAAA;QACZ,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,SAAS,EAAE;YACtC,OAAO,IAAI,CAAC,WAAW,CAAC,UAAU,IAAI,IAAI,CAAC,SAAS,CAAC,UAAU;;AAGjE,QAAA,OAAO,KAAK;;;AAId,IAAA,IAAI,KAAK,GAAA;AACP,QAAA,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,GAAG,KAAK;AACxE,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,KAAK;QAClE,OAAO,UAAU,IAAI,QAAQ;;;IAI/B,gBAAgB,GAAkB,IAAI;;AAG9B,IAAA,MAAM;;IAGL,SAAS,GAAG,GAAG;;IAGf,eAAe,GAAa,IAAI;;IAGhC,aAAa,GAAa,IAAI;AAEvC;;;;AAIG;AACH,IAAA,SAAS;;AAGA,IAAA,YAAY,GAAG,IAAI,OAAO,EAAQ;AAE3C;;;AAGG;IACM,wBAAwB,GAAG,IAAI;AAIxC,IAAA,WAAA,GAAA;AACE,QAAA,IAAI,CAAC,IAAI,CAAC,YAAY,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAAE;AACzE,YAAA,MAAM,0BAA0B,CAAC,aAAa,CAAC;;;;AAKjD,QAAA,IAAI,IAAI,CAAC,UAAU,EAAE,WAAW,CAAC,aAAa,CAAC,SAAS,CAAC,QAAQ,CAAC,oBAAoB,CAAC,EAAE;AACvF,YAAA,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,SAAS,CAAC,GAAG,CAC1C,uBAAuB,EACvB,kCAAkC,EAClC,uBAAuB,CACxB;;;AAIH,QAAA,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC,gBAAgB,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAC,CAAQ;;AAGhF;;;AAGG;AACH,IAAA,iBAAiB,CAAC,GAAa,EAAA;AAC7B,QAAA,IAAI,CAAC,gBAAgB,GAAG,GAAG,CAAC,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI;;AAG3D;;;AAGG;IACH,gBAAgB,GAAA;QACd,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AACnC,YAAA,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,EAAE;AAChD,gBAAA,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE;;iBACnB;AACL,gBAAA,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE;;;;IAK5B,kBAAkB,GAAA;AAChB,QAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;AACjD,YAAA,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;AACrB,gBAAA,MAAM,KAAK,CAAC,wDAAwD,CAAC;;AAGvE,YAAA,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;AACnB,gBAAA,MAAM,KAAK,CAAC,sDAAsD,CAAC;;;AAIvE,QAAA,IAAI,IAAI,CAAC,MAAM,EAAE;AACf,YAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC;;;;AAKlC,QAAA,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,YAAY,EAAE,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,SAAS,CAAC,MAAK;AAC/E,YAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC;AACnC,SAAC,CAAC;;AAGJ,IAAA,WAAW,CAAC,OAAsB,EAAA;QAChC,IAAI,qBAAqB,CAAC,OAAO,EAAE,IAAI,CAAC,YAAY,CAAC,EAAE;AACrD,YAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC;;;IAIrC,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,mBAAmB,CAAC,WAAW,EAAE;AACtC,QAAA,IAAI,CAAC,mBAAmB,CAAC,WAAW,EAAE;AACtC,QAAA,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE;;;IAI9B,aAAa,GAAA;AACX,QAAA,OAAO,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI;;;IAI7C,eAAe,GAAA;AACb,QAAA,OAAO,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,GAAG,SAAS;;;IAI5D,yBAAyB,GAAA;AACvB,QAAA,OAAO,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,yBAAyB,EAAE,GAAG,IAAI,CAAC,WAAW;;;IAIzF,iBAAiB,GAAA;AACf,QAAA,OAAO,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE,GAAG,IAAI;;;AAI9D,IAAA,oBAAoB,CAAC,IAAqB,EAAA;AACxC,QAAA,MAAM,KAAK,GAAG,IAAI,KAAK,OAAO,GAAG,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,SAAS;AAClE,QAAA,OAAO,KAAK,GAAG,KAAK,CAAC,cAAc,EAAE,GAAG,EAAE;;;IAI5C,uBAAuB,GAAA;AACrB,QAAA,OAAO,IAAI,CAAC,WAAW,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,GAAG,KAAK;;;IAI/D,uBAAuB,GAAA;AACrB,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC;AACjC,QAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE;;;IAIxC,eAAe,GAAA;AACb,QAAA,IAAI,IAAI,CAAC,YAAY,EAAE;AACrB,YAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE;;;;IAK5B,oBAAoB,GAAA;AAClB,QAAA,QACE,CAAC,CAAC,IAAI,CAAC,UAAU;AACf,aAAC,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,iBAAiB,EAAE,CAAC;YACxE,IAAI,CAAC,KAAK;;;IAKd,kBAAkB,GAAA;AAChB,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU;AACjC,QAAA,OAAO,SAAS,IAAI,SAAS,CAAC,iBAAiB,EAAE,GAAG,SAAS,CAAC,QAAQ,GAAG,IAAI;;IAG/E,2BAA2B,GAAA;AACzB,QAAA,OAAO,IAAI,CAAC,WAAW,CAAC,kBAAkB,EAAE;;IAG9C,yBAAyB,GAAA;AACvB,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,kBAAkB,EAAE;;;AAI5C,IAAA,YAAY,CAAC,MAAmB,EAAA;AAC9B,QAAA,IAAI,CAAC,OAAO,GAAG,MAAM,KAAK,IAAI;AAC9B,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE;;;IAIlB,WAAW,GAAA;AACjB,QAAA,IAAI,IAAI,CAAC,WAAW,EAAE;AACpB,YAAA,IAAI,CAAC,WAAW,CAAC,kBAAkB,EAAE;;AAGvC,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;AAClB,YAAA,IAAI,CAAC,SAAS,CAAC,kBAAkB,EAAE;;;;AAK/B,IAAA,cAAc,CAAC,KAA0C,EAAA;AAC/D,QAAA,IAAI,IAAI,CAAC,WAAW,EAAE;AACpB,YAAA,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC,KAAK,CAAC;;AAGxC,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;AAClB,YAAA,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,KAAK,CAAC;;;;AAKhC,IAAA,iBAAiB,CAAC,MAA4C,EAAA;AACpE,QAAA,OAAO,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,YAAY,CAAC,UAAU,CAAC,QAAQ,CAAC;;uGA9X3D,iBAAiB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAjB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,iBAAiB,iIA+ET,gBAAgB,CAAA,EAAA,UAAA,EAAA,YAAA,EAAA,GAAA,EAAA,KAAA,EAAA,GAAA,EAAA,KAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAoEhB,gBAAgB,CAtJxB,EAAA,SAAA,EAAA,WAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,aAAA,EAAA,eAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,MAAA,EAAA,OAAA,EAAA,EAAA,UAAA,EAAA,EAAA,8CAAA,EAAA,2BAAA,EAAA,qCAAA,EAAA,UAAA,EAAA,SAAA,EAAA,IAAA,EAAA,sBAAA,EAAA,sBAAA,EAAA,uBAAA,EAAA,kBAAA,EAAA,wBAAA,EAAA,qCAAA,EAAA,EAAA,cAAA,EAAA,sBAAA,EAAA,EAAA,SAAA,EAAA,CAAC,EAAC,OAAO,EAAE,mBAAmB,EAAE,WAAW,EAAE,iBAAiB,EAAC,CAAC,ECtD7E,QAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,2yBAuBA,i2IDgCY,eAAe,EAAA,QAAA,EAAA,oDAAA,EAAA,OAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,iBAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAEd,iBAAiB,EAAA,UAAA,EAAA,CAAA;kBAtB7B,SAAS;+BACE,sBAAsB,EAAA,QAAA,EAGtB,mBAAmB,EACvB,IAAA,EAAA;AACJ,wBAAA,OAAO,EAAE,sBAAsB;AAC/B,wBAAA,gDAAgD,EAAE,2BAA2B;AAC7E,wBAAA,uCAAuC,EAAE,UAAU;AACnD,wBAAA,WAAW,EAAE,IAAI;AACjB,wBAAA,MAAM,EAAE,OAAO;AACf,wBAAA,wBAAwB,EAAE,sBAAsB;AAChD,wBAAA,yBAAyB,EAAE,kBAAkB;;;AAG7C,wBAAA,0BAA0B,EAAE,qCAAqC;qBAClE,EACgB,eAAA,EAAA,uBAAuB,CAAC,MAAM,EAAA,aAAA,EAChC,iBAAiB,CAAC,IAAI,aAC1B,CAAC,EAAC,OAAO,EAAE,mBAAmB,EAAE,WAAW,EAAA,iBAAmB,EAAC,CAAC,EAAA,OAAA,EAClE,CAAC,eAAe,CAAC,EAAA,QAAA,EAAA,2yBAAA,EAAA,MAAA,EAAA,CAAA,yyIAAA,CAAA,EAAA;wDAsDtB,WAAW,EAAA,CAAA;sBADd;gBA6BG,QAAQ,EAAA,CAAA;sBADX,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBAiBhC,UAAU,EAAA,CAAA;sBADb;gBAuBG,GAAG,EAAA,CAAA;sBADN;gBAgBG,GAAG,EAAA,CAAA;sBADN;gBAgBG,QAAQ,EAAA,CAAA;sBADX,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBAqC3B,SAAS,EAAA,CAAA;sBAAjB;gBAGQ,eAAe,EAAA,CAAA;sBAAvB;gBAGQ,aAAa,EAAA,CAAA;sBAArB;;;AE/OH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;;;;;;;;;;;;;;;;;;;AAmBG;AACG,SAAU,0BAA0B,CACxC,OAA+C,EAAA;AAE/C,IAAA,OAAO,kCAAkC,CAAC,OAAO,EAAE,IAAI,CAAC;AAC1D;AAEA;;;AAGG;AACH,SAAS,gBAAgB,CAAC,IAAU,EAAA;AAClC,IAAA,OAAO,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,YAAY;AAC5C;AAEA;;;AAGG;AACH,SAAS,yBAAyB,CAAC,IAAU,EAAA;AAC3C,IAAA,OAAO,IAAI,CAAC,QAAQ,KAAK,OAAO;AAClC;AAEA;;;AAGG;AACH,SAAS,4BAA4B,CAAC,IAAU,EAAA;AAC9C,IAAA,OAAO,IAAI,CAAC,QAAQ,KAAK,UAAU;AACrC;AAEA;;;;;;;;;AASG;AACH,SAAS,kCAAkC,CACzC,WAAiB,EACjB,oBAA6B,EAAA;;;;;;;AAS7B,IAAA,IAAI,gBAAgB,CAAC,WAAW,CAAC,IAAI,oBAAoB,EAAE;AACzD,QAAA,MAAM,aAAa,GACjB,WAAW,CAAC,YAAY,GAAG,iBAAiB,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,IAAI,EAAE;QACpE,MAAM,WAAW,GAAkB,aAAa,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,EAAE,KAAI;YACvE,MAAM,IAAI,GAAG,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC;YACxC,IAAI,IAAI,EAAE;AACR,gBAAA,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;;AAErB,YAAA,OAAO,QAAQ;SAChB,EAAE,EAAmB,CAAC;AAEvB,QAAA,IAAI,WAAW,CAAC,MAAM,EAAE;AACtB,YAAA,OAAO;iBACJ,GAAG,CAAC,KAAK,IAAG;AACX,gBAAA,OAAO,kCAAkC,CAAC,KAAK,EAAE,KAAK,CAAC;AACzD,aAAC;iBACA,IAAI,CAAC,GAAG,CAAC;;;;AAKhB,IAAA,IAAI,gBAAgB,CAAC,WAAW,CAAC,EAAE;QACjC,MAAM,SAAS,GAAG,WAAW,CAAC,YAAY,CAAC,YAAY,CAAC,EAAE,IAAI,EAAE;QAEhE,IAAI,SAAS,EAAE;AACb,YAAA,OAAO,SAAS;;;;;;;;IASpB,IAAI,yBAAyB,CAAC,WAAW,CAAC,IAAI,4BAA4B,CAAC,WAAW,CAAC,EAAE;;AAEvF,QAAA,IAAI,WAAW,CAAC,MAAM,EAAE,MAAM,EAAE;AAC9B,YAAA,OAAO,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM;iBACjC,GAAG,CAAC,CAAC,IAAI,kCAAkC,CAAC,CAAC,EAAE,KAAK,CAAC;iBACrD,IAAI,CAAC,GAAG,CAAC;;;QAId,MAAM,WAAW,GAAG,WAAW,CAAC,YAAY,CAAC,aAAa,CAAC,EAAE,IAAI,EAAE;QACnE,IAAI,WAAW,EAAE;AACf,YAAA,OAAO,WAAW;;;QAIpB,MAAM,KAAK,GAAG,WAAW,CAAC,YAAY,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE;QACvD,IAAI,KAAK,EAAE;AACT,YAAA,OAAO,KAAK;;;;;;;;;;;;;;;;AAkBhB,IAAA,OAAO,CAAC,WAAW,CAAC,WAAW,IAAI,EAAE,EAAE,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,IAAI,EAAE;AACpE;;AC7IA;;AAEG;AACH,MACe,yBACb,SAAQ,sBAAoC,CAAA;AAG5C,IAAA,WAAW,GAAG,MAAM,CAAuB,iBAAiB,CAAC;AACpD,IAAA,WAAW,GAAG,MAAM,CAA+B,UAAU,CAAC;AACvE,IAAA,yBAAyB,GAAG,MAAM,CAAC,iBAAiB,CAAC;AAC7C,IAAA,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC;IACpC,WAAW,GAAG,MAAM,CAAC,MAAM,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;IAC9C,gBAAgB,GAAG,MAAM,CAAC,kBAAkB,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;AAE/D;;;AAGG;AACH,IAAA,SAAS;IAMU,IAAI,GAAG,MAAM,CAAC,cAAc,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;AAC1D,IAAA,kBAAkB;;AAG1B,IAAA,IACI,iBAAiB,GAAA;AACnB,QAAA,OAAO,IAAI,CAAC,kBAAkB,CAAC,OAAO;;IAExC,IAAI,iBAAiB,CAAC,KAAwB,EAAA;AAC5C,QAAA,IAAI,CAAC,kBAAkB,CAAC,OAAO,GAAG,KAAK;;;AAIzC,IAAA,IAAI,UAAU,GAAA;AACZ,QAAA,OAAO,IAAI,CAAC,kBAAkB,CAAC,UAAU;;IAE3C,IAAI,UAAU,CAAC,KAAc,EAAA;AAC3B,QAAA,IAAI,CAAC,kBAAkB,CAAC,UAAU,GAAG,KAAK;;AAK5C,IAAA,WAAA,GAAA;AACE,QAAA,KAAK,EAAE;QAEP,IAAI,CAAC,kBAAkB,GAAG,IAAI,kBAAkB,CAC9C,IAAI,CAAC,yBAAyB,EAC9B,IAAI,EACJ,IAAI,CAAC,gBAAgB,EACrB,IAAI,CAAC,WAAW,EAChB,IAAI,CAAC,YAAY,CAClB;;IAGH,QAAQ,GAAA;;;;;;;QAON,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,IAAI,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAC,CAAC;QAEnF,IAAI,SAAS,EAAE;AACb,YAAA,IAAI,CAAC,SAAS,GAAG,SAAS;AAC1B,YAAA,IAAI,CAAC,kBAAkB,CAAC,SAAS,GAAG,SAAS;;;IAIjD,kBAAkB,GAAA;QAChB,IAAI,CAAC,SAAS,EAAE;;IAGlB,SAAS,GAAA;AACP,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;;;;YAIlB,IAAI,CAAC,gBAAgB,EAAE;;;;IAK3B,OAAO,GAAA;QACL,OAAO,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC;;;IAI1D,eAAe,GAAA;AACb,QAAA,OAAO,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,WAAW;;;IAInD,KAAK,GAAA;AACH,QAAA,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,KAAK,EAAE;;;IAIxC,cAAc,GAAA;AACZ,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa;AAC9C,QAAA,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK;AAC3B,QAAA,OAAO,KAAK,CAAC,MAAM,GAAG,CAAC,GAAG,KAAK,GAAG,OAAO,CAAC,WAAW;;;IAIvD,gBAAgB,GAAA;AACd,QAAA,IAAI,CAAC,kBAAkB,CAAC,gBAAgB,EAAE;;;AAInC,IAAA,QAAQ,CAAC,KAAa,EAAA;AAC7B,QAAA,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC;AACrB,QAAA,IAAI,CAAC,WAAW,CAAC,uBAAuB,EAAE;;;IAIlC,UAAU,GAAA;AAClB,QAAA,IAAI,CAAC,WAAW,CAAC,eAAe,EAAE;;;IAIpC,WAAW,GAAA;AACT,QAAA,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG;;;IAI7B,WAAW,GAAA;AACT,QAAA,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG;;;IAInB,cAAc,GAAA;AACtB,QAAA,OAAO,IAAI,CAAC,WAAW,CAAC,UAAU;;IAGjB,eAAe,GAAA;AAChC,QAAA,OAAO,IAAI,CAAC,WAAW,CAAC,cAAc;;IAG9B,wBAAwB,CAAC,EAAC,MAAM,EAAyC,EAAA;AACjF,QAAA,OAAO,MAAM,KAAK,IAAI,CAAC,WAAW,CAAC,WAAW,IAAI,MAAM,KAAK,IAAI,CAAC,WAAW,CAAC,SAAS;;AAGtE,IAAA,4BAA4B,CAAC,KAAe,EAAA;AAC7D,QAAA,KAAK,CAAC,4BAA4B,CAAC,KAAK,CAAC;QACzC,MAAM,QAAQ,IACZ,IAAI,KAAM,IAAI,CAAC,WAAW,CAAC;AACzB,cAAE,IAAI,CAAC,WAAW,CAAC;AACnB,cAAE,IAAI,CAAC,WAAW,CAAC,WAAW,CACW;QAC7C,QAAQ,EAAE,kBAAkB,EAAE;;AAGb,IAAA,YAAY,CAAC,KAAe,EAAA;AAC7C,QAAA,KAAK,CAAC,YAAY,CAAC,KAAK,CAAC;;AAEzB,QAAA,IAAI,CAAC,WAAW,CAAC,uBAAuB,EAAE;;;IAI5C,kBAAkB,GAAA;QAChB,OAAO,0BAA0B,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC;;uGAlKtD,yBAAyB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAzB,yBAAyB,EAAA,YAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAAzB,yBAAyB,EAAA,UAAA,EAAA,CAAA;kBADvC;wDA2BK,iBAAiB,EAAA,CAAA;sBADpB;;AA6IH;AA0BM,MAAO,YAAgB,SAAQ,yBAA4B,CAAA;;AAEvD,IAAA,eAAe,GAAgB,CAAC,OAAwB,KAA6B;AAC3F,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAChD,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC,CAC7C;AACD,QAAA,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,GAAG,GAAG,IAAI;AAC1D,QAAA,OAAO,CAAC,KAAK,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,KAAK,EAAE,GAAG,CAAC,IAAI;AACpE,cAAE;AACF,cAAE,EAAC,qBAAqB,EAAE,EAAC,KAAK,EAAE,GAAG,EAAE,QAAQ,EAAE,KAAK,EAAC,EAAC;AAC5D,KAAC;AAES,IAAA,UAAU,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,GAAG,KAAK,CAAC,cAAc,EAAE,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;IAEzE,SAAS,GAAA;AAC1B,QAAA,IAAI,CAAC,WAAW,CAAC,WAAW,GAAG,IAAI;;AAG3B,IAAA,kBAAkB,CAAC,UAAwB,EAAA;QACnD,OAAO,UAAU,CAAC,KAAK;;AAGN,IAAA,wBAAwB,CACzC,MAA8C,EAAA;QAE9C,IAAI,CAAC,KAAK,CAAC,wBAAwB,CAAC,MAAM,CAAC,EAAE;AAC3C,YAAA,OAAO,KAAK;;aACP;AACL,YAAA,OAAO,CAAC,MAAM,CAAC,QAAQ,EAAE;AACvB,kBAAE,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC;AACrB,kBAAE,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK;AACrB,oBAAA,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,EAAE,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC;;;AAI9E,IAAA,mBAAmB,CAAC,KAAe,EAAA;AAC3C,QAAA,IAAI,IAAI,CAAC,MAAM,EAAE;AACf,YAAA,MAAM,KAAK,GAAG,IAAI,SAAS,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC;YAC7D,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,KAAK,EAAE,IAAI,CAAC;AACxC,YAAA,IAAI,CAAC,WAAW,CAAC,uBAAuB,EAAE;;;AAIrC,IAAA,UAAU,CAAC,KAAoB,EAAA;AACtC,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,SAAS;AAC3C,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa;QAC9C,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,EAAE,KAAK,KAAK,KAAK;;;QAIxC,IACE,CAAC,CAAC,KAAK,CAAC,OAAO,KAAK,WAAW,IAAI,KAAK,MAAM,KAAK,CAAC,OAAO,KAAK,UAAU,IAAI,CAAC,KAAK,CAAC;AACrF,YAAA,OAAO,CAAC,cAAc,KAAK,OAAO,CAAC,KAAK,CAAC,MAAM;YAC/C,OAAO,CAAC,YAAY,KAAK,OAAO,CAAC,KAAK,CAAC,MAAM,EAC7C;YACA,KAAK,CAAC,cAAc,EAAE;YACtB,QAAQ,CAAC,WAAW,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC;YAC1D,QAAQ,CAAC,KAAK,EAAE;;aACX;AACL,YAAA,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC;;;uGA3DhB,YAAY,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAZ,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,YAAY,EARZ,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,qBAAA,EAAA,OAAA,EAAA,EAAA,UAAA,EAAA,YAAA,EAAA,SAAA,EAAA,WAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,MAAA,EAAA,MAAA,EAAA,EAAA,SAAA,EAAA,EAAA,OAAA,EAAA,+BAAA,EAAA,QAAA,EAAA,aAAA,EAAA,SAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,WAAA,EAAA,EAAA,UAAA,EAAA,EAAA,UAAA,EAAA,UAAA,EAAA,oBAAA,EAAA,6CAAA,EAAA,gBAAA,EAAA,6IAAA,EAAA,UAAA,EAAA,8DAAA,EAAA,UAAA,EAAA,8DAAA,EAAA,EAAA,cAAA,EAAA,2CAAA,EAAA,EAAA,SAAA,EAAA;YACT,EAAC,OAAO,EAAE,iBAAiB,EAAE,WAAW,EAAE,YAAY,EAAE,KAAK,EAAE,IAAI,EAAC;YACpE,EAAC,OAAO,EAAE,aAAa,EAAE,WAAW,EAAE,YAAY,EAAE,KAAK,EAAE,IAAI,EAAC;AACjE,SAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAKU,YAAY,EAAA,UAAA,EAAA,CAAA;kBAzBxB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,qBAAqB;AAC/B,oBAAA,IAAI,EAAE;AACJ,wBAAA,OAAO,EAAE,2CAA2C;AACpD,wBAAA,YAAY,EAAE,UAAU;AACxB,wBAAA,SAAS,EAAE,+BAA+B;AAC1C,wBAAA,UAAU,EAAE,aAAa;AACzB,wBAAA,WAAW,EAAE,oBAAoB;AACjC,wBAAA,sBAAsB,EAAE,2CAA2C;AACnE,wBAAA,kBAAkB,EAAE,CAAA;;AAE0D,iFAAA,CAAA;AAC9E,wBAAA,YAAY,EAAE,8DAA8D;AAC5E,wBAAA,YAAY,EAAE,8DAA8D;AAC5E,wBAAA,QAAQ,EAAE,WAAW;AACrB,wBAAA,MAAM,EAAE,MAAM;AACf,qBAAA;AACD,oBAAA,SAAS,EAAE;wBACT,EAAC,OAAO,EAAE,iBAAiB,EAAE,WAAW,cAAc,EAAE,KAAK,EAAE,IAAI,EAAC;wBACpE,EAAC,OAAO,EAAE,aAAa,EAAE,WAAW,cAAc,EAAE,KAAK,EAAE,IAAI,EAAC;AACjE,qBAAA;;;AAGD,oBAAA,OAAO,EAAE,CAAC,YAAY,EAAE,WAAW,CAAC;AACrC,iBAAA;;AAiED;AA0BM,MAAO,UAAc,SAAQ,yBAA4B,CAAA;;AAErD,IAAA,aAAa,GAAgB,CAAC,OAAwB,KAA6B;AACzF,QAAA,MAAM,GAAG,GAAG,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AAC9F,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,GAAG,IAAI;AAC9D,QAAA,OAAO,CAAC,GAAG,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI;AACpE,cAAE;AACF,cAAE,EAAC,mBAAmB,EAAE,EAAC,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,EAAC,EAAC;AAC5D,KAAC;IAEkB,SAAS,GAAA;AAC1B,QAAA,IAAI,CAAC,WAAW,CAAC,SAAS,GAAG,IAAI;;AAGzB,IAAA,UAAU,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,GAAG,KAAK,CAAC,cAAc,EAAE,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;AAEhF,IAAA,kBAAkB,CAAC,UAAwB,EAAA;QACnD,OAAO,UAAU,CAAC,GAAG;;AAGJ,IAAA,wBAAwB,CACzC,MAA8C,EAAA;QAE9C,IAAI,CAAC,KAAK,CAAC,wBAAwB,CAAC,MAAM,CAAC,EAAE;AAC3C,YAAA,OAAO,KAAK;;aACP;AACL,YAAA,OAAO,CAAC,MAAM,CAAC,QAAQ,EAAE;AACvB,kBAAE,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC;AACrB,kBAAE,CAAC,MAAM,CAAC,SAAS,CAAC,GAAG;AACnB,oBAAA,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,EAAE,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC;;;AAI1E,IAAA,mBAAmB,CAAC,KAAe,EAAA;AAC3C,QAAA,IAAI,IAAI,CAAC,MAAM,EAAE;AACf,YAAA,MAAM,KAAK,GAAG,IAAI,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,EAAE,KAAK,CAAC;YAC/D,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,KAAK,EAAE,IAAI,CAAC;;;IAIpC,2BAA2B,GAAA;QACjC,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,WAAW,CAAC,aAAa;AACzE,QAAA,MAAM,KAAK,GAAG,UAAU,CAAC,KAAK;AAE9B,QAAA,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;YACpB,UAAU,CAAC,iBAAiB,CAAC,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC,MAAM,CAAC;;QAG1D,UAAU,CAAC,KAAK,EAAE;;AAGX,IAAA,UAAU,CAAC,KAAoB,EAAA;AACtC,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa;QAC9C,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,EAAE,KAAK,KAAK,KAAK;;QAGxC,IAAI,KAAK,CAAC,OAAO,KAAK,SAAS,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE;YACjD,IAAI,CAAC,2BAA2B,EAAE;;;;aAI/B,IACH,CAAC,CAAC,KAAK,CAAC,OAAO,KAAK,UAAU,IAAI,KAAK,MAAM,KAAK,CAAC,OAAO,KAAK,WAAW,IAAI,CAAC,KAAK,CAAC;YACrF,OAAO,CAAC,cAAc,KAAK,CAAC;AAC5B,YAAA,OAAO,CAAC,YAAY,KAAK,CAAC,EAC1B;YACA,KAAK,CAAC,cAAc,EAAE;YACtB,IAAI,CAAC,2BAA2B,EAAE;;aAC7B;AACL,YAAA,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC;;;uGArEhB,UAAU,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAV,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,UAAU,EARV,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,mBAAA,EAAA,OAAA,EAAA,EAAA,UAAA,EAAA,YAAA,EAAA,SAAA,EAAA,WAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,MAAA,EAAA,MAAA,EAAA,EAAA,SAAA,EAAA,EAAA,OAAA,EAAA,+BAAA,EAAA,QAAA,EAAA,aAAA,EAAA,SAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,WAAA,EAAA,EAAA,UAAA,EAAA,EAAA,UAAA,EAAA,UAAA,EAAA,oBAAA,EAAA,6CAAA,EAAA,gBAAA,EAAA,6IAAA,EAAA,UAAA,EAAA,8DAAA,EAAA,UAAA,EAAA,8DAAA,EAAA,EAAA,cAAA,EAAA,yCAAA,EAAA,EAAA,SAAA,EAAA;YACT,EAAC,OAAO,EAAE,iBAAiB,EAAE,WAAW,EAAE,UAAU,EAAE,KAAK,EAAE,IAAI,EAAC;YAClE,EAAC,OAAO,EAAE,aAAa,EAAE,WAAW,EAAE,UAAU,EAAE,KAAK,EAAE,IAAI,EAAC;AAC/D,SAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAKU,UAAU,EAAA,UAAA,EAAA,CAAA;kBAzBtB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,mBAAmB;AAC7B,oBAAA,IAAI,EAAE;AACJ,wBAAA,OAAO,EAAE,yCAAyC;AAClD,wBAAA,YAAY,EAAE,UAAU;AACxB,wBAAA,SAAS,EAAE,+BAA+B;AAC1C,wBAAA,UAAU,EAAE,aAAa;AACzB,wBAAA,WAAW,EAAE,oBAAoB;AACjC,wBAAA,sBAAsB,EAAE,2CAA2C;AACnE,wBAAA,kBAAkB,EAAE,CAAA;;AAE0D,iFAAA,CAAA;AAC9E,wBAAA,YAAY,EAAE,8DAA8D;AAC5E,wBAAA,YAAY,EAAE,8DAA8D;AAC5E,wBAAA,QAAQ,EAAE,WAAW;AACrB,wBAAA,MAAM,EAAE,MAAM;AACf,qBAAA;AACD,oBAAA,SAAS,EAAE;wBACT,EAAC,OAAO,EAAE,iBAAiB,EAAE,WAAW,YAAY,EAAE,KAAK,EAAE,IAAI,EAAC;wBAClE,EAAC,OAAO,EAAE,aAAa,EAAE,WAAW,YAAY,EAAE,KAAK,EAAE,IAAI,EAAC;AAC/D,qBAAA;;;AAGD,oBAAA,OAAO,EAAE,CAAC,YAAY,EAAE,WAAW,CAAC;AACrC,iBAAA;;;AC1SD;AACA;AACA;AACA;AAaM,MAAO,kBAAsB,SAAQ,iBAI1C,CAAA;AACoB,IAAA,qBAAqB,CAAC,QAA+C,EAAA;AACtF,QAAA,KAAK,CAAC,qBAAqB,CAAC,QAAQ,CAAC;AAErC,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,eAAe;QAElC,IAAI,KAAK,EAAE;AACT,YAAA,QAAQ,CAAC,eAAe,GAAG,KAAK,CAAC,eAAe;AAChD,YAAA,QAAQ,CAAC,aAAa,GAAG,KAAK,CAAC,aAAa;AAC5C,YAAA,QAAQ,CAAC,uBAAuB,GAAG,KAAK,CAAC,2BAA2B,EAAE;AACtE,YAAA,QAAQ,CAAC,qBAAqB,GAAG,KAAK,CAAC,yBAAyB,EAAE;;;uGAd3D,kBAAkB,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAlB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,kBAAkB,EANlB,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,uBAAA,EAAA,SAAA,EAAA;YACT,uCAAuC;YACvC,oCAAoC;AACpC,YAAA,EAAC,OAAO,EAAE,iBAAiB,EAAE,WAAW,EAAE,kBAAkB,EAAC;AAC9D,SAAA,EAAA,QAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EARS,EAAE,EAAA,QAAA,EAAA,IAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAUD,kBAAkB,EAAA,UAAA,EAAA,CAAA;kBAZ9B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,uBAAuB;AACjC,oBAAA,QAAQ,EAAE,EAAE;AACZ,oBAAA,QAAQ,EAAE,oBAAoB;oBAC9B,eAAe,EAAE,uBAAuB,CAAC,MAAM;oBAC/C,aAAa,EAAE,iBAAiB,CAAC,IAAI;AACrC,oBAAA,SAAS,EAAE;wBACT,uCAAuC;wBACvC,oCAAoC;AACpC,wBAAA,EAAC,OAAO,EAAE,iBAAiB,EAAE,WAAW,oBAAoB,EAAC;AAC9D,qBAAA;AACF,iBAAA;;;AChBD;MAKa,kBAAkB,CAAA;AACrB,IAAA,WAAW,GACjB,MAAM,CAAwD,iBAAiB,CAAC;AAIlF,IAAA,WAAA,GAAA;IAEA,eAAe,GAAA;AACb,QAAA,IAAI,CAAC,WAAW,CAAC,sBAAsB,EAAE;AACzC,QAAA,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE;;uGAVf,kBAAkB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAlB,kBAAkB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,iDAAA,EAAA,IAAA,EAAA,EAAA,SAAA,EAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAAlB,kBAAkB,EAAA,UAAA,EAAA,CAAA;kBAJ9B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,iDAAiD;AAC3D,oBAAA,IAAI,EAAE,EAAC,SAAS,EAAE,mBAAmB,EAAC;AACvC,iBAAA;;AAeD;MAKa,mBAAmB,CAAA;AAC9B,IAAA,WAAW,GAAG,MAAM,CAAwD,iBAAiB,CAAC;AAG9F,IAAA,WAAA,GAAA;uGAJW,mBAAmB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAnB,mBAAmB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,mDAAA,EAAA,IAAA,EAAA,EAAA,SAAA,EAAA,EAAA,OAAA,EAAA,qBAAA,EAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAAnB,mBAAmB,EAAA,UAAA,EAAA,CAAA;kBAJ/B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,mDAAmD;AAC7D,oBAAA,IAAI,EAAE,EAAC,SAAS,EAAE,qBAAqB,EAAC;AACzC,iBAAA;;AAQD;;;AAGG;MAcU,oBAAoB,CAAA;AACvB,IAAA,WAAW,GACjB,MAAM,CAAwD,iBAAiB,CAAC;AAC1E,IAAA,iBAAiB,GAAG,MAAM,CAAC,gBAAgB,CAAC;AAE5B,IAAA,SAAS;AACzB,IAAA,OAAO;AAGf,IAAA,WAAA,GAAA;IAEA,eAAe,GAAA;AACb,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI,cAAc,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,iBAAiB,CAAC;QACzE,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC;;IAGhD,WAAW,GAAA;QACT,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC;;QAG5C,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE;AAC3C,YAAA,IAAI,CAAC,OAAO,EAAE,MAAM,EAAE;;;uGArBf,oBAAoB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAApB,oBAAoB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,uDAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,WAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAKpB,WAAW,EAfZ,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA;;;;;;AAMT,EAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,wSAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAIU,oBAAoB,EAAA,UAAA,EAAA,CAAA;kBAbhC,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,uDAAuD,EAEvD,QAAA,EAAA;;;;;;AAMT,EAAA,CAAA,EAAA,eAAA,EACgB,uBAAuB,CAAC,MAAM,EAChC,aAAA,EAAA,iBAAiB,CAAC,IAAI,EAAA,MAAA,EAAA,CAAA,wSAAA,CAAA,EAAA;wDAOb,SAAS,EAAA,CAAA;sBAAhC,SAAS;uBAAC,WAAW;;;MCMX,mBAAmB,CAAA;uGAAnB,mBAAmB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;AAAnB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,mBAAmB,YA/C5B,eAAe;YACf,aAAa;YACb,UAAU;YACV,YAAY;YACZ,eAAe;YACf,WAAW;YACX,eAAe;YACf,aAAa;YACb,oBAAoB;YACpB,kBAAkB;YAClB,mBAAmB;YACnB,uBAAuB;YACvB,YAAY;YACZ,WAAW;YACX,gBAAgB;YAChB,iBAAiB;YACjB,iBAAiB;YACjB,YAAY;YACZ,UAAU;YACV,kBAAkB;YAClB,oBAAoB;YACpB,mBAAmB;AACnB,YAAA,kBAAkB,aAGlB,mBAAmB;YACnB,WAAW;YACX,eAAe;YACf,aAAa;YACb,oBAAoB;YACpB,kBAAkB;YAClB,mBAAmB;YACnB,uBAAuB;YACvB,YAAY;YACZ,WAAW;YACX,gBAAgB;YAChB,iBAAiB;YACjB,iBAAiB;YACjB,YAAY;YACZ,UAAU;YACV,kBAAkB;YAClB,oBAAoB;YACpB,mBAAmB;YACnB,kBAAkB,CAAA,EAAA,CAAA;AAIT,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,mBAAmB,aAFnB,CAAC,iBAAiB,EAAE,+CAA+C,CAAC,YA7C7E,eAAe;YACf,aAAa;YACb,UAAU;YACV,YAAY;YACZ,eAAe;YAIf,oBAAoB;YAEpB,mBAAmB;AAKnB,YAAA,iBAAiB,EAUjB,mBAAmB,CAAA,EAAA,CAAA;;2FAsBV,mBAAmB,EAAA,UAAA,EAAA,CAAA;kBAjD/B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,OAAO,EAAE;wBACP,eAAe;wBACf,aAAa;wBACb,UAAU;wBACV,YAAY;wBACZ,eAAe;wBACf,WAAW;wBACX,eAAe;wBACf,aAAa;wBACb,oBAAoB;wBACpB,kBAAkB;wBAClB,mBAAmB;wBACnB,uBAAuB;wBACvB,YAAY;wBACZ,WAAW;wBACX,gBAAgB;wBAChB,iBAAiB;wBACjB,iBAAiB;wBACjB,YAAY;wBACZ,UAAU;wBACV,kBAAkB;wBAClB,oBAAoB;wBACpB,mBAAmB;wBACnB,kBAAkB;AACnB,qBAAA;AACD,oBAAA,OAAO,EAAE;wBACP,mBAAmB;wBACnB,WAAW;wBACX,eAAe;wBACf,aAAa;wBACb,oBAAoB;wBACpB,kBAAkB;wBAClB,mBAAmB;wBACnB,uBAAuB;wBACvB,YAAY;wBACZ,WAAW;wBACX,gBAAgB;wBAChB,iBAAiB;wBACjB,iBAAiB;wBACjB,YAAY;wBACZ,UAAU;wBACV,kBAAkB;wBAClB,oBAAoB;wBACpB,mBAAmB;wBACnB,kBAAkB;AACnB,qBAAA;AACD,oBAAA,SAAS,EAAE,CAAC,iBAAiB,EAAE,+CAA+C,CAAC;AAChF,iBAAA;;;ACzED;;;;;AAKG;AACU,MAAA,uBAAuB,GAGhC;;;;;;;;;;;;;;;;;;;;;;;;;;AA2BF,IAAA,cAAc,EAAE;AACd,QAAA,IAAI,EAAE,CAAC;AACP,QAAA,IAAI,EAAE,gBAAgB;AACtB,QAAA,WAAW,EAAE;AACX,YAAA;AACE,gBAAA,IAAI,EAAE,CAAC;AACP,gBAAA,IAAI,EAAE,wBAAwB;AAC9B,gBAAA,SAAS,EAAE;AACT,oBAAA,IAAI,EAAE,CAAC;AACP,oBAAA,MAAM,EAAE;AACN,wBAAA,IAAI,EAAE,CAAC;AACP,wBAAA,KAAK,EAAE;AACL,4BAAA,EAAC,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,EAAC,OAAO,EAAE,CAAC,EAAE,SAAS,EAAE,eAAe,EAAC,EAAE,MAAM,EAAE,IAAI,EAAC;AACzE,4BAAA,EAAC,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,EAAC,OAAO,EAAE,CAAC,EAAE,SAAS,EAAE,aAAa,EAAC,EAAE,MAAM,EAAE,IAAI,EAAC;AACxE,yBAAA;AACF,qBAAA;AACD,oBAAA,OAAO,EAAE,kCAAkC;AAC5C,iBAAA;AACD,gBAAA,OAAO,EAAE,IAAI;AACd,aAAA;AACD,YAAA;AACE,gBAAA,IAAI,EAAE,CAAC;AACP,gBAAA,IAAI,EAAE,sBAAsB;AAC5B,gBAAA,SAAS,EAAE;AACT,oBAAA,IAAI,EAAE,CAAC;AACP,oBAAA,MAAM,EAAE;AACN,wBAAA,IAAI,EAAE,CAAC;AACP,wBAAA,KAAK,EAAE;AACL,4BAAA,EAAC,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,EAAC,OAAO,EAAE,CAAC,EAAE,SAAS,EAAE,YAAY,EAAC,EAAE,MAAM,EAAE,IAAI,EAAC;AACtE,4BAAA,EAAC,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,EAAC,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,EAAC,EAAE,MAAM,EAAE,IAAI,EAAC;AACjE,yBAAA;AACF,qBAAA;AACD,oBAAA,OAAO,EAAE,kCAAkC;AAC5C,iBAAA;AACD,gBAAA,OAAO,EAAE,IAAI;AACd,aAAA;AACD,YAAA;AACE,gBAAA,IAAI,EAAE,CAAC;AACP,gBAAA,IAAI,EAAE,WAAW;AACjB,gBAAA,SAAS,EAAE;AACT,oBAAA,IAAI,EAAE,CAAC;AACP,oBAAA,MAAM,EAAE,EAAC,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,EAAC,OAAO,EAAE,CAAC,EAAC,EAAE,MAAM,EAAE,IAAI,EAAC;AACrD,oBAAA,OAAO,EAAE,cAAc;AACxB,iBAAA;AACD,gBAAA,OAAO,EAAE,IAAI;AACd,aAAA;AACF,SAAA;AACD,QAAA,OAAO,EAAE,EAAE;AACZ,KAAA;;;;;;;;;;AAaD,IAAA,cAAc,EAAE;AACd,QAAA,IAAI,EAAE,CAAC;AACP,QAAA,IAAI,EAAE,gBAAgB;AACtB,QAAA,WAAW,EAAE;YACX,EAAC,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,EAAC,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,EAAC,OAAO,EAAE,CAAC,EAAC,EAAE,MAAM,EAAE,IAAI,EAAC,EAAC;YAC9E,EAAC,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,EAAC,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,EAAC,OAAO,EAAE,CAAC,EAAC,EAAE,MAAM,EAAE,IAAI,EAAC,EAAC;AAC/E,YAAA;AACE,gBAAA,IAAI,EAAE,CAAC;AACP,gBAAA,IAAI,EAAE,WAAW;AACjB,gBAAA,SAAS,EAAE;AACT,oBAAA,IAAI,EAAE,CAAC;AACP,oBAAA,MAAM,EAAE,IAAI;AACZ,oBAAA,OAAO,EAAE,8CAA8C;AACxD,iBAAA;AACD,gBAAA,OAAO,EAAE,IAAI;AACd,aAAA;AACF,SAAA;AACD,QAAA,OAAO,EAAE,EAAE;AACZ,KAAA;;;;;"}