{"version": 3, "file": "di.mjs", "sources": ["../../../../../../darwin_arm64-fastbuild-ST-2d99d9656325/bin/packages/core/primitives/di/src/injector.ts", "../../../../../../darwin_arm64-fastbuild-ST-2d99d9656325/bin/packages/core/primitives/di/src/not_found.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {InjectionToken} from './injection_token';\nimport {NotFound} from './not_found';\n\nexport interface Injector {\n  retrieve<T>(token: InjectionToken<T>, options?: unknown): T | NotFound;\n}\n\n/**\n * Current injector value used by `inject`.\n * - `undefined`: it is an error to call `inject`\n * - `null`: `inject` can be called but there is no injector (limp-mode).\n * - Injector instance: Use the injector for resolution.\n */\nlet _currentInjector: Injector | undefined | null = undefined;\n\nexport function getCurrentInjector(): Injector | undefined | null {\n  return _currentInjector;\n}\n\nexport function setCurrentInjector(\n  injector: Injector | null | undefined,\n): Injector | undefined | null {\n  const former = _currentInjector;\n  _currentInjector = injector;\n  return former;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/**\n * Value returned if the key-value pair couldn't be found in the context\n * hierarchy.\n */\nexport const NOT_FOUND: unique symbol = Symbol('NotFound');\n\n/**\n * Error thrown when the key-value pair couldn't be found in the context\n * hierarchy. Context can be attached below.\n */\nexport class NotFoundError extends Error {\n  constructor(message: string) {\n    super(message);\n  }\n}\n\n/**\n * Type guard for checking if an unknown value is a NotFound.\n */\nexport function isNotFound(e: unknown): e is NotFound {\n  return e === NOT_FOUND || e instanceof NotFoundError;\n}\n\n/**\n * Type union of NotFound and NotFoundError.\n */\nexport type NotFound = typeof NOT_FOUND | NotFoundError;\n"], "names": [], "mappings": ";;;;;;AAeA;;;;;AAKG;AACH,IAAI,gBAAgB,GAAgC,SAAS;SAE7C,kBAAkB,GAAA;AAChC,IAAA,OAAO,gBAAgB;AACzB;AAEM,SAAU,kBAAkB,CAChC,QAAqC,EAAA;IAErC,MAAM,MAAM,GAAG,gBAAgB;IAC/B,gBAAgB,GAAG,QAAQ;AAC3B,IAAA,OAAO,MAAM;AACf;;ACzBA;;;AAGG;MACU,SAAS,GAAkB,MAAM,CAAC,UAAU;AAEzD;;;AAGG;AACG,MAAO,aAAc,SAAQ,KAAK,CAAA;AACtC,IAAA,WAAA,CAAY,OAAe,EAAA;QACzB,KAAK,CAAC,OAAO,CAAC;;AAEjB;AAED;;AAEG;AACG,SAAU,UAAU,CAAC,CAAU,EAAA;AACnC,IAAA,OAAO,CAAC,KAAK,SAAS,IAAI,CAAC,YAAY,aAAa;AACtD;;;;"}