{"version": 3, "file": "menu.mjs", "sources": ["../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/menu/menu-panel.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/menu/menu-item.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/menu/menu-item.html", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/menu/menu-errors.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/menu/menu-content.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/menu/menu.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/menu/menu.html", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/menu/menu-trigger.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/menu/module.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/menu/menu-animations.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {EventEmitter, TemplateRef, InjectionToken} from '@angular/core';\nimport {MenuPositionX, MenuPositionY} from './menu-positions';\nimport {Direction} from '@angular/cdk/bidi';\nimport {FocusOrigin} from '@angular/cdk/a11y';\nimport {MatMenuContent} from './menu-content';\n\n/**\n * Injection token used to provide the parent menu to menu-specific components.\n * @docs-private\n */\nexport const MAT_MENU_PANEL = new InjectionToken<MatMenuPanel>('MAT_MENU_PANEL');\n\n/**\n * Interface for a custom menu panel that can be used with `matMenuTriggerFor`.\n * @docs-private\n */\nexport interface MatMenuPanel<T = any> {\n  xPosition: MenuPositionX;\n  yPosition: MenuPositionY;\n  overlapTrigger: boolean;\n  templateRef: TemplateRef<any>;\n  readonly close: EventEmitter<void | 'click' | 'keydown' | 'tab'>;\n  parentMenu?: MatMenuPanel | undefined;\n  direction?: Direction;\n  focusFirstItem: (origin?: FocusOrigin) => void;\n  resetActiveItem: () => void;\n  setPositionClasses?: (x: MenuPositionX, y: MenuPositionY) => void;\n\n  /**\n   * @deprecated No longer used and will be removed.\n   * @breaking-change 21.0.0\n   */\n  setElevation?(depth: number): void;\n  lazyContent?: MatMenuContent;\n  backdropClass?: string;\n  overlayPanelClass?: string | string[];\n  hasBackdrop?: boolean;\n  readonly panelId?: string;\n\n  /**\n   * @deprecated To be removed.\n   * @breaking-change 8.0.0\n   */\n  addItem?: (item: T) => void;\n\n  /**\n   * @deprecated To be removed.\n   * @breaking-change 8.0.0\n   */\n  removeItem?: (item: T) => void;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  ChangeDetectionStrategy,\n  Component,\n  ElementRef,\n  OnDestroy,\n  ViewEncapsulation,\n  Input,\n  AfterViewInit,\n  ChangeDetectorRef,\n  booleanAttribute,\n  inject,\n} from '@angular/core';\nimport {FocusableOption, FocusMonitor, FocusOrigin} from '@angular/cdk/a11y';\nimport {Subject} from 'rxjs';\nimport {DOCUMENT} from '@angular/common';\nimport {MatMenuPanel, MAT_MENU_PANEL} from './menu-panel';\nimport {_StructuralStylesLoader, MatRipple} from '../core';\nimport {_CdkPrivateStyleLoader} from '@angular/cdk/private';\n\n/**\n * Single item inside a `mat-menu`. Provides the menu item styling and accessibility treatment.\n */\n@Component({\n  selector: '[mat-menu-item]',\n  exportAs: 'matMenuItem',\n  host: {\n    '[attr.role]': 'role',\n    'class': 'mat-mdc-menu-item mat-focus-indicator',\n    '[class.mat-mdc-menu-item-highlighted]': '_highlighted',\n    '[class.mat-mdc-menu-item-submenu-trigger]': '_triggersSubmenu',\n    '[attr.tabindex]': '_getTabIndex()',\n    '[attr.aria-disabled]': 'disabled',\n    '[attr.disabled]': 'disabled || null',\n    '(click)': '_checkDisabled($event)',\n    '(mouseenter)': '_handleMouseEnter()',\n  },\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  encapsulation: ViewEncapsulation.None,\n  templateUrl: 'menu-item.html',\n  imports: [MatRipple],\n})\nexport class MatMenuItem implements FocusableOption, AfterViewInit, OnDestroy {\n  private _elementRef = inject<ElementRef<HTMLElement>>(ElementRef);\n  private _document = inject(DOCUMENT);\n  private _focusMonitor = inject(FocusMonitor);\n  _parentMenu? = inject<MatMenuPanel<MatMenuItem>>(MAT_MENU_PANEL, {optional: true});\n  private _changeDetectorRef = inject(ChangeDetectorRef);\n\n  /** ARIA role for the menu item. */\n  @Input() role: 'menuitem' | 'menuitemradio' | 'menuitemcheckbox' = 'menuitem';\n\n  /** Whether the menu item is disabled. */\n  @Input({transform: booleanAttribute}) disabled: boolean = false;\n\n  /** Whether ripples are disabled on the menu item. */\n  @Input({transform: booleanAttribute}) disableRipple: boolean = false;\n\n  /** Stream that emits when the menu item is hovered. */\n  readonly _hovered: Subject<MatMenuItem> = new Subject<MatMenuItem>();\n\n  /** Stream that emits when the menu item is focused. */\n  readonly _focused = new Subject<MatMenuItem>();\n\n  /** Whether the menu item is highlighted. */\n  _highlighted: boolean = false;\n\n  /** Whether the menu item acts as a trigger for a sub-menu. */\n  _triggersSubmenu: boolean = false;\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    inject(_CdkPrivateStyleLoader).load(_StructuralStylesLoader);\n    this._parentMenu?.addItem?.(this);\n  }\n\n  /** Focuses the menu item. */\n  focus(origin?: FocusOrigin, options?: FocusOptions): void {\n    if (this._focusMonitor && origin) {\n      this._focusMonitor.focusVia(this._getHostElement(), origin, options);\n    } else {\n      this._getHostElement().focus(options);\n    }\n\n    this._focused.next(this);\n  }\n\n  ngAfterViewInit() {\n    if (this._focusMonitor) {\n      // Start monitoring the element, so it gets the appropriate focused classes. We want\n      // to show the focus style for menu items only when the focus was not caused by a\n      // mouse or touch interaction.\n      this._focusMonitor.monitor(this._elementRef, false);\n    }\n  }\n\n  ngOnDestroy() {\n    if (this._focusMonitor) {\n      this._focusMonitor.stopMonitoring(this._elementRef);\n    }\n\n    if (this._parentMenu && this._parentMenu.removeItem) {\n      this._parentMenu.removeItem(this);\n    }\n\n    this._hovered.complete();\n    this._focused.complete();\n  }\n\n  /** Used to set the `tabindex`. */\n  _getTabIndex(): string {\n    return this.disabled ? '-1' : '0';\n  }\n\n  /** Returns the host DOM element. */\n  _getHostElement(): HTMLElement {\n    return this._elementRef.nativeElement;\n  }\n\n  /** Prevents the default element actions if it is disabled. */\n  _checkDisabled(event: Event): void {\n    if (this.disabled) {\n      event.preventDefault();\n      event.stopPropagation();\n    }\n  }\n\n  /** Emits to the hover stream. */\n  _handleMouseEnter() {\n    this._hovered.next(this);\n  }\n\n  /** Gets the label to be used when determining whether the option should be focused. */\n  getLabel(): string {\n    const clone = this._elementRef.nativeElement.cloneNode(true) as HTMLElement;\n    const icons = clone.querySelectorAll('mat-icon, .material-icons');\n\n    // Strip away icons, so they don't show up in the text.\n    for (let i = 0; i < icons.length; i++) {\n      icons[i].remove();\n    }\n\n    return clone.textContent?.trim() || '';\n  }\n\n  _setHighlighted(isHighlighted: boolean) {\n    // We need to mark this for check for the case where the content is coming from a\n    // `matMenuContent` whose change detection tree is at the declaration position,\n    // not the insertion position. See #23175.\n    this._highlighted = isHighlighted;\n    this._changeDetectorRef.markForCheck();\n  }\n\n  _setTriggersSubmenu(triggersSubmenu: boolean) {\n    this._triggersSubmenu = triggersSubmenu;\n    this._changeDetectorRef.markForCheck();\n  }\n\n  _hasFocus(): boolean {\n    return this._document && this._document.activeElement === this._getHostElement();\n  }\n}\n", "<ng-content select=\"mat-icon, [matMenuItemIcon]\"></ng-content>\n<span class=\"mat-mdc-menu-item-text\"><ng-content></ng-content></span>\n<div class=\"mat-mdc-menu-ripple\" matRipple\n     [matRippleDisabled]=\"disableRipple || disabled\"\n     [matRippleTrigger]=\"_getHostElement()\">\n</div>\n\n@if (_triggersSubmenu) {\n     <svg\n       class=\"mat-mdc-menu-submenu-icon\"\n       viewBox=\"0 0 5 10\"\n       focusable=\"false\"\n       aria-hidden=\"true\"><polygon points=\"0,0 5,5 0,10\"/></svg>\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/**\n * Throws an exception for the case when menu's x-position value isn't valid.\n * In other words, it doesn't match 'before' or 'after'.\n * @docs-private\n */\nexport function throwMatMenuInvalidPositionX() {\n  throw Error(`xPosition value must be either 'before' or after'.\n      Example: <mat-menu xPosition=\"before\" #menu=\"matMenu\"></mat-menu>`);\n}\n\n/**\n * Throws an exception for the case when menu's y-position value isn't valid.\n * In other words, it doesn't match 'above' or 'below'.\n * @docs-private\n */\nexport function throwMatMenuInvalidPositionY() {\n  throw Error(`yPosition value must be either 'above' or below'.\n      Example: <mat-menu yPosition=\"above\" #menu=\"matMenu\"></mat-menu>`);\n}\n\n/**\n * Throws an exception for the case when a menu is assigned\n * to a trigger that is placed inside the same menu.\n * @docs-private\n */\nexport function throwMatMenuRecursiveError() {\n  throw Error(\n    `matMenuTriggerFor: menu cannot contain its own trigger. Assign a menu that is ` +\n      `not a parent of the trigger or move the trigger outside of the menu.`,\n  );\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {DomPortalOutlet, TemplatePortal} from '@angular/cdk/portal';\nimport {DOCUMENT} from '@angular/common';\nimport {\n  ApplicationRef,\n  ChangeDetectorRef,\n  Directive,\n  InjectionToken,\n  Injector,\n  OnDestroy,\n  TemplateRef,\n  ViewContainerRef,\n  inject,\n} from '@angular/core';\nimport {Subject} from 'rxjs';\n\n/**\n * Injection token that can be used to reference instances of `MatMenuContent`. It serves\n * as alternative token to the actual `MatMenuContent` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nexport const MAT_MENU_CONTENT = new InjectionToken<MatMenuContent>('MatMenuContent');\n\n/** Menu content that will be rendered lazily once the menu is opened. */\n@Directive({\n  selector: 'ng-template[matMenuContent]',\n  providers: [{provide: MAT_MENU_CONTENT, useExisting: MatMenuContent}],\n})\nexport class MatMenuContent implements OnDestroy {\n  private _template = inject<TemplateRef<any>>(TemplateRef);\n  private _appRef = inject(ApplicationRef);\n  private _injector = inject(Injector);\n  private _viewContainerRef = inject(ViewContainerRef);\n  private _document = inject(DOCUMENT);\n  private _changeDetectorRef = inject(ChangeDetectorRef);\n\n  private _portal: TemplatePortal<any> | undefined;\n  private _outlet: DomPortalOutlet | undefined;\n\n  /** Emits when the menu content has been attached. */\n  readonly _attached = new Subject<void>();\n\n  constructor(...args: unknown[]);\n\n  constructor() {}\n\n  /**\n   * Attaches the content with a particular context.\n   * @docs-private\n   */\n  attach(context: any = {}) {\n    if (!this._portal) {\n      this._portal = new TemplatePortal(this._template, this._viewContainerRef);\n    }\n\n    this.detach();\n\n    if (!this._outlet) {\n      this._outlet = new DomPortalOutlet(\n        this._document.createElement('div'),\n        null,\n        this._appRef,\n        this._injector,\n      );\n    }\n\n    const element: HTMLElement = this._template.elementRef.nativeElement;\n\n    // Because we support opening the same menu from different triggers (which in turn have their\n    // own `OverlayRef` panel), we have to re-insert the host element every time, otherwise we\n    // risk it staying attached to a pane that's no longer in the DOM.\n    element.parentNode!.insertBefore(this._outlet.outletElement, element);\n\n    // When `MatMenuContent` is used in an `OnPush` component, the insertion of the menu\n    // content via `createEmbeddedView` does not cause the content to be seen as \"dirty\"\n    // by Angular. This causes the `@ContentChildren` for menu items within the menu to\n    // not be updated by Angular. By explicitly marking for check here, we tell Angular that\n    // it needs to check for new menu items and update the `@ContentChild` in `MatMenu`.\n    this._changeDetectorRef.markForCheck();\n    this._portal.attach(this._outlet, context);\n    this._attached.next();\n  }\n\n  /**\n   * Detaches the content.\n   * @docs-private\n   */\n  detach() {\n    if (this._portal?.isAttached) {\n      this._portal.detach();\n    }\n  }\n\n  ngOnDestroy() {\n    this.detach();\n    this._outlet?.dispose();\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  AfterContentInit,\n  ChangeDetectionStrategy,\n  Component,\n  ContentChild,\n  ContentChildren,\n  ElementRef,\n  EventEmitter,\n  InjectionToken,\n  Input,\n  OnDestroy,\n  Output,\n  TemplateRef,\n  QueryList,\n  ViewChild,\n  ViewEncapsulation,\n  OnInit,\n  ChangeDetectorRef,\n  booleanAttribute,\n  afterNextRender,\n  AfterRenderRef,\n  inject,\n  Injector,\n  ANIMATION_MODULE_TYPE,\n} from '@angular/core';\nimport {_Id<PERSON>enerator, FocusKeyManager, FocusOrigin} from '@angular/cdk/a11y';\nimport {Direction} from '@angular/cdk/bidi';\nimport {\n  ESCAPE,\n  LEFT_ARROW,\n  RIGHT_ARROW,\n  DOWN_ARROW,\n  UP_ARROW,\n  hasModifierKey,\n} from '@angular/cdk/keycodes';\nimport {merge, Observable, Subject} from 'rxjs';\nimport {startWith, switchMap} from 'rxjs/operators';\nimport {MatMenuItem} from './menu-item';\nimport {MatMenuPanel, MAT_MENU_PANEL} from './menu-panel';\nimport {MenuPositionX, MenuPositionY} from './menu-positions';\nimport {throwMatMenuInvalidPositionX, throwMatMenuInvalidPositionY} from './menu-errors';\nimport {MatMenuContent, MAT_MENU_CONTENT} from './menu-content';\n\n/** Reason why the menu was closed. */\nexport type MenuCloseReason = void | 'click' | 'keydown' | 'tab';\n\n/** Default `mat-menu` options that can be overridden. */\nexport interface MatMenuDefaultOptions {\n  /** The x-axis position of the menu. */\n  xPosition: MenuPositionX;\n\n  /** The y-axis position of the menu. */\n  yPosition: MenuPositionY;\n\n  /** Whether the menu should overlap the menu trigger. */\n  overlapTrigger: boolean;\n\n  /** Class to be applied to the menu's backdrop. */\n  backdropClass: string;\n\n  /** Class or list of classes to be applied to the menu's overlay panel. */\n  overlayPanelClass?: string | string[];\n\n  /** Whether the menu has a backdrop. */\n  hasBackdrop?: boolean;\n}\n\n/** Injection token to be used to override the default options for `mat-menu`. */\nexport const MAT_MENU_DEFAULT_OPTIONS = new InjectionToken<MatMenuDefaultOptions>(\n  'mat-menu-default-options',\n  {\n    providedIn: 'root',\n    factory: MAT_MENU_DEFAULT_OPTIONS_FACTORY,\n  },\n);\n\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nexport function MAT_MENU_DEFAULT_OPTIONS_FACTORY(): MatMenuDefaultOptions {\n  return {\n    overlapTrigger: false,\n    xPosition: 'after',\n    yPosition: 'below',\n    backdropClass: 'cdk-overlay-transparent-backdrop',\n  };\n}\n\n/** Name of the enter animation `@keyframes`. */\nconst ENTER_ANIMATION = '_mat-menu-enter';\n\n/** Name of the exit animation `@keyframes`. */\nconst EXIT_ANIMATION = '_mat-menu-exit';\n\n@Component({\n  selector: 'mat-menu',\n  templateUrl: 'menu.html',\n  styleUrl: 'menu.css',\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  encapsulation: ViewEncapsulation.None,\n  exportAs: 'matMenu',\n  host: {\n    '[attr.aria-label]': 'null',\n    '[attr.aria-labelledby]': 'null',\n    '[attr.aria-describedby]': 'null',\n  },\n  providers: [{provide: MAT_MENU_PANEL, useExisting: MatMenu}],\n})\nexport class MatMenu implements AfterContentInit, MatMenuPanel<MatMenuItem>, OnInit, OnDestroy {\n  private _elementRef = inject<ElementRef<HTMLElement>>(ElementRef);\n  private _changeDetectorRef = inject(ChangeDetectorRef);\n  private _injector = inject(Injector);\n\n  private _keyManager: FocusKeyManager<MatMenuItem>;\n  private _xPosition: MenuPositionX;\n  private _yPosition: MenuPositionY;\n  private _firstItemFocusRef?: AfterRenderRef;\n  private _exitFallbackTimeout: ReturnType<typeof setTimeout> | undefined;\n\n  /** Whether animations are currently disabled. */\n  protected _animationsDisabled: boolean;\n\n  /** All items inside the menu. Includes items nested inside another menu. */\n  @ContentChildren(MatMenuItem, {descendants: true}) _allItems: QueryList<MatMenuItem>;\n\n  /** Only the direct descendant menu items. */\n  _directDescendantItems = new QueryList<MatMenuItem>();\n\n  /** Classes to be applied to the menu panel. */\n  _classList: {[key: string]: boolean} = {};\n\n  /** Current state of the panel animation. */\n  _panelAnimationState: 'void' | 'enter' = 'void';\n\n  /** Emits whenever an animation on the menu completes. */\n  readonly _animationDone = new Subject<'void' | 'enter'>();\n\n  /** Whether the menu is animating. */\n  _isAnimating = false;\n\n  /** Parent menu of the current menu panel. */\n  parentMenu: MatMenuPanel | undefined;\n\n  /** Layout direction of the menu. */\n  direction: Direction;\n\n  /** Class or list of classes to be added to the overlay panel. */\n  overlayPanelClass: string | string[];\n\n  /** Class to be added to the backdrop element. */\n  @Input() backdropClass: string;\n\n  /** aria-label for the menu panel. */\n  @Input('aria-label') ariaLabel: string;\n\n  /** aria-labelledby for the menu panel. */\n  @Input('aria-labelledby') ariaLabelledby: string;\n\n  /** aria-describedby for the menu panel. */\n  @Input('aria-describedby') ariaDescribedby: string;\n\n  /** Position of the menu in the X axis. */\n  @Input()\n  get xPosition(): MenuPositionX {\n    return this._xPosition;\n  }\n  set xPosition(value: MenuPositionX) {\n    if (\n      value !== 'before' &&\n      value !== 'after' &&\n      (typeof ngDevMode === 'undefined' || ngDevMode)\n    ) {\n      throwMatMenuInvalidPositionX();\n    }\n    this._xPosition = value;\n    this.setPositionClasses();\n  }\n\n  /** Position of the menu in the Y axis. */\n  @Input()\n  get yPosition(): MenuPositionY {\n    return this._yPosition;\n  }\n  set yPosition(value: MenuPositionY) {\n    if (value !== 'above' && value !== 'below' && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throwMatMenuInvalidPositionY();\n    }\n    this._yPosition = value;\n    this.setPositionClasses();\n  }\n\n  /** @docs-private */\n  @ViewChild(TemplateRef) templateRef: TemplateRef<any>;\n\n  /**\n   * List of the items inside of a menu.\n   * @deprecated\n   * @breaking-change 8.0.0\n   */\n  @ContentChildren(MatMenuItem, {descendants: false}) items: QueryList<MatMenuItem>;\n\n  /**\n   * Menu content that will be rendered lazily.\n   * @docs-private\n   */\n  @ContentChild(MAT_MENU_CONTENT) lazyContent: MatMenuContent;\n\n  /** Whether the menu should overlap its trigger. */\n  @Input({transform: booleanAttribute}) overlapTrigger: boolean;\n\n  /** Whether the menu has a backdrop. */\n  @Input({transform: (value: any) => (value == null ? null : booleanAttribute(value))})\n  hasBackdrop?: boolean;\n\n  /**\n   * This method takes classes set on the host mat-menu element and applies them on the\n   * menu template that displays in the overlay container.  Otherwise, it's difficult\n   * to style the containing menu from outside the component.\n   * @param classes list of class names\n   */\n  @Input('class')\n  set panelClass(classes: string) {\n    const previousPanelClass = this._previousPanelClass;\n    const newClassList = {...this._classList};\n\n    if (previousPanelClass && previousPanelClass.length) {\n      previousPanelClass.split(' ').forEach((className: string) => {\n        newClassList[className] = false;\n      });\n    }\n\n    this._previousPanelClass = classes;\n\n    if (classes && classes.length) {\n      classes.split(' ').forEach((className: string) => {\n        newClassList[className] = true;\n      });\n\n      this._elementRef.nativeElement.className = '';\n    }\n\n    this._classList = newClassList;\n  }\n  private _previousPanelClass: string;\n\n  /**\n   * This method takes classes set on the host mat-menu element and applies them on the\n   * menu template that displays in the overlay container.  Otherwise, it's difficult\n   * to style the containing menu from outside the component.\n   * @deprecated Use `panelClass` instead.\n   * @breaking-change 8.0.0\n   */\n  @Input()\n  get classList(): string {\n    return this.panelClass;\n  }\n  set classList(classes: string) {\n    this.panelClass = classes;\n  }\n\n  /** Event emitted when the menu is closed. */\n  @Output() readonly closed: EventEmitter<MenuCloseReason> = new EventEmitter<MenuCloseReason>();\n\n  /**\n   * Event emitted when the menu is closed.\n   * @deprecated Switch to `closed` instead\n   * @breaking-change 8.0.0\n   */\n  @Output() readonly close: EventEmitter<MenuCloseReason> = this.closed;\n\n  readonly panelId: string = inject(_IdGenerator).getId('mat-menu-panel-');\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    const defaultOptions = inject<MatMenuDefaultOptions>(MAT_MENU_DEFAULT_OPTIONS);\n    this.overlayPanelClass = defaultOptions.overlayPanelClass || '';\n    this._xPosition = defaultOptions.xPosition;\n    this._yPosition = defaultOptions.yPosition;\n    this.backdropClass = defaultOptions.backdropClass;\n    this.overlapTrigger = defaultOptions.overlapTrigger;\n    this.hasBackdrop = defaultOptions.hasBackdrop;\n    this._animationsDisabled = inject(ANIMATION_MODULE_TYPE, {optional: true}) === 'NoopAnimations';\n  }\n\n  ngOnInit() {\n    this.setPositionClasses();\n  }\n\n  ngAfterContentInit() {\n    this._updateDirectDescendants();\n    this._keyManager = new FocusKeyManager(this._directDescendantItems)\n      .withWrap()\n      .withTypeAhead()\n      .withHomeAndEnd();\n    this._keyManager.tabOut.subscribe(() => this.closed.emit('tab'));\n\n    // If a user manually (programmatically) focuses a menu item, we need to reflect that focus\n    // change back to the key manager. Note that we don't need to unsubscribe here because _focused\n    // is internal and we know that it gets completed on destroy.\n    this._directDescendantItems.changes\n      .pipe(\n        startWith(this._directDescendantItems),\n        switchMap(items => merge(...items.map((item: MatMenuItem) => item._focused))),\n      )\n      .subscribe(focusedItem => this._keyManager.updateActiveItem(focusedItem as MatMenuItem));\n\n    this._directDescendantItems.changes.subscribe((itemsList: QueryList<MatMenuItem>) => {\n      // Move focus to another item, if the active item is removed from the list.\n      // We need to debounce the callback, because multiple items might be removed\n      // in quick succession.\n      const manager = this._keyManager;\n\n      if (this._panelAnimationState === 'enter' && manager.activeItem?._hasFocus()) {\n        const items = itemsList.toArray();\n        const index = Math.max(0, Math.min(items.length - 1, manager.activeItemIndex || 0));\n\n        if (items[index] && !items[index].disabled) {\n          manager.setActiveItem(index);\n        } else {\n          manager.setNextItemActive();\n        }\n      }\n    });\n  }\n\n  ngOnDestroy() {\n    this._keyManager?.destroy();\n    this._directDescendantItems.destroy();\n    this.closed.complete();\n    this._firstItemFocusRef?.destroy();\n    clearTimeout(this._exitFallbackTimeout);\n  }\n\n  /** Stream that emits whenever the hovered menu item changes. */\n  _hovered(): Observable<MatMenuItem> {\n    // Coerce the `changes` property because Angular types it as `Observable<any>`\n    const itemChanges = this._directDescendantItems.changes as Observable<QueryList<MatMenuItem>>;\n    return itemChanges.pipe(\n      startWith(this._directDescendantItems),\n      switchMap(items => merge(...items.map((item: MatMenuItem) => item._hovered))),\n    ) as Observable<MatMenuItem>;\n  }\n\n  /*\n   * Registers a menu item with the menu.\n   * @docs-private\n   * @deprecated No longer being used. To be removed.\n   * @breaking-change 9.0.0\n   */\n  addItem(_item: MatMenuItem) {}\n\n  /**\n   * Removes an item from the menu.\n   * @docs-private\n   * @deprecated No longer being used. To be removed.\n   * @breaking-change 9.0.0\n   */\n  removeItem(_item: MatMenuItem) {}\n\n  /** Handle a keyboard event from the menu, delegating to the appropriate action. */\n  _handleKeydown(event: KeyboardEvent) {\n    const keyCode = event.keyCode;\n    const manager = this._keyManager;\n\n    switch (keyCode) {\n      case ESCAPE:\n        if (!hasModifierKey(event)) {\n          event.preventDefault();\n          this.closed.emit('keydown');\n        }\n        break;\n      case LEFT_ARROW:\n        if (this.parentMenu && this.direction === 'ltr') {\n          this.closed.emit('keydown');\n        }\n        break;\n      case RIGHT_ARROW:\n        if (this.parentMenu && this.direction === 'rtl') {\n          this.closed.emit('keydown');\n        }\n        break;\n      default:\n        if (keyCode === UP_ARROW || keyCode === DOWN_ARROW) {\n          manager.setFocusOrigin('keyboard');\n        }\n\n        manager.onKeydown(event);\n        return;\n    }\n  }\n\n  /**\n   * Focus the first item in the menu.\n   * @param origin Action from which the focus originated. Used to set the correct styling.\n   */\n  focusFirstItem(origin: FocusOrigin = 'program'): void {\n    // Wait for `afterNextRender` to ensure iOS VoiceOver screen reader focuses the first item (#24735).\n    this._firstItemFocusRef?.destroy();\n    this._firstItemFocusRef = afterNextRender(\n      () => {\n        const menuPanel = this._resolvePanel();\n\n        // If an item in the menuPanel is already focused, avoid overriding the focus.\n        if (!menuPanel || !menuPanel.contains(document.activeElement)) {\n          const manager = this._keyManager;\n          manager.setFocusOrigin(origin).setFirstItemActive();\n\n          // If there's no active item at this point, it means that all the items are disabled.\n          // Move focus to the menuPanel panel so keyboard events like Escape still work. Also this will\n          // give _some_ feedback to screen readers.\n          if (!manager.activeItem && menuPanel) {\n            menuPanel.focus();\n          }\n        }\n      },\n      {injector: this._injector},\n    );\n  }\n\n  /**\n   * Resets the active item in the menu. This is used when the menu is opened, allowing\n   * the user to start from the first option when pressing the down arrow.\n   */\n  resetActiveItem() {\n    this._keyManager.setActiveItem(-1);\n  }\n\n  /**\n   * @deprecated No longer used and will be removed.\n   * @breaking-change 21.0.0\n   */\n  setElevation(_depth: number): void {}\n\n  /**\n   * Adds classes to the menu panel based on its position. Can be used by\n   * consumers to add specific styling based on the position.\n   * @param posX Position of the menu along the x axis.\n   * @param posY Position of the menu along the y axis.\n   * @docs-private\n   */\n  setPositionClasses(posX: MenuPositionX = this.xPosition, posY: MenuPositionY = this.yPosition) {\n    this._classList = {\n      ...this._classList,\n      ['mat-menu-before']: posX === 'before',\n      ['mat-menu-after']: posX === 'after',\n      ['mat-menu-above']: posY === 'above',\n      ['mat-menu-below']: posY === 'below',\n    };\n\n    this._changeDetectorRef.markForCheck();\n  }\n\n  /** Callback that is invoked when the panel animation completes. */\n  protected _onAnimationDone(state: string) {\n    const isExit = state === EXIT_ANIMATION;\n\n    if (isExit || state === ENTER_ANIMATION) {\n      if (isExit) {\n        clearTimeout(this._exitFallbackTimeout);\n        this._exitFallbackTimeout = undefined;\n      }\n      this._animationDone.next(isExit ? 'void' : 'enter');\n      this._isAnimating = false;\n    }\n  }\n\n  protected _onAnimationStart(state: string) {\n    if (state === ENTER_ANIMATION || state === EXIT_ANIMATION) {\n      this._isAnimating = true;\n    }\n  }\n\n  _setIsOpen(isOpen: boolean) {\n    this._panelAnimationState = isOpen ? 'enter' : 'void';\n\n    if (isOpen) {\n      if (this._keyManager.activeItemIndex === 0) {\n        // Scroll the content element to the top as soon as the animation starts. This is necessary,\n        // because we move focus to the first item while it's still being animated, which can throw\n        // the browser off when it determines the scroll position. Alternatively we can move focus\n        // when the animation is done, however moving focus asynchronously will interrupt screen\n        // readers which are in the process of reading out the menu already. We take the `element`\n        // from the `event` since we can't use a `ViewChild` to access the pane.\n        const menuPanel = this._resolvePanel();\n\n        if (menuPanel) {\n          menuPanel.scrollTop = 0;\n        }\n      }\n    } else if (!this._animationsDisabled) {\n      // Some apps do `* { animation: none !important; }` in tests which will prevent the\n      // `animationend` event from firing. Since the exit animation is loading-bearing for\n      // removing the content from the DOM, add a fallback timer.\n      this._exitFallbackTimeout = setTimeout(() => this._onAnimationDone(EXIT_ANIMATION), 200);\n    }\n\n    // Animation events won't fire when animations are disabled so we simulate them.\n    if (this._animationsDisabled) {\n      setTimeout(() => {\n        this._onAnimationDone(isOpen ? ENTER_ANIMATION : EXIT_ANIMATION);\n      });\n    }\n\n    this._changeDetectorRef.markForCheck();\n  }\n\n  /**\n   * Sets up a stream that will keep track of any newly-added menu items and will update the list\n   * of direct descendants. We collect the descendants this way, because `_allItems` can include\n   * items that are part of child menus, and using a custom way of registering items is unreliable\n   * when it comes to maintaining the item order.\n   */\n  private _updateDirectDescendants() {\n    this._allItems.changes\n      .pipe(startWith(this._allItems))\n      .subscribe((items: QueryList<MatMenuItem>) => {\n        this._directDescendantItems.reset(items.filter(item => item._parentMenu === this));\n        this._directDescendantItems.notifyOnChanges();\n      });\n  }\n\n  /** Gets the menu panel DOM node. */\n  private _resolvePanel(): HTMLElement | null {\n    let menuPanel: HTMLElement | null = null;\n\n    if (this._directDescendantItems.length) {\n      // Because the `mat-menuPanel` is at the DOM insertion point, not inside the overlay, we don't\n      // have a nice way of getting a hold of the menuPanel panel. We can't use a `ViewChild` either\n      // because the panel is inside an `ng-template`. We work around it by starting from one of\n      // the items and walking up the DOM.\n      menuPanel = this._directDescendantItems.first!._getHostElement().closest('[role=\"menu\"]');\n    }\n\n    return menuPanel;\n  }\n}\n", "<ng-template>\n  <div\n    class=\"mat-mdc-menu-panel\"\n    [id]=\"panelId\"\n    [class]=\"_classList\"\n    [class.mat-menu-panel-animations-disabled]=\"_animationsDisabled\"\n    [class.mat-menu-panel-exit-animation]=\"_panelAnimationState === 'void'\"\n    [class.mat-menu-panel-animating]=\"_isAnimating\"\n    (click)=\"closed.emit('click')\"\n    tabindex=\"-1\"\n    role=\"menu\"\n    (animationstart)=\"_onAnimationStart($event.animationName)\"\n    (animationend)=\"_onAnimationDone($event.animationName)\"\n    (animationcancel)=\"_onAnimationDone($event.animationName)\"\n    [attr.aria-label]=\"ariaLabel || null\"\n    [attr.aria-labelledby]=\"ariaLabelledby || null\"\n    [attr.aria-describedby]=\"ariaDescribedby || null\">\n    <div class=\"mat-mdc-menu-content\">\n      <ng-content></ng-content>\n    </div>\n  </div>\n</ng-template>\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  FocusMonitor,\n  FocusOrigin,\n  isFakeMousedownFromScreenReader,\n  isFakeTouchstartFromScreenReader,\n} from '@angular/cdk/a11y';\nimport {Direction, Directionality} from '@angular/cdk/bidi';\nimport {ENTER, LEFT_ARROW, RIGHT_ARROW, SPACE} from '@angular/cdk/keycodes';\nimport {\n  FlexibleConnectedPositionStrategy,\n  HorizontalConnectionPos,\n  Overlay,\n  OverlayConfig,\n  OverlayRef,\n  ScrollStrategy,\n  VerticalConnectionPos,\n} from '@angular/cdk/overlay';\nimport {TemplatePortal} from '@angular/cdk/portal';\nimport {\n  AfterContentInit,\n  ChangeDetectorRef,\n  Directive,\n  ElementRef,\n  EventEmitter,\n  inject,\n  InjectionToken,\n  Input,\n  NgZone,\n  OnD<PERSON>roy,\n  Output,\n  Renderer2,\n  ViewContainerRef,\n} from '@angular/core';\nimport {_bindEventWithOptions} from '@angular/cdk/platform';\nimport {merge, Observable, of as observableOf, Subscription} from 'rxjs';\nimport {filter, take, takeUntil} from 'rxjs/operators';\nimport {MatMenu, MenuCloseReason} from './menu';\nimport {throwMatMenuRecursiveError} from './menu-errors';\nimport {MatMenuItem} from './menu-item';\nimport {MAT_MENU_PANEL, MatMenuPanel} from './menu-panel';\nimport {MenuPositionX, MenuPositionY} from './menu-positions';\n\n/** Injection token that determines the scroll handling while the menu is open. */\nexport const MAT_MENU_SCROLL_STRATEGY = new InjectionToken<() => ScrollStrategy>(\n  'mat-menu-scroll-strategy',\n  {\n    providedIn: 'root',\n    factory: () => {\n      const overlay = inject(Overlay);\n      return () => overlay.scrollStrategies.reposition();\n    },\n  },\n);\n\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nexport function MAT_MENU_SCROLL_STRATEGY_FACTORY(overlay: Overlay): () => ScrollStrategy {\n  return () => overlay.scrollStrategies.reposition();\n}\n\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nexport const MAT_MENU_SCROLL_STRATEGY_FACTORY_PROVIDER = {\n  provide: MAT_MENU_SCROLL_STRATEGY,\n  deps: [Overlay],\n  useFactory: MAT_MENU_SCROLL_STRATEGY_FACTORY,\n};\n\n/** Options for binding a passive event listener. */\nconst passiveEventListenerOptions = {passive: true};\n\n/**\n * Default top padding of the menu panel.\n * @deprecated No longer being used. Will be removed.\n * @breaking-change 15.0.0\n */\nexport const MENU_PANEL_TOP_PADDING = 8;\n\n/** Mapping between menu panels and the last trigger that opened them. */\nconst PANELS_TO_TRIGGERS = new WeakMap<MatMenuPanel, MatMenuTrigger>();\n\n/** Directive applied to an element that should trigger a `mat-menu`. */\n@Directive({\n  selector: `[mat-menu-trigger-for], [matMenuTriggerFor]`,\n  host: {\n    'class': 'mat-mdc-menu-trigger',\n    '[attr.aria-haspopup]': 'menu ? \"menu\" : null',\n    '[attr.aria-expanded]': 'menuOpen',\n    '[attr.aria-controls]': 'menuOpen ? menu.panelId : null',\n    '(click)': '_handleClick($event)',\n    '(mousedown)': '_handleMousedown($event)',\n    '(keydown)': '_handleKeydown($event)',\n  },\n  exportAs: 'matMenuTrigger',\n})\nexport class MatMenuTrigger implements AfterContentInit, OnDestroy {\n  private _overlay = inject(Overlay);\n  private _element = inject<ElementRef<HTMLElement>>(ElementRef);\n  private _viewContainerRef = inject(ViewContainerRef);\n  private _menuItemInstance = inject(MatMenuItem, {optional: true, self: true})!;\n  private _dir = inject(Directionality, {optional: true});\n  private _focusMonitor = inject(FocusMonitor);\n  private _ngZone = inject(NgZone);\n  private _scrollStrategy = inject(MAT_MENU_SCROLL_STRATEGY);\n  private _changeDetectorRef = inject(ChangeDetectorRef);\n  private _cleanupTouchstart: () => void;\n\n  private _portal: TemplatePortal;\n  private _overlayRef: OverlayRef | null = null;\n  private _menuOpen: boolean = false;\n  private _closingActionsSubscription = Subscription.EMPTY;\n  private _hoverSubscription = Subscription.EMPTY;\n  private _menuCloseSubscription = Subscription.EMPTY;\n  private _pendingRemoval: Subscription | undefined;\n\n  /**\n   * We're specifically looking for a `MatMenu` here since the generic `MatMenuPanel`\n   * interface lacks some functionality around nested menus and animations.\n   */\n  private _parentMaterialMenu: MatMenu | undefined;\n\n  /**\n   * Cached value of the padding of the parent menu panel.\n   * Used to offset sub-menus to compensate for the padding.\n   */\n  private _parentInnerPadding: number | undefined;\n\n  // Tracking input type is necessary so it's possible to only auto-focus\n  // the first item of the list when the menu is opened via the keyboard\n  _openedBy: Exclude<FocusOrigin, 'program' | null> | undefined = undefined;\n\n  /**\n   * @deprecated\n   * @breaking-change 8.0.0\n   */\n  @Input('mat-menu-trigger-for')\n  get _deprecatedMatMenuTriggerFor(): MatMenuPanel | null {\n    return this.menu;\n  }\n  set _deprecatedMatMenuTriggerFor(v: MatMenuPanel | null) {\n    this.menu = v;\n  }\n\n  /** References the menu instance that the trigger is associated with. */\n  @Input('matMenuTriggerFor')\n  get menu(): MatMenuPanel | null {\n    return this._menu;\n  }\n  set menu(menu: MatMenuPanel | null) {\n    if (menu === this._menu) {\n      return;\n    }\n\n    this._menu = menu;\n    this._menuCloseSubscription.unsubscribe();\n\n    if (menu) {\n      if (menu === this._parentMaterialMenu && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throwMatMenuRecursiveError();\n      }\n\n      this._menuCloseSubscription = menu.close.subscribe((reason: MenuCloseReason) => {\n        this._destroyMenu(reason);\n\n        // If a click closed the menu, we should close the entire chain of nested menus.\n        if ((reason === 'click' || reason === 'tab') && this._parentMaterialMenu) {\n          this._parentMaterialMenu.closed.emit(reason);\n        }\n      });\n    }\n\n    this._menuItemInstance?._setTriggersSubmenu(this.triggersSubmenu());\n  }\n  private _menu: MatMenuPanel | null;\n\n  /** Data to be passed along to any lazily-rendered content. */\n  @Input('matMenuTriggerData') menuData: any;\n\n  /**\n   * Whether focus should be restored when the menu is closed.\n   * Note that disabling this option can have accessibility implications\n   * and it's up to you to manage focus, if you decide to turn it off.\n   */\n  @Input('matMenuTriggerRestoreFocus') restoreFocus: boolean = true;\n\n  /** Event emitted when the associated menu is opened. */\n  @Output() readonly menuOpened: EventEmitter<void> = new EventEmitter<void>();\n\n  /**\n   * Event emitted when the associated menu is opened.\n   * @deprecated Switch to `menuOpened` instead\n   * @breaking-change 8.0.0\n   */\n  // tslint:disable-next-line:no-output-on-prefix\n  @Output() readonly onMenuOpen: EventEmitter<void> = this.menuOpened;\n\n  /** Event emitted when the associated menu is closed. */\n  @Output() readonly menuClosed: EventEmitter<void> = new EventEmitter<void>();\n\n  /**\n   * Event emitted when the associated menu is closed.\n   * @deprecated Switch to `menuClosed` instead\n   * @breaking-change 8.0.0\n   */\n  // tslint:disable-next-line:no-output-on-prefix\n  @Output() readonly onMenuClose: EventEmitter<void> = this.menuClosed;\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    const parentMenu = inject<MatMenuPanel>(MAT_MENU_PANEL, {optional: true});\n    const renderer = inject(Renderer2);\n\n    this._parentMaterialMenu = parentMenu instanceof MatMenu ? parentMenu : undefined;\n    this._cleanupTouchstart = _bindEventWithOptions(\n      renderer,\n      this._element.nativeElement,\n      'touchstart',\n      (event: TouchEvent) => {\n        if (!isFakeTouchstartFromScreenReader(event)) {\n          this._openedBy = 'touch';\n        }\n      },\n      passiveEventListenerOptions,\n    );\n  }\n\n  ngAfterContentInit() {\n    this._handleHover();\n  }\n\n  ngOnDestroy() {\n    if (this.menu && this._ownsMenu(this.menu)) {\n      PANELS_TO_TRIGGERS.delete(this.menu);\n    }\n\n    this._cleanupTouchstart();\n    this._pendingRemoval?.unsubscribe();\n    this._menuCloseSubscription.unsubscribe();\n    this._closingActionsSubscription.unsubscribe();\n    this._hoverSubscription.unsubscribe();\n\n    if (this._overlayRef) {\n      this._overlayRef.dispose();\n      this._overlayRef = null;\n    }\n  }\n\n  /** Whether the menu is open. */\n  get menuOpen(): boolean {\n    return this._menuOpen;\n  }\n\n  /** The text direction of the containing app. */\n  get dir(): Direction {\n    return this._dir && this._dir.value === 'rtl' ? 'rtl' : 'ltr';\n  }\n\n  /** Whether the menu triggers a sub-menu or a top-level one. */\n  triggersSubmenu(): boolean {\n    return !!(this._menuItemInstance && this._parentMaterialMenu && this.menu);\n  }\n\n  /** Toggles the menu between the open and closed states. */\n  toggleMenu(): void {\n    return this._menuOpen ? this.closeMenu() : this.openMenu();\n  }\n\n  /** Opens the menu. */\n  openMenu(): void {\n    const menu = this.menu;\n\n    if (this._menuOpen || !menu) {\n      return;\n    }\n\n    this._pendingRemoval?.unsubscribe();\n    const previousTrigger = PANELS_TO_TRIGGERS.get(menu);\n    PANELS_TO_TRIGGERS.set(menu, this);\n\n    // If the same menu is currently attached to another trigger,\n    // we need to close it so it doesn't end up in a broken state.\n    if (previousTrigger && previousTrigger !== this) {\n      previousTrigger.closeMenu();\n    }\n\n    const overlayRef = this._createOverlay(menu);\n    const overlayConfig = overlayRef.getConfig();\n    const positionStrategy = overlayConfig.positionStrategy as FlexibleConnectedPositionStrategy;\n\n    this._setPosition(menu, positionStrategy);\n    overlayConfig.hasBackdrop =\n      menu.hasBackdrop == null ? !this.triggersSubmenu() : menu.hasBackdrop;\n\n    // We need the `hasAttached` check for the case where the user kicked off a removal animation,\n    // but re-entered the menu. Re-attaching the same portal will trigger an error otherwise.\n    if (!overlayRef.hasAttached()) {\n      overlayRef.attach(this._getPortal(menu));\n      menu.lazyContent?.attach(this.menuData);\n    }\n\n    this._closingActionsSubscription = this._menuClosingActions().subscribe(() => this.closeMenu());\n    menu.parentMenu = this.triggersSubmenu() ? this._parentMaterialMenu : undefined;\n    menu.direction = this.dir;\n    menu.focusFirstItem(this._openedBy || 'program');\n    this._setIsMenuOpen(true);\n\n    if (menu instanceof MatMenu) {\n      menu._setIsOpen(true);\n      menu._directDescendantItems.changes.pipe(takeUntil(menu.close)).subscribe(() => {\n        // Re-adjust the position without locking when the amount of items\n        // changes so that the overlay is allowed to pick a new optimal position.\n        positionStrategy.withLockedPosition(false).reapplyLastPosition();\n        positionStrategy.withLockedPosition(true);\n      });\n    }\n  }\n\n  /** Closes the menu. */\n  closeMenu(): void {\n    this.menu?.close.emit();\n  }\n\n  /**\n   * Focuses the menu trigger.\n   * @param origin Source of the menu trigger's focus.\n   */\n  focus(origin?: FocusOrigin, options?: FocusOptions) {\n    if (this._focusMonitor && origin) {\n      this._focusMonitor.focusVia(this._element, origin, options);\n    } else {\n      this._element.nativeElement.focus(options);\n    }\n  }\n\n  /**\n   * Updates the position of the menu to ensure that it fits all options within the viewport.\n   */\n  updatePosition(): void {\n    this._overlayRef?.updatePosition();\n  }\n\n  /** Closes the menu and does the necessary cleanup. */\n  private _destroyMenu(reason: MenuCloseReason) {\n    const overlayRef = this._overlayRef;\n    const menu = this._menu;\n\n    if (!overlayRef || !this.menuOpen) {\n      return;\n    }\n\n    this._closingActionsSubscription.unsubscribe();\n    this._pendingRemoval?.unsubscribe();\n\n    // Note that we don't wait for the animation to finish if another trigger took\n    // over the menu, because the panel will end up empty which looks glitchy.\n    if (menu instanceof MatMenu && this._ownsMenu(menu)) {\n      this._pendingRemoval = menu._animationDone.pipe(take(1)).subscribe(() => {\n        overlayRef.detach();\n        menu.lazyContent?.detach();\n      });\n      menu._setIsOpen(false);\n    } else {\n      overlayRef.detach();\n      menu?.lazyContent?.detach();\n    }\n\n    if (menu && this._ownsMenu(menu)) {\n      PANELS_TO_TRIGGERS.delete(menu);\n    }\n\n    // Always restore focus if the user is navigating using the keyboard or the menu was opened\n    // programmatically. We don't restore for non-root triggers, because it can prevent focus\n    // from making it back to the root trigger when closing a long chain of menus by clicking\n    // on the backdrop.\n    if (this.restoreFocus && (reason === 'keydown' || !this._openedBy || !this.triggersSubmenu())) {\n      this.focus(this._openedBy);\n    }\n\n    this._openedBy = undefined;\n    this._setIsMenuOpen(false);\n  }\n\n  // set state rather than toggle to support triggers sharing a menu\n  private _setIsMenuOpen(isOpen: boolean): void {\n    if (isOpen !== this._menuOpen) {\n      this._menuOpen = isOpen;\n      this._menuOpen ? this.menuOpened.emit() : this.menuClosed.emit();\n\n      if (this.triggersSubmenu()) {\n        this._menuItemInstance._setHighlighted(isOpen);\n      }\n\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n\n  /**\n   * This method creates the overlay from the provided menu's template and saves its\n   * OverlayRef so that it can be attached to the DOM when openMenu is called.\n   */\n  private _createOverlay(menu: MatMenuPanel): OverlayRef {\n    if (!this._overlayRef) {\n      const config = this._getOverlayConfig(menu);\n      this._subscribeToPositions(\n        menu,\n        config.positionStrategy as FlexibleConnectedPositionStrategy,\n      );\n      this._overlayRef = this._overlay.create(config);\n      this._overlayRef.keydownEvents().subscribe(event => {\n        if (this.menu instanceof MatMenu) {\n          this.menu._handleKeydown(event);\n        }\n      });\n    }\n\n    return this._overlayRef;\n  }\n\n  /**\n   * This method builds the configuration object needed to create the overlay, the OverlayState.\n   * @returns OverlayConfig\n   */\n  private _getOverlayConfig(menu: MatMenuPanel): OverlayConfig {\n    return new OverlayConfig({\n      positionStrategy: this._overlay\n        .position()\n        .flexibleConnectedTo(this._element)\n        .withLockedPosition()\n        .withGrowAfterOpen()\n        .withTransformOriginOn('.mat-menu-panel, .mat-mdc-menu-panel'),\n      backdropClass: menu.backdropClass || 'cdk-overlay-transparent-backdrop',\n      panelClass: menu.overlayPanelClass,\n      scrollStrategy: this._scrollStrategy(),\n      direction: this._dir || 'ltr',\n    });\n  }\n\n  /**\n   * Listens to changes in the position of the overlay and sets the correct classes\n   * on the menu based on the new position. This ensures the animation origin is always\n   * correct, even if a fallback position is used for the overlay.\n   */\n  private _subscribeToPositions(menu: MatMenuPanel, position: FlexibleConnectedPositionStrategy) {\n    if (menu.setPositionClasses) {\n      position.positionChanges.subscribe(change => {\n        this._ngZone.run(() => {\n          const posX: MenuPositionX =\n            change.connectionPair.overlayX === 'start' ? 'after' : 'before';\n          const posY: MenuPositionY = change.connectionPair.overlayY === 'top' ? 'below' : 'above';\n          menu.setPositionClasses!(posX, posY);\n        });\n      });\n    }\n  }\n\n  /**\n   * Sets the appropriate positions on a position strategy\n   * so the overlay connects with the trigger correctly.\n   * @param positionStrategy Strategy whose position to update.\n   */\n  private _setPosition(menu: MatMenuPanel, positionStrategy: FlexibleConnectedPositionStrategy) {\n    let [originX, originFallbackX]: HorizontalConnectionPos[] =\n      menu.xPosition === 'before' ? ['end', 'start'] : ['start', 'end'];\n\n    let [overlayY, overlayFallbackY]: VerticalConnectionPos[] =\n      menu.yPosition === 'above' ? ['bottom', 'top'] : ['top', 'bottom'];\n\n    let [originY, originFallbackY] = [overlayY, overlayFallbackY];\n    let [overlayX, overlayFallbackX] = [originX, originFallbackX];\n    let offsetY = 0;\n\n    if (this.triggersSubmenu()) {\n      // When the menu is a sub-menu, it should always align itself\n      // to the edges of the trigger, instead of overlapping it.\n      overlayFallbackX = originX = menu.xPosition === 'before' ? 'start' : 'end';\n      originFallbackX = overlayX = originX === 'end' ? 'start' : 'end';\n\n      if (this._parentMaterialMenu) {\n        if (this._parentInnerPadding == null) {\n          const firstItem = this._parentMaterialMenu.items.first;\n          this._parentInnerPadding = firstItem ? firstItem._getHostElement().offsetTop : 0;\n        }\n\n        offsetY = overlayY === 'bottom' ? this._parentInnerPadding : -this._parentInnerPadding;\n      }\n    } else if (!menu.overlapTrigger) {\n      originY = overlayY === 'top' ? 'bottom' : 'top';\n      originFallbackY = overlayFallbackY === 'top' ? 'bottom' : 'top';\n    }\n\n    positionStrategy.withPositions([\n      {originX, originY, overlayX, overlayY, offsetY},\n      {originX: originFallbackX, originY, overlayX: overlayFallbackX, overlayY, offsetY},\n      {\n        originX,\n        originY: originFallbackY,\n        overlayX,\n        overlayY: overlayFallbackY,\n        offsetY: -offsetY,\n      },\n      {\n        originX: originFallbackX,\n        originY: originFallbackY,\n        overlayX: overlayFallbackX,\n        overlayY: overlayFallbackY,\n        offsetY: -offsetY,\n      },\n    ]);\n  }\n\n  /** Returns a stream that emits whenever an action that should close the menu occurs. */\n  private _menuClosingActions() {\n    const backdrop = this._overlayRef!.backdropClick();\n    const detachments = this._overlayRef!.detachments();\n    const parentClose = this._parentMaterialMenu ? this._parentMaterialMenu.closed : observableOf();\n    const hover = this._parentMaterialMenu\n      ? this._parentMaterialMenu\n          ._hovered()\n          .pipe(filter(active => this._menuOpen && active !== this._menuItemInstance))\n      : observableOf();\n\n    return merge(backdrop, parentClose as Observable<MenuCloseReason>, hover, detachments);\n  }\n\n  /** Handles mouse presses on the trigger. */\n  _handleMousedown(event: MouseEvent): void {\n    if (!isFakeMousedownFromScreenReader(event)) {\n      // Since right or middle button clicks won't trigger the `click` event,\n      // we shouldn't consider the menu as opened by mouse in those cases.\n      this._openedBy = event.button === 0 ? 'mouse' : undefined;\n\n      // Since clicking on the trigger won't close the menu if it opens a sub-menu,\n      // we should prevent focus from moving onto it via click to avoid the\n      // highlight from lingering on the menu item.\n      if (this.triggersSubmenu()) {\n        event.preventDefault();\n      }\n    }\n  }\n\n  /** Handles key presses on the trigger. */\n  _handleKeydown(event: KeyboardEvent): void {\n    const keyCode = event.keyCode;\n\n    // Pressing enter on the trigger will trigger the click handler later.\n    if (keyCode === ENTER || keyCode === SPACE) {\n      this._openedBy = 'keyboard';\n    }\n\n    if (\n      this.triggersSubmenu() &&\n      ((keyCode === RIGHT_ARROW && this.dir === 'ltr') ||\n        (keyCode === LEFT_ARROW && this.dir === 'rtl'))\n    ) {\n      this._openedBy = 'keyboard';\n      this.openMenu();\n    }\n  }\n\n  /** Handles click events on the trigger. */\n  _handleClick(event: MouseEvent): void {\n    if (this.triggersSubmenu()) {\n      // Stop event propagation to avoid closing the parent menu.\n      event.stopPropagation();\n      this.openMenu();\n    } else {\n      this.toggleMenu();\n    }\n  }\n\n  /** Handles the cases where the user hovers over the trigger. */\n  private _handleHover() {\n    // Subscribe to changes in the hovered item in order to toggle the panel.\n    if (this.triggersSubmenu() && this._parentMaterialMenu) {\n      this._hoverSubscription = this._parentMaterialMenu._hovered().subscribe(active => {\n        if (active === this._menuItemInstance && !active.disabled) {\n          this._openedBy = 'mouse';\n          this.openMenu();\n        }\n      });\n    }\n  }\n\n  /** Gets the portal that should be attached to the overlay. */\n  private _getPortal(menu: MatMenuPanel): TemplatePortal {\n    // Note that we can avoid this check by keeping the portal on the menu panel.\n    // While it would be cleaner, we'd have to introduce another required method on\n    // `MatMenuPanel`, making it harder to consume.\n    if (!this._portal || this._portal.templateRef !== menu.templateRef) {\n      this._portal = new TemplatePortal(menu.templateRef, this._viewContainerRef);\n    }\n\n    return this._portal;\n  }\n\n  /**\n   * Determines whether the trigger owns a specific menu panel, at the current point in time.\n   * This allows us to distinguish the case where the same panel is passed into multiple triggers\n   * and multiple are open at a time.\n   */\n  private _ownsMenu(menu: MatMenuPanel): boolean {\n    return PANELS_TO_TRIGGERS.get(menu) === this;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {NgModule} from '@angular/core';\nimport {MatCommonModule, MatRippleModule} from '../core';\nimport {OverlayModule} from '@angular/cdk/overlay';\nimport {CdkScrollableModule} from '@angular/cdk/scrolling';\nimport {MatMenu} from './menu';\nimport {MatMenuItem} from './menu-item';\nimport {MatMenuContent} from './menu-content';\nimport {MAT_MENU_SCROLL_STRATEGY_FACTORY_PROVIDER, MatMenuTrigger} from './menu-trigger';\n\n@NgModule({\n  imports: [\n    MatRippleModule,\n    MatCommonModule,\n    OverlayModule,\n    MatMenu,\n    MatMenuItem,\n    MatMenuContent,\n    MatMenuTrigger,\n  ],\n  exports: [\n    CdkScrollableModule,\n    MatMenu,\n    MatCommonModule,\n    MatMenuItem,\n    MatMenuContent,\n    MatMenuTrigger,\n  ],\n  providers: [MAT_MENU_SCROLL_STRATEGY_FACTORY_PROVIDER],\n})\nexport class MatMenuModule {}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/**\n * Animations used by the mat-menu component.\n * Animation duration and timing values are based on:\n * https://material.io/guidelines/components/menus.html#menus-usage\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nexport const matMenuAnimations: {\n  readonly transformMenu: any;\n  readonly fadeInItems: any;\n} = {\n  // Represents:\n  // trigger('transformMenu', [\n  //   state(\n  //     'void',\n  //     style({\n  //       opacity: 0,\n  //       transform: 'scale(0.8)',\n  //     }),\n  //   ),\n  //   transition(\n  //     'void => enter',\n  //     animate(\n  //       '120ms cubic-bezier(0, 0, 0.2, 1)',\n  //       style({\n  //         opacity: 1,\n  //         transform: 'scale(1)',\n  //       }),\n  //     ),\n  //   ),\n  //   transition('* => void', animate('100ms 25ms linear', style({opacity: 0}))),\n  // ])\n\n  /**\n   * This animation controls the menu panel's entry and exit from the page.\n   *\n   * When the menu panel is added to the DOM, it scales in and fades in its border.\n   *\n   * When the menu panel is removed from the DOM, it simply fades out after a brief\n   * delay to display the ripple.\n   */\n  transformMenu: {\n    type: 7,\n    name: 'transformMenu',\n    definitions: [\n      {\n        type: 0,\n        name: 'void',\n        styles: {type: 6, styles: {opacity: 0, transform: 'scale(0.8)'}, offset: null},\n      },\n      {\n        type: 1,\n        expr: 'void => enter',\n        animation: {\n          type: 4,\n          styles: {type: 6, styles: {opacity: 1, transform: 'scale(1)'}, offset: null},\n          timings: '120ms cubic-bezier(0, 0, 0.2, 1)',\n        },\n        options: null,\n      },\n      {\n        type: 1,\n        expr: '* => void',\n        animation: {\n          type: 4,\n          styles: {type: 6, styles: {opacity: 0}, offset: null},\n          timings: '100ms 25ms linear',\n        },\n        options: null,\n      },\n    ],\n    options: {},\n  },\n\n  // Represents:\n  // trigger('fadeInItems', [\n  //   // TODO(crisbeto): this is inside the `transformMenu`\n  //   // now. Remove next time we do breaking changes.\n  //   state('showing', style({opacity: 1})),\n  //   transition('void => *', [\n  //     style({opacity: 0}),\n  //     animate('400ms 100ms cubic-bezier(0.55, 0, 0.55, 0.2)'),\n  //   ]),\n  // ])\n\n  /**\n   * This animation fades in the background color and content of the menu panel\n   * after its containing element is scaled in.\n   */\n  fadeInItems: {\n    type: 7,\n    name: 'fadeInItems',\n    definitions: [\n      {\n        type: 0,\n        name: 'showing',\n        styles: {type: 6, styles: {opacity: 1}, offset: null},\n      },\n      {\n        type: 1,\n        expr: 'void => *',\n        animation: [\n          {type: 6, styles: {opacity: 0}, offset: null},\n          {type: 4, styles: null, timings: '400ms 100ms cubic-bezier(0.55, 0, 0.55, 0.2)'},\n        ],\n        options: null,\n      },\n    ],\n    options: {},\n  },\n};\n\n/**\n * @deprecated\n * @breaking-change 8.0.0\n * @docs-private\n */\nexport const fadeInItems = matMenuAnimations.fadeInItems;\n\n/**\n * @deprecated\n * @breaking-change 8.0.0\n * @docs-private\n */\nexport const transformMenu = matMenuAnimations.transformMenu;\n"], "names": ["observableOf"], "mappings": ";;;;;;;;;;;;;;;;;;;AAcA;;;AAGG;MACU,cAAc,GAAG,IAAI,cAAc,CAAe,gBAAgB;;ACS/E;;AAEG;MAoBU,WAAW,CAAA;AACd,IAAA,WAAW,GAAG,MAAM,CAA0B,UAAU,CAAC;AACzD,IAAA,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC;AAC5B,IAAA,aAAa,GAAG,MAAM,CAAC,YAAY,CAAC;IAC5C,WAAW,GAAI,MAAM,CAA4B,cAAc,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;AAC1E,IAAA,kBAAkB,GAAG,MAAM,CAAC,iBAAiB,CAAC;;IAG7C,IAAI,GAAsD,UAAU;;IAGvC,QAAQ,GAAY,KAAK;;IAGzB,aAAa,GAAY,KAAK;;AAG3D,IAAA,QAAQ,GAAyB,IAAI,OAAO,EAAe;;AAG3D,IAAA,QAAQ,GAAG,IAAI,OAAO,EAAe;;IAG9C,YAAY,GAAY,KAAK;;IAG7B,gBAAgB,GAAY,KAAK;AAIjC,IAAA,WAAA,GAAA;QACE,MAAM,CAAC,sBAAsB,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC;QAC5D,IAAI,CAAC,WAAW,EAAE,OAAO,GAAG,IAAI,CAAC;;;IAInC,KAAK,CAAC,MAAoB,EAAE,OAAsB,EAAA;AAChD,QAAA,IAAI,IAAI,CAAC,aAAa,IAAI,MAAM,EAAE;AAChC,YAAA,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,EAAE,EAAE,MAAM,EAAE,OAAO,CAAC;;aAC/D;YACL,IAAI,CAAC,eAAe,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC;;AAGvC,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;;IAG1B,eAAe,GAAA;AACb,QAAA,IAAI,IAAI,CAAC,aAAa,EAAE;;;;YAItB,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,CAAC;;;IAIvD,WAAW,GAAA;AACT,QAAA,IAAI,IAAI,CAAC,aAAa,EAAE;YACtB,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,IAAI,CAAC,WAAW,CAAC;;QAGrD,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,CAAC,UAAU,EAAE;AACnD,YAAA,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC;;AAGnC,QAAA,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE;AACxB,QAAA,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE;;;IAI1B,YAAY,GAAA;QACV,OAAO,IAAI,CAAC,QAAQ,GAAG,IAAI,GAAG,GAAG;;;IAInC,eAAe,GAAA;AACb,QAAA,OAAO,IAAI,CAAC,WAAW,CAAC,aAAa;;;AAIvC,IAAA,cAAc,CAAC,KAAY,EAAA;AACzB,QAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,KAAK,CAAC,cAAc,EAAE;YACtB,KAAK,CAAC,eAAe,EAAE;;;;IAK3B,iBAAiB,GAAA;AACf,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;;;IAI1B,QAAQ,GAAA;AACN,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,SAAS,CAAC,IAAI,CAAgB;QAC3E,MAAM,KAAK,GAAG,KAAK,CAAC,gBAAgB,CAAC,2BAA2B,CAAC;;AAGjE,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACrC,YAAA,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE;;QAGnB,OAAO,KAAK,CAAC,WAAW,EAAE,IAAI,EAAE,IAAI,EAAE;;AAGxC,IAAA,eAAe,CAAC,aAAsB,EAAA;;;;AAIpC,QAAA,IAAI,CAAC,YAAY,GAAG,aAAa;AACjC,QAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE;;AAGxC,IAAA,mBAAmB,CAAC,eAAwB,EAAA;AAC1C,QAAA,IAAI,CAAC,gBAAgB,GAAG,eAAe;AACvC,QAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE;;IAGxC,SAAS,GAAA;AACP,QAAA,OAAO,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,aAAa,KAAK,IAAI,CAAC,eAAe,EAAE;;uGAtHvE,WAAW,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAX,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,WAAW,8GAWH,gBAAgB,CAAA,EAAA,aAAA,EAAA,CAAA,eAAA,EAAA,eAAA,EAGhB,gBAAgB,CC/DrC,EAAA,EAAA,IAAA,EAAA,EAAA,SAAA,EAAA,EAAA,OAAA,EAAA,wBAAA,EAAA,YAAA,EAAA,qBAAA,EAAA,EAAA,UAAA,EAAA,EAAA,WAAA,EAAA,MAAA,EAAA,qCAAA,EAAA,cAAA,EAAA,yCAAA,EAAA,kBAAA,EAAA,eAAA,EAAA,gBAAA,EAAA,oBAAA,EAAA,UAAA,EAAA,eAAA,EAAA,kBAAA,EAAA,EAAA,cAAA,EAAA,uCAAA,EAAA,EAAA,QAAA,EAAA,CAAA,aAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,ggBAcA,4CDiCY,SAAS,EAAA,QAAA,EAAA,2BAAA,EAAA,MAAA,EAAA,CAAA,gBAAA,EAAA,oBAAA,EAAA,mBAAA,EAAA,iBAAA,EAAA,oBAAA,EAAA,mBAAA,EAAA,kBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,WAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAER,WAAW,EAAA,UAAA,EAAA,CAAA;kBAnBvB,SAAS;+BACE,iBAAiB,EAAA,QAAA,EACjB,aAAa,EACjB,IAAA,EAAA;AACJ,wBAAA,aAAa,EAAE,MAAM;AACrB,wBAAA,OAAO,EAAE,uCAAuC;AAChD,wBAAA,uCAAuC,EAAE,cAAc;AACvD,wBAAA,2CAA2C,EAAE,kBAAkB;AAC/D,wBAAA,iBAAiB,EAAE,gBAAgB;AACnC,wBAAA,sBAAsB,EAAE,UAAU;AAClC,wBAAA,iBAAiB,EAAE,kBAAkB;AACrC,wBAAA,SAAS,EAAE,wBAAwB;AACnC,wBAAA,cAAc,EAAE,qBAAqB;qBACtC,EACgB,eAAA,EAAA,uBAAuB,CAAC,MAAM,EAChC,aAAA,EAAA,iBAAiB,CAAC,IAAI,EAAA,OAAA,EAE5B,CAAC,SAAS,CAAC,EAAA,QAAA,EAAA,ggBAAA,EAAA;wDAUX,IAAI,EAAA,CAAA;sBAAZ;gBAGqC,QAAQ,EAAA,CAAA;sBAA7C,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBAGE,aAAa,EAAA,CAAA;sBAAlD,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;;;AEvDtC;;;;AAIG;SACa,4BAA4B,GAAA;AAC1C,IAAA,MAAM,KAAK,CAAC,CAAA;AAC0D,uEAAA,CAAA,CAAC;AACzE;AAEA;;;;AAIG;SACa,4BAA4B,GAAA;AAC1C,IAAA,MAAM,KAAK,CAAC,CAAA;AACyD,sEAAA,CAAA,CAAC;AACxE;AAEA;;;;AAIG;SACa,0BAA0B,GAAA;IACxC,MAAM,KAAK,CACT,CAAgF,8EAAA,CAAA;AAC9E,QAAA,CAAA,oEAAA,CAAsE,CACzE;AACH;;ACfA;;;;AAIG;MACU,gBAAgB,GAAG,IAAI,cAAc,CAAiB,gBAAgB;AAEnF;MAKa,cAAc,CAAA;AACjB,IAAA,SAAS,GAAG,MAAM,CAAmB,WAAW,CAAC;AACjD,IAAA,OAAO,GAAG,MAAM,CAAC,cAAc,CAAC;AAChC,IAAA,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC;AAC5B,IAAA,iBAAiB,GAAG,MAAM,CAAC,gBAAgB,CAAC;AAC5C,IAAA,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC;AAC5B,IAAA,kBAAkB,GAAG,MAAM,CAAC,iBAAiB,CAAC;AAE9C,IAAA,OAAO;AACP,IAAA,OAAO;;AAGN,IAAA,SAAS,GAAG,IAAI,OAAO,EAAQ;AAIxC,IAAA,WAAA,GAAA;AAEA;;;AAGG;IACH,MAAM,CAAC,UAAe,EAAE,EAAA;AACtB,QAAA,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;AACjB,YAAA,IAAI,CAAC,OAAO,GAAG,IAAI,cAAc,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,iBAAiB,CAAC;;QAG3E,IAAI,CAAC,MAAM,EAAE;AAEb,QAAA,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACjB,IAAI,CAAC,OAAO,GAAG,IAAI,eAAe,CAChC,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,EACnC,IAAI,EACJ,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,SAAS,CACf;;QAGH,MAAM,OAAO,GAAgB,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,aAAa;;;;AAKpE,QAAA,OAAO,CAAC,UAAW,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,OAAO,CAAC;;;;;;AAOrE,QAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE;QACtC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC;AAC1C,QAAA,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE;;AAGvB;;;AAGG;IACH,MAAM,GAAA;AACJ,QAAA,IAAI,IAAI,CAAC,OAAO,EAAE,UAAU,EAAE;AAC5B,YAAA,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;;;IAIzB,WAAW,GAAA;QACT,IAAI,CAAC,MAAM,EAAE;AACb,QAAA,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE;;uGAnEd,cAAc,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAd,cAAc,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,6BAAA,EAAA,SAAA,EAFd,CAAC,EAAC,OAAO,EAAE,gBAAgB,EAAE,WAAW,EAAE,cAAc,EAAC,CAAC,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAE1D,cAAc,EAAA,UAAA,EAAA,CAAA;kBAJ1B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,6BAA6B;oBACvC,SAAS,EAAE,CAAC,EAAC,OAAO,EAAE,gBAAgB,EAAE,WAAW,EAAgB,cAAA,EAAC,CAAC;AACtE,iBAAA;;;ACyCD;MACa,wBAAwB,GAAG,IAAI,cAAc,CACxD,0BAA0B,EAC1B;AACE,IAAA,UAAU,EAAE,MAAM;AAClB,IAAA,OAAO,EAAE,gCAAgC;AAC1C,CAAA;AAGH;;;;AAIG;SACa,gCAAgC,GAAA;IAC9C,OAAO;AACL,QAAA,cAAc,EAAE,KAAK;AACrB,QAAA,SAAS,EAAE,OAAO;AAClB,QAAA,SAAS,EAAE,OAAO;AAClB,QAAA,aAAa,EAAE,kCAAkC;KAClD;AACH;AAEA;AACA,MAAM,eAAe,GAAG,iBAAiB;AAEzC;AACA,MAAM,cAAc,GAAG,gBAAgB;MAgB1B,OAAO,CAAA;AACV,IAAA,WAAW,GAAG,MAAM,CAA0B,UAAU,CAAC;AACzD,IAAA,kBAAkB,GAAG,MAAM,CAAC,iBAAiB,CAAC;AAC9C,IAAA,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC;AAE5B,IAAA,WAAW;AACX,IAAA,UAAU;AACV,IAAA,UAAU;AACV,IAAA,kBAAkB;AAClB,IAAA,oBAAoB;;AAGlB,IAAA,mBAAmB;;AAGsB,IAAA,SAAS;;AAG5D,IAAA,sBAAsB,GAAG,IAAI,SAAS,EAAe;;IAGrD,UAAU,GAA6B,EAAE;;IAGzC,oBAAoB,GAAqB,MAAM;;AAGtC,IAAA,cAAc,GAAG,IAAI,OAAO,EAAoB;;IAGzD,YAAY,GAAG,KAAK;;AAGpB,IAAA,UAAU;;AAGV,IAAA,SAAS;;AAGT,IAAA,iBAAiB;;AAGR,IAAA,aAAa;;AAGD,IAAA,SAAS;;AAGJ,IAAA,cAAc;;AAGb,IAAA,eAAe;;AAG1C,IAAA,IACI,SAAS,GAAA;QACX,OAAO,IAAI,CAAC,UAAU;;IAExB,IAAI,SAAS,CAAC,KAAoB,EAAA;QAChC,IACE,KAAK,KAAK,QAAQ;AAClB,YAAA,KAAK,KAAK,OAAO;aAChB,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAC/C;AACA,YAAA,4BAA4B,EAAE;;AAEhC,QAAA,IAAI,CAAC,UAAU,GAAG,KAAK;QACvB,IAAI,CAAC,kBAAkB,EAAE;;;AAI3B,IAAA,IACI,SAAS,GAAA;QACX,OAAO,IAAI,CAAC,UAAU;;IAExB,IAAI,SAAS,CAAC,KAAoB,EAAA;AAChC,QAAA,IAAI,KAAK,KAAK,OAAO,IAAI,KAAK,KAAK,OAAO,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAAE;AAC7F,YAAA,4BAA4B,EAAE;;AAEhC,QAAA,IAAI,CAAC,UAAU,GAAG,KAAK;QACvB,IAAI,CAAC,kBAAkB,EAAE;;;AAIH,IAAA,WAAW;AAEnC;;;;AAIG;AACiD,IAAA,KAAK;AAEzD;;;AAGG;AAC6B,IAAA,WAAW;;AAGL,IAAA,cAAc;;AAIpD,IAAA,WAAW;AAEX;;;;;AAKG;IACH,IACI,UAAU,CAAC,OAAe,EAAA;AAC5B,QAAA,MAAM,kBAAkB,GAAG,IAAI,CAAC,mBAAmB;QACnD,MAAM,YAAY,GAAG,EAAC,GAAG,IAAI,CAAC,UAAU,EAAC;AAEzC,QAAA,IAAI,kBAAkB,IAAI,kBAAkB,CAAC,MAAM,EAAE;YACnD,kBAAkB,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,SAAiB,KAAI;AAC1D,gBAAA,YAAY,CAAC,SAAS,CAAC,GAAG,KAAK;AACjC,aAAC,CAAC;;AAGJ,QAAA,IAAI,CAAC,mBAAmB,GAAG,OAAO;AAElC,QAAA,IAAI,OAAO,IAAI,OAAO,CAAC,MAAM,EAAE;YAC7B,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,SAAiB,KAAI;AAC/C,gBAAA,YAAY,CAAC,SAAS,CAAC,GAAG,IAAI;AAChC,aAAC,CAAC;YAEF,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,SAAS,GAAG,EAAE;;AAG/C,QAAA,IAAI,CAAC,UAAU,GAAG,YAAY;;AAExB,IAAA,mBAAmB;AAE3B;;;;;;AAMG;AACH,IAAA,IACI,SAAS,GAAA;QACX,OAAO,IAAI,CAAC,UAAU;;IAExB,IAAI,SAAS,CAAC,OAAe,EAAA;AAC3B,QAAA,IAAI,CAAC,UAAU,GAAG,OAAO;;;AAIR,IAAA,MAAM,GAAkC,IAAI,YAAY,EAAmB;AAE9F;;;;AAIG;AACgB,IAAA,KAAK,GAAkC,IAAI,CAAC,MAAM;IAE5D,OAAO,GAAW,MAAM,CAAC,YAAY,CAAC,CAAC,KAAK,CAAC,iBAAiB,CAAC;AAIxE,IAAA,WAAA,GAAA;AACE,QAAA,MAAM,cAAc,GAAG,MAAM,CAAwB,wBAAwB,CAAC;QAC9E,IAAI,CAAC,iBAAiB,GAAG,cAAc,CAAC,iBAAiB,IAAI,EAAE;AAC/D,QAAA,IAAI,CAAC,UAAU,GAAG,cAAc,CAAC,SAAS;AAC1C,QAAA,IAAI,CAAC,UAAU,GAAG,cAAc,CAAC,SAAS;AAC1C,QAAA,IAAI,CAAC,aAAa,GAAG,cAAc,CAAC,aAAa;AACjD,QAAA,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC,cAAc;AACnD,QAAA,IAAI,CAAC,WAAW,GAAG,cAAc,CAAC,WAAW;AAC7C,QAAA,IAAI,CAAC,mBAAmB,GAAG,MAAM,CAAC,qBAAqB,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC,KAAK,gBAAgB;;IAGjG,QAAQ,GAAA;QACN,IAAI,CAAC,kBAAkB,EAAE;;IAG3B,kBAAkB,GAAA;QAChB,IAAI,CAAC,wBAAwB,EAAE;QAC/B,IAAI,CAAC,WAAW,GAAG,IAAI,eAAe,CAAC,IAAI,CAAC,sBAAsB;AAC/D,aAAA,QAAQ;AACR,aAAA,aAAa;AACb,aAAA,cAAc,EAAE;AACnB,QAAA,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;;;;QAKhE,IAAI,CAAC,sBAAsB,CAAC;AACzB,aAAA,IAAI,CACH,SAAS,CAAC,IAAI,CAAC,sBAAsB,CAAC,EACtC,SAAS,CAAC,KAAK,IAAI,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,IAAiB,KAAK,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;AAE9E,aAAA,SAAS,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,WAA0B,CAAC,CAAC;QAE1F,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,SAAiC,KAAI;;;;AAIlF,YAAA,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW;AAEhC,YAAA,IAAI,IAAI,CAAC,oBAAoB,KAAK,OAAO,IAAI,OAAO,CAAC,UAAU,EAAE,SAAS,EAAE,EAAE;AAC5E,gBAAA,MAAM,KAAK,GAAG,SAAS,CAAC,OAAO,EAAE;gBACjC,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,OAAO,CAAC,eAAe,IAAI,CAAC,CAAC,CAAC;AAEnF,gBAAA,IAAI,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,QAAQ,EAAE;AAC1C,oBAAA,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC;;qBACvB;oBACL,OAAO,CAAC,iBAAiB,EAAE;;;AAGjC,SAAC,CAAC;;IAGJ,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,WAAW,EAAE,OAAO,EAAE;AAC3B,QAAA,IAAI,CAAC,sBAAsB,CAAC,OAAO,EAAE;AACrC,QAAA,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;AACtB,QAAA,IAAI,CAAC,kBAAkB,EAAE,OAAO,EAAE;AAClC,QAAA,YAAY,CAAC,IAAI,CAAC,oBAAoB,CAAC;;;IAIzC,QAAQ,GAAA;;AAEN,QAAA,MAAM,WAAW,GAAG,IAAI,CAAC,sBAAsB,CAAC,OAA6C;AAC7F,QAAA,OAAO,WAAW,CAAC,IAAI,CACrB,SAAS,CAAC,IAAI,CAAC,sBAAsB,CAAC,EACtC,SAAS,CAAC,KAAK,IAAI,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,IAAiB,KAAK,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CACnD;;AAG9B;;;;;AAKG;IACH,OAAO,CAAC,KAAkB,EAAA;AAE1B;;;;;AAKG;IACH,UAAU,CAAC,KAAkB,EAAA;;AAG7B,IAAA,cAAc,CAAC,KAAoB,EAAA;AACjC,QAAA,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO;AAC7B,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW;QAEhC,QAAQ,OAAO;AACb,YAAA,KAAK,MAAM;AACT,gBAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE;oBAC1B,KAAK,CAAC,cAAc,EAAE;AACtB,oBAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC;;gBAE7B;AACF,YAAA,KAAK,UAAU;gBACb,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,SAAS,KAAK,KAAK,EAAE;AAC/C,oBAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC;;gBAE7B;AACF,YAAA,KAAK,WAAW;gBACd,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,SAAS,KAAK,KAAK,EAAE;AAC/C,oBAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC;;gBAE7B;AACF,YAAA;gBACE,IAAI,OAAO,KAAK,QAAQ,IAAI,OAAO,KAAK,UAAU,EAAE;AAClD,oBAAA,OAAO,CAAC,cAAc,CAAC,UAAU,CAAC;;AAGpC,gBAAA,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC;gBACxB;;;AAIN;;;AAGG;IACH,cAAc,CAAC,SAAsB,SAAS,EAAA;;AAE5C,QAAA,IAAI,CAAC,kBAAkB,EAAE,OAAO,EAAE;AAClC,QAAA,IAAI,CAAC,kBAAkB,GAAG,eAAe,CACvC,MAAK;AACH,YAAA,MAAM,SAAS,GAAG,IAAI,CAAC,aAAa,EAAE;;AAGtC,YAAA,IAAI,CAAC,SAAS,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE;AAC7D,gBAAA,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW;gBAChC,OAAO,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,kBAAkB,EAAE;;;;AAKnD,gBAAA,IAAI,CAAC,OAAO,CAAC,UAAU,IAAI,SAAS,EAAE;oBACpC,SAAS,CAAC,KAAK,EAAE;;;SAGtB,EACD,EAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,EAAC,CAC3B;;AAGH;;;AAGG;IACH,eAAe,GAAA;QACb,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;;AAGpC;;;AAGG;IACH,YAAY,CAAC,MAAc,EAAA;AAE3B;;;;;;AAMG;IACH,kBAAkB,CAAC,OAAsB,IAAI,CAAC,SAAS,EAAE,IAAA,GAAsB,IAAI,CAAC,SAAS,EAAA;QAC3F,IAAI,CAAC,UAAU,GAAG;YAChB,GAAG,IAAI,CAAC,UAAU;AAClB,YAAA,CAAC,iBAAiB,GAAG,IAAI,KAAK,QAAQ;AACtC,YAAA,CAAC,gBAAgB,GAAG,IAAI,KAAK,OAAO;AACpC,YAAA,CAAC,gBAAgB,GAAG,IAAI,KAAK,OAAO;AACpC,YAAA,CAAC,gBAAgB,GAAG,IAAI,KAAK,OAAO;SACrC;AAED,QAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE;;;AAI9B,IAAA,gBAAgB,CAAC,KAAa,EAAA;AACtC,QAAA,MAAM,MAAM,GAAG,KAAK,KAAK,cAAc;AAEvC,QAAA,IAAI,MAAM,IAAI,KAAK,KAAK,eAAe,EAAE;YACvC,IAAI,MAAM,EAAE;AACV,gBAAA,YAAY,CAAC,IAAI,CAAC,oBAAoB,CAAC;AACvC,gBAAA,IAAI,CAAC,oBAAoB,GAAG,SAAS;;AAEvC,YAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,GAAG,MAAM,GAAG,OAAO,CAAC;AACnD,YAAA,IAAI,CAAC,YAAY,GAAG,KAAK;;;AAInB,IAAA,iBAAiB,CAAC,KAAa,EAAA;QACvC,IAAI,KAAK,KAAK,eAAe,IAAI,KAAK,KAAK,cAAc,EAAE;AACzD,YAAA,IAAI,CAAC,YAAY,GAAG,IAAI;;;AAI5B,IAAA,UAAU,CAAC,MAAe,EAAA;AACxB,QAAA,IAAI,CAAC,oBAAoB,GAAG,MAAM,GAAG,OAAO,GAAG,MAAM;QAErD,IAAI,MAAM,EAAE;YACV,IAAI,IAAI,CAAC,WAAW,CAAC,eAAe,KAAK,CAAC,EAAE;;;;;;;AAO1C,gBAAA,MAAM,SAAS,GAAG,IAAI,CAAC,aAAa,EAAE;gBAEtC,IAAI,SAAS,EAAE;AACb,oBAAA,SAAS,CAAC,SAAS,GAAG,CAAC;;;;AAGtB,aAAA,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE;;;;AAIpC,YAAA,IAAI,CAAC,oBAAoB,GAAG,UAAU,CAAC,MAAM,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,EAAE,GAAG,CAAC;;;AAI1F,QAAA,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC5B,UAAU,CAAC,MAAK;AACd,gBAAA,IAAI,CAAC,gBAAgB,CAAC,MAAM,GAAG,eAAe,GAAG,cAAc,CAAC;AAClE,aAAC,CAAC;;AAGJ,QAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE;;AAGxC;;;;;AAKG;IACK,wBAAwB,GAAA;QAC9B,IAAI,CAAC,SAAS,CAAC;AACZ,aAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC;AAC9B,aAAA,SAAS,CAAC,CAAC,KAA6B,KAAI;YAC3C,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,IAAI,IAAI,CAAC,WAAW,KAAK,IAAI,CAAC,CAAC;AAClF,YAAA,IAAI,CAAC,sBAAsB,CAAC,eAAe,EAAE;AAC/C,SAAC,CAAC;;;IAIE,aAAa,GAAA;QACnB,IAAI,SAAS,GAAuB,IAAI;AAExC,QAAA,IAAI,IAAI,CAAC,sBAAsB,CAAC,MAAM,EAAE;;;;;AAKtC,YAAA,SAAS,GAAG,IAAI,CAAC,sBAAsB,CAAC,KAAM,CAAC,eAAe,EAAE,CAAC,OAAO,CAAC,eAAe,CAAC;;AAG3F,QAAA,OAAO,SAAS;;uGA1aP,OAAO,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAP,OAAO,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,UAAA,EAAA,MAAA,EAAA,EAAA,aAAA,EAAA,eAAA,EAAA,SAAA,EAAA,CAAA,YAAA,EAAA,WAAA,CAAA,EAAA,cAAA,EAAA,CAAA,iBAAA,EAAA,gBAAA,CAAA,EAAA,eAAA,EAAA,CAAA,kBAAA,EAAA,iBAAA,CAAA,EAAA,SAAA,EAAA,WAAA,EAAA,SAAA,EAAA,WAAA,EAAA,cAAA,EAAA,CAAA,gBAAA,EAAA,gBAAA,EAoGC,gBAAgB,CAGhB,EAAA,WAAA,EAAA,CAAA,aAAA,EAAA,aAAA,EAAA,CAAC,KAAU,MAAM,KAAK,IAAI,IAAI,GAAG,IAAI,GAAG,gBAAgB,CAAC,KAAK,CAAC,CAAC,CAAA,EAAA,UAAA,EAAA,CAAA,OAAA,EAAA,YAAA,CAAA,EAAA,SAAA,EAAA,WAAA,EAAA,EAAA,OAAA,EAAA,EAAA,MAAA,EAAA,QAAA,EAAA,KAAA,EAAA,OAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,iBAAA,EAAA,MAAA,EAAA,sBAAA,EAAA,MAAA,EAAA,uBAAA,EAAA,MAAA,EAAA,EAAA,EAAA,SAAA,EAzGxE,CAAC,EAAC,OAAO,EAAE,cAAc,EAAE,WAAW,EAAE,OAAO,EAAC,CAAC,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,aAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAmG9C,gBAAgB,EAlFb,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,WAAA,EAAA,SAAA,EAAA,WAAW,2DA4EX,WAAW,EAAA,CAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,aAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAPjB,WAAW,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,SAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EC1MxB,21BAsBA,EAAA,MAAA,EAAA,CAAA,08JAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FDgGa,OAAO,EAAA,UAAA,EAAA,CAAA;kBAdnB,SAAS;+BACE,UAAU,EAAA,eAAA,EAGH,uBAAuB,CAAC,MAAM,EAAA,aAAA,EAChC,iBAAiB,CAAC,IAAI,EAC3B,QAAA,EAAA,SAAS,EACb,IAAA,EAAA;AACJ,wBAAA,mBAAmB,EAAE,MAAM;AAC3B,wBAAA,wBAAwB,EAAE,MAAM;AAChC,wBAAA,yBAAyB,EAAE,MAAM;qBAClC,EACU,SAAA,EAAA,CAAC,EAAC,OAAO,EAAE,cAAc,EAAE,WAAW,EAAS,OAAA,EAAC,CAAC,EAAA,QAAA,EAAA,21BAAA,EAAA,MAAA,EAAA,CAAA,08JAAA,CAAA,EAAA;wDAiBT,SAAS,EAAA,CAAA;sBAA3D,eAAe;AAAC,gBAAA,IAAA,EAAA,CAAA,WAAW,EAAE,EAAC,WAAW,EAAE,IAAI,EAAC;gBA2BxC,aAAa,EAAA,CAAA;sBAArB;gBAGoB,SAAS,EAAA,CAAA;sBAA7B,KAAK;uBAAC,YAAY;gBAGO,cAAc,EAAA,CAAA;sBAAvC,KAAK;uBAAC,iBAAiB;gBAGG,eAAe,EAAA,CAAA;sBAAzC,KAAK;uBAAC,kBAAkB;gBAIrB,SAAS,EAAA,CAAA;sBADZ;gBAkBG,SAAS,EAAA,CAAA;sBADZ;gBAauB,WAAW,EAAA,CAAA;sBAAlC,SAAS;uBAAC,WAAW;gBAO8B,KAAK,EAAA,CAAA;sBAAxD,eAAe;AAAC,gBAAA,IAAA,EAAA,CAAA,WAAW,EAAE,EAAC,WAAW,EAAE,KAAK,EAAC;gBAMlB,WAAW,EAAA,CAAA;sBAA1C,YAAY;uBAAC,gBAAgB;gBAGQ,cAAc,EAAA,CAAA;sBAAnD,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBAIpC,WAAW,EAAA,CAAA;sBADV,KAAK;uBAAC,EAAC,SAAS,EAAE,CAAC,KAAU,MAAM,KAAK,IAAI,IAAI,GAAG,IAAI,GAAG,gBAAgB,CAAC,KAAK,CAAC,CAAC,EAAC;gBAUhF,UAAU,EAAA,CAAA;sBADb,KAAK;uBAAC,OAAO;gBAiCV,SAAS,EAAA,CAAA;sBADZ;gBASkB,MAAM,EAAA,CAAA;sBAAxB;gBAOkB,KAAK,EAAA,CAAA;sBAAvB;;;AEpOH;MACa,wBAAwB,GAAG,IAAI,cAAc,CACxD,0BAA0B,EAC1B;AACE,IAAA,UAAU,EAAE,MAAM;IAClB,OAAO,EAAE,MAAK;AACZ,QAAA,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC;QAC/B,OAAO,MAAM,OAAO,CAAC,gBAAgB,CAAC,UAAU,EAAE;KACnD;AACF,CAAA;AAGH;;;;AAIG;AACG,SAAU,gCAAgC,CAAC,OAAgB,EAAA;IAC/D,OAAO,MAAM,OAAO,CAAC,gBAAgB,CAAC,UAAU,EAAE;AACpD;AAEA;;;;AAIG;AACU,MAAA,yCAAyC,GAAG;AACvD,IAAA,OAAO,EAAE,wBAAwB;IACjC,IAAI,EAAE,CAAC,OAAO,CAAC;AACf,IAAA,UAAU,EAAE,gCAAgC;;AAG9C;AACA,MAAM,2BAA2B,GAAG,EAAC,OAAO,EAAE,IAAI,EAAC;AAEnD;;;;AAIG;AACI,MAAM,sBAAsB,GAAG;AAEtC;AACA,MAAM,kBAAkB,GAAG,IAAI,OAAO,EAAgC;AAEtE;MAca,cAAc,CAAA;AACjB,IAAA,QAAQ,GAAG,MAAM,CAAC,OAAO,CAAC;AAC1B,IAAA,QAAQ,GAAG,MAAM,CAA0B,UAAU,CAAC;AACtD,IAAA,iBAAiB,GAAG,MAAM,CAAC,gBAAgB,CAAC;AAC5C,IAAA,iBAAiB,GAAG,MAAM,CAAC,WAAW,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAC,CAAE;IACtE,IAAI,GAAG,MAAM,CAAC,cAAc,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;AAC/C,IAAA,aAAa,GAAG,MAAM,CAAC,YAAY,CAAC;AACpC,IAAA,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;AACxB,IAAA,eAAe,GAAG,MAAM,CAAC,wBAAwB,CAAC;AAClD,IAAA,kBAAkB,GAAG,MAAM,CAAC,iBAAiB,CAAC;AAC9C,IAAA,kBAAkB;AAElB,IAAA,OAAO;IACP,WAAW,GAAsB,IAAI;IACrC,SAAS,GAAY,KAAK;AAC1B,IAAA,2BAA2B,GAAG,YAAY,CAAC,KAAK;AAChD,IAAA,kBAAkB,GAAG,YAAY,CAAC,KAAK;AACvC,IAAA,sBAAsB,GAAG,YAAY,CAAC,KAAK;AAC3C,IAAA,eAAe;AAEvB;;;AAGG;AACK,IAAA,mBAAmB;AAE3B;;;AAGG;AACK,IAAA,mBAAmB;;;IAI3B,SAAS,GAAuD,SAAS;AAEzE;;;AAGG;AACH,IAAA,IACI,4BAA4B,GAAA;QAC9B,OAAO,IAAI,CAAC,IAAI;;IAElB,IAAI,4BAA4B,CAAC,CAAsB,EAAA;AACrD,QAAA,IAAI,CAAC,IAAI,GAAG,CAAC;;;AAIf,IAAA,IACI,IAAI,GAAA;QACN,OAAO,IAAI,CAAC,KAAK;;IAEnB,IAAI,IAAI,CAAC,IAAyB,EAAA;AAChC,QAAA,IAAI,IAAI,KAAK,IAAI,CAAC,KAAK,EAAE;YACvB;;AAGF,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI;AACjB,QAAA,IAAI,CAAC,sBAAsB,CAAC,WAAW,EAAE;QAEzC,IAAI,IAAI,EAAE;AACR,YAAA,IAAI,IAAI,KAAK,IAAI,CAAC,mBAAmB,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAAE;AACxF,gBAAA,0BAA0B,EAAE;;AAG9B,YAAA,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,MAAuB,KAAI;AAC7E,gBAAA,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC;;AAGzB,gBAAA,IAAI,CAAC,MAAM,KAAK,OAAO,IAAI,MAAM,KAAK,KAAK,KAAK,IAAI,CAAC,mBAAmB,EAAE;oBACxE,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;;AAEhD,aAAC,CAAC;;QAGJ,IAAI,CAAC,iBAAiB,EAAE,mBAAmB,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;;AAE7D,IAAA,KAAK;;AAGgB,IAAA,QAAQ;AAErC;;;;AAIG;IACkC,YAAY,GAAY,IAAI;;AAG9C,IAAA,UAAU,GAAuB,IAAI,YAAY,EAAQ;AAE5E;;;;AAIG;;AAEgB,IAAA,UAAU,GAAuB,IAAI,CAAC,UAAU;;AAGhD,IAAA,UAAU,GAAuB,IAAI,YAAY,EAAQ;AAE5E;;;;AAIG;;AAEgB,IAAA,WAAW,GAAuB,IAAI,CAAC,UAAU;AAIpE,IAAA,WAAA,GAAA;AACE,QAAA,MAAM,UAAU,GAAG,MAAM,CAAe,cAAc,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;AACzE,QAAA,MAAM,QAAQ,GAAG,MAAM,CAAC,SAAS,CAAC;AAElC,QAAA,IAAI,CAAC,mBAAmB,GAAG,UAAU,YAAY,OAAO,GAAG,UAAU,GAAG,SAAS;AACjF,QAAA,IAAI,CAAC,kBAAkB,GAAG,qBAAqB,CAC7C,QAAQ,EACR,IAAI,CAAC,QAAQ,CAAC,aAAa,EAC3B,YAAY,EACZ,CAAC,KAAiB,KAAI;AACpB,YAAA,IAAI,CAAC,gCAAgC,CAAC,KAAK,CAAC,EAAE;AAC5C,gBAAA,IAAI,CAAC,SAAS,GAAG,OAAO;;SAE3B,EACD,2BAA2B,CAC5B;;IAGH,kBAAkB,GAAA;QAChB,IAAI,CAAC,YAAY,EAAE;;IAGrB,WAAW,GAAA;AACT,QAAA,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;AAC1C,YAAA,kBAAkB,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC;;QAGtC,IAAI,CAAC,kBAAkB,EAAE;AACzB,QAAA,IAAI,CAAC,eAAe,EAAE,WAAW,EAAE;AACnC,QAAA,IAAI,CAAC,sBAAsB,CAAC,WAAW,EAAE;AACzC,QAAA,IAAI,CAAC,2BAA2B,CAAC,WAAW,EAAE;AAC9C,QAAA,IAAI,CAAC,kBAAkB,CAAC,WAAW,EAAE;AAErC,QAAA,IAAI,IAAI,CAAC,WAAW,EAAE;AACpB,YAAA,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE;AAC1B,YAAA,IAAI,CAAC,WAAW,GAAG,IAAI;;;;AAK3B,IAAA,IAAI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,SAAS;;;AAIvB,IAAA,IAAI,GAAG,GAAA;AACL,QAAA,OAAO,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,KAAK,GAAG,KAAK,GAAG,KAAK;;;IAI/D,eAAe,GAAA;AACb,QAAA,OAAO,CAAC,EAAE,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,mBAAmB,IAAI,IAAI,CAAC,IAAI,CAAC;;;IAI5E,UAAU,GAAA;AACR,QAAA,OAAO,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,EAAE,GAAG,IAAI,CAAC,QAAQ,EAAE;;;IAI5D,QAAQ,GAAA;AACN,QAAA,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI;AAEtB,QAAA,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,EAAE;YAC3B;;AAGF,QAAA,IAAI,CAAC,eAAe,EAAE,WAAW,EAAE;QACnC,MAAM,eAAe,GAAG,kBAAkB,CAAC,GAAG,CAAC,IAAI,CAAC;AACpD,QAAA,kBAAkB,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC;;;AAIlC,QAAA,IAAI,eAAe,IAAI,eAAe,KAAK,IAAI,EAAE;YAC/C,eAAe,CAAC,SAAS,EAAE;;QAG7B,MAAM,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC;AAC5C,QAAA,MAAM,aAAa,GAAG,UAAU,CAAC,SAAS,EAAE;AAC5C,QAAA,MAAM,gBAAgB,GAAG,aAAa,CAAC,gBAAqD;AAE5F,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,gBAAgB,CAAC;AACzC,QAAA,aAAa,CAAC,WAAW;AACvB,YAAA,IAAI,CAAC,WAAW,IAAI,IAAI,GAAG,CAAC,IAAI,CAAC,eAAe,EAAE,GAAG,IAAI,CAAC,WAAW;;;AAIvE,QAAA,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE,EAAE;YAC7B,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YACxC,IAAI,CAAC,WAAW,EAAE,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;;AAGzC,QAAA,IAAI,CAAC,2BAA2B,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,SAAS,EAAE,CAAC;AAC/F,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,eAAe,EAAE,GAAG,IAAI,CAAC,mBAAmB,GAAG,SAAS;AAC/E,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG;QACzB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC;AAChD,QAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC;AAEzB,QAAA,IAAI,IAAI,YAAY,OAAO,EAAE;AAC3B,YAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;AACrB,YAAA,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC,MAAK;;;gBAG7E,gBAAgB,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC,mBAAmB,EAAE;AAChE,gBAAA,gBAAgB,CAAC,kBAAkB,CAAC,IAAI,CAAC;AAC3C,aAAC,CAAC;;;;IAKN,SAAS,GAAA;AACP,QAAA,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE;;AAGzB;;;AAGG;IACH,KAAK,CAAC,MAAoB,EAAE,OAAsB,EAAA;AAChD,QAAA,IAAI,IAAI,CAAC,aAAa,IAAI,MAAM,EAAE;AAChC,YAAA,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,EAAE,OAAO,CAAC;;aACtD;YACL,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,OAAO,CAAC;;;AAI9C;;AAEG;IACH,cAAc,GAAA;AACZ,QAAA,IAAI,CAAC,WAAW,EAAE,cAAc,EAAE;;;AAI5B,IAAA,YAAY,CAAC,MAAuB,EAAA;AAC1C,QAAA,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW;AACnC,QAAA,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK;QAEvB,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YACjC;;AAGF,QAAA,IAAI,CAAC,2BAA2B,CAAC,WAAW,EAAE;AAC9C,QAAA,IAAI,CAAC,eAAe,EAAE,WAAW,EAAE;;;QAInC,IAAI,IAAI,YAAY,OAAO,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE;AACnD,YAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,MAAK;gBACtE,UAAU,CAAC,MAAM,EAAE;AACnB,gBAAA,IAAI,CAAC,WAAW,EAAE,MAAM,EAAE;AAC5B,aAAC,CAAC;AACF,YAAA,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;;aACjB;YACL,UAAU,CAAC,MAAM,EAAE;AACnB,YAAA,IAAI,EAAE,WAAW,EAAE,MAAM,EAAE;;QAG7B,IAAI,IAAI,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE;AAChC,YAAA,kBAAkB,CAAC,MAAM,CAAC,IAAI,CAAC;;;;;;QAOjC,IAAI,IAAI,CAAC,YAAY,KAAK,MAAM,KAAK,SAAS,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC,EAAE;AAC7F,YAAA,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC;;AAG5B,QAAA,IAAI,CAAC,SAAS,GAAG,SAAS;AAC1B,QAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC;;;AAIpB,IAAA,cAAc,CAAC,MAAe,EAAA;AACpC,QAAA,IAAI,MAAM,KAAK,IAAI,CAAC,SAAS,EAAE;AAC7B,YAAA,IAAI,CAAC,SAAS,GAAG,MAAM;YACvB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE;AAEhE,YAAA,IAAI,IAAI,CAAC,eAAe,EAAE,EAAE;AAC1B,gBAAA,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,MAAM,CAAC;;AAGhD,YAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE;;;AAI1C;;;AAGG;AACK,IAAA,cAAc,CAAC,IAAkB,EAAA;AACvC,QAAA,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACrB,MAAM,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC;YAC3C,IAAI,CAAC,qBAAqB,CACxB,IAAI,EACJ,MAAM,CAAC,gBAAqD,CAC7D;YACD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC;YAC/C,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,CAAC,SAAS,CAAC,KAAK,IAAG;AACjD,gBAAA,IAAI,IAAI,CAAC,IAAI,YAAY,OAAO,EAAE;AAChC,oBAAA,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC;;AAEnC,aAAC,CAAC;;QAGJ,OAAO,IAAI,CAAC,WAAW;;AAGzB;;;AAGG;AACK,IAAA,iBAAiB,CAAC,IAAkB,EAAA;QAC1C,OAAO,IAAI,aAAa,CAAC;YACvB,gBAAgB,EAAE,IAAI,CAAC;AACpB,iBAAA,QAAQ;AACR,iBAAA,mBAAmB,CAAC,IAAI,CAAC,QAAQ;AACjC,iBAAA,kBAAkB;AAClB,iBAAA,iBAAiB;iBACjB,qBAAqB,CAAC,sCAAsC,CAAC;AAChE,YAAA,aAAa,EAAE,IAAI,CAAC,aAAa,IAAI,kCAAkC;YACvE,UAAU,EAAE,IAAI,CAAC,iBAAiB;AAClC,YAAA,cAAc,EAAE,IAAI,CAAC,eAAe,EAAE;AACtC,YAAA,SAAS,EAAE,IAAI,CAAC,IAAI,IAAI,KAAK;AAC9B,SAAA,CAAC;;AAGJ;;;;AAIG;IACK,qBAAqB,CAAC,IAAkB,EAAE,QAA2C,EAAA;AAC3F,QAAA,IAAI,IAAI,CAAC,kBAAkB,EAAE;AAC3B,YAAA,QAAQ,CAAC,eAAe,CAAC,SAAS,CAAC,MAAM,IAAG;AAC1C,gBAAA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAK;AACpB,oBAAA,MAAM,IAAI,GACR,MAAM,CAAC,cAAc,CAAC,QAAQ,KAAK,OAAO,GAAG,OAAO,GAAG,QAAQ;AACjE,oBAAA,MAAM,IAAI,GAAkB,MAAM,CAAC,cAAc,CAAC,QAAQ,KAAK,KAAK,GAAG,OAAO,GAAG,OAAO;AACxF,oBAAA,IAAI,CAAC,kBAAmB,CAAC,IAAI,EAAE,IAAI,CAAC;AACtC,iBAAC,CAAC;AACJ,aAAC,CAAC;;;AAIN;;;;AAIG;IACK,YAAY,CAAC,IAAkB,EAAE,gBAAmD,EAAA;QAC1F,IAAI,CAAC,OAAO,EAAE,eAAe,CAAC,GAC5B,IAAI,CAAC,SAAS,KAAK,QAAQ,GAAG,CAAC,KAAK,EAAE,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,KAAK,CAAC;QAEnE,IAAI,CAAC,QAAQ,EAAE,gBAAgB,CAAC,GAC9B,IAAI,CAAC,SAAS,KAAK,OAAO,GAAG,CAAC,QAAQ,EAAE,KAAK,CAAC,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC;QAEpE,IAAI,CAAC,OAAO,EAAE,eAAe,CAAC,GAAG,CAAC,QAAQ,EAAE,gBAAgB,CAAC;QAC7D,IAAI,CAAC,QAAQ,EAAE,gBAAgB,CAAC,GAAG,CAAC,OAAO,EAAE,eAAe,CAAC;QAC7D,IAAI,OAAO,GAAG,CAAC;AAEf,QAAA,IAAI,IAAI,CAAC,eAAe,EAAE,EAAE;;;AAG1B,YAAA,gBAAgB,GAAG,OAAO,GAAG,IAAI,CAAC,SAAS,KAAK,QAAQ,GAAG,OAAO,GAAG,KAAK;AAC1E,YAAA,eAAe,GAAG,QAAQ,GAAG,OAAO,KAAK,KAAK,GAAG,OAAO,GAAG,KAAK;AAEhE,YAAA,IAAI,IAAI,CAAC,mBAAmB,EAAE;AAC5B,gBAAA,IAAI,IAAI,CAAC,mBAAmB,IAAI,IAAI,EAAE;oBACpC,MAAM,SAAS,GAAG,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,KAAK;AACtD,oBAAA,IAAI,CAAC,mBAAmB,GAAG,SAAS,GAAG,SAAS,CAAC,eAAe,EAAE,CAAC,SAAS,GAAG,CAAC;;AAGlF,gBAAA,OAAO,GAAG,QAAQ,KAAK,QAAQ,GAAG,IAAI,CAAC,mBAAmB,GAAG,CAAC,IAAI,CAAC,mBAAmB;;;AAEnF,aAAA,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;AAC/B,YAAA,OAAO,GAAG,QAAQ,KAAK,KAAK,GAAG,QAAQ,GAAG,KAAK;AAC/C,YAAA,eAAe,GAAG,gBAAgB,KAAK,KAAK,GAAG,QAAQ,GAAG,KAAK;;QAGjE,gBAAgB,CAAC,aAAa,CAAC;YAC7B,EAAC,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAC;AAC/C,YAAA,EAAC,OAAO,EAAE,eAAe,EAAE,OAAO,EAAE,QAAQ,EAAE,gBAAgB,EAAE,QAAQ,EAAE,OAAO,EAAC;AAClF,YAAA;gBACE,OAAO;AACP,gBAAA,OAAO,EAAE,eAAe;gBACxB,QAAQ;AACR,gBAAA,QAAQ,EAAE,gBAAgB;gBAC1B,OAAO,EAAE,CAAC,OAAO;AAClB,aAAA;AACD,YAAA;AACE,gBAAA,OAAO,EAAE,eAAe;AACxB,gBAAA,OAAO,EAAE,eAAe;AACxB,gBAAA,QAAQ,EAAE,gBAAgB;AAC1B,gBAAA,QAAQ,EAAE,gBAAgB;gBAC1B,OAAO,EAAE,CAAC,OAAO;AAClB,aAAA;AACF,SAAA,CAAC;;;IAII,mBAAmB,GAAA;QACzB,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAY,CAAC,aAAa,EAAE;QAClD,MAAM,WAAW,GAAG,IAAI,CAAC,WAAY,CAAC,WAAW,EAAE;AACnD,QAAA,MAAM,WAAW,GAAG,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,GAAGA,EAAY,EAAE;AAC/F,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC;cACf,IAAI,CAAC;AACF,iBAAA,QAAQ;AACR,iBAAA,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,IAAI,CAAC,SAAS,IAAI,MAAM,KAAK,IAAI,CAAC,iBAAiB,CAAC;cAC7EA,EAAY,EAAE;QAElB,OAAO,KAAK,CAAC,QAAQ,EAAE,WAA0C,EAAE,KAAK,EAAE,WAAW,CAAC;;;AAIxF,IAAA,gBAAgB,CAAC,KAAiB,EAAA;AAChC,QAAA,IAAI,CAAC,+BAA+B,CAAC,KAAK,CAAC,EAAE;;;AAG3C,YAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,MAAM,KAAK,CAAC,GAAG,OAAO,GAAG,SAAS;;;;AAKzD,YAAA,IAAI,IAAI,CAAC,eAAe,EAAE,EAAE;gBAC1B,KAAK,CAAC,cAAc,EAAE;;;;;AAM5B,IAAA,cAAc,CAAC,KAAoB,EAAA;AACjC,QAAA,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO;;QAG7B,IAAI,OAAO,KAAK,KAAK,IAAI,OAAO,KAAK,KAAK,EAAE;AAC1C,YAAA,IAAI,CAAC,SAAS,GAAG,UAAU;;QAG7B,IACE,IAAI,CAAC,eAAe,EAAE;aACrB,CAAC,OAAO,KAAK,WAAW,IAAI,IAAI,CAAC,GAAG,KAAK,KAAK;AAC7C,iBAAC,OAAO,KAAK,UAAU,IAAI,IAAI,CAAC,GAAG,KAAK,KAAK,CAAC,CAAC,EACjD;AACA,YAAA,IAAI,CAAC,SAAS,GAAG,UAAU;YAC3B,IAAI,CAAC,QAAQ,EAAE;;;;AAKnB,IAAA,YAAY,CAAC,KAAiB,EAAA;AAC5B,QAAA,IAAI,IAAI,CAAC,eAAe,EAAE,EAAE;;YAE1B,KAAK,CAAC,eAAe,EAAE;YACvB,IAAI,CAAC,QAAQ,EAAE;;aACV;YACL,IAAI,CAAC,UAAU,EAAE;;;;IAKb,YAAY,GAAA;;QAElB,IAAI,IAAI,CAAC,eAAe,EAAE,IAAI,IAAI,CAAC,mBAAmB,EAAE;AACtD,YAAA,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,mBAAmB,CAAC,QAAQ,EAAE,CAAC,SAAS,CAAC,MAAM,IAAG;gBAC/E,IAAI,MAAM,KAAK,IAAI,CAAC,iBAAiB,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;AACzD,oBAAA,IAAI,CAAC,SAAS,GAAG,OAAO;oBACxB,IAAI,CAAC,QAAQ,EAAE;;AAEnB,aAAC,CAAC;;;;AAKE,IAAA,UAAU,CAAC,IAAkB,EAAA;;;;AAInC,QAAA,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,KAAK,IAAI,CAAC,WAAW,EAAE;AAClE,YAAA,IAAI,CAAC,OAAO,GAAG,IAAI,cAAc,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,iBAAiB,CAAC;;QAG7E,OAAO,IAAI,CAAC,OAAO;;AAGrB;;;;AAIG;AACK,IAAA,SAAS,CAAC,IAAkB,EAAA;QAClC,OAAO,kBAAkB,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,IAAI;;uGA3fnC,cAAc,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAd,cAAc,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,6CAAA,EAAA,MAAA,EAAA,EAAA,4BAAA,EAAA,CAAA,sBAAA,EAAA,8BAAA,CAAA,EAAA,IAAA,EAAA,CAAA,mBAAA,EAAA,MAAA,CAAA,EAAA,QAAA,EAAA,CAAA,oBAAA,EAAA,UAAA,CAAA,EAAA,YAAA,EAAA,CAAA,4BAAA,EAAA,cAAA,CAAA,EAAA,EAAA,OAAA,EAAA,EAAA,UAAA,EAAA,YAAA,EAAA,UAAA,EAAA,YAAA,EAAA,UAAA,EAAA,YAAA,EAAA,WAAA,EAAA,aAAA,EAAA,EAAA,IAAA,EAAA,EAAA,SAAA,EAAA,EAAA,OAAA,EAAA,sBAAA,EAAA,WAAA,EAAA,0BAAA,EAAA,SAAA,EAAA,wBAAA,EAAA,EAAA,UAAA,EAAA,EAAA,oBAAA,EAAA,wBAAA,EAAA,oBAAA,EAAA,UAAA,EAAA,oBAAA,EAAA,gCAAA,EAAA,EAAA,cAAA,EAAA,sBAAA,EAAA,EAAA,QAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAAd,cAAc,EAAA,UAAA,EAAA,CAAA;kBAb1B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,CAA6C,2CAAA,CAAA;AACvD,oBAAA,IAAI,EAAE;AACJ,wBAAA,OAAO,EAAE,sBAAsB;AAC/B,wBAAA,sBAAsB,EAAE,sBAAsB;AAC9C,wBAAA,sBAAsB,EAAE,UAAU;AAClC,wBAAA,sBAAsB,EAAE,gCAAgC;AACxD,wBAAA,SAAS,EAAE,sBAAsB;AACjC,wBAAA,aAAa,EAAE,0BAA0B;AACzC,wBAAA,WAAW,EAAE,wBAAwB;AACtC,qBAAA;AACD,oBAAA,QAAQ,EAAE,gBAAgB;AAC3B,iBAAA;wDA0CK,4BAA4B,EAAA,CAAA;sBAD/B,KAAK;uBAAC,sBAAsB;gBAUzB,IAAI,EAAA,CAAA;sBADP,KAAK;uBAAC,mBAAmB;gBAgCG,QAAQ,EAAA,CAAA;sBAApC,KAAK;uBAAC,oBAAoB;gBAOU,YAAY,EAAA,CAAA;sBAAhD,KAAK;uBAAC,4BAA4B;gBAGhB,UAAU,EAAA,CAAA;sBAA5B;gBAQkB,UAAU,EAAA,CAAA;sBAA5B;gBAGkB,UAAU,EAAA,CAAA;sBAA5B;gBAQkB,WAAW,EAAA,CAAA;sBAA7B;;;MCtLU,aAAa,CAAA;uGAAb,aAAa,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;AAAb,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,aAAa,YAlBtB,eAAe;YACf,eAAe;YACf,aAAa;YACb,OAAO;YACP,WAAW;YACX,cAAc;AACd,YAAA,cAAc,aAGd,mBAAmB;YACnB,OAAO;YACP,eAAe;YACf,WAAW;YACX,cAAc;YACd,cAAc,CAAA,EAAA,CAAA;AAIL,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,aAAa,EAFb,SAAA,EAAA,CAAC,yCAAyC,CAAC,YAhBpD,eAAe;YACf,eAAe;AACf,YAAA,aAAa,EAOb,mBAAmB;YAEnB,eAAe,CAAA,EAAA,CAAA;;2FAON,aAAa,EAAA,UAAA,EAAA,CAAA;kBApBzB,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,OAAO,EAAE;wBACP,eAAe;wBACf,eAAe;wBACf,aAAa;wBACb,OAAO;wBACP,WAAW;wBACX,cAAc;wBACd,cAAc;AACf,qBAAA;AACD,oBAAA,OAAO,EAAE;wBACP,mBAAmB;wBACnB,OAAO;wBACP,eAAe;wBACf,WAAW;wBACX,cAAc;wBACd,cAAc;AACf,qBAAA;oBACD,SAAS,EAAE,CAAC,yCAAyC,CAAC;AACvD,iBAAA;;;AC5BD;;;;;;;AAOG;AACU,MAAA,iBAAiB,GAG1B;;;;;;;;;;;;;;;;;;;;;;AAuBF;;;;;;;AAOG;AACH,IAAA,aAAa,EAAE;AACb,QAAA,IAAI,EAAE,CAAC;AACP,QAAA,IAAI,EAAE,eAAe;AACrB,QAAA,WAAW,EAAE;AACX,YAAA;AACE,gBAAA,IAAI,EAAE,CAAC;AACP,gBAAA,IAAI,EAAE,MAAM;gBACZ,MAAM,EAAE,EAAC,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,EAAC,OAAO,EAAE,CAAC,EAAE,SAAS,EAAE,YAAY,EAAC,EAAE,MAAM,EAAE,IAAI,EAAC;AAC/E,aAAA;AACD,YAAA;AACE,gBAAA,IAAI,EAAE,CAAC;AACP,gBAAA,IAAI,EAAE,eAAe;AACrB,gBAAA,SAAS,EAAE;AACT,oBAAA,IAAI,EAAE,CAAC;oBACP,MAAM,EAAE,EAAC,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,EAAC,OAAO,EAAE,CAAC,EAAE,SAAS,EAAE,UAAU,EAAC,EAAE,MAAM,EAAE,IAAI,EAAC;AAC5E,oBAAA,OAAO,EAAE,kCAAkC;AAC5C,iBAAA;AACD,gBAAA,OAAO,EAAE,IAAI;AACd,aAAA;AACD,YAAA;AACE,gBAAA,IAAI,EAAE,CAAC;AACP,gBAAA,IAAI,EAAE,WAAW;AACjB,gBAAA,SAAS,EAAE;AACT,oBAAA,IAAI,EAAE,CAAC;AACP,oBAAA,MAAM,EAAE,EAAC,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,EAAC,OAAO,EAAE,CAAC,EAAC,EAAE,MAAM,EAAE,IAAI,EAAC;AACrD,oBAAA,OAAO,EAAE,mBAAmB;AAC7B,iBAAA;AACD,gBAAA,OAAO,EAAE,IAAI;AACd,aAAA;AACF,SAAA;AACD,QAAA,OAAO,EAAE,EAAE;AACZ,KAAA;;;;;;;;;;;AAaD;;;AAGG;AACH,IAAA,WAAW,EAAE;AACX,QAAA,IAAI,EAAE,CAAC;AACP,QAAA,IAAI,EAAE,aAAa;AACnB,QAAA,WAAW,EAAE;AACX,YAAA;AACE,gBAAA,IAAI,EAAE,CAAC;AACP,gBAAA,IAAI,EAAE,SAAS;AACf,gBAAA,MAAM,EAAE,EAAC,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,EAAC,OAAO,EAAE,CAAC,EAAC,EAAE,MAAM,EAAE,IAAI,EAAC;AACtD,aAAA;AACD,YAAA;AACE,gBAAA,IAAI,EAAE,CAAC;AACP,gBAAA,IAAI,EAAE,WAAW;AACjB,gBAAA,SAAS,EAAE;AACT,oBAAA,EAAC,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,EAAC,OAAO,EAAE,CAAC,EAAC,EAAE,MAAM,EAAE,IAAI,EAAC;oBAC7C,EAAC,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,8CAA8C,EAAC;AACjF,iBAAA;AACD,gBAAA,OAAO,EAAE,IAAI;AACd,aAAA;AACF,SAAA;AACD,QAAA,OAAO,EAAE,EAAE;AACZ,KAAA;;AAGH;;;;AAIG;AACU,MAAA,WAAW,GAAG,iBAAiB,CAAC;AAE7C;;;;AAIG;AACU,MAAA,aAAa,GAAG,iBAAiB,CAAC;;;;"}