{"version": 3, "file": "tree.mjs", "sources": ["../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/tree/node.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/tree/padding.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/tree/outlet.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/tree/tree.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/tree/toggle.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/tree/tree-module.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/tree/data-source/flat-data-source.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/tree/data-source/nested-data-source.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  CDK_TREE_NODE_OUTLET_NODE,\n  CdkNestedTreeNode,\n  CdkTreeNode,\n  CdkTreeNodeDef,\n} from '@angular/cdk/tree';\nimport {\n  AfterContentInit,\n  Directive,\n  Input,\n  OnDestroy,\n  OnInit,\n  booleanAttribute,\n  numberAttribute,\n  inject,\n  HostAttributeToken,\n} from '@angular/core';\nimport {NoopTreeKeyManager, TreeKeyManagerItem, TreeKeyManagerStrategy} from '@angular/cdk/a11y';\n\n/**\n * Determinte if argument TreeKeyManager is the NoopTreeKeyManager. This function is safe to use with SSR.\n */\nfunction isNoopTreeKeyManager<T extends TreeKeyManagerItem>(\n  keyManager: TreeKeyManagerStrategy<T>,\n): keyManager is NoopTreeKeyManager<T> {\n  return !!(keyManager as any)._isNoopTreeKeyManager;\n}\n\n/**\n * Wrapper for the CdkTree node with Material design styles.\n */\n@Directive({\n  selector: 'mat-tree-node',\n  exportAs: 'matTreeNode',\n  outputs: ['activation', 'expandedChange'],\n  providers: [{provide: CdkTreeNode, useExisting: MatTreeNode}],\n  host: {\n    'class': 'mat-tree-node',\n    '[attr.aria-expanded]': '_getAriaExpanded()',\n    '[attr.aria-level]': 'level + 1',\n    '[attr.aria-posinset]': '_getPositionInSet()',\n    '[attr.aria-setsize]': '_getSetSize()',\n    '(click)': '_focusItem()',\n    '[tabindex]': '_getTabindexAttribute()',\n  },\n})\nexport class MatTreeNode<T, K = T> extends CdkTreeNode<T, K> implements OnInit, OnDestroy {\n  /**\n   * The tabindex of the tree node.\n   *\n   * @deprecated By default MatTreeNode manages focus using TreeKeyManager instead of tabIndex.\n   *   Recommend to avoid setting tabIndex directly to prevent TreeKeyManager form getting into\n   *   an unexpected state. Tabindex to be removed in a future version.\n   * @breaking-change 21.0.0 Remove this attribute.\n   */\n  @Input({\n    transform: (value: unknown) => (value == null ? 0 : numberAttribute(value)),\n    alias: 'tabIndex',\n  })\n  get tabIndexInputBinding(): number {\n    return this._tabIndexInputBinding;\n  }\n  set tabIndexInputBinding(value: number) {\n    // If the specified tabIndex value is null or undefined, fall back to the default value.\n    this._tabIndexInputBinding = value;\n  }\n  private _tabIndexInputBinding: number;\n\n  /**\n   * The default tabindex of the tree node.\n   *\n   * @deprecated By default MatTreeNode manages focus using TreeKeyManager instead of tabIndex.\n   *   Recommend to avoid setting tabIndex directly to prevent TreeKeyManager form getting into\n   *   an unexpected state. Tabindex to be removed in a future version.\n   * @breaking-change 21.0.0 Remove this attribute.\n   */\n  defaultTabIndex = 0;\n\n  protected _getTabindexAttribute() {\n    if (isNoopTreeKeyManager(this._tree._keyManager)) {\n      return this.tabIndexInputBinding;\n    }\n    return this._tabindex;\n  }\n\n  /**\n   * Whether the component is disabled.\n   *\n   * @deprecated This is an alias for `isDisabled`.\n   * @breaking-change 21.0.0 Remove this input\n   */\n  @Input({transform: booleanAttribute})\n  get disabled(): boolean {\n    return this.isDisabled;\n  }\n  set disabled(value: boolean) {\n    this.isDisabled = value;\n  }\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    super();\n\n    const tabIndex = inject(new HostAttributeToken('tabindex'), {optional: true});\n    this.tabIndexInputBinding = Number(tabIndex) || this.defaultTabIndex;\n  }\n\n  // This is a workaround for https://github.com/angular/angular/issues/23091\n  // In aot mode, the lifecycle hooks from parent class are not called.\n  override ngOnInit() {\n    super.ngOnInit();\n  }\n\n  override ngOnDestroy() {\n    super.ngOnDestroy();\n  }\n}\n\n/**\n * Wrapper for the CdkTree node definition with Material design styles.\n * Captures the node's template and a when predicate that describes when this node should be used.\n */\n@Directive({\n  selector: '[matTreeNodeDef]',\n  inputs: [{name: 'when', alias: 'matTreeNodeDefWhen'}],\n  providers: [{provide: CdkTreeNodeDef, useExisting: MatTreeNodeDef}],\n})\nexport class MatTreeNodeDef<T> extends CdkTreeNodeDef<T> {\n  @Input('matTreeNode') data: T;\n}\n\n/**\n * Wrapper for the CdkTree nested node with Material design styles.\n */\n@Directive({\n  selector: 'mat-nested-tree-node',\n  exportAs: 'matNestedTreeNode',\n  outputs: ['activation', 'expandedChange'],\n  providers: [\n    {provide: CdkNestedTreeNode, useExisting: MatNestedTreeNode},\n    {provide: CdkTreeNode, useExisting: MatNestedTreeNode},\n    {provide: CDK_TREE_NODE_OUTLET_NODE, useExisting: MatNestedTreeNode},\n  ],\n  host: {\n    'class': 'mat-nested-tree-node',\n  },\n})\nexport class MatNestedTreeNode<T, K = T>\n  extends CdkNestedTreeNode<T, K>\n  implements AfterContentInit, OnDestroy, OnInit\n{\n  @Input('matNestedTreeNode') node: T;\n\n  /**\n   * Whether the node is disabled.\n   *\n   * @deprecated This is an alias for `isDisabled`.\n   * @breaking-change 21.0.0 Remove this input\n   */\n  @Input({transform: booleanAttribute})\n  get disabled(): boolean {\n    return this.isDisabled;\n  }\n  set disabled(value: boolean) {\n    this.isDisabled = value;\n  }\n\n  /** Tabindex of the node. */\n  @Input({\n    transform: (value: unknown) => (value == null ? 0 : numberAttribute(value)),\n  })\n  get tabIndex(): number {\n    return this.isDisabled ? -1 : this._tabIndex;\n  }\n  set tabIndex(value: number) {\n    // If the specified tabIndex value is null or undefined, fall back to the default value.\n    this._tabIndex = value;\n  }\n  private _tabIndex: number;\n\n  // This is a workaround for https://github.com/angular/angular/issues/19145\n  // In aot mode, the lifecycle hooks from parent class are not called.\n  // TODO(tinayuangao): Remove when the angular issue #19145 is fixed\n  override ngOnInit() {\n    super.ngOnInit();\n  }\n\n  override ngAfterContentInit() {\n    super.ngAfterContentInit();\n  }\n\n  override ngOnDestroy() {\n    super.ngOnDestroy();\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\nimport {CdkTreeNodePadding} from '@angular/cdk/tree';\nimport {Directive, Input, numberAttribute} from '@angular/core';\n\n/**\n * Wrapper for the CdkTree padding with Material design styles.\n */\n@Directive({\n  selector: '[matTreeNodePadding]',\n  providers: [{provide: CdkTreeNodePadding, useExisting: MatTreeNodePadding}],\n})\nexport class MatTreeNodePadding<T, K = T> extends CdkTreeNodePadding<T, K> {\n  /** The level of depth of the tree node. The padding will be `level * indent` pixels. */\n  @Input({alias: 'matTreeNodePadding', transform: numberAttribute})\n  override get level(): number {\n    return this._level;\n  }\n  override set level(value: number) {\n    this._setLevelInput(value);\n  }\n\n  /** The indent for each level. Default number 40px from material design menu sub-menu spec. */\n  @Input('matTreeNodePaddingIndent')\n  override get indent(): number | string {\n    return this._indent;\n  }\n  override set indent(indent: number | string) {\n    this._setIndentInput(indent);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\nimport {CDK_TREE_NODE_OUTLET_NODE, CdkTreeNodeOutlet} from '@angular/cdk/tree';\nimport {Directive, ViewContainerRef, inject} from '@angular/core';\n\n/**\n * Outlet for nested CdkNode. Put `[matTreeNodeOutlet]` on a tag to place children dataNodes\n * inside the outlet.\n */\n@Directive({\n  selector: '[matTreeNodeOutlet]',\n  providers: [\n    {\n      provide: CdkTreeNodeOutlet,\n      useExisting: MatTreeNodeOutlet,\n    },\n  ],\n})\nexport class MatTreeNodeOutlet implements CdkTreeNodeOutlet {\n  viewContainer = inject(ViewContainerRef);\n  _node = inject(CDK_TREE_NODE_OUTLET_NODE, {optional: true});\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {CdkTree} from '@angular/cdk/tree';\nimport {ChangeDetectionStrategy, Component, ViewChild, ViewEncapsulation} from '@angular/core';\nimport {MatTreeNodeOutlet} from './outlet';\n\n/**\n * Wrapper for the CdkTable with Material design styles.\n */\n@Component({\n  selector: 'mat-tree',\n  exportAs: 'matTree',\n  template: `<ng-container matTreeNodeOutlet></ng-container>`,\n  host: {\n    'class': 'mat-tree',\n  },\n  styleUrl: 'tree.css',\n  encapsulation: ViewEncapsulation.None,\n  // See note on CdkTree for explanation on why this uses the default change detection strategy.\n  // tslint:disable-next-line:validate-decorators\n  changeDetection: ChangeDetectionStrategy.Default,\n  providers: [{provide: CdkTree, useExisting: MatTree}],\n  imports: [MatTreeNodeOutlet],\n})\nexport class MatTree<T, K = T> extends CdkTree<T, K> {\n  // Outlets within the tree's template where the dataNodes will be inserted.\n  // We need an initializer here to avoid a TS error. The value will be set in `ngAfterViewInit`.\n  @ViewChild(MatTreeNodeOutlet, {static: true}) override _nodeOutlet: MatTreeNodeOutlet =\n    undefined!;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {CdkTreeNodeToggle} from '@angular/cdk/tree';\nimport {Directive} from '@angular/core';\n\n/**\n * Wrapper for the CdkTree's toggle with Material design styles.\n */\n@Directive({\n  selector: '[matTreeNodeToggle]',\n  providers: [{provide: CdkTreeNodeToggle, useExisting: MatTreeNodeToggle}],\n  inputs: [{name: 'recursive', alias: 'matTreeNodeToggleRecursive'}],\n})\nexport class MatTreeNodeToggle<T, K = T> extends CdkTreeNodeToggle<T, K> {}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {NgModule} from '@angular/core';\n\nimport {CdkTreeModule} from '@angular/cdk/tree';\nimport {MatCommonModule} from '../core';\nimport {MatNestedTreeNode, MatTreeNodeDef, MatTreeNode} from './node';\nimport {MatTree} from './tree';\nimport {MatTreeNodeToggle} from './toggle';\nimport {MatTreeNodeOutlet} from './outlet';\nimport {MatTreeNodePadding} from './padding';\n\nconst MAT_TREE_DIRECTIVES = [\n  MatNestedTreeNode,\n  MatTreeNodeDef,\n  MatTreeNodePadding,\n  MatTreeNodeToggle,\n  MatTree,\n  MatTreeNode,\n  MatTreeNodeOutlet,\n];\n\n@NgModule({\n  imports: [CdkTreeModule, MatCommonModule, ...MAT_TREE_DIRECTIVES],\n  exports: [MatCommonModule, MAT_TREE_DIRECTIVES],\n})\nexport class MatTreeModule {}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {CollectionViewer, DataSource} from '@angular/cdk/collections';\nimport {FlatTreeControl, TreeControl} from '@angular/cdk/tree';\nimport {BehaviorSubject, merge, Observable} from 'rxjs';\nimport {map, take} from 'rxjs/operators';\n\n/**\n * Tree flattener to convert a normal type of node to node with children & level information.\n * Transform nested nodes of type `T` to flattened nodes of type `F`.\n *\n * For example, the input data of type `T` is nested, and contains its children data:\n *   SomeNode: {\n *     key: 'Fruits',\n *     children: [\n *       NodeOne: {\n *         key: 'Apple',\n *       },\n *       NodeTwo: {\n *        key: 'Pear',\n *      }\n *    ]\n *  }\n *  After flattener flatten the tree, the structure will become\n *  SomeNode: {\n *    key: 'Fruits',\n *    expandable: true,\n *    level: 1\n *  },\n *  NodeOne: {\n *    key: 'Apple',\n *    expandable: false,\n *    level: 2\n *  },\n *  NodeTwo: {\n *   key: 'Pear',\n *   expandable: false,\n *   level: 2\n * }\n * and the output flattened type is `F` with additional information.\n *\n * @deprecated Use MatTree#childrenAccessor and MatTreeNode#isExpandable\n * instead. To be removed in a future version.\n * @breaking-change 21.0.0\n */\nexport class MatTreeFlattener<T, F, K = F> {\n  constructor(\n    public transformFunction: (node: T, level: number) => F,\n    public getLevel: (node: F) => number,\n    public isExpandable: (node: F) => boolean,\n    public getChildren: (node: T) => Observable<T[]> | T[] | undefined | null,\n  ) {}\n\n  _flattenNode(node: T, level: number, resultNodes: F[], parentMap: boolean[]): F[] {\n    const flatNode = this.transformFunction(node, level);\n    resultNodes.push(flatNode);\n\n    if (this.isExpandable(flatNode)) {\n      const childrenNodes = this.getChildren(node);\n      if (childrenNodes) {\n        if (Array.isArray(childrenNodes)) {\n          this._flattenChildren(childrenNodes, level, resultNodes, parentMap);\n        } else {\n          childrenNodes.pipe(take(1)).subscribe(children => {\n            this._flattenChildren(children, level, resultNodes, parentMap);\n          });\n        }\n      }\n    }\n    return resultNodes;\n  }\n\n  _flattenChildren(children: T[], level: number, resultNodes: F[], parentMap: boolean[]): void {\n    children.forEach((child, index) => {\n      let childParentMap: boolean[] = parentMap.slice();\n      childParentMap.push(index != children.length - 1);\n      this._flattenNode(child, level + 1, resultNodes, childParentMap);\n    });\n  }\n\n  /**\n   * Flatten a list of node type T to flattened version of node F.\n   * Please note that type T may be nested, and the length of `structuredData` may be different\n   * from that of returned list `F[]`.\n   */\n  flattenNodes(structuredData: T[]): F[] {\n    let resultNodes: F[] = [];\n    structuredData.forEach(node => this._flattenNode(node, 0, resultNodes, []));\n    return resultNodes;\n  }\n\n  /**\n   * Expand flattened node with current expansion status.\n   * The returned list may have different length.\n   */\n  expandFlattenedNodes(nodes: F[], treeControl: TreeControl<F, K>): F[] {\n    let results: F[] = [];\n    let currentExpand: boolean[] = [];\n    currentExpand[0] = true;\n\n    nodes.forEach(node => {\n      let expand = true;\n      for (let i = 0; i <= this.getLevel(node); i++) {\n        expand = expand && currentExpand[i];\n      }\n      if (expand) {\n        results.push(node);\n      }\n      if (this.isExpandable(node)) {\n        currentExpand[this.getLevel(node) + 1] = treeControl.isExpanded(node);\n      }\n    });\n    return results;\n  }\n}\n\n/**\n * Data source for flat tree.\n * The data source need to handle expansion/collapsion of the tree node and change the data feed\n * to `MatTree`.\n * The nested tree nodes of type `T` are flattened through `MatTreeFlattener`, and converted\n * to type `F` for `MatTree` to consume.\n *\n * @deprecated Use one of levelAccessor or childrenAccessor instead. To be removed in a future\n * version.\n * @breaking-change 21.0.0\n */\nexport class MatTreeFlatDataSource<T, F, K = F> extends DataSource<F> {\n  private readonly _flattenedData = new BehaviorSubject<F[]>([]);\n  private readonly _expandedData = new BehaviorSubject<F[]>([]);\n\n  get data() {\n    return this._data.value;\n  }\n  set data(value: T[]) {\n    this._data.next(value);\n    this._flattenedData.next(this._treeFlattener.flattenNodes(this.data));\n    this._treeControl.dataNodes = this._flattenedData.value;\n  }\n  private readonly _data = new BehaviorSubject<T[]>([]);\n\n  constructor(\n    private _treeControl: FlatTreeControl<F, K>,\n    private _treeFlattener: MatTreeFlattener<T, F, K>,\n    initialData?: T[],\n  ) {\n    super();\n\n    if (initialData) {\n      // Assign the data through the constructor to ensure that all of the logic is executed.\n      this.data = initialData;\n    }\n  }\n\n  connect(collectionViewer: CollectionViewer): Observable<F[]> {\n    return merge(\n      collectionViewer.viewChange,\n      this._treeControl.expansionModel.changed,\n      this._flattenedData,\n    ).pipe(\n      map(() => {\n        this._expandedData.next(\n          this._treeFlattener.expandFlattenedNodes(this._flattenedData.value, this._treeControl),\n        );\n        return this._expandedData.value;\n      }),\n    );\n  }\n\n  disconnect() {\n    // no op\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {CollectionViewer, DataSource} from '@angular/cdk/collections';\nimport {BehaviorSubject, merge, Observable} from 'rxjs';\nimport {map} from 'rxjs/operators';\n\n/**\n * Data source for nested tree.\n *\n * The data source for nested tree doesn't have to consider node flattener, or the way to expand\n * or collapse. The expansion/collapsion will be handled by TreeControl and each non-leaf node.\n */\nexport class MatTreeNestedDataSource<T> extends DataSource<T> {\n  /**\n   * Data for the nested tree\n   */\n  get data() {\n    return this._data.value;\n  }\n  set data(value: T[]) {\n    this._data.next(value);\n  }\n  private readonly _data = new BehaviorSubject<T[]>([]);\n\n  connect(collectionViewer: CollectionViewer): Observable<T[]> {\n    return merge(...([collectionViewer.viewChange, this._data] as Observable<unknown>[])).pipe(\n      map(() => this.data),\n    );\n  }\n\n  disconnect() {\n    // no op\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;AA2BA;;AAEG;AACH,SAAS,oBAAoB,CAC3B,UAAqC,EAAA;AAErC,IAAA,OAAO,CAAC,CAAE,UAAkB,CAAC,qBAAqB;AACpD;AAEA;;AAEG;AAgBG,MAAO,WAAsB,SAAQ,WAAiB,CAAA;AAC1D;;;;;;;AAOG;AACH,IAAA,IAII,oBAAoB,GAAA;QACtB,OAAO,IAAI,CAAC,qBAAqB;;IAEnC,IAAI,oBAAoB,CAAC,KAAa,EAAA;;AAEpC,QAAA,IAAI,CAAC,qBAAqB,GAAG,KAAK;;AAE5B,IAAA,qBAAqB;AAE7B;;;;;;;AAOG;IACH,eAAe,GAAG,CAAC;IAET,qBAAqB,GAAA;QAC7B,IAAI,oBAAoB,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,EAAE;YAChD,OAAO,IAAI,CAAC,oBAAoB;;QAElC,OAAO,IAAI,CAAC,SAAS;;AAGvB;;;;;AAKG;AACH,IAAA,IACI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,UAAU;;IAExB,IAAI,QAAQ,CAAC,KAAc,EAAA;AACzB,QAAA,IAAI,CAAC,UAAU,GAAG,KAAK;;AAKzB,IAAA,WAAA,GAAA;AACE,QAAA,KAAK,EAAE;AAEP,QAAA,MAAM,QAAQ,GAAG,MAAM,CAAC,IAAI,kBAAkB,CAAC,UAAU,CAAC,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;QAC7E,IAAI,CAAC,oBAAoB,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,eAAe;;;;IAK7D,QAAQ,GAAA;QACf,KAAK,CAAC,QAAQ,EAAE;;IAGT,WAAW,GAAA;QAClB,KAAK,CAAC,WAAW,EAAE;;uGArEV,WAAW,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAX,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,WAAW,EAUT,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,eAAA,EAAA,MAAA,EAAA,EAAA,oBAAA,EAAA,CAAA,UAAA,EAAA,sBAAA,EAAA,CAAC,KAAc,MAAM,KAAK,IAAI,IAAI,GAAG,CAAC,GAAG,eAAe,CAAC,KAAK,CAAC,CAAC,CAmC1D,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAAA,gBAAgB,kYAxDxB,CAAC,EAAC,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAC,CAAC,EAAA,QAAA,EAAA,CAAA,aAAA,CAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAWlD,WAAW,EAAA,UAAA,EAAA,CAAA;kBAfvB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,eAAe;AACzB,oBAAA,QAAQ,EAAE,aAAa;AACvB,oBAAA,OAAO,EAAE,CAAC,YAAY,EAAE,gBAAgB,CAAC;oBACzC,SAAS,EAAE,CAAC,EAAC,OAAO,EAAE,WAAW,EAAE,WAAW,EAAa,WAAA,EAAC,CAAC;AAC7D,oBAAA,IAAI,EAAE;AACJ,wBAAA,OAAO,EAAE,eAAe;AACxB,wBAAA,sBAAsB,EAAE,oBAAoB;AAC5C,wBAAA,mBAAmB,EAAE,WAAW;AAChC,wBAAA,sBAAsB,EAAE,qBAAqB;AAC7C,wBAAA,qBAAqB,EAAE,eAAe;AACtC,wBAAA,SAAS,EAAE,cAAc;AACzB,wBAAA,YAAY,EAAE,yBAAyB;AACxC,qBAAA;AACF,iBAAA;wDAcK,oBAAoB,EAAA,CAAA;sBAJvB,KAAK;AAAC,gBAAA,IAAA,EAAA,CAAA;wBACL,SAAS,EAAE,CAAC,KAAc,MAAM,KAAK,IAAI,IAAI,GAAG,CAAC,GAAG,eAAe,CAAC,KAAK,CAAC,CAAC;AAC3E,wBAAA,KAAK,EAAE,UAAU;AAClB,qBAAA;gBAkCG,QAAQ,EAAA,CAAA;sBADX,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;;AA4BtC;;;AAGG;AAMG,MAAO,cAAkB,SAAQ,cAAiB,CAAA;AAChC,IAAA,IAAI;uGADf,cAAc,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAd,cAAc,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,kBAAA,EAAA,MAAA,EAAA,EAAA,IAAA,EAAA,CAAA,oBAAA,EAAA,MAAA,CAAA,EAAA,IAAA,EAAA,CAAA,aAAA,EAAA,MAAA,CAAA,EAAA,EAAA,SAAA,EAFd,CAAC,EAAC,OAAO,EAAE,cAAc,EAAE,WAAW,EAAE,cAAc,EAAC,CAAC,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAExD,cAAc,EAAA,UAAA,EAAA,CAAA;kBAL1B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,kBAAkB;oBAC5B,MAAM,EAAE,CAAC,EAAC,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,oBAAoB,EAAC,CAAC;oBACrD,SAAS,EAAE,CAAC,EAAC,OAAO,EAAE,cAAc,EAAE,WAAW,EAAgB,cAAA,EAAC,CAAC;AACpE,iBAAA;8BAEuB,IAAI,EAAA,CAAA;sBAAzB,KAAK;uBAAC,aAAa;;AAGtB;;AAEG;AAcG,MAAO,iBACX,SAAQ,iBAAuB,CAAA;AAGH,IAAA,IAAI;AAEhC;;;;;AAKG;AACH,IAAA,IACI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,UAAU;;IAExB,IAAI,QAAQ,CAAC,KAAc,EAAA;AACzB,QAAA,IAAI,CAAC,UAAU,GAAG,KAAK;;;AAIzB,IAAA,IAGI,QAAQ,GAAA;AACV,QAAA,OAAO,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS;;IAE9C,IAAI,QAAQ,CAAC,KAAa,EAAA;;AAExB,QAAA,IAAI,CAAC,SAAS,GAAG,KAAK;;AAEhB,IAAA,SAAS;;;;IAKR,QAAQ,GAAA;QACf,KAAK,CAAC,QAAQ,EAAE;;IAGT,kBAAkB,GAAA;QACzB,KAAK,CAAC,kBAAkB,EAAE;;IAGnB,WAAW,GAAA;QAClB,KAAK,CAAC,WAAW,EAAE;;uGA7CV,iBAAiB,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAjB,iBAAiB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,sBAAA,EAAA,MAAA,EAAA,EAAA,IAAA,EAAA,CAAA,mBAAA,EAAA,MAAA,CAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAYT,gBAAgB,CAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAUtB,CAAC,KAAc,MAAM,KAAK,IAAI,IAAI,GAAG,CAAC,GAAG,eAAe,CAAC,KAAK,CAAC,CAAC,CA/BlE,EAAA,EAAA,OAAA,EAAA,EAAA,UAAA,EAAA,YAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,sBAAA,EAAA,EAAA,SAAA,EAAA;AACT,YAAA,EAAC,OAAO,EAAE,iBAAiB,EAAE,WAAW,EAAE,iBAAiB,EAAC;AAC5D,YAAA,EAAC,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,iBAAiB,EAAC;AACtD,YAAA,EAAC,OAAO,EAAE,yBAAyB,EAAE,WAAW,EAAE,iBAAiB,EAAC;AACrE,SAAA,EAAA,QAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAKU,iBAAiB,EAAA,UAAA,EAAA,CAAA;kBAb7B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,sBAAsB;AAChC,oBAAA,QAAQ,EAAE,mBAAmB;AAC7B,oBAAA,OAAO,EAAE,CAAC,YAAY,EAAE,gBAAgB,CAAC;AACzC,oBAAA,SAAS,EAAE;AACT,wBAAA,EAAC,OAAO,EAAE,iBAAiB,EAAE,WAAW,mBAAmB,EAAC;AAC5D,wBAAA,EAAC,OAAO,EAAE,WAAW,EAAE,WAAW,mBAAmB,EAAC;AACtD,wBAAA,EAAC,OAAO,EAAE,yBAAyB,EAAE,WAAW,mBAAmB,EAAC;AACrE,qBAAA;AACD,oBAAA,IAAI,EAAE;AACJ,wBAAA,OAAO,EAAE,sBAAsB;AAChC,qBAAA;AACF,iBAAA;8BAK6B,IAAI,EAAA,CAAA;sBAA/B,KAAK;uBAAC,mBAAmB;gBAStB,QAAQ,EAAA,CAAA;sBADX,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBAYhC,QAAQ,EAAA,CAAA;sBAHX,KAAK;AAAC,gBAAA,IAAA,EAAA,CAAA;wBACL,SAAS,EAAE,CAAC,KAAc,MAAM,KAAK,IAAI,IAAI,GAAG,CAAC,GAAG,eAAe,CAAC,KAAK,CAAC,CAAC;AAC5E,qBAAA;;;ACzKH;;AAEG;AAKG,MAAO,kBAA6B,SAAQ,kBAAwB,CAAA;;AAExE,IAAA,IACa,KAAK,GAAA;QAChB,OAAO,IAAI,CAAC,MAAM;;IAEpB,IAAa,KAAK,CAAC,KAAa,EAAA;AAC9B,QAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC;;;AAI5B,IAAA,IACa,MAAM,GAAA;QACjB,OAAO,IAAI,CAAC,OAAO;;IAErB,IAAa,MAAM,CAAC,MAAuB,EAAA;AACzC,QAAA,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC;;uGAhBnB,kBAAkB,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAlB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,kBAAkB,EAEmB,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,sBAAA,EAAA,MAAA,EAAA,EAAA,KAAA,EAAA,CAAA,oBAAA,EAAA,OAAA,EAAA,eAAe,CAJpD,EAAA,MAAA,EAAA,CAAA,0BAAA,EAAA,QAAA,CAAA,EAAA,EAAA,SAAA,EAAA,CAAC,EAAC,OAAO,EAAE,kBAAkB,EAAE,WAAW,EAAE,kBAAkB,EAAC,CAAC,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAEhE,kBAAkB,EAAA,UAAA,EAAA,CAAA;kBAJ9B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,sBAAsB;oBAChC,SAAS,EAAE,CAAC,EAAC,OAAO,EAAE,kBAAkB,EAAE,WAAW,EAAoB,kBAAA,EAAC,CAAC;AAC5E,iBAAA;8BAIc,KAAK,EAAA,CAAA;sBADjB,KAAK;AAAC,gBAAA,IAAA,EAAA,CAAA,EAAC,KAAK,EAAE,oBAAoB,EAAE,SAAS,EAAE,eAAe,EAAC;gBAUnD,MAAM,EAAA,CAAA;sBADlB,KAAK;uBAAC,0BAA0B;;;AClBnC;;;AAGG;MAUU,iBAAiB,CAAA;AAC5B,IAAA,aAAa,GAAG,MAAM,CAAC,gBAAgB,CAAC;IACxC,KAAK,GAAG,MAAM,CAAC,yBAAyB,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;uGAFhD,iBAAiB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAjB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,iBAAiB,EAPjB,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,qBAAA,EAAA,SAAA,EAAA;AACT,YAAA;AACE,gBAAA,OAAO,EAAE,iBAAiB;AAC1B,gBAAA,WAAW,EAAE,iBAAiB;AAC/B,aAAA;AACF,SAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAEU,iBAAiB,EAAA,UAAA,EAAA,CAAA;kBAT7B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,qBAAqB;AAC/B,oBAAA,SAAS,EAAE;AACT,wBAAA;AACE,4BAAA,OAAO,EAAE,iBAAiB;AAC1B,4BAAA,WAAW,EAAmB,iBAAA;AAC/B,yBAAA;AACF,qBAAA;AACF,iBAAA;;;ACVD;;AAEG;AAgBG,MAAO,OAAkB,SAAQ,OAAa,CAAA;;;IAGK,WAAW,GAChE,SAAU;uGAJD,OAAO,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAP,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,OAAO,6FAHP,CAAC,EAAC,OAAO,EAAE,OAAO,EAAE,WAAW,EAAE,OAAO,EAAC,CAAC,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,aAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAM1C,iBAAiB,EAflB,WAAA,EAAA,IAAA,EAAA,MAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,SAAA,CAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA,+CAAA,CAAiD,yqBAUjD,iBAAiB,EAAA,QAAA,EAAA,qBAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,OAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAEhB,OAAO,EAAA,UAAA,EAAA,CAAA;kBAfnB,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,UAAU,EACV,QAAA,EAAA,SAAS,EACT,QAAA,EAAA,CAAA,+CAAA,CAAiD,EACrD,IAAA,EAAA;AACJ,wBAAA,OAAO,EAAE,UAAU;qBACpB,EAEc,aAAA,EAAA,iBAAiB,CAAC,IAAI,EAAA,eAAA,EAGpB,uBAAuB,CAAC,OAAO,aACrC,CAAC,EAAC,OAAO,EAAE,OAAO,EAAE,WAAW,EAAA,OAAS,EAAC,CAAC,EAAA,OAAA,EAC5C,CAAC,iBAAiB,CAAC,EAAA,MAAA,EAAA,CAAA,imBAAA,CAAA,EAAA;8BAK2B,WAAW,EAAA,CAAA;sBAAjE,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,iBAAiB,EAAE,EAAC,MAAM,EAAE,IAAI,EAAC;;;ACtB9C;;AAEG;AAMG,MAAO,iBAA4B,SAAQ,iBAAuB,CAAA;uGAA3D,iBAAiB,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAjB,iBAAiB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,qBAAA,EAAA,MAAA,EAAA,EAAA,SAAA,EAAA,CAAA,4BAAA,EAAA,WAAA,CAAA,EAAA,EAAA,SAAA,EAHjB,CAAC,EAAC,OAAO,EAAE,iBAAiB,EAAE,WAAW,EAAE,iBAAiB,EAAC,CAAC,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAG9D,iBAAiB,EAAA,UAAA,EAAA,CAAA;kBAL7B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,qBAAqB;oBAC/B,SAAS,EAAE,CAAC,EAAC,OAAO,EAAE,iBAAiB,EAAE,WAAW,EAAmB,iBAAA,EAAC,CAAC;oBACzE,MAAM,EAAE,CAAC,EAAC,IAAI,EAAE,WAAW,EAAE,KAAK,EAAE,4BAA4B,EAAC,CAAC;AACnE,iBAAA;;;ACAD,MAAM,mBAAmB,GAAG;IAC1B,iBAAiB;IACjB,cAAc;IACd,kBAAkB;IAClB,iBAAiB;IACjB,OAAO;IACP,WAAW;IACX,iBAAiB;CAClB;MAMY,aAAa,CAAA;uGAAb,aAAa,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;AAAb,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,aAAa,EAHd,OAAA,EAAA,CAAA,aAAa,EAAE,eAAe,EAVxC,iBAAiB;YACjB,cAAc;YACd,kBAAkB;YAClB,iBAAiB;YACjB,OAAO;YACP,WAAW;YACX,iBAAiB,CAAA,EAAA,OAAA,EAAA,CAKP,eAAe,EAXzB,iBAAiB;YACjB,cAAc;YACd,kBAAkB;YAClB,iBAAiB;YACjB,OAAO;YACP,WAAW;YACX,iBAAiB,CAAA,EAAA,CAAA;AAON,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,aAAa,EAHd,OAAA,EAAA,CAAA,aAAa,EAAE,eAAe,EAC9B,eAAe,CAAA,EAAA,CAAA;;2FAEd,aAAa,EAAA,UAAA,EAAA,CAAA;kBAJzB,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACR,OAAO,EAAE,CAAC,aAAa,EAAE,eAAe,EAAE,GAAG,mBAAmB,CAAC;AACjE,oBAAA,OAAO,EAAE,CAAC,eAAe,EAAE,mBAAmB,CAAC;AAChD,iBAAA;;;AClBD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqCG;MACU,gBAAgB,CAAA;AAElB,IAAA,iBAAA;AACA,IAAA,QAAA;AACA,IAAA,YAAA;AACA,IAAA,WAAA;AAJT,IAAA,WAAA,CACS,iBAAgD,EAChD,QAA6B,EAC7B,YAAkC,EAClC,WAAkE,EAAA;QAHlE,IAAiB,CAAA,iBAAA,GAAjB,iBAAiB;QACjB,IAAQ,CAAA,QAAA,GAAR,QAAQ;QACR,IAAY,CAAA,YAAA,GAAZ,YAAY;QACZ,IAAW,CAAA,WAAA,GAAX,WAAW;;AAGpB,IAAA,YAAY,CAAC,IAAO,EAAE,KAAa,EAAE,WAAgB,EAAE,SAAoB,EAAA;QACzE,MAAM,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,KAAK,CAAC;AACpD,QAAA,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC;AAE1B,QAAA,IAAI,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,EAAE;YAC/B,MAAM,aAAa,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;YAC5C,IAAI,aAAa,EAAE;AACjB,gBAAA,IAAI,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE;oBAChC,IAAI,CAAC,gBAAgB,CAAC,aAAa,EAAE,KAAK,EAAE,WAAW,EAAE,SAAS,CAAC;;qBAC9D;AACL,oBAAA,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,QAAQ,IAAG;wBAC/C,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,KAAK,EAAE,WAAW,EAAE,SAAS,CAAC;AAChE,qBAAC,CAAC;;;;AAIR,QAAA,OAAO,WAAW;;AAGpB,IAAA,gBAAgB,CAAC,QAAa,EAAE,KAAa,EAAE,WAAgB,EAAE,SAAoB,EAAA;QACnF,QAAQ,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,KAAK,KAAI;AAChC,YAAA,IAAI,cAAc,GAAc,SAAS,CAAC,KAAK,EAAE;YACjD,cAAc,CAAC,IAAI,CAAC,KAAK,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC;AACjD,YAAA,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,KAAK,GAAG,CAAC,EAAE,WAAW,EAAE,cAAc,CAAC;AAClE,SAAC,CAAC;;AAGJ;;;;AAIG;AACH,IAAA,YAAY,CAAC,cAAmB,EAAA;QAC9B,IAAI,WAAW,GAAQ,EAAE;QACzB,cAAc,CAAC,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC,EAAE,WAAW,EAAE,EAAE,CAAC,CAAC;AAC3E,QAAA,OAAO,WAAW;;AAGpB;;;AAGG;IACH,oBAAoB,CAAC,KAAU,EAAE,WAA8B,EAAA;QAC7D,IAAI,OAAO,GAAQ,EAAE;QACrB,IAAI,aAAa,GAAc,EAAE;AACjC,QAAA,aAAa,CAAC,CAAC,CAAC,GAAG,IAAI;AAEvB,QAAA,KAAK,CAAC,OAAO,CAAC,IAAI,IAAG;YACnB,IAAI,MAAM,GAAG,IAAI;AACjB,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;AAC7C,gBAAA,MAAM,GAAG,MAAM,IAAI,aAAa,CAAC,CAAC,CAAC;;YAErC,IAAI,MAAM,EAAE;AACV,gBAAA,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC;;AAEpB,YAAA,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE;AAC3B,gBAAA,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC;;AAEzE,SAAC,CAAC;AACF,QAAA,OAAO,OAAO;;AAEjB;AAED;;;;;;;;;;AAUG;AACG,MAAO,qBAAmC,SAAQ,UAAa,CAAA;AAezD,IAAA,YAAA;AACA,IAAA,cAAA;AAfO,IAAA,cAAc,GAAG,IAAI,eAAe,CAAM,EAAE,CAAC;AAC7C,IAAA,aAAa,GAAG,IAAI,eAAe,CAAM,EAAE,CAAC;AAE7D,IAAA,IAAI,IAAI,GAAA;AACN,QAAA,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK;;IAEzB,IAAI,IAAI,CAAC,KAAU,EAAA;AACjB,QAAA,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC;AACtB,QAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACrE,IAAI,CAAC,YAAY,CAAC,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK;;AAExC,IAAA,KAAK,GAAG,IAAI,eAAe,CAAM,EAAE,CAAC;AAErD,IAAA,WAAA,CACU,YAAmC,EACnC,cAAyC,EACjD,WAAiB,EAAA;AAEjB,QAAA,KAAK,EAAE;QAJC,IAAY,CAAA,YAAA,GAAZ,YAAY;QACZ,IAAc,CAAA,cAAA,GAAd,cAAc;QAKtB,IAAI,WAAW,EAAE;;AAEf,YAAA,IAAI,CAAC,IAAI,GAAG,WAAW;;;AAI3B,IAAA,OAAO,CAAC,gBAAkC,EAAA;QACxC,OAAO,KAAK,CACV,gBAAgB,CAAC,UAAU,EAC3B,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,OAAO,EACxC,IAAI,CAAC,cAAc,CACpB,CAAC,IAAI,CACJ,GAAG,CAAC,MAAK;YACP,IAAI,CAAC,aAAa,CAAC,IAAI,CACrB,IAAI,CAAC,cAAc,CAAC,oBAAoB,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,IAAI,CAAC,YAAY,CAAC,CACvF;AACD,YAAA,OAAO,IAAI,CAAC,aAAa,CAAC,KAAK;SAChC,CAAC,CACH;;IAGH,UAAU,GAAA;;;AAGX;;ACtKD;;;;;AAKG;AACG,MAAO,uBAA2B,SAAQ,UAAa,CAAA;AAC3D;;AAEG;AACH,IAAA,IAAI,IAAI,GAAA;AACN,QAAA,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK;;IAEzB,IAAI,IAAI,CAAC,KAAU,EAAA;AACjB,QAAA,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC;;AAEP,IAAA,KAAK,GAAG,IAAI,eAAe,CAAM,EAAE,CAAC;AAErD,IAAA,OAAO,CAAC,gBAAkC,EAAA;QACxC,OAAO,KAAK,CAAC,GAAI,CAAC,gBAAgB,CAAC,UAAU,EAAE,IAAI,CAAC,KAAK,CAA2B,CAAC,CAAC,IAAI,CACxF,GAAG,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,CACrB;;IAGH,UAAU,GAAA;;;AAGX;;;;"}