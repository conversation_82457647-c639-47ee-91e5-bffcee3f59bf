{"version": 3, "file": "form-field-DqPi4knt.mjs", "sources": ["../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/form-field/directives/label.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/form-field/directives/error.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/form-field/directives/hint.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/form-field/directives/prefix.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/form-field/directives/suffix.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/form-field/directives/floating-label.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/form-field/directives/line-ripple.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/form-field/directives/notched-outline.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/form-field/directives/notched-outline.html", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/form-field/form-field-control.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/form-field/form-field-errors.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/form-field/form-field.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/form-field/form-field.html"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Directive} from '@angular/core';\n\n/** The floating label for a `mat-form-field`. */\n@Directive({\n  selector: 'mat-label',\n})\nexport class MatLabel {}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Directive, InjectionToken, Input, inject} from '@angular/core';\nimport {_IdGenerator} from '@angular/cdk/a11y';\n\n/**\n * Injection token that can be used to reference instances of `MatError`. It serves as\n * alternative token to the actual `MatError` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nexport const MAT_ERROR = new InjectionToken<MatError>('MatError');\n\n/** Single error message to be shown underneath the form-field. */\n@Directive({\n  selector: 'mat-error, [matError]',\n  host: {\n    'class': 'mat-mdc-form-field-error mat-mdc-form-field-bottom-align',\n    '[id]': 'id',\n  },\n  providers: [{provide: MAT_ERROR, useExisting: MatError}],\n})\nexport class MatError {\n  @Input() id: string = inject(_IdGenerator).getId('mat-mdc-error-');\n\n  constructor(...args: unknown[]);\n\n  constructor() {}\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Directive, inject, Input} from '@angular/core';\nimport {_IdGenerator} from '@angular/cdk/a11y';\n\n/** Hint text to be shown underneath the form field control. */\n@Directive({\n  selector: 'mat-hint',\n  host: {\n    'class': 'mat-mdc-form-field-hint mat-mdc-form-field-bottom-align',\n    '[class.mat-mdc-form-field-hint-end]': 'align === \"end\"',\n    '[id]': 'id',\n    // Remove align attribute to prevent it from interfering with layout.\n    '[attr.align]': 'null',\n  },\n})\nexport class MatHint {\n  /** Whether to align the hint label at the start or end of the line. */\n  @Input() align: 'start' | 'end' = 'start';\n\n  /** Unique ID for the hint. Used for the aria-describedby on the form field control. */\n  @Input() id: string = inject(_IdGenerator).getId('mat-mdc-hint-');\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Directive, InjectionToken, Input} from '@angular/core';\n\n/**\n * Injection token that can be used to reference instances of `MatPrefix`. It serves as\n * alternative token to the actual `MatPrefix` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nexport const MAT_PREFIX = new InjectionToken<MatPrefix>('MatPrefix');\n\n/** Prefix to be placed in front of the form field. */\n@Directive({\n  selector: '[matPrefix], [matIconPrefix], [matTextPrefix]',\n  providers: [{provide: MAT_PREFIX, useExisting: MatPrefix}],\n})\nexport class MatPrefix {\n  @Input('matTextPrefix')\n  set _isTextSelector(value: '') {\n    this._isText = true;\n  }\n\n  _isText = false;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Directive, InjectionToken, Input} from '@angular/core';\n\n/**\n * Injection token that can be used to reference instances of `MatSuffix`. It serves as\n * alternative token to the actual `MatSuffix` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nexport const MAT_SUFFIX = new InjectionToken<MatSuffix>('MatSuffix');\n\n/** Suffix to be placed at the end of the form field. */\n@Directive({\n  selector: '[matSuffix], [matIconSuffix], [matTextSuffix]',\n  providers: [{provide: MAT_SUFFIX, useExisting: MatSuffix}],\n})\nexport class MatSuffix {\n  @Input('matTextSuffix')\n  set _isTextSelector(value: '') {\n    this._isText = true;\n  }\n\n  _isText = false;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  Directive,\n  ElementRef,\n  inject,\n  Input,\n  NgZone,\n  OnDestroy,\n  InjectionToken,\n} from '@angular/core';\nimport {SharedResizeObserver} from '@angular/cdk/observers/private';\nimport {Subscription} from 'rxjs';\n\n/** An interface that the parent form-field should implement to receive resize events. */\nexport interface FloatingLabelParent {\n  _handleLabelResized(): void;\n}\n\n/** An injion token for the parent form-field. */\nexport const FLOATING_LABEL_PARENT = new InjectionToken<FloatingLabelParent>('FloatingLabelParent');\n\n/**\n * Internal directive that maintains a MDC floating label. This directive does not\n * use the `MDCFloatingLabelFoundation` class, as it is not worth the size cost of\n * including it just to measure the label width and toggle some classes.\n *\n * The use of a directive allows us to conditionally render a floating label in the\n * template without having to manually manage instantiation and destruction of the\n * floating label component based on.\n *\n * The component is responsible for setting up the floating label styles, measuring label\n * width for the outline notch, and providing inputs that can be used to toggle the\n * label's floating or required state.\n */\n@Directive({\n  selector: 'label[matFormFieldFloatingLabel]',\n  host: {\n    'class': 'mdc-floating-label mat-mdc-floating-label',\n    '[class.mdc-floating-label--float-above]': 'floating',\n  },\n})\nexport class MatFormFieldFloatingLabel implements OnDestroy {\n  private _elementRef = inject<ElementRef<HTMLElement>>(ElementRef);\n\n  /** Whether the label is floating. */\n  @Input()\n  get floating() {\n    return this._floating;\n  }\n  set floating(value: boolean) {\n    this._floating = value;\n    if (this.monitorResize) {\n      this._handleResize();\n    }\n  }\n  private _floating = false;\n\n  /** Whether to monitor for resize events on the floating label. */\n  @Input()\n  get monitorResize() {\n    return this._monitorResize;\n  }\n  set monitorResize(value: boolean) {\n    this._monitorResize = value;\n    if (this._monitorResize) {\n      this._subscribeToResize();\n    } else {\n      this._resizeSubscription.unsubscribe();\n    }\n  }\n  private _monitorResize = false;\n\n  /** The shared ResizeObserver. */\n  private _resizeObserver = inject(SharedResizeObserver);\n\n  /** The Angular zone. */\n  private _ngZone = inject(NgZone);\n\n  /** The parent form-field. */\n  private _parent = inject(FLOATING_LABEL_PARENT);\n\n  /** The current resize event subscription. */\n  private _resizeSubscription = new Subscription();\n\n  constructor(...args: unknown[]);\n  constructor() {}\n\n  ngOnDestroy() {\n    this._resizeSubscription.unsubscribe();\n  }\n\n  /** Gets the width of the label. Used for the outline notch. */\n  getWidth(): number {\n    return estimateScrollWidth(this._elementRef.nativeElement);\n  }\n\n  /** Gets the HTML element for the floating label. */\n  get element(): HTMLElement {\n    return this._elementRef.nativeElement;\n  }\n\n  /** Handles resize events from the ResizeObserver. */\n  private _handleResize() {\n    // In the case where the label grows in size, the following sequence of events occurs:\n    // 1. The label grows by 1px triggering the ResizeObserver\n    // 2. The notch is expanded to accommodate the entire label\n    // 3. The label expands to its full width, triggering the ResizeObserver again\n    //\n    // This is expected, but If we allow this to all happen within the same macro task it causes an\n    // error: `ResizeObserver loop limit exceeded`. Therefore we push the notch resize out until\n    // the next macro task.\n    setTimeout(() => this._parent._handleLabelResized());\n  }\n\n  /** Subscribes to resize events. */\n  private _subscribeToResize() {\n    this._resizeSubscription.unsubscribe();\n    this._ngZone.runOutsideAngular(() => {\n      this._resizeSubscription = this._resizeObserver\n        .observe(this._elementRef.nativeElement, {box: 'border-box'})\n        .subscribe(() => this._handleResize());\n    });\n  }\n}\n\n/**\n * Estimates the scroll width of an element.\n * via https://github.com/material-components/material-components-web/blob/c0a11ef0d000a098fd0c372be8f12d6a99302855/packages/mdc-dom/ponyfill.ts\n */\nfunction estimateScrollWidth(element: HTMLElement): number {\n  // Check the offsetParent. If the element inherits display: none from any\n  // parent, the offsetParent property will be null (see\n  // https://developer.mozilla.org/en-US/docs/Web/API/HTMLElement/offsetParent).\n  // This check ensures we only clone the node when necessary.\n  const htmlEl = element as HTMLElement;\n  if (htmlEl.offsetParent !== null) {\n    return htmlEl.scrollWidth;\n  }\n\n  const clone = htmlEl.cloneNode(true) as HTMLElement;\n  clone.style.setProperty('position', 'absolute');\n  clone.style.setProperty('transform', 'translate(-9999px, -9999px)');\n  document.documentElement.appendChild(clone);\n  const scrollWidth = clone.scrollWidth;\n  clone.remove();\n  return scrollWidth;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Directive, ElementRef, NgZone, OnDestroy, Renderer2, inject} from '@angular/core';\n\n/** Class added when the line ripple is active. */\nconst ACTIVATE_CLASS = 'mdc-line-ripple--active';\n\n/** Class added when the line ripple is being deactivated. */\nconst DEACTIVATING_CLASS = 'mdc-line-ripple--deactivating';\n\n/**\n * Internal directive that creates an instance of the MDC line-ripple component. Using a\n * directive allows us to conditionally render a line-ripple in the template without having\n * to manually create and destroy the `MDCLineRipple` component whenever the condition changes.\n *\n * The directive sets up the styles for the line-ripple and provides an API for activating\n * and deactivating the line-ripple.\n */\n@Directive({\n  selector: 'div[matFormFieldLineRipple]',\n  host: {\n    'class': 'mdc-line-ripple',\n  },\n})\nexport class MatFormFieldLineRipple implements OnDestroy {\n  private _elementRef = inject<ElementRef<HTMLElement>>(ElementRef);\n  private _cleanupTransitionEnd: () => void;\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    const ngZone = inject(NgZone);\n    const renderer = inject(Renderer2);\n\n    ngZone.runOutsideAngular(() => {\n      this._cleanupTransitionEnd = renderer.listen(\n        this._elementRef.nativeElement,\n        'transitionend',\n        this._handleTransitionEnd,\n      );\n    });\n  }\n\n  activate() {\n    const classList = this._elementRef.nativeElement.classList;\n    classList.remove(DEACTIVATING_CLASS);\n    classList.add(ACTIVATE_CLASS);\n  }\n\n  deactivate() {\n    this._elementRef.nativeElement.classList.add(DEACTIVATING_CLASS);\n  }\n\n  private _handleTransitionEnd = (event: TransitionEvent) => {\n    const classList = this._elementRef.nativeElement.classList;\n    const isDeactivating = classList.contains(DEACTIVATING_CLASS);\n\n    if (event.propertyName === 'opacity' && isDeactivating) {\n      classList.remove(ACTIVATE_CLASS, DEACTIVATING_CLASS);\n    }\n  };\n\n  ngOnDestroy() {\n    this._cleanupTransitionEnd();\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  AfterViewInit,\n  ChangeDetectionStrategy,\n  Component,\n  ElementRef,\n  Input,\n  NgZone,\n  ViewChild,\n  ViewEncapsulation,\n  inject,\n} from '@angular/core';\n\n/**\n * Internal component that creates an instance of the MDC notched-outline component.\n *\n * The component sets up the HTML structure and styles for the notched-outline. It provides\n * inputs to toggle the notch state and width.\n */\n@Component({\n  selector: 'div[matFormFieldNotchedOutline]',\n  templateUrl: './notched-outline.html',\n  host: {\n    'class': 'mdc-notched-outline',\n    // Besides updating the notch state through the MDC component, we toggle this class through\n    // a host binding in order to ensure that the notched-outline renders correctly on the server.\n    '[class.mdc-notched-outline--notched]': 'open',\n  },\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  encapsulation: ViewEncapsulation.None,\n})\nexport class MatFormFieldNotchedOutline implements AfterViewInit {\n  private _elementRef = inject<ElementRef<HTMLElement>>(ElementRef);\n  private _ngZone = inject(NgZone);\n\n  /** Whether the notch should be opened. */\n  @Input('matFormFieldNotchedOutlineOpen') open: boolean = false;\n\n  @ViewChild('notch') _notch: ElementRef;\n\n  constructor(...args: unknown[]);\n  constructor() {}\n\n  ngAfterViewInit(): void {\n    const label = this._elementRef.nativeElement.querySelector<HTMLElement>('.mdc-floating-label');\n    if (label) {\n      this._elementRef.nativeElement.classList.add('mdc-notched-outline--upgraded');\n\n      if (typeof requestAnimationFrame === 'function') {\n        label.style.transitionDuration = '0s';\n        this._ngZone.runOutsideAngular(() => {\n          requestAnimationFrame(() => (label.style.transitionDuration = ''));\n        });\n      }\n    } else {\n      this._elementRef.nativeElement.classList.add('mdc-notched-outline--no-label');\n    }\n  }\n\n  _setNotchWidth(labelWidth: number) {\n    if (!this.open || !labelWidth) {\n      this._notch.nativeElement.style.width = '';\n    } else {\n      const NOTCH_ELEMENT_PADDING = 8;\n      const NOTCH_ELEMENT_BORDER = 1;\n      this._notch.nativeElement.style.width = `calc(${labelWidth}px * var(--mat-mdc-form-field-floating-label-scale, 0.75) + ${\n        NOTCH_ELEMENT_PADDING + NOTCH_ELEMENT_BORDER\n      }px)`;\n    }\n  }\n}\n", "<div class=\"mat-mdc-notch-piece mdc-notched-outline__leading\"></div>\n<div class=\"mat-mdc-notch-piece mdc-notched-outline__notch\" #notch>\n  <ng-content></ng-content>\n</div>\n<div class=\"mat-mdc-notch-piece mdc-notched-outline__trailing\"></div>\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Observable} from 'rxjs';\nimport {AbstractControlDirective, NgControl} from '@angular/forms';\nimport {Directive} from '@angular/core';\n\n/** An interface which allows a control to work inside of a `MatFormField`. */\n@Directive()\nexport abstract class MatFormFieldControl<T> {\n  /** The value of the control. */\n  value: T | null;\n\n  /**\n   * Stream that emits whenever the state of the control changes such that the parent `MatFormField`\n   * needs to run change detection.\n   */\n  readonly stateChanges: Observable<void>;\n\n  /** The element ID for this control. */\n  readonly id: string;\n\n  /** The placeholder for this control. */\n  readonly placeholder: string;\n\n  /** Gets the AbstractControlDirective for this control. */\n  readonly ngControl: NgControl | AbstractControlDirective | null;\n\n  /** Whether the control is focused. */\n  readonly focused: boolean;\n\n  /** Whether the control is empty. */\n  readonly empty: boolean;\n\n  /** Whether the `MatForm<PERSON>ield` label should try to float. */\n  readonly shouldLabelFloat: boolean;\n\n  /** Whether the control is required. */\n  readonly required: boolean;\n\n  /** Whether the control is disabled. */\n  readonly disabled: boolean;\n\n  /** Whether the control is in an error state. */\n  readonly errorState: boolean;\n\n  /**\n   * An optional name for the control type that can be used to distinguish `mat-form-field` elements\n   * based on their control type. The form field will add a class,\n   * `mat-form-field-type-{{controlType}}` to its root element.\n   */\n  readonly controlType?: string;\n\n  /**\n   * Whether the input is currently in an autofilled state. If property is not present on the\n   * control it is assumed to be false.\n   */\n  readonly autofilled?: boolean;\n\n  /**\n   * Value of `aria-describedby` that should be merged with the described-by ids\n   * which are set by the form-field.\n   */\n  readonly userAriaDescribedBy?: string;\n\n  /**\n   * Whether to automatically assign the ID of the form field as the `for` attribute\n   * on the `<label>` inside the form field. Set this to true to prevent the form\n   * field from associating the label with non-native elements.\n   */\n  readonly disableAutomaticLabeling?: boolean;\n\n  /** Sets the list of element IDs that currently describe this control. */\n  abstract setDescribedByIds(ids: string[]): void;\n\n  /** Handles a click on the control's container. */\n  abstract onContainerClick(event: MouseEvent): void;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/** @docs-private */\nexport function getMatFormFieldPlaceholderConflictError(): Error {\n  return Error('Placeholder attribute and child element were both specified.');\n}\n\n/** @docs-private */\nexport function getMatFormFieldDuplicatedHintError(align: string): Error {\n  return Error(`A hint was already declared for 'align=\"${align}\"'.`);\n}\n\n/** @docs-private */\nexport function getMatFormFieldMissingControlError(): Error {\n  return Error('mat-form-field must contain a MatFormFieldControl.');\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\nimport {Directionality} from '@angular/cdk/bidi';\nimport {BooleanInput, coerceBooleanProperty} from '@angular/cdk/coercion';\nimport {Platform} from '@angular/cdk/platform';\nimport {NgTemplateOutlet} from '@angular/common';\nimport {\n  ANIMATION_MODULE_TYPE,\n  AfterContentChecked,\n  AfterContentInit,\n  AfterViewInit,\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  ContentChild,\n  ContentChildren,\n  ElementRef,\n  InjectionToken,\n  Injector,\n  Input,\n  NgZone,\n  OnDestroy,\n  QueryList,\n  ViewChild,\n  ViewEncapsulation,\n  afterRender,\n  computed,\n  contentChild,\n  inject,\n} from '@angular/core';\nimport {AbstractControlDirective, ValidatorFn} from '@angular/forms';\nimport {ThemePalette} from '../core';\nimport {_IdGenerator} from '@angular/cdk/a11y';\nimport {Subject, Subscription, merge} from 'rxjs';\nimport {map, pairwise, takeUntil, filter, startWith} from 'rxjs/operators';\nimport {MAT_ERROR, MatError} from './directives/error';\nimport {\n  FLOATING_LABEL_PARENT,\n  FloatingLabelParent,\n  MatFormFieldFloatingLabel,\n} from './directives/floating-label';\nimport {MatHint} from './directives/hint';\nimport {MatLabel} from './directives/label';\nimport {MatFormFieldLineRipple} from './directives/line-ripple';\nimport {MatFormFieldNotchedOutline} from './directives/notched-outline';\nimport {MAT_PREFIX, MatPrefix} from './directives/prefix';\nimport {MAT_SUFFIX, MatSuffix} from './directives/suffix';\nimport {MatFormFieldControl as _MatFormFieldControl} from './form-field-control';\nimport {\n  getMatFormFieldDuplicatedHintError,\n  getMatFormFieldMissingControlError,\n} from './form-field-errors';\n\n/** Type for the available floatLabel values. */\nexport type FloatLabelType = 'always' | 'auto';\n\n/** Possible appearance styles for the form field. */\nexport type MatFormFieldAppearance = 'fill' | 'outline';\n\n/** Behaviors for how the subscript height is set. */\nexport type SubscriptSizing = 'fixed' | 'dynamic';\n\n/**\n * Represents the default options for the form field that can be configured\n * using the `MAT_FORM_FIELD_DEFAULT_OPTIONS` injection token.\n */\nexport interface MatFormFieldDefaultOptions {\n  /** Default form field appearance style. */\n  appearance?: MatFormFieldAppearance;\n  /**\n   * Default theme color of the form field. This API is supported in M2 themes only, it has no\n   * effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/form-field/styling.\n   *\n   * For information on applying color variants in M3, see\n   * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n   */\n  color?: ThemePalette;\n  /** Whether the required marker should be hidden by default. */\n  hideRequiredMarker?: boolean;\n  /**\n   * Whether the label for form fields should by default float `always`,\n   * `never`, or `auto` (only when necessary).\n   */\n  floatLabel?: FloatLabelType;\n  /** Whether the form field should reserve space for one line by default. */\n  subscriptSizing?: SubscriptSizing;\n}\n\n/**\n * Injection token that can be used to inject an instances of `MatFormField`. It serves\n * as alternative token to the actual `MatFormField` class which would cause unnecessary\n * retention of the `MatFormField` class and its component metadata.\n */\nexport const MAT_FORM_FIELD = new InjectionToken<MatFormField>('MatFormField');\n\n/**\n * Injection token that can be used to configure the\n * default options for all form field within an app.\n */\nexport const MAT_FORM_FIELD_DEFAULT_OPTIONS = new InjectionToken<MatFormFieldDefaultOptions>(\n  'MAT_FORM_FIELD_DEFAULT_OPTIONS',\n);\n\n/** Default appearance used by the form field. */\nconst DEFAULT_APPEARANCE: MatFormFieldAppearance = 'fill';\n\n/**\n * Whether the label for form fields should by default float `always`,\n * `never`, or `auto`.\n */\nconst DEFAULT_FLOAT_LABEL: FloatLabelType = 'auto';\n\n/** Default way that the subscript element height is set. */\nconst DEFAULT_SUBSCRIPT_SIZING: SubscriptSizing = 'fixed';\n\n/**\n * Default transform for docked floating labels in a MDC text-field. This value has been\n * extracted from the MDC text-field styles because we programmatically modify the docked\n * label transform, but do not want to accidentally discard the default label transform.\n */\nconst FLOATING_LABEL_DEFAULT_DOCKED_TRANSFORM = `translateY(-50%)`;\n\n/**\n * Despite `MatFormFieldControl` being an abstract class, most of our usages enforce its shape\n * using `implements` instead of `extends`. This appears to be problematic when Closure compiler\n * is configured to use type information to rename properties, because it can't figure out which\n * class properties are coming from. This interface seems to work around the issue while preserving\n * our type safety (alternative being using `any` everywhere).\n * @docs-private\n */\ninterface MatFormFieldControl<T> extends _MatFormFieldControl<T> {}\n\n/** Container for form controls that applies Material Design styling and behavior. */\n@Component({\n  selector: 'mat-form-field',\n  exportAs: 'matFormField',\n  templateUrl: './form-field.html',\n  styleUrl: './form-field.css',\n  host: {\n    'class': 'mat-mdc-form-field',\n    '[class.mat-mdc-form-field-label-always-float]': '_shouldAlwaysFloat()',\n    '[class.mat-mdc-form-field-has-icon-prefix]': '_hasIconPrefix',\n    '[class.mat-mdc-form-field-has-icon-suffix]': '_hasIconSuffix',\n    // Note that these classes reuse the same names as the non-MDC version, because they can be\n    // considered a public API since custom form controls may use them to style themselves.\n    // See https://github.com/angular/components/pull/20502#discussion_r486124901.\n    '[class.mat-form-field-invalid]': '_control.errorState',\n    '[class.mat-form-field-disabled]': '_control.disabled',\n    '[class.mat-form-field-autofilled]': '_control.autofilled',\n    '[class.mat-form-field-appearance-fill]': 'appearance == \"fill\"',\n    '[class.mat-form-field-appearance-outline]': 'appearance == \"outline\"',\n    '[class.mat-form-field-hide-placeholder]': '_hasFloatingLabel() && !_shouldLabelFloat()',\n    '[class.mat-focused]': '_control.focused',\n    '[class.mat-primary]': 'color !== \"accent\" && color !== \"warn\"',\n    '[class.mat-accent]': 'color === \"accent\"',\n    '[class.mat-warn]': 'color === \"warn\"',\n    '[class.ng-untouched]': '_shouldForward(\"untouched\")',\n    '[class.ng-touched]': '_shouldForward(\"touched\")',\n    '[class.ng-pristine]': '_shouldForward(\"pristine\")',\n    '[class.ng-dirty]': '_shouldForward(\"dirty\")',\n    '[class.ng-valid]': '_shouldForward(\"valid\")',\n    '[class.ng-invalid]': '_shouldForward(\"invalid\")',\n    '[class.ng-pending]': '_shouldForward(\"pending\")',\n  },\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  providers: [\n    {provide: MAT_FORM_FIELD, useExisting: MatFormField},\n    {provide: FLOATING_LABEL_PARENT, useExisting: MatFormField},\n  ],\n  imports: [\n    MatFormFieldFloatingLabel,\n    MatFormFieldNotchedOutline,\n    NgTemplateOutlet,\n    MatFormFieldLineRipple,\n    MatHint,\n  ],\n})\nexport class MatFormField\n  implements FloatingLabelParent, AfterContentInit, AfterContentChecked, AfterViewInit, OnDestroy\n{\n  _elementRef = inject(ElementRef);\n  private _changeDetectorRef = inject(ChangeDetectorRef);\n  private _dir = inject(Directionality);\n  private _platform = inject(Platform);\n  private _idGenerator = inject(_IdGenerator);\n  private _ngZone = inject(NgZone);\n  private _injector = inject(Injector);\n  private _defaults = inject<MatFormFieldDefaultOptions>(MAT_FORM_FIELD_DEFAULT_OPTIONS, {\n    optional: true,\n  });\n\n  @ViewChild('textField') _textField: ElementRef<HTMLElement>;\n  @ViewChild('iconPrefixContainer') _iconPrefixContainer: ElementRef<HTMLElement>;\n  @ViewChild('textPrefixContainer') _textPrefixContainer: ElementRef<HTMLElement>;\n  @ViewChild('iconSuffixContainer') _iconSuffixContainer: ElementRef<HTMLElement>;\n  @ViewChild('textSuffixContainer') _textSuffixContainer: ElementRef<HTMLElement>;\n  @ViewChild(MatFormFieldFloatingLabel) _floatingLabel: MatFormFieldFloatingLabel | undefined;\n  @ViewChild(MatFormFieldNotchedOutline) _notchedOutline: MatFormFieldNotchedOutline | undefined;\n  @ViewChild(MatFormFieldLineRipple) _lineRipple: MatFormFieldLineRipple | undefined;\n\n  @ContentChild(_MatFormFieldControl) _formFieldControl: MatFormFieldControl<any>;\n  @ContentChildren(MAT_PREFIX, {descendants: true}) _prefixChildren: QueryList<MatPrefix>;\n  @ContentChildren(MAT_SUFFIX, {descendants: true}) _suffixChildren: QueryList<MatSuffix>;\n  @ContentChildren(MAT_ERROR, {descendants: true}) _errorChildren: QueryList<MatError>;\n  @ContentChildren(MatHint, {descendants: true}) _hintChildren: QueryList<MatHint>;\n\n  private readonly _labelChild = contentChild(MatLabel);\n\n  /** Whether the required marker should be hidden. */\n  @Input()\n  get hideRequiredMarker(): boolean {\n    return this._hideRequiredMarker;\n  }\n  set hideRequiredMarker(value: BooleanInput) {\n    this._hideRequiredMarker = coerceBooleanProperty(value);\n  }\n  private _hideRequiredMarker = false;\n\n  /**\n   * Theme color of the form field. This API is supported in M2 themes only, it\n   * has no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/form-field/styling.\n   *\n   * For information on applying color variants in M3, see\n   * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n   */\n  @Input() color: ThemePalette = 'primary';\n\n  /** Whether the label should always float or float as the user types. */\n  @Input()\n  get floatLabel(): FloatLabelType {\n    return this._floatLabel || this._defaults?.floatLabel || DEFAULT_FLOAT_LABEL;\n  }\n  set floatLabel(value: FloatLabelType) {\n    if (value !== this._floatLabel) {\n      this._floatLabel = value;\n      // For backwards compatibility. Custom form field controls or directives might set\n      // the \"floatLabel\" input and expect the form field view to be updated automatically.\n      // e.g. autocomplete trigger. Ideally we'd get rid of this and the consumers would just\n      // emit the \"stateChanges\" observable. TODO(devversion): consider removing.\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  private _floatLabel: FloatLabelType;\n\n  /** The form field appearance style. */\n  @Input()\n  get appearance(): MatFormFieldAppearance {\n    return this._appearance;\n  }\n  set appearance(value: MatFormFieldAppearance) {\n    const oldValue = this._appearance;\n    const newAppearance = value || this._defaults?.appearance || DEFAULT_APPEARANCE;\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (newAppearance !== 'fill' && newAppearance !== 'outline') {\n        throw new Error(\n          `MatFormField: Invalid appearance \"${newAppearance}\", valid values are \"fill\" or \"outline\".`,\n        );\n      }\n    }\n    this._appearance = newAppearance;\n    if (this._appearance === 'outline' && this._appearance !== oldValue) {\n      // If the appearance has been switched to `outline`, the label offset needs to be updated.\n      // The update can happen once the view has been re-checked, but not immediately because\n      // the view has not been updated and the notched-outline floating label is not present.\n      this._needsOutlineLabelOffsetUpdate = true;\n    }\n  }\n  private _appearance: MatFormFieldAppearance = DEFAULT_APPEARANCE;\n\n  /**\n   * Whether the form field should reserve space for one line of hint/error text (default)\n   * or to have the spacing grow from 0px as needed based on the size of the hint/error content.\n   * Note that when using dynamic sizing, layout shifts will occur when hint/error text changes.\n   */\n  @Input()\n  get subscriptSizing(): SubscriptSizing {\n    return this._subscriptSizing || this._defaults?.subscriptSizing || DEFAULT_SUBSCRIPT_SIZING;\n  }\n  set subscriptSizing(value: SubscriptSizing) {\n    this._subscriptSizing = value || this._defaults?.subscriptSizing || DEFAULT_SUBSCRIPT_SIZING;\n  }\n  private _subscriptSizing: SubscriptSizing | null = null;\n\n  /** Text for the form field hint. */\n  @Input()\n  get hintLabel(): string {\n    return this._hintLabel;\n  }\n  set hintLabel(value: string) {\n    this._hintLabel = value;\n    this._processHints();\n  }\n  private _hintLabel = '';\n\n  _hasIconPrefix = false;\n  _hasTextPrefix = false;\n  _hasIconSuffix = false;\n  _hasTextSuffix = false;\n\n  // Unique id for the internal form field label.\n  readonly _labelId = this._idGenerator.getId('mat-mdc-form-field-label-');\n\n  // Unique id for the hint label.\n  readonly _hintLabelId = this._idGenerator.getId('mat-mdc-hint-');\n\n  /** Gets the current form field control */\n  get _control(): MatFormFieldControl<any> {\n    return this._explicitFormFieldControl || this._formFieldControl;\n  }\n  set _control(value) {\n    this._explicitFormFieldControl = value;\n  }\n\n  private _destroyed = new Subject<void>();\n  private _isFocused: boolean | null = null;\n  private _explicitFormFieldControl: MatFormFieldControl<any>;\n  private _needsOutlineLabelOffsetUpdate = false;\n  private _previousControl: MatFormFieldControl<unknown> | null = null;\n  private _previousControlValidatorFn: ValidatorFn | null = null;\n  private _stateChanges: Subscription | undefined;\n  private _valueChanges: Subscription | undefined;\n  private _describedByChanges: Subscription | undefined;\n  protected readonly _animationsDisabled: boolean;\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    const defaults = this._defaults;\n\n    if (defaults) {\n      if (defaults.appearance) {\n        this.appearance = defaults.appearance;\n      }\n      this._hideRequiredMarker = Boolean(defaults?.hideRequiredMarker);\n      if (defaults.color) {\n        this.color = defaults.color;\n      }\n    }\n\n    this._animationsDisabled = inject(ANIMATION_MODULE_TYPE, {optional: true}) === 'NoopAnimations';\n  }\n\n  ngAfterViewInit() {\n    // Initial focus state sync. This happens rarely, but we want to account for\n    // it in case the form field control has \"focused\" set to true on init.\n    this._updateFocusState();\n\n    if (!this._animationsDisabled) {\n      this._ngZone.runOutsideAngular(() => {\n        // Enable animations after a certain amount of time so that they don't run on init.\n        setTimeout(() => {\n          this._elementRef.nativeElement.classList.add('mat-form-field-animations-enabled');\n        }, 300);\n      });\n    }\n\n    // Because the above changes a value used in the template after it was checked, we need\n    // to trigger CD or the change might not be reflected if there is no other CD scheduled.\n    this._changeDetectorRef.detectChanges();\n  }\n\n  ngAfterContentInit() {\n    this._assertFormFieldControl();\n    this._initializeSubscript();\n    this._initializePrefixAndSuffix();\n    this._initializeOutlineLabelOffsetSubscriptions();\n  }\n\n  ngAfterContentChecked() {\n    this._assertFormFieldControl();\n\n    // if form field was being used with an input in first place and then replaced by other\n    // component such as select.\n    if (this._control !== this._previousControl) {\n      this._initializeControl(this._previousControl);\n\n      // keep a reference for last validator we had.\n      if (this._control.ngControl && this._control.ngControl.control) {\n        this._previousControlValidatorFn = this._control.ngControl.control.validator;\n      }\n\n      this._previousControl = this._control;\n    }\n\n    // make sure the the control has been initialized.\n    if (this._control.ngControl && this._control.ngControl.control) {\n      // get the validators for current control.\n      const validatorFn = this._control.ngControl.control.validator;\n\n      // if our current validatorFn isn't equal to it might be we are CD behind, marking the\n      // component will allow us to catch up.\n      if (validatorFn !== this._previousControlValidatorFn) {\n        this._changeDetectorRef.markForCheck();\n      }\n    }\n  }\n\n  ngOnDestroy() {\n    this._stateChanges?.unsubscribe();\n    this._valueChanges?.unsubscribe();\n    this._describedByChanges?.unsubscribe();\n    this._destroyed.next();\n    this._destroyed.complete();\n  }\n\n  /**\n   * Gets the id of the label element. If no label is present, returns `null`.\n   */\n  getLabelId = computed(() => (this._hasFloatingLabel() ? this._labelId : null));\n\n  /**\n   * Gets an ElementRef for the element that a overlay attached to the form field\n   * should be positioned relative to.\n   */\n  getConnectedOverlayOrigin(): ElementRef {\n    return this._textField || this._elementRef;\n  }\n\n  /** Animates the placeholder up and locks it in position. */\n  _animateAndLockLabel(): void {\n    // This is for backwards compatibility only. Consumers of the form field might use\n    // this method. e.g. the autocomplete trigger. This method has been added to the non-MDC\n    // form field because setting \"floatLabel\" to \"always\" caused the label to float without\n    // animation. This is different in MDC where the label always animates, so this method\n    // is no longer necessary. There doesn't seem any benefit in adding logic to allow changing\n    // the floating label state without animations. The non-MDC implementation was inconsistent\n    // because it always animates if \"floatLabel\" is set away from \"always\".\n    // TODO(devversion): consider removing this method when releasing the MDC form field.\n    if (this._hasFloatingLabel()) {\n      this.floatLabel = 'always';\n    }\n  }\n\n  /** Initializes the registered form field control. */\n  private _initializeControl(previousControl: MatFormFieldControl<unknown> | null) {\n    const control = this._control;\n    const classPrefix = 'mat-mdc-form-field-type-';\n\n    if (previousControl) {\n      this._elementRef.nativeElement.classList.remove(classPrefix + previousControl.controlType);\n    }\n\n    if (control.controlType) {\n      this._elementRef.nativeElement.classList.add(classPrefix + control.controlType);\n    }\n\n    // Subscribe to changes in the child control state in order to update the form field UI.\n    this._stateChanges?.unsubscribe();\n    this._stateChanges = control.stateChanges.subscribe(() => {\n      this._updateFocusState();\n      this._changeDetectorRef.markForCheck();\n    });\n\n    // Updating the `aria-describedby` touches the DOM. Only do it if it actually needs to change.\n    this._describedByChanges?.unsubscribe();\n    this._describedByChanges = control.stateChanges\n      .pipe(\n        startWith([undefined, undefined] as const),\n        map(() => [control.errorState, control.userAriaDescribedBy] as const),\n        pairwise(),\n        filter(([[prevErrorState, prevDescribedBy], [currentErrorState, currentDescribedBy]]) => {\n          return prevErrorState !== currentErrorState || prevDescribedBy !== currentDescribedBy;\n        }),\n      )\n      .subscribe(() => this._syncDescribedByIds());\n\n    this._valueChanges?.unsubscribe();\n\n    // Run change detection if the value changes.\n    if (control.ngControl && control.ngControl.valueChanges) {\n      this._valueChanges = control.ngControl.valueChanges\n        .pipe(takeUntil(this._destroyed))\n        .subscribe(() => this._changeDetectorRef.markForCheck());\n    }\n  }\n\n  private _checkPrefixAndSuffixTypes() {\n    this._hasIconPrefix = !!this._prefixChildren.find(p => !p._isText);\n    this._hasTextPrefix = !!this._prefixChildren.find(p => p._isText);\n    this._hasIconSuffix = !!this._suffixChildren.find(s => !s._isText);\n    this._hasTextSuffix = !!this._suffixChildren.find(s => s._isText);\n  }\n\n  /** Initializes the prefix and suffix containers. */\n  private _initializePrefixAndSuffix() {\n    this._checkPrefixAndSuffixTypes();\n    // Mark the form field as dirty whenever the prefix or suffix children change. This\n    // is necessary because we conditionally display the prefix/suffix containers based\n    // on whether there is projected content.\n    merge(this._prefixChildren.changes, this._suffixChildren.changes).subscribe(() => {\n      this._checkPrefixAndSuffixTypes();\n      this._changeDetectorRef.markForCheck();\n    });\n  }\n\n  /**\n   * Initializes the subscript by validating hints and synchronizing \"aria-describedby\" ids\n   * with the custom form field control. Also subscribes to hint and error changes in order\n   * to be able to validate and synchronize ids on change.\n   */\n  private _initializeSubscript() {\n    // Re-validate when the number of hints changes.\n    this._hintChildren.changes.subscribe(() => {\n      this._processHints();\n      this._changeDetectorRef.markForCheck();\n    });\n\n    // Update the aria-described by when the number of errors changes.\n    this._errorChildren.changes.subscribe(() => {\n      this._syncDescribedByIds();\n      this._changeDetectorRef.markForCheck();\n    });\n\n    // Initial mat-hint validation and subscript describedByIds sync.\n    this._validateHints();\n    this._syncDescribedByIds();\n  }\n\n  /** Throws an error if the form field's control is missing. */\n  private _assertFormFieldControl() {\n    if (!this._control && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getMatFormFieldMissingControlError();\n    }\n  }\n\n  private _updateFocusState() {\n    // Usually the MDC foundation would call \"activateFocus\" and \"deactivateFocus\" whenever\n    // certain DOM events are emitted. This is not possible in our implementation of the\n    // form field because we support abstract form field controls which are not necessarily\n    // of type input, nor do we have a reference to a native form field control element. Instead\n    // we handle the focus by checking if the abstract form field control focused state changes.\n    if (this._control.focused && !this._isFocused) {\n      this._isFocused = true;\n      this._lineRipple?.activate();\n    } else if (!this._control.focused && (this._isFocused || this._isFocused === null)) {\n      this._isFocused = false;\n      this._lineRipple?.deactivate();\n    }\n\n    this._textField?.nativeElement.classList.toggle(\n      'mdc-text-field--focused',\n      this._control.focused,\n    );\n  }\n\n  /**\n   * The floating label in the docked state needs to account for prefixes. The horizontal offset\n   * is calculated whenever the appearance changes to `outline`, the prefixes change, or when the\n   * form field is added to the DOM. This method sets up all subscriptions which are needed to\n   * trigger the label offset update.\n   */\n  private _initializeOutlineLabelOffsetSubscriptions() {\n    // Whenever the prefix changes, schedule an update of the label offset.\n    // TODO(mmalerba): Use ResizeObserver to better support dynamically changing prefix content.\n    this._prefixChildren.changes.subscribe(() => (this._needsOutlineLabelOffsetUpdate = true));\n\n    // TODO(mmalerba): Split this into separate `afterRender` calls using the `EarlyRead` and\n    //  `Write` phases.\n    afterRender(\n      () => {\n        if (this._needsOutlineLabelOffsetUpdate) {\n          this._needsOutlineLabelOffsetUpdate = false;\n          this._updateOutlineLabelOffset();\n        }\n      },\n      {\n        injector: this._injector,\n      },\n    );\n\n    this._dir.change\n      .pipe(takeUntil(this._destroyed))\n      .subscribe(() => (this._needsOutlineLabelOffsetUpdate = true));\n  }\n\n  /** Whether the floating label should always float or not. */\n  _shouldAlwaysFloat() {\n    return this.floatLabel === 'always';\n  }\n\n  _hasOutline() {\n    return this.appearance === 'outline';\n  }\n\n  /**\n   * Whether the label should display in the infix. Labels in the outline appearance are\n   * displayed as part of the notched-outline and are horizontally offset to account for\n   * form field prefix content. This won't work in server side rendering since we cannot\n   * measure the width of the prefix container. To make the docked label appear as if the\n   * right offset has been calculated, we forcibly render the label inside the infix. Since\n   * the label is part of the infix, the label cannot overflow the prefix content.\n   */\n  _forceDisplayInfixLabel() {\n    return !this._platform.isBrowser && this._prefixChildren.length && !this._shouldLabelFloat();\n  }\n\n  _hasFloatingLabel = computed(() => !!this._labelChild());\n\n  _shouldLabelFloat(): boolean {\n    if (!this._hasFloatingLabel()) {\n      return false;\n    }\n    return this._control.shouldLabelFloat || this._shouldAlwaysFloat();\n  }\n\n  /**\n   * Determines whether a class from the AbstractControlDirective\n   * should be forwarded to the host element.\n   */\n  _shouldForward(prop: keyof AbstractControlDirective): boolean {\n    const control = this._control ? this._control.ngControl : null;\n    return control && control[prop];\n  }\n\n  /** Gets the type of subscript message to render (error or hint). */\n  _getSubscriptMessageType(): 'error' | 'hint' {\n    return this._errorChildren && this._errorChildren.length > 0 && this._control.errorState\n      ? 'error'\n      : 'hint';\n  }\n\n  /** Handle label resize events. */\n  _handleLabelResized() {\n    this._refreshOutlineNotchWidth();\n  }\n\n  /** Refreshes the width of the outline-notch, if present. */\n  _refreshOutlineNotchWidth() {\n    if (!this._hasOutline() || !this._floatingLabel || !this._shouldLabelFloat()) {\n      this._notchedOutline?._setNotchWidth(0);\n    } else {\n      this._notchedOutline?._setNotchWidth(this._floatingLabel.getWidth());\n    }\n  }\n\n  /** Does any extra processing that is required when handling the hints. */\n  private _processHints() {\n    this._validateHints();\n    this._syncDescribedByIds();\n  }\n\n  /**\n   * Ensure that there is a maximum of one of each \"mat-hint\" alignment specified. The hint\n   * label specified set through the input is being considered as \"start\" aligned.\n   *\n   * This method is a noop if Angular runs in production mode.\n   */\n  private _validateHints() {\n    if (this._hintChildren && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      let startHint: MatHint;\n      let endHint: MatHint;\n      this._hintChildren.forEach((hint: MatHint) => {\n        if (hint.align === 'start') {\n          if (startHint || this.hintLabel) {\n            throw getMatFormFieldDuplicatedHintError('start');\n          }\n          startHint = hint;\n        } else if (hint.align === 'end') {\n          if (endHint) {\n            throw getMatFormFieldDuplicatedHintError('end');\n          }\n          endHint = hint;\n        }\n      });\n    }\n  }\n\n  /**\n   * Sets the list of element IDs that describe the child control. This allows the control to update\n   * its `aria-describedby` attribute accordingly.\n   */\n  private _syncDescribedByIds() {\n    if (this._control) {\n      let ids: string[] = [];\n\n      // TODO(wagnermaciel): Remove the type check when we find the root cause of this bug.\n      if (\n        this._control.userAriaDescribedBy &&\n        typeof this._control.userAriaDescribedBy === 'string'\n      ) {\n        ids.push(...this._control.userAriaDescribedBy.split(' '));\n      }\n\n      if (this._getSubscriptMessageType() === 'hint') {\n        const startHint = this._hintChildren\n          ? this._hintChildren.find(hint => hint.align === 'start')\n          : null;\n        const endHint = this._hintChildren\n          ? this._hintChildren.find(hint => hint.align === 'end')\n          : null;\n\n        if (startHint) {\n          ids.push(startHint.id);\n        } else if (this._hintLabel) {\n          ids.push(this._hintLabelId);\n        }\n\n        if (endHint) {\n          ids.push(endHint.id);\n        }\n      } else if (this._errorChildren) {\n        ids.push(...this._errorChildren.map(error => error.id));\n      }\n\n      this._control.setDescribedByIds(ids);\n    }\n  }\n\n  /**\n   * Updates the horizontal offset of the label in the outline appearance. In the outline\n   * appearance, the notched-outline and label are not relative to the infix container because\n   * the outline intends to surround prefixes, suffixes and the infix. This means that the\n   * floating label by default overlaps prefixes in the docked state. To avoid this, we need to\n   * horizontally offset the label by the width of the prefix container. The MDC text-field does\n   * not need to do this because they use a fixed width for prefixes. Hence, they can simply\n   * incorporate the horizontal offset into their default text-field styles.\n   */\n  private _updateOutlineLabelOffset() {\n    if (!this._hasOutline() || !this._floatingLabel) {\n      return;\n    }\n    const floatingLabel = this._floatingLabel.element;\n    // If no prefix is displayed, reset the outline label offset from potential\n    // previous label offset updates.\n    if (!(this._iconPrefixContainer || this._textPrefixContainer)) {\n      floatingLabel.style.transform = '';\n      return;\n    }\n    // If the form field is not attached to the DOM yet (e.g. in a tab), we defer\n    // the label offset update until the zone stabilizes.\n    if (!this._isAttachedToDom()) {\n      this._needsOutlineLabelOffsetUpdate = true;\n      return;\n    }\n    const iconPrefixContainer = this._iconPrefixContainer?.nativeElement;\n    const textPrefixContainer = this._textPrefixContainer?.nativeElement;\n    const iconSuffixContainer = this._iconSuffixContainer?.nativeElement;\n    const textSuffixContainer = this._textSuffixContainer?.nativeElement;\n    const iconPrefixContainerWidth = iconPrefixContainer?.getBoundingClientRect().width ?? 0;\n    const textPrefixContainerWidth = textPrefixContainer?.getBoundingClientRect().width ?? 0;\n    const iconSuffixContainerWidth = iconSuffixContainer?.getBoundingClientRect().width ?? 0;\n    const textSuffixContainerWidth = textSuffixContainer?.getBoundingClientRect().width ?? 0;\n    // If the directionality is RTL, the x-axis transform needs to be inverted. This\n    // is because `transformX` does not change based on the page directionality.\n    const negate = this._dir.value === 'rtl' ? '-1' : '1';\n    const prefixWidth = `${iconPrefixContainerWidth + textPrefixContainerWidth}px`;\n    const labelOffset = `var(--mat-mdc-form-field-label-offset-x, 0px)`;\n    const labelHorizontalOffset = `calc(${negate} * (${prefixWidth} + ${labelOffset}))`;\n\n    // Update the translateX of the floating label to account for the prefix container,\n    // but allow the CSS to override this setting via a CSS variable when the label is\n    // floating.\n    floatingLabel.style.transform = `var(\n        --mat-mdc-form-field-label-transform,\n        ${FLOATING_LABEL_DEFAULT_DOCKED_TRANSFORM} translateX(${labelHorizontalOffset})\n    )`;\n\n    // Prevent the label from overlapping the suffix when in resting position.\n    const prefixAndSuffixWidth =\n      iconPrefixContainerWidth +\n      textPrefixContainerWidth +\n      iconSuffixContainerWidth +\n      textSuffixContainerWidth;\n    this._elementRef.nativeElement.style.setProperty(\n      '--mat-form-field-notch-max-width',\n      `calc(100% - ${prefixAndSuffixWidth}px)`,\n    );\n  }\n\n  /** Checks whether the form field is attached to the DOM. */\n  private _isAttachedToDom(): boolean {\n    const element: HTMLElement = this._elementRef.nativeElement;\n    if (element.getRootNode) {\n      const rootNode = element.getRootNode();\n      // If the element is inside the DOM the root node will be either the document\n      // or the closest shadow root, otherwise it'll be the element itself.\n      return rootNode && rootNode !== element;\n    }\n    // Otherwise fall back to checking if it's in the document. This doesn't account for\n    // shadow DOM, however browser that support shadow DOM should support `getRootNode` as well.\n    return document.documentElement!.contains(element);\n  }\n}\n", "<ng-template #labelTemplate>\n  <!--\n    MDC recommends that the text-field is a `<label>` element. This rather complicates the\n    setup because it would require every form-field control to explicitly set `aria-labelledby`.\n    This is because the `<label>` itself contains more than the actual label (e.g. prefix, suffix\n    or other projected content), and screen readers could potentially read out undesired content.\n    Excluding elements from being printed out requires them to be marked with `aria-hidden`, or\n    the form control is set to a scoped element for the label (using `aria-labelledby`). Both of\n    these options seem to complicate the setup because we know exactly what content is rendered\n    as part of the label, and we don't want to spend resources on walking through projected content\n    to set `aria-hidden`. Nor do we want to set `aria-labelledby` on every form control if we could\n    simply link the label to the control using the label `for` attribute.\n  -->\n  @if (_hasFloatingLabel()) {\n    <label\n      matFormFieldFloatingLabel\n      [floating]=\"_shouldLabelFloat()\"\n      [monitorResize]=\"_hasOutline()\"\n      [id]=\"_labelId\"\n      [attr.for]=\"_control.disableAutomaticLabeling ? null : _control.id\"\n    >\n      <ng-content select=\"mat-label\"></ng-content>\n      <!--\n        We set the required marker as a separate element, in order to make it easier to target if\n        apps want to override it and to be able to set `aria-hidden` so that screen readers don't\n        pick it up.\n       -->\n      @if (!hideRequiredMarker && _control.required) {\n        <span\n          aria-hidden=\"true\"\n          class=\"mat-mdc-form-field-required-marker mdc-floating-label--required\"\n        ></span>\n      }\n    </label>\n  }\n</ng-template>\n\n<div\n  class=\"mat-mdc-text-field-wrapper mdc-text-field\"\n  #textField\n  [class.mdc-text-field--filled]=\"!_hasOutline()\"\n  [class.mdc-text-field--outlined]=\"_hasOutline()\"\n  [class.mdc-text-field--no-label]=\"!_hasFloatingLabel()\"\n  [class.mdc-text-field--disabled]=\"_control.disabled\"\n  [class.mdc-text-field--invalid]=\"_control.errorState\"\n  (click)=\"_control.onContainerClick($event)\"\n>\n  @if (!_hasOutline() && !_control.disabled) {\n    <div class=\"mat-mdc-form-field-focus-overlay\"></div>\n  }\n  <div class=\"mat-mdc-form-field-flex\">\n    @if (_hasOutline()) {\n      <div matFormFieldNotchedOutline [matFormFieldNotchedOutlineOpen]=\"_shouldLabelFloat()\">\n        @if (!_forceDisplayInfixLabel()) {\n          <ng-template [ngTemplateOutlet]=\"labelTemplate\"></ng-template>\n        }\n      </div>\n    }\n\n    @if (_hasIconPrefix) {\n      <div class=\"mat-mdc-form-field-icon-prefix\" #iconPrefixContainer>\n        <ng-content select=\"[matPrefix], [matIconPrefix]\"></ng-content>\n      </div>\n    }\n\n    @if (_hasTextPrefix) {\n      <div class=\"mat-mdc-form-field-text-prefix\" #textPrefixContainer>\n        <ng-content select=\"[matTextPrefix]\"></ng-content>\n      </div>\n    }\n\n    <div class=\"mat-mdc-form-field-infix\">\n      @if (!_hasOutline() || _forceDisplayInfixLabel()) {\n        <ng-template [ngTemplateOutlet]=\"labelTemplate\"></ng-template>\n      }\n\n      <ng-content></ng-content>\n    </div>\n\n    @if (_hasTextSuffix) {\n      <div class=\"mat-mdc-form-field-text-suffix\" #textSuffixContainer>\n        <ng-content select=\"[matTextSuffix]\"></ng-content>\n      </div>\n    }\n\n    @if (_hasIconSuffix) {\n      <div class=\"mat-mdc-form-field-icon-suffix\" #iconSuffixContainer>\n        <ng-content select=\"[matSuffix], [matIconSuffix]\"></ng-content>\n      </div>\n    }\n  </div>\n\n  @if (!_hasOutline()) {\n    <div matFormFieldLineRipple></div>\n  }\n</div>\n\n<div\n    class=\"mat-mdc-form-field-subscript-wrapper mat-mdc-form-field-bottom-align\"\n    [class.mat-mdc-form-field-subscript-dynamic-size]=\"subscriptSizing === 'dynamic'\"\n>\n  @let subscriptMessageType = _getSubscriptMessageType();\n\n  <!-- \n    Use a single permanent wrapper for both hints and errors so aria-live works correctly,\n    as having it appear post render will not consistently work. We also do not want to add\n    additional divs as it causes styling regressions.\n    -->\n  <div aria-atomic=\"true\" aria-live=\"polite\" \n      [class.mat-mdc-form-field-error-wrapper]=\"subscriptMessageType === 'error'\"\n      [class.mat-mdc-form-field-hint-wrapper]=\"subscriptMessageType === 'hint'\"\n    >\n    @switch (subscriptMessageType) {\n      @case ('error') {\n        <ng-content select=\"mat-error, [matError]\"></ng-content>\n      }\n\n      @case ('hint') {\n        @if (hintLabel) {\n          <mat-hint [id]=\"_hintLabelId\">{{hintLabel}}</mat-hint>\n        }\n        <ng-content select=\"mat-hint:not([align='end'])\"></ng-content>\n        <div class=\"mat-mdc-form-field-hint-spacer\"></div>\n        <ng-content select=\"mat-hint[align='end']\"></ng-content>\n      }\n    }\n  </div>\n</div>\n"], "names": ["_MatFormFieldControl"], "mappings": ";;;;;;;;;;;AAUA;MAIa,QAAQ,CAAA;uGAAR,QAAQ,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAR,QAAQ,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,WAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAAR,QAAQ,EAAA,UAAA,EAAA,CAAA;kBAHpB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,WAAW;AACtB,iBAAA;;;ACFD;;;;AAIG;MACU,SAAS,GAAG,IAAI,cAAc,CAAW,UAAU;AAEhE;MASa,QAAQ,CAAA;IACV,EAAE,GAAW,MAAM,CAAC,YAAY,CAAC,CAAC,KAAK,CAAC,gBAAgB,CAAC;AAIlE,IAAA,WAAA,GAAA;uGALW,QAAQ,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAR,QAAQ,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,uBAAA,EAAA,MAAA,EAAA,EAAA,EAAA,EAAA,IAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,IAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,0DAAA,EAAA,EAAA,SAAA,EAFR,CAAC,EAAC,OAAO,EAAE,SAAS,EAAE,WAAW,EAAE,QAAQ,EAAC,CAAC,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAE7C,QAAQ,EAAA,UAAA,EAAA,CAAA;kBARpB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,uBAAuB;AACjC,oBAAA,IAAI,EAAE;AACJ,wBAAA,OAAO,EAAE,0DAA0D;AACnE,wBAAA,MAAM,EAAE,IAAI;AACb,qBAAA;oBACD,SAAS,EAAE,CAAC,EAAC,OAAO,EAAE,SAAS,EAAE,WAAW,EAAU,QAAA,EAAC,CAAC;AACzD,iBAAA;wDAEU,EAAE,EAAA,CAAA;sBAAV;;;ACjBH;MAWa,OAAO,CAAA;;IAET,KAAK,GAAoB,OAAO;;IAGhC,EAAE,GAAW,MAAM,CAAC,YAAY,CAAC,CAAC,KAAK,CAAC,eAAe,CAAC;uGALtD,OAAO,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAP,OAAO,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,UAAA,EAAA,MAAA,EAAA,EAAA,KAAA,EAAA,OAAA,EAAA,EAAA,EAAA,IAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,mCAAA,EAAA,mBAAA,EAAA,IAAA,EAAA,IAAA,EAAA,YAAA,EAAA,MAAA,EAAA,EAAA,cAAA,EAAA,yDAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAAP,OAAO,EAAA,UAAA,EAAA,CAAA;kBAVnB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,UAAU;AACpB,oBAAA,IAAI,EAAE;AACJ,wBAAA,OAAO,EAAE,yDAAyD;AAClE,wBAAA,qCAAqC,EAAE,iBAAiB;AACxD,wBAAA,MAAM,EAAE,IAAI;;AAEZ,wBAAA,cAAc,EAAE,MAAM;AACvB,qBAAA;AACF,iBAAA;8BAGU,KAAK,EAAA,CAAA;sBAAb;gBAGQ,EAAE,EAAA,CAAA;sBAAV;;;ACjBH;;;;AAIG;MACU,UAAU,GAAG,IAAI,cAAc,CAAY,WAAW;AAEnE;MAKa,SAAS,CAAA;IACpB,IACI,eAAe,CAAC,KAAS,EAAA;AAC3B,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI;;IAGrB,OAAO,GAAG,KAAK;uGANJ,SAAS,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAT,SAAS,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,+CAAA,EAAA,MAAA,EAAA,EAAA,eAAA,EAAA,CAAA,eAAA,EAAA,iBAAA,CAAA,EAAA,EAAA,SAAA,EAFT,CAAC,EAAC,OAAO,EAAE,UAAU,EAAE,WAAW,EAAE,SAAS,EAAC,CAAC,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAE/C,SAAS,EAAA,UAAA,EAAA,CAAA;kBAJrB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,+CAA+C;oBACzD,SAAS,EAAE,CAAC,EAAC,OAAO,EAAE,UAAU,EAAE,WAAW,EAAW,SAAA,EAAC,CAAC;AAC3D,iBAAA;8BAGK,eAAe,EAAA,CAAA;sBADlB,KAAK;uBAAC,eAAe;;;ACbxB;;;;AAIG;MACU,UAAU,GAAG,IAAI,cAAc,CAAY,WAAW;AAEnE;MAKa,SAAS,CAAA;IACpB,IACI,eAAe,CAAC,KAAS,EAAA;AAC3B,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI;;IAGrB,OAAO,GAAG,KAAK;uGANJ,SAAS,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAT,SAAS,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,+CAAA,EAAA,MAAA,EAAA,EAAA,eAAA,EAAA,CAAA,eAAA,EAAA,iBAAA,CAAA,EAAA,EAAA,SAAA,EAFT,CAAC,EAAC,OAAO,EAAE,UAAU,EAAE,WAAW,EAAE,SAAS,EAAC,CAAC,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAE/C,SAAS,EAAA,UAAA,EAAA,CAAA;kBAJrB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,+CAA+C;oBACzD,SAAS,EAAE,CAAC,EAAC,OAAO,EAAE,UAAU,EAAE,WAAW,EAAW,SAAA,EAAC,CAAC;AAC3D,iBAAA;8BAGK,eAAe,EAAA,CAAA;sBADlB,KAAK;uBAAC,eAAe;;;ACExB;AACO,MAAM,qBAAqB,GAAG,IAAI,cAAc,CAAsB,qBAAqB,CAAC;AAEnG;;;;;;;;;;;;AAYG;MAQU,yBAAyB,CAAA;AAC5B,IAAA,WAAW,GAAG,MAAM,CAA0B,UAAU,CAAC;;AAGjE,IAAA,IACI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,SAAS;;IAEvB,IAAI,QAAQ,CAAC,KAAc,EAAA;AACzB,QAAA,IAAI,CAAC,SAAS,GAAG,KAAK;AACtB,QAAA,IAAI,IAAI,CAAC,aAAa,EAAE;YACtB,IAAI,CAAC,aAAa,EAAE;;;IAGhB,SAAS,GAAG,KAAK;;AAGzB,IAAA,IACI,aAAa,GAAA;QACf,OAAO,IAAI,CAAC,cAAc;;IAE5B,IAAI,aAAa,CAAC,KAAc,EAAA;AAC9B,QAAA,IAAI,CAAC,cAAc,GAAG,KAAK;AAC3B,QAAA,IAAI,IAAI,CAAC,cAAc,EAAE;YACvB,IAAI,CAAC,kBAAkB,EAAE;;aACpB;AACL,YAAA,IAAI,CAAC,mBAAmB,CAAC,WAAW,EAAE;;;IAGlC,cAAc,GAAG,KAAK;;AAGtB,IAAA,eAAe,GAAG,MAAM,CAAC,oBAAoB,CAAC;;AAG9C,IAAA,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;;AAGxB,IAAA,OAAO,GAAG,MAAM,CAAC,qBAAqB,CAAC;;AAGvC,IAAA,mBAAmB,GAAG,IAAI,YAAY,EAAE;AAGhD,IAAA,WAAA,GAAA;IAEA,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,mBAAmB,CAAC,WAAW,EAAE;;;IAIxC,QAAQ,GAAA;QACN,OAAO,mBAAmB,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC;;;AAI5D,IAAA,IAAI,OAAO,GAAA;AACT,QAAA,OAAO,IAAI,CAAC,WAAW,CAAC,aAAa;;;IAI/B,aAAa,GAAA;;;;;;;;;QASnB,UAAU,CAAC,MAAM,IAAI,CAAC,OAAO,CAAC,mBAAmB,EAAE,CAAC;;;IAI9C,kBAAkB,GAAA;AACxB,QAAA,IAAI,CAAC,mBAAmB,CAAC,WAAW,EAAE;AACtC,QAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;AAClC,YAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;AAC7B,iBAAA,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,EAAC,GAAG,EAAE,YAAY,EAAC;iBAC3D,SAAS,CAAC,MAAM,IAAI,CAAC,aAAa,EAAE,CAAC;AAC1C,SAAC,CAAC;;uGAhFO,yBAAyB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAzB,yBAAyB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,kCAAA,EAAA,MAAA,EAAA,EAAA,QAAA,EAAA,UAAA,EAAA,aAAA,EAAA,eAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,uCAAA,EAAA,UAAA,EAAA,EAAA,cAAA,EAAA,2CAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAAzB,yBAAyB,EAAA,UAAA,EAAA,CAAA;kBAPrC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,kCAAkC;AAC5C,oBAAA,IAAI,EAAE;AACJ,wBAAA,OAAO,EAAE,2CAA2C;AACpD,wBAAA,yCAAyC,EAAE,UAAU;AACtD,qBAAA;AACF,iBAAA;wDAMK,QAAQ,EAAA,CAAA;sBADX;gBAcG,aAAa,EAAA,CAAA;sBADhB;;AAmEH;;;AAGG;AACH,SAAS,mBAAmB,CAAC,OAAoB,EAAA;;;;;IAK/C,MAAM,MAAM,GAAG,OAAsB;AACrC,IAAA,IAAI,MAAM,CAAC,YAAY,KAAK,IAAI,EAAE;QAChC,OAAO,MAAM,CAAC,WAAW;;IAG3B,MAAM,KAAK,GAAG,MAAM,CAAC,SAAS,CAAC,IAAI,CAAgB;IACnD,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC,UAAU,EAAE,UAAU,CAAC;IAC/C,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC,WAAW,EAAE,6BAA6B,CAAC;AACnE,IAAA,QAAQ,CAAC,eAAe,CAAC,WAAW,CAAC,KAAK,CAAC;AAC3C,IAAA,MAAM,WAAW,GAAG,KAAK,CAAC,WAAW;IACrC,KAAK,CAAC,MAAM,EAAE;AACd,IAAA,OAAO,WAAW;AACpB;;AC/IA;AACA,MAAM,cAAc,GAAG,yBAAyB;AAEhD;AACA,MAAM,kBAAkB,GAAG,+BAA+B;AAE1D;;;;;;;AAOG;MAOU,sBAAsB,CAAA;AACzB,IAAA,WAAW,GAAG,MAAM,CAA0B,UAAU,CAAC;AACzD,IAAA,qBAAqB;AAI7B,IAAA,WAAA,GAAA;AACE,QAAA,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;AAC7B,QAAA,MAAM,QAAQ,GAAG,MAAM,CAAC,SAAS,CAAC;AAElC,QAAA,MAAM,CAAC,iBAAiB,CAAC,MAAK;AAC5B,YAAA,IAAI,CAAC,qBAAqB,GAAG,QAAQ,CAAC,MAAM,CAC1C,IAAI,CAAC,WAAW,CAAC,aAAa,EAC9B,eAAe,EACf,IAAI,CAAC,oBAAoB,CAC1B;AACH,SAAC,CAAC;;IAGJ,QAAQ,GAAA;QACN,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,SAAS;AAC1D,QAAA,SAAS,CAAC,MAAM,CAAC,kBAAkB,CAAC;AACpC,QAAA,SAAS,CAAC,GAAG,CAAC,cAAc,CAAC;;IAG/B,UAAU,GAAA;QACR,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,SAAS,CAAC,GAAG,CAAC,kBAAkB,CAAC;;AAG1D,IAAA,oBAAoB,GAAG,CAAC,KAAsB,KAAI;QACxD,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,SAAS;QAC1D,MAAM,cAAc,GAAG,SAAS,CAAC,QAAQ,CAAC,kBAAkB,CAAC;QAE7D,IAAI,KAAK,CAAC,YAAY,KAAK,SAAS,IAAI,cAAc,EAAE;AACtD,YAAA,SAAS,CAAC,MAAM,CAAC,cAAc,EAAE,kBAAkB,CAAC;;AAExD,KAAC;IAED,WAAW,GAAA;QACT,IAAI,CAAC,qBAAqB,EAAE;;uGAvCnB,sBAAsB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAtB,sBAAsB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,6BAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,iBAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAAtB,sBAAsB,EAAA,UAAA,EAAA,CAAA;kBANlC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,6BAA6B;AACvC,oBAAA,IAAI,EAAE;AACJ,wBAAA,OAAO,EAAE,iBAAiB;AAC3B,qBAAA;AACF,iBAAA;;;ACTD;;;;;AAKG;MAaU,0BAA0B,CAAA;AAC7B,IAAA,WAAW,GAAG,MAAM,CAA0B,UAAU,CAAC;AACzD,IAAA,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;;IAGS,IAAI,GAAY,KAAK;AAE1C,IAAA,MAAM;AAG1B,IAAA,WAAA,GAAA;IAEA,eAAe,GAAA;AACb,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,aAAa,CAAc,qBAAqB,CAAC;QAC9F,IAAI,KAAK,EAAE;YACT,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,SAAS,CAAC,GAAG,CAAC,+BAA+B,CAAC;AAE7E,YAAA,IAAI,OAAO,qBAAqB,KAAK,UAAU,EAAE;AAC/C,gBAAA,KAAK,CAAC,KAAK,CAAC,kBAAkB,GAAG,IAAI;AACrC,gBAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;AAClC,oBAAA,qBAAqB,CAAC,OAAO,KAAK,CAAC,KAAK,CAAC,kBAAkB,GAAG,EAAE,CAAC,CAAC;AACpE,iBAAC,CAAC;;;aAEC;YACL,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,SAAS,CAAC,GAAG,CAAC,+BAA+B,CAAC;;;AAIjF,IAAA,cAAc,CAAC,UAAkB,EAAA;QAC/B,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,UAAU,EAAE;YAC7B,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG,EAAE;;aACrC;YACL,MAAM,qBAAqB,GAAG,CAAC;YAC/B,MAAM,oBAAoB,GAAG,CAAC;AAC9B,YAAA,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG,CAAA,KAAA,EAAQ,UAAU,CACxD,4DAAA,EAAA,qBAAqB,GAAG,oBAC1B,KAAK;;;uGApCE,0BAA0B,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAA1B,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,0BAA0B,yWCtCvC,+PAKA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FDiCa,0BAA0B,EAAA,UAAA,EAAA,CAAA;kBAZtC,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,iCAAiC,EAErC,IAAA,EAAA;AACJ,wBAAA,OAAO,EAAE,qBAAqB;;;AAG9B,wBAAA,sCAAsC,EAAE,MAAM;AAC/C,qBAAA,EAAA,eAAA,EACgB,uBAAuB,CAAC,MAAM,EAChC,aAAA,EAAA,iBAAiB,CAAC,IAAI,EAAA,QAAA,EAAA,+PAAA,EAAA;wDAOI,IAAI,EAAA,CAAA;sBAA5C,KAAK;uBAAC,gCAAgC;gBAEnB,MAAM,EAAA,CAAA;sBAAzB,SAAS;uBAAC,OAAO;;;AEjCpB;MAEsB,mBAAmB,CAAA;;AAEvC,IAAA,KAAK;AAEL;;;AAGG;AACM,IAAA,YAAY;;AAGZ,IAAA,EAAE;;AAGF,IAAA,WAAW;;AAGX,IAAA,SAAS;;AAGT,IAAA,OAAO;;AAGP,IAAA,KAAK;;AAGL,IAAA,gBAAgB;;AAGhB,IAAA,QAAQ;;AAGR,IAAA,QAAQ;;AAGR,IAAA,UAAU;AAEnB;;;;AAIG;AACM,IAAA,WAAW;AAEpB;;;AAGG;AACM,IAAA,UAAU;AAEnB;;;AAGG;AACM,IAAA,mBAAmB;AAE5B;;;;AAIG;AACM,IAAA,wBAAwB;uGA7Db,mBAAmB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAnB,mBAAmB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAAnB,mBAAmB,EAAA,UAAA,EAAA,CAAA;kBADxC;;;ACLD;SACgB,uCAAuC,GAAA;AACrD,IAAA,OAAO,KAAK,CAAC,8DAA8D,CAAC;AAC9E;AAEA;AACM,SAAU,kCAAkC,CAAC,KAAa,EAAA;AAC9D,IAAA,OAAO,KAAK,CAAC,CAAA,wCAAA,EAA2C,KAAK,CAAA,GAAA,CAAK,CAAC;AACrE;AAEA;SACgB,kCAAkC,GAAA;AAChD,IAAA,OAAO,KAAK,CAAC,oDAAoD,CAAC;AACpE;;ACwEA;;;;AAIG;MACU,cAAc,GAAG,IAAI,cAAc,CAAe,cAAc;AAE7E;;;AAGG;MACU,8BAA8B,GAAG,IAAI,cAAc,CAC9D,gCAAgC;AAGlC;AACA,MAAM,kBAAkB,GAA2B,MAAM;AAEzD;;;AAGG;AACH,MAAM,mBAAmB,GAAmB,MAAM;AAElD;AACA,MAAM,wBAAwB,GAAoB,OAAO;AAEzD;;;;AAIG;AACH,MAAM,uCAAuC,GAAG,CAAA,gBAAA,CAAkB;AAYlE;MA8Ca,YAAY,CAAA;AAGvB,IAAA,WAAW,GAAG,MAAM,CAAC,UAAU,CAAC;AACxB,IAAA,kBAAkB,GAAG,MAAM,CAAC,iBAAiB,CAAC;AAC9C,IAAA,IAAI,GAAG,MAAM,CAAC,cAAc,CAAC;AAC7B,IAAA,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC;AAC5B,IAAA,YAAY,GAAG,MAAM,CAAC,YAAY,CAAC;AACnC,IAAA,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;AACxB,IAAA,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC;AAC5B,IAAA,SAAS,GAAG,MAAM,CAA6B,8BAA8B,EAAE;AACrF,QAAA,QAAQ,EAAE,IAAI;AACf,KAAA,CAAC;AAEsB,IAAA,UAAU;AACA,IAAA,oBAAoB;AACpB,IAAA,oBAAoB;AACpB,IAAA,oBAAoB;AACpB,IAAA,oBAAoB;AAChB,IAAA,cAAc;AACb,IAAA,eAAe;AACnB,IAAA,WAAW;AAEV,IAAA,iBAAiB;AACH,IAAA,eAAe;AACf,IAAA,eAAe;AAChB,IAAA,cAAc;AAChB,IAAA,aAAa;AAE3C,IAAA,WAAW,GAAG,YAAY,CAAC,QAAQ,CAAC;;AAGrD,IAAA,IACI,kBAAkB,GAAA;QACpB,OAAO,IAAI,CAAC,mBAAmB;;IAEjC,IAAI,kBAAkB,CAAC,KAAmB,EAAA;AACxC,QAAA,IAAI,CAAC,mBAAmB,GAAG,qBAAqB,CAAC,KAAK,CAAC;;IAEjD,mBAAmB,GAAG,KAAK;AAEnC;;;;;;AAMG;IACM,KAAK,GAAiB,SAAS;;AAGxC,IAAA,IACI,UAAU,GAAA;QACZ,OAAO,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,SAAS,EAAE,UAAU,IAAI,mBAAmB;;IAE9E,IAAI,UAAU,CAAC,KAAqB,EAAA;AAClC,QAAA,IAAI,KAAK,KAAK,IAAI,CAAC,WAAW,EAAE;AAC9B,YAAA,IAAI,CAAC,WAAW,GAAG,KAAK;;;;;AAKxB,YAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE;;;AAGlC,IAAA,WAAW;;AAGnB,IAAA,IACI,UAAU,GAAA;QACZ,OAAO,IAAI,CAAC,WAAW;;IAEzB,IAAI,UAAU,CAAC,KAA6B,EAAA;AAC1C,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW;QACjC,MAAM,aAAa,GAAG,KAAK,IAAI,IAAI,CAAC,SAAS,EAAE,UAAU,IAAI,kBAAkB;AAC/E,QAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;YACjD,IAAI,aAAa,KAAK,MAAM,IAAI,aAAa,KAAK,SAAS,EAAE;AAC3D,gBAAA,MAAM,IAAI,KAAK,CACb,qCAAqC,aAAa,CAAA,wCAAA,CAA0C,CAC7F;;;AAGL,QAAA,IAAI,CAAC,WAAW,GAAG,aAAa;AAChC,QAAA,IAAI,IAAI,CAAC,WAAW,KAAK,SAAS,IAAI,IAAI,CAAC,WAAW,KAAK,QAAQ,EAAE;;;;AAInE,YAAA,IAAI,CAAC,8BAA8B,GAAG,IAAI;;;IAGtC,WAAW,GAA2B,kBAAkB;AAEhE;;;;AAIG;AACH,IAAA,IACI,eAAe,GAAA;QACjB,OAAO,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,SAAS,EAAE,eAAe,IAAI,wBAAwB;;IAE7F,IAAI,eAAe,CAAC,KAAsB,EAAA;AACxC,QAAA,IAAI,CAAC,gBAAgB,GAAG,KAAK,IAAI,IAAI,CAAC,SAAS,EAAE,eAAe,IAAI,wBAAwB;;IAEtF,gBAAgB,GAA2B,IAAI;;AAGvD,IAAA,IACI,SAAS,GAAA;QACX,OAAO,IAAI,CAAC,UAAU;;IAExB,IAAI,SAAS,CAAC,KAAa,EAAA;AACzB,QAAA,IAAI,CAAC,UAAU,GAAG,KAAK;QACvB,IAAI,CAAC,aAAa,EAAE;;IAEd,UAAU,GAAG,EAAE;IAEvB,cAAc,GAAG,KAAK;IACtB,cAAc,GAAG,KAAK;IACtB,cAAc,GAAG,KAAK;IACtB,cAAc,GAAG,KAAK;;IAGb,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,2BAA2B,CAAC;;IAG/D,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,eAAe,CAAC;;AAGhE,IAAA,IAAI,QAAQ,GAAA;AACV,QAAA,OAAO,IAAI,CAAC,yBAAyB,IAAI,IAAI,CAAC,iBAAiB;;IAEjE,IAAI,QAAQ,CAAC,KAAK,EAAA;AAChB,QAAA,IAAI,CAAC,yBAAyB,GAAG,KAAK;;AAGhC,IAAA,UAAU,GAAG,IAAI,OAAO,EAAQ;IAChC,UAAU,GAAmB,IAAI;AACjC,IAAA,yBAAyB;IACzB,8BAA8B,GAAG,KAAK;IACtC,gBAAgB,GAAwC,IAAI;IAC5D,2BAA2B,GAAuB,IAAI;AACtD,IAAA,aAAa;AACb,IAAA,aAAa;AACb,IAAA,mBAAmB;AACR,IAAA,mBAAmB;AAItC,IAAA,WAAA,GAAA;AACE,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS;QAE/B,IAAI,QAAQ,EAAE;AACZ,YAAA,IAAI,QAAQ,CAAC,UAAU,EAAE;AACvB,gBAAA,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC,UAAU;;YAEvC,IAAI,CAAC,mBAAmB,GAAG,OAAO,CAAC,QAAQ,EAAE,kBAAkB,CAAC;AAChE,YAAA,IAAI,QAAQ,CAAC,KAAK,EAAE;AAClB,gBAAA,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK;;;AAI/B,QAAA,IAAI,CAAC,mBAAmB,GAAG,MAAM,CAAC,qBAAqB,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC,KAAK,gBAAgB;;IAGjG,eAAe,GAAA;;;QAGb,IAAI,CAAC,iBAAiB,EAAE;AAExB,QAAA,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE;AAC7B,YAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;;gBAElC,UAAU,CAAC,MAAK;oBACd,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,SAAS,CAAC,GAAG,CAAC,mCAAmC,CAAC;iBAClF,EAAE,GAAG,CAAC;AACT,aAAC,CAAC;;;;AAKJ,QAAA,IAAI,CAAC,kBAAkB,CAAC,aAAa,EAAE;;IAGzC,kBAAkB,GAAA;QAChB,IAAI,CAAC,uBAAuB,EAAE;QAC9B,IAAI,CAAC,oBAAoB,EAAE;QAC3B,IAAI,CAAC,0BAA0B,EAAE;QACjC,IAAI,CAAC,0CAA0C,EAAE;;IAGnD,qBAAqB,GAAA;QACnB,IAAI,CAAC,uBAAuB,EAAE;;;QAI9B,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,gBAAgB,EAAE;AAC3C,YAAA,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,gBAAgB,CAAC;;AAG9C,YAAA,IAAI,IAAI,CAAC,QAAQ,CAAC,SAAS,IAAI,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,OAAO,EAAE;AAC9D,gBAAA,IAAI,CAAC,2BAA2B,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,OAAO,CAAC,SAAS;;AAG9E,YAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,QAAQ;;;AAIvC,QAAA,IAAI,IAAI,CAAC,QAAQ,CAAC,SAAS,IAAI,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,OAAO,EAAE;;YAE9D,MAAM,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,OAAO,CAAC,SAAS;;;AAI7D,YAAA,IAAI,WAAW,KAAK,IAAI,CAAC,2BAA2B,EAAE;AACpD,gBAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE;;;;IAK5C,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,aAAa,EAAE,WAAW,EAAE;AACjC,QAAA,IAAI,CAAC,aAAa,EAAE,WAAW,EAAE;AACjC,QAAA,IAAI,CAAC,mBAAmB,EAAE,WAAW,EAAE;AACvC,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE;AACtB,QAAA,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE;;AAG5B;;AAEG;IACH,UAAU,GAAG,QAAQ,CAAC,OAAO,IAAI,CAAC,iBAAiB,EAAE,GAAG,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,CAAC;AAE9E;;;AAGG;IACH,yBAAyB,GAAA;AACvB,QAAA,OAAO,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,WAAW;;;IAI5C,oBAAoB,GAAA;;;;;;;;;AASlB,QAAA,IAAI,IAAI,CAAC,iBAAiB,EAAE,EAAE;AAC5B,YAAA,IAAI,CAAC,UAAU,GAAG,QAAQ;;;;AAKtB,IAAA,kBAAkB,CAAC,eAAoD,EAAA;AAC7E,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ;QAC7B,MAAM,WAAW,GAAG,0BAA0B;QAE9C,IAAI,eAAe,EAAE;AACnB,YAAA,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,SAAS,CAAC,MAAM,CAAC,WAAW,GAAG,eAAe,CAAC,WAAW,CAAC;;AAG5F,QAAA,IAAI,OAAO,CAAC,WAAW,EAAE;AACvB,YAAA,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,SAAS,CAAC,GAAG,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;;;AAIjF,QAAA,IAAI,CAAC,aAAa,EAAE,WAAW,EAAE;QACjC,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,YAAY,CAAC,SAAS,CAAC,MAAK;YACvD,IAAI,CAAC,iBAAiB,EAAE;AACxB,YAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE;AACxC,SAAC,CAAC;;AAGF,QAAA,IAAI,CAAC,mBAAmB,EAAE,WAAW,EAAE;AACvC,QAAA,IAAI,CAAC,mBAAmB,GAAG,OAAO,CAAC;aAChC,IAAI,CACH,SAAS,CAAC,CAAC,SAAS,EAAE,SAAS,CAAU,CAAC,EAC1C,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,UAAU,EAAE,OAAO,CAAC,mBAAmB,CAAU,CAAC,EACrE,QAAQ,EAAE,EACV,MAAM,CAAC,CAAC,CAAC,CAAC,cAAc,EAAE,eAAe,CAAC,EAAE,CAAC,iBAAiB,EAAE,kBAAkB,CAAC,CAAC,KAAI;AACtF,YAAA,OAAO,cAAc,KAAK,iBAAiB,IAAI,eAAe,KAAK,kBAAkB;AACvF,SAAC,CAAC;aAEH,SAAS,CAAC,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;AAE9C,QAAA,IAAI,CAAC,aAAa,EAAE,WAAW,EAAE;;QAGjC,IAAI,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC,SAAS,CAAC,YAAY,EAAE;AACvD,YAAA,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,SAAS,CAAC;AACpC,iBAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC;iBAC/B,SAAS,CAAC,MAAM,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;;;IAItD,0BAA0B,GAAA;QAChC,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC;AAClE,QAAA,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;QACjE,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC;AAClE,QAAA,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;;;IAI3D,0BAA0B,GAAA;QAChC,IAAI,CAAC,0BAA0B,EAAE;;;;AAIjC,QAAA,KAAK,CAAC,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,MAAK;YAC/E,IAAI,CAAC,0BAA0B,EAAE;AACjC,YAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE;AACxC,SAAC,CAAC;;AAGJ;;;;AAIG;IACK,oBAAoB,GAAA;;QAE1B,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,SAAS,CAAC,MAAK;YACxC,IAAI,CAAC,aAAa,EAAE;AACpB,YAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE;AACxC,SAAC,CAAC;;QAGF,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,SAAS,CAAC,MAAK;YACzC,IAAI,CAAC,mBAAmB,EAAE;AAC1B,YAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE;AACxC,SAAC,CAAC;;QAGF,IAAI,CAAC,cAAc,EAAE;QACrB,IAAI,CAAC,mBAAmB,EAAE;;;IAIpB,uBAAuB,GAAA;AAC7B,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAAE;YACrE,MAAM,kCAAkC,EAAE;;;IAItC,iBAAiB,GAAA;;;;;;QAMvB,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;AAC7C,YAAA,IAAI,CAAC,UAAU,GAAG,IAAI;AACtB,YAAA,IAAI,CAAC,WAAW,EAAE,QAAQ,EAAE;;AACvB,aAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,KAAK,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,KAAK,IAAI,CAAC,EAAE;AAClF,YAAA,IAAI,CAAC,UAAU,GAAG,KAAK;AACvB,YAAA,IAAI,CAAC,WAAW,EAAE,UAAU,EAAE;;AAGhC,QAAA,IAAI,CAAC,UAAU,EAAE,aAAa,CAAC,SAAS,CAAC,MAAM,CAC7C,yBAAyB,EACzB,IAAI,CAAC,QAAQ,CAAC,OAAO,CACtB;;AAGH;;;;;AAKG;IACK,0CAA0C,GAAA;;;AAGhD,QAAA,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,IAAI,CAAC,8BAA8B,GAAG,IAAI,CAAC,CAAC;;;QAI1F,WAAW,CACT,MAAK;AACH,YAAA,IAAI,IAAI,CAAC,8BAA8B,EAAE;AACvC,gBAAA,IAAI,CAAC,8BAA8B,GAAG,KAAK;gBAC3C,IAAI,CAAC,yBAAyB,EAAE;;AAEpC,SAAC,EACD;YACE,QAAQ,EAAE,IAAI,CAAC,SAAS;AACzB,SAAA,CACF;QAED,IAAI,CAAC,IAAI,CAAC;AACP,aAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC;AAC/B,aAAA,SAAS,CAAC,OAAO,IAAI,CAAC,8BAA8B,GAAG,IAAI,CAAC,CAAC;;;IAIlE,kBAAkB,GAAA;AAChB,QAAA,OAAO,IAAI,CAAC,UAAU,KAAK,QAAQ;;IAGrC,WAAW,GAAA;AACT,QAAA,OAAO,IAAI,CAAC,UAAU,KAAK,SAAS;;AAGtC;;;;;;;AAOG;IACH,uBAAuB,GAAA;AACrB,QAAA,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE;;AAG9F,IAAA,iBAAiB,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;IAExD,iBAAiB,GAAA;AACf,QAAA,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,EAAE;AAC7B,YAAA,OAAO,KAAK;;QAEd,OAAO,IAAI,CAAC,QAAQ,CAAC,gBAAgB,IAAI,IAAI,CAAC,kBAAkB,EAAE;;AAGpE;;;AAGG;AACH,IAAA,cAAc,CAAC,IAAoC,EAAA;AACjD,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,GAAG,IAAI;AAC9D,QAAA,OAAO,OAAO,IAAI,OAAO,CAAC,IAAI,CAAC;;;IAIjC,wBAAwB,GAAA;AACtB,QAAA,OAAO,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC;AAC5E,cAAE;cACA,MAAM;;;IAIZ,mBAAmB,GAAA;QACjB,IAAI,CAAC,yBAAyB,EAAE;;;IAIlC,yBAAyB,GAAA;AACvB,QAAA,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,IAAI,CAAC,cAAc,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,EAAE;AAC5E,YAAA,IAAI,CAAC,eAAe,EAAE,cAAc,CAAC,CAAC,CAAC;;aAClC;AACL,YAAA,IAAI,CAAC,eAAe,EAAE,cAAc,CAAC,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,CAAC;;;;IAKhE,aAAa,GAAA;QACnB,IAAI,CAAC,cAAc,EAAE;QACrB,IAAI,CAAC,mBAAmB,EAAE;;AAG5B;;;;;AAKG;IACK,cAAc,GAAA;AACpB,QAAA,IAAI,IAAI,CAAC,aAAa,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAAE;AACzE,YAAA,IAAI,SAAkB;AACtB,YAAA,IAAI,OAAgB;YACpB,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,IAAa,KAAI;AAC3C,gBAAA,IAAI,IAAI,CAAC,KAAK,KAAK,OAAO,EAAE;AAC1B,oBAAA,IAAI,SAAS,IAAI,IAAI,CAAC,SAAS,EAAE;AAC/B,wBAAA,MAAM,kCAAkC,CAAC,OAAO,CAAC;;oBAEnD,SAAS,GAAG,IAAI;;AACX,qBAAA,IAAI,IAAI,CAAC,KAAK,KAAK,KAAK,EAAE;oBAC/B,IAAI,OAAO,EAAE;AACX,wBAAA,MAAM,kCAAkC,CAAC,KAAK,CAAC;;oBAEjD,OAAO,GAAG,IAAI;;AAElB,aAAC,CAAC;;;AAIN;;;AAGG;IACK,mBAAmB,GAAA;AACzB,QAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,IAAI,GAAG,GAAa,EAAE;;AAGtB,YAAA,IACE,IAAI,CAAC,QAAQ,CAAC,mBAAmB;gBACjC,OAAO,IAAI,CAAC,QAAQ,CAAC,mBAAmB,KAAK,QAAQ,EACrD;AACA,gBAAA,GAAG,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,mBAAmB,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;;AAG3D,YAAA,IAAI,IAAI,CAAC,wBAAwB,EAAE,KAAK,MAAM,EAAE;AAC9C,gBAAA,MAAM,SAAS,GAAG,IAAI,CAAC;AACrB,sBAAE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,KAAK,OAAO;sBACtD,IAAI;AACR,gBAAA,MAAM,OAAO,GAAG,IAAI,CAAC;AACnB,sBAAE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,KAAK,KAAK;sBACpD,IAAI;gBAER,IAAI,SAAS,EAAE;AACb,oBAAA,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;;AACjB,qBAAA,IAAI,IAAI,CAAC,UAAU,EAAE;AAC1B,oBAAA,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC;;gBAG7B,IAAI,OAAO,EAAE;AACX,oBAAA,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;;;AAEjB,iBAAA,IAAI,IAAI,CAAC,cAAc,EAAE;AAC9B,gBAAA,GAAG,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,KAAK,IAAI,KAAK,CAAC,EAAE,CAAC,CAAC;;AAGzD,YAAA,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,GAAG,CAAC;;;AAIxC;;;;;;;;AAQG;IACK,yBAAyB,GAAA;QAC/B,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YAC/C;;AAEF,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO;;;QAGjD,IAAI,EAAE,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,oBAAoB,CAAC,EAAE;AAC7D,YAAA,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG,EAAE;YAClC;;;;AAIF,QAAA,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,EAAE;AAC5B,YAAA,IAAI,CAAC,8BAA8B,GAAG,IAAI;YAC1C;;AAEF,QAAA,MAAM,mBAAmB,GAAG,IAAI,CAAC,oBAAoB,EAAE,aAAa;AACpE,QAAA,MAAM,mBAAmB,GAAG,IAAI,CAAC,oBAAoB,EAAE,aAAa;AACpE,QAAA,MAAM,mBAAmB,GAAG,IAAI,CAAC,oBAAoB,EAAE,aAAa;AACpE,QAAA,MAAM,mBAAmB,GAAG,IAAI,CAAC,oBAAoB,EAAE,aAAa;QACpE,MAAM,wBAAwB,GAAG,mBAAmB,EAAE,qBAAqB,EAAE,CAAC,KAAK,IAAI,CAAC;QACxF,MAAM,wBAAwB,GAAG,mBAAmB,EAAE,qBAAqB,EAAE,CAAC,KAAK,IAAI,CAAC;QACxF,MAAM,wBAAwB,GAAG,mBAAmB,EAAE,qBAAqB,EAAE,CAAC,KAAK,IAAI,CAAC;QACxF,MAAM,wBAAwB,GAAG,mBAAmB,EAAE,qBAAqB,EAAE,CAAC,KAAK,IAAI,CAAC;;;AAGxF,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,KAAK,GAAG,IAAI,GAAG,GAAG;AACrD,QAAA,MAAM,WAAW,GAAG,CAAA,EAAG,wBAAwB,GAAG,wBAAwB,IAAI;QAC9E,MAAM,WAAW,GAAG,CAAA,6CAAA,CAA+C;QACnE,MAAM,qBAAqB,GAAG,CAAQ,KAAA,EAAA,MAAM,OAAO,WAAW,CAAA,GAAA,EAAM,WAAW,CAAA,EAAA,CAAI;;;;AAKnF,QAAA,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG,CAAA;;AAE1B,QAAA,EAAA,uCAAuC,eAAe,qBAAqB,CAAA;MAC/E;;QAGF,MAAM,oBAAoB,GACxB,wBAAwB;YACxB,wBAAwB;YACxB,wBAAwB;AACxB,YAAA,wBAAwB;AAC1B,QAAA,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,KAAK,CAAC,WAAW,CAC9C,kCAAkC,EAClC,CAAA,YAAA,EAAe,oBAAoB,CAAA,GAAA,CAAK,CACzC;;;IAIK,gBAAgB,GAAA;AACtB,QAAA,MAAM,OAAO,GAAgB,IAAI,CAAC,WAAW,CAAC,aAAa;AAC3D,QAAA,IAAI,OAAO,CAAC,WAAW,EAAE;AACvB,YAAA,MAAM,QAAQ,GAAG,OAAO,CAAC,WAAW,EAAE;;;AAGtC,YAAA,OAAO,QAAQ,IAAI,QAAQ,KAAK,OAAO;;;;QAIzC,OAAO,QAAQ,CAAC,eAAgB,CAAC,QAAQ,CAAC,OAAO,CAAC;;uGA3lBzC,YAAY,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAZ,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,YAAY,EAZZ,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,gBAAA,EAAA,MAAA,EAAA,EAAA,kBAAA,EAAA,oBAAA,EAAA,KAAA,EAAA,OAAA,EAAA,UAAA,EAAA,YAAA,EAAA,UAAA,EAAA,YAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,SAAA,EAAA,WAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,6CAAA,EAAA,sBAAA,EAAA,0CAAA,EAAA,gBAAA,EAAA,0CAAA,EAAA,gBAAA,EAAA,8BAAA,EAAA,qBAAA,EAAA,+BAAA,EAAA,mBAAA,EAAA,iCAAA,EAAA,qBAAA,EAAA,sCAAA,EAAA,wBAAA,EAAA,yCAAA,EAAA,2BAAA,EAAA,uCAAA,EAAA,6CAAA,EAAA,mBAAA,EAAA,kBAAA,EAAA,mBAAA,EAAA,4CAAA,EAAA,kBAAA,EAAA,sBAAA,EAAA,gBAAA,EAAA,oBAAA,EAAA,oBAAA,EAAA,+BAAA,EAAA,kBAAA,EAAA,6BAAA,EAAA,mBAAA,EAAA,8BAAA,EAAA,gBAAA,EAAA,2BAAA,EAAA,gBAAA,EAAA,2BAAA,EAAA,kBAAA,EAAA,6BAAA,EAAA,kBAAA,EAAA,6BAAA,EAAA,EAAA,cAAA,EAAA,oBAAA,EAAA,EAAA,SAAA,EAAA;AACT,YAAA,EAAC,OAAO,EAAE,cAAc,EAAE,WAAW,EAAE,YAAY,EAAC;AACpD,YAAA,EAAC,OAAO,EAAE,qBAAqB,EAAE,WAAW,EAAE,YAAY,EAAC;AAC5D,SAAA,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,aAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAsC2C,QAAQ,EAAA,WAAA,EAAA,IAAA,EAAA,QAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,mBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EANtCA,mBAAoB,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,iBAAA,EAAA,SAAA,EACjB,UAAU,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,iBAAA,EAAA,SAAA,EACV,UAAU,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,gBAAA,EAAA,SAAA,EACV,SAAS,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,eAAA,EAAA,SAAA,EACT,OAAO,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,YAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,WAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,sBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,sBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,sBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,sBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,gBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EARb,yBAAyB,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,iBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EACzB,0BAA0B,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,aAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAC1B,sBAAsB,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,cAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EC5MnC,i5JAgIA,EAAA,MAAA,EAAA,CAAA,2+hCAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EDgDI,yBAAyB,EAAA,QAAA,EAAA,kCAAA,EAAA,MAAA,EAAA,CAAA,UAAA,EAAA,eAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EACzB,0BAA0B,EAAA,QAAA,EAAA,iCAAA,EAAA,MAAA,EAAA,CAAA,gCAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAC1B,gBAAgB,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAChB,sBAAsB,EAAA,QAAA,EAAA,6BAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EACtB,OAAO,EAAA,QAAA,EAAA,UAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,IAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAGE,YAAY,EAAA,UAAA,EAAA,CAAA;kBA7CxB,SAAS;+BACE,gBAAgB,EAAA,QAAA,EAChB,cAAc,EAGlB,IAAA,EAAA;AACJ,wBAAA,OAAO,EAAE,oBAAoB;AAC7B,wBAAA,+CAA+C,EAAE,sBAAsB;AACvE,wBAAA,4CAA4C,EAAE,gBAAgB;AAC9D,wBAAA,4CAA4C,EAAE,gBAAgB;;;;AAI9D,wBAAA,gCAAgC,EAAE,qBAAqB;AACvD,wBAAA,iCAAiC,EAAE,mBAAmB;AACtD,wBAAA,mCAAmC,EAAE,qBAAqB;AAC1D,wBAAA,wCAAwC,EAAE,sBAAsB;AAChE,wBAAA,2CAA2C,EAAE,yBAAyB;AACtE,wBAAA,yCAAyC,EAAE,6CAA6C;AACxF,wBAAA,qBAAqB,EAAE,kBAAkB;AACzC,wBAAA,qBAAqB,EAAE,wCAAwC;AAC/D,wBAAA,oBAAoB,EAAE,oBAAoB;AAC1C,wBAAA,kBAAkB,EAAE,kBAAkB;AACtC,wBAAA,sBAAsB,EAAE,6BAA6B;AACrD,wBAAA,oBAAoB,EAAE,2BAA2B;AACjD,wBAAA,qBAAqB,EAAE,4BAA4B;AACnD,wBAAA,kBAAkB,EAAE,yBAAyB;AAC7C,wBAAA,kBAAkB,EAAE,yBAAyB;AAC7C,wBAAA,oBAAoB,EAAE,2BAA2B;AACjD,wBAAA,oBAAoB,EAAE,2BAA2B;AAClD,qBAAA,EAAA,aAAA,EACc,iBAAiB,CAAC,IAAI,mBACpB,uBAAuB,CAAC,MAAM,EACpC,SAAA,EAAA;AACT,wBAAA,EAAC,OAAO,EAAE,cAAc,EAAE,WAAW,cAAc,EAAC;AACpD,wBAAA,EAAC,OAAO,EAAE,qBAAqB,EAAE,WAAW,cAAc,EAAC;qBAC5D,EACQ,OAAA,EAAA;wBACP,yBAAyB;wBACzB,0BAA0B;wBAC1B,gBAAgB;wBAChB,sBAAsB;wBACtB,OAAO;AACR,qBAAA,EAAA,QAAA,EAAA,i5JAAA,EAAA,MAAA,EAAA,CAAA,2+hCAAA,CAAA,EAAA;wDAgBuB,UAAU,EAAA,CAAA;sBAAjC,SAAS;uBAAC,WAAW;gBACY,oBAAoB,EAAA,CAAA;sBAArD,SAAS;uBAAC,qBAAqB;gBACE,oBAAoB,EAAA,CAAA;sBAArD,SAAS;uBAAC,qBAAqB;gBACE,oBAAoB,EAAA,CAAA;sBAArD,SAAS;uBAAC,qBAAqB;gBACE,oBAAoB,EAAA,CAAA;sBAArD,SAAS;uBAAC,qBAAqB;gBACM,cAAc,EAAA,CAAA;sBAAnD,SAAS;uBAAC,yBAAyB;gBACG,eAAe,EAAA,CAAA;sBAArD,SAAS;uBAAC,0BAA0B;gBACF,WAAW,EAAA,CAAA;sBAA7C,SAAS;uBAAC,sBAAsB;gBAEG,iBAAiB,EAAA,CAAA;sBAApD,YAAY;uBAACA,mBAAoB;gBACgB,eAAe,EAAA,CAAA;sBAAhE,eAAe;AAAC,gBAAA,IAAA,EAAA,CAAA,UAAU,EAAE,EAAC,WAAW,EAAE,IAAI,EAAC;gBACE,eAAe,EAAA,CAAA;sBAAhE,eAAe;AAAC,gBAAA,IAAA,EAAA,CAAA,UAAU,EAAE,EAAC,WAAW,EAAE,IAAI,EAAC;gBACC,cAAc,EAAA,CAAA;sBAA9D,eAAe;AAAC,gBAAA,IAAA,EAAA,CAAA,SAAS,EAAE,EAAC,WAAW,EAAE,IAAI,EAAC;gBACA,aAAa,EAAA,CAAA;sBAA3D,eAAe;AAAC,gBAAA,IAAA,EAAA,CAAA,OAAO,EAAE,EAAC,WAAW,EAAE,IAAI,EAAC;gBAMzC,kBAAkB,EAAA,CAAA;sBADrB;gBAgBQ,KAAK,EAAA,CAAA;sBAAb;gBAIG,UAAU,EAAA,CAAA;sBADb;gBAkBG,UAAU,EAAA,CAAA;sBADb;gBA8BG,eAAe,EAAA,CAAA;sBADlB;gBAWG,SAAS,EAAA,CAAA;sBADZ;;;;;"}