{"version": 3, "file": "focus-key-manager-C1rAQJ5z.mjs", "sources": ["../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/a11y/key-manager/focus-key-manager.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {ListKeyManager, ListKeyManagerOption} from './list-key-manager';\nimport {FocusOrigin} from '../focus-monitor/focus-monitor';\n\n/**\n * This is the interface for focusable items (used by the FocusKeyManager).\n * Each item must know how to focus itself, whether or not it is currently disabled\n * and be able to supply its label.\n */\nexport interface FocusableOption extends ListKeyManagerOption {\n  /** Focuses the `FocusableOption`. */\n  focus(origin?: FocusOrigin): void;\n}\n\nexport class FocusKeyManager<T> extends ListKeyManager<FocusableOption & T> {\n  private _origin: FocusOrigin = 'program';\n\n  /**\n   * Sets the focus origin that will be passed in to the items for any subsequent `focus` calls.\n   * @param origin Focus origin to be used when focusing items.\n   */\n  setFocusOrigin(origin: FocusOrigin): this {\n    this._origin = origin;\n    return this;\n  }\n\n  /**\n   * Sets the active item to the item at the specified\n   * index and focuses the newly active item.\n   * @param index Index of the item to be set as active.\n   */\n  override setActiveItem(index: number): void;\n\n  /**\n   * Sets the active item to the item that is specified and focuses it.\n   * @param item Item to be set as active.\n   */\n  override setActiveItem(item: T): void;\n\n  override setActiveItem(item: any): void {\n    super.setActiveItem(item);\n\n    if (this.activeItem) {\n      this.activeItem.focus(this._origin);\n    }\n  }\n}\n"], "names": [], "mappings": ";;AAqBM,MAAO,eAAmB,SAAQ,cAAmC,CAAA;IACjE,OAAO,GAAgB,SAAS;AAExC;;;AAGG;AACH,IAAA,cAAc,CAAC,MAAmB,EAAA;AAChC,QAAA,IAAI,CAAC,OAAO,GAAG,MAAM;AACrB,QAAA,OAAO,IAAI;;AAgBJ,IAAA,aAAa,CAAC,IAAS,EAAA;AAC9B,QAAA,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC;AAEzB,QAAA,IAAI,IAAI,CAAC,UAAU,EAAE;YACnB,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC;;;AAGxC;;;;"}