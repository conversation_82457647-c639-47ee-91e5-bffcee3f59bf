{"version": 3, "file": "sort.mjs", "sources": ["../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/sort/sort-errors.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/sort/sort.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/sort/sort-header-intl.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/sort/sort-header.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/sort/sort-header.html", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/sort/sort-module.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/sort/sort-animations.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/** @docs-private */\nexport function getSortDuplicateSortableIdError(id: string): Error {\n  return Error(`Cannot have two MatSortables with the same id (${id}).`);\n}\n\n/** @docs-private */\nexport function getSortHeaderNotContainedWithinSortError(): Error {\n  return Error(`MatSortHeader must be placed within a parent element with the MatSort directive.`);\n}\n\n/** @docs-private */\nexport function getSortHeaderMissingIdError(): Error {\n  return Error(`MatSortHeader must be provided with a unique id.`);\n}\n\n/** @docs-private */\nexport function getSortInvalidDirectionError(direction: string): Error {\n  return Error(`${direction} is not a valid sort direction ('asc' or 'desc').`);\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  Directive,\n  EventEmitter,\n  Inject,\n  InjectionToken,\n  Input,\n  OnChanges,\n  OnDestroy,\n  OnInit,\n  Optional,\n  Output,\n  booleanAttribute,\n} from '@angular/core';\nimport {Observable, ReplaySubject, Subject} from 'rxjs';\nimport {SortDirection} from './sort-direction';\nimport {\n  getSortDuplicateSortableIdError,\n  getSortHeaderMissingIdError,\n  getSortInvalidDirectionError,\n} from './sort-errors';\n\n/** Position of the arrow that displays when sorted. */\nexport type SortHeaderArrowPosition = 'before' | 'after';\n\n/** Interface for a directive that holds sorting state consumed by `MatSortHeader`. */\nexport interface MatSortable {\n  /** The id of the column being sorted. */\n  id: string;\n\n  /** Starting sort direction. */\n  start: SortDirection;\n\n  /** Whether to disable clearing the sorting state. */\n  disableClear: boolean;\n}\n\n/** The current sort state. */\nexport interface Sort {\n  /** The id of the column being sorted. */\n  active: string;\n\n  /** The sort direction. */\n  direction: SortDirection;\n}\n\n/** Default options for `mat-sort`.  */\nexport interface MatSortDefaultOptions {\n  /** Whether to disable clearing the sorting state. */\n  disableClear?: boolean;\n  /** Position of the arrow that displays when sorted. */\n  arrowPosition?: SortHeaderArrowPosition;\n}\n\n/** Injection token to be used to override the default options for `mat-sort`. */\nexport const MAT_SORT_DEFAULT_OPTIONS = new InjectionToken<MatSortDefaultOptions>(\n  'MAT_SORT_DEFAULT_OPTIONS',\n);\n\n/** Container for MatSortables to manage the sort state and provide default sort parameters. */\n@Directive({\n  selector: '[matSort]',\n  exportAs: 'matSort',\n  host: {\n    'class': 'mat-sort',\n  },\n})\nexport class MatSort implements OnChanges, OnDestroy, OnInit {\n  private _initializedStream = new ReplaySubject<void>(1);\n\n  /** Collection of all registered sortables that this directive manages. */\n  sortables = new Map<string, MatSortable>();\n\n  /** Used to notify any child components listening to state changes. */\n  readonly _stateChanges = new Subject<void>();\n\n  /** The id of the most recently sorted MatSortable. */\n  @Input('matSortActive') active: string;\n\n  /**\n   * The direction to set when an MatSortable is initially sorted.\n   * May be overridden by the MatSortable's sort start.\n   */\n  @Input('matSortStart') start: SortDirection = 'asc';\n\n  /** The sort direction of the currently active MatSortable. */\n  @Input('matSortDirection')\n  get direction(): SortDirection {\n    return this._direction;\n  }\n  set direction(direction: SortDirection) {\n    if (\n      direction &&\n      direction !== 'asc' &&\n      direction !== 'desc' &&\n      (typeof ngDevMode === 'undefined' || ngDevMode)\n    ) {\n      throw getSortInvalidDirectionError(direction);\n    }\n    this._direction = direction;\n  }\n  private _direction: SortDirection = '';\n\n  /**\n   * Whether to disable the user from clearing the sort by finishing the sort direction cycle.\n   * May be overridden by the MatSortable's disable clear input.\n   */\n  @Input({alias: 'matSortDisableClear', transform: booleanAttribute})\n  disableClear: boolean;\n\n  /** Whether the sortable is disabled. */\n  @Input({alias: 'matSortDisabled', transform: booleanAttribute})\n  disabled: boolean = false;\n\n  /** Event emitted when the user changes either the active sort or sort direction. */\n  @Output('matSortChange') readonly sortChange: EventEmitter<Sort> = new EventEmitter<Sort>();\n\n  /** Emits when the paginator is initialized. */\n  initialized: Observable<void> = this._initializedStream;\n\n  constructor(\n    @Optional()\n    @Inject(MAT_SORT_DEFAULT_OPTIONS)\n    private _defaultOptions?: MatSortDefaultOptions,\n  ) {}\n\n  /**\n   * Register function to be used by the contained MatSortables. Adds the MatSortable to the\n   * collection of MatSortables.\n   */\n  register(sortable: MatSortable): void {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (!sortable.id) {\n        throw getSortHeaderMissingIdError();\n      }\n\n      if (this.sortables.has(sortable.id)) {\n        throw getSortDuplicateSortableIdError(sortable.id);\n      }\n    }\n\n    this.sortables.set(sortable.id, sortable);\n  }\n\n  /**\n   * Unregister function to be used by the contained MatSortables. Removes the MatSortable from the\n   * collection of contained MatSortables.\n   */\n  deregister(sortable: MatSortable): void {\n    this.sortables.delete(sortable.id);\n  }\n\n  /** Sets the active sort id and determines the new sort direction. */\n  sort(sortable: MatSortable): void {\n    if (this.active != sortable.id) {\n      this.active = sortable.id;\n      this.direction = sortable.start ? sortable.start : this.start;\n    } else {\n      this.direction = this.getNextSortDirection(sortable);\n    }\n\n    this.sortChange.emit({active: this.active, direction: this.direction});\n  }\n\n  /** Returns the next sort direction of the active sortable, checking for potential overrides. */\n  getNextSortDirection(sortable: MatSortable): SortDirection {\n    if (!sortable) {\n      return '';\n    }\n\n    // Get the sort direction cycle with the potential sortable overrides.\n    const disableClear =\n      sortable?.disableClear ?? this.disableClear ?? !!this._defaultOptions?.disableClear;\n    let sortDirectionCycle = getSortDirectionCycle(sortable.start || this.start, disableClear);\n\n    // Get and return the next direction in the cycle\n    let nextDirectionIndex = sortDirectionCycle.indexOf(this.direction) + 1;\n    if (nextDirectionIndex >= sortDirectionCycle.length) {\n      nextDirectionIndex = 0;\n    }\n    return sortDirectionCycle[nextDirectionIndex];\n  }\n\n  ngOnInit() {\n    this._initializedStream.next();\n  }\n\n  ngOnChanges() {\n    this._stateChanges.next();\n  }\n\n  ngOnDestroy() {\n    this._stateChanges.complete();\n    this._initializedStream.complete();\n  }\n}\n\n/** Returns the sort direction cycle to use given the provided parameters of order and clear. */\nfunction getSortDirectionCycle(start: SortDirection, disableClear: boolean): SortDirection[] {\n  let sortOrder: SortDirection[] = ['asc', 'desc'];\n  if (start == 'desc') {\n    sortOrder.reverse();\n  }\n  if (!disableClear) {\n    sortOrder.push('');\n  }\n\n  return sortOrder;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Injectable, SkipSelf, Optional} from '@angular/core';\nimport {Subject} from 'rxjs';\n\n/**\n * To modify the labels and text displayed, create a new instance of MatSortHeaderIntl and\n * include it in a custom provider.\n */\n@Injectable({providedIn: 'root'})\nexport class MatSortHeaderIntl {\n  /**\n   * Stream that emits whenever the labels here are changed. Use this to notify\n   * components if the labels have changed after initialization.\n   */\n  readonly changes: Subject<void> = new Subject<void>();\n}\n\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nexport function MAT_SORT_HEADER_INTL_PROVIDER_FACTORY(parentIntl: MatSortHeaderIntl) {\n  return parentIntl || new MatSortHeaderIntl();\n}\n\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nexport const MAT_SORT_HEADER_INTL_PROVIDER = {\n  // If there is already an MatSortHeaderIntl available, use that. Otherwise, provide a new one.\n  provide: MatSortHeaderIntl,\n  deps: [[new Optional(), new SkipSelf(), MatSortHeaderIntl]],\n  useFactory: MAT_SORT_HEADER_INTL_PROVIDER_FACTORY,\n};\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {AriaDescriber, FocusMonitor} from '@angular/cdk/a11y';\nimport {ENTER, SPACE} from '@angular/cdk/keycodes';\nimport {\n  AfterViewInit,\n  ChangeDetectionStrategy,\n  Component,\n  ElementRef,\n  Input,\n  OnDestroy,\n  OnInit,\n  ViewEncapsulation,\n  booleanAttribute,\n  inject,\n  signal,\n  ANIMATION_MODULE_TYPE,\n  ChangeDetectorRef,\n} from '@angular/core';\nimport {merge, Subscription} from 'rxjs';\nimport {\n  MAT_SORT_DEFAULT_OPTIONS,\n  MatSort,\n  MatSortable,\n  MatSortDefaultOptions,\n  SortHeaderArrowPosition,\n} from './sort';\nimport {SortDirection} from './sort-direction';\nimport {getSortHeaderNotContainedWithinSortError} from './sort-errors';\nimport {MatSortHeaderIntl} from './sort-header-intl';\nimport {_CdkPrivateStyleLoader} from '@angular/cdk/private';\nimport {_StructuralStylesLoader} from '../core';\n\n/**\n * Valid positions for the arrow to be in for its opacity and translation. If the state is a\n * sort direction, the position of the arrow will be above/below and opacity 0. If the state is\n * hint, the arrow will be in the center with a slight opacity. Active state means the arrow will\n * be fully opaque in the center.\n *\n * @docs-private\n * @deprecated No longer being used, to be removed.\n * @breaking-change 21.0.0\n */\nexport type ArrowViewState = SortDirection | 'hint' | 'active';\n\n/**\n * States describing the arrow's animated position (animating fromState to toState).\n * If the fromState is not defined, there will be no animated transition to the toState.\n * @docs-private\n * @deprecated No longer being used, to be removed.\n * @breaking-change 21.0.0\n */\nexport interface ArrowViewStateTransition {\n  fromState?: ArrowViewState;\n  toState?: ArrowViewState;\n}\n\n/** Column definition associated with a `MatSortHeader`. */\ninterface MatSortHeaderColumnDef {\n  name: string;\n}\n\n/**\n * Applies sorting behavior (click to change sort) and styles to an element, including an\n * arrow to display the current sort direction.\n *\n * Must be provided with an id and contained within a parent MatSort directive.\n *\n * If used on header cells in a CdkTable, it will automatically default its id from its containing\n * column definition.\n */\n@Component({\n  selector: '[mat-sort-header]',\n  exportAs: 'matSortHeader',\n  templateUrl: 'sort-header.html',\n  styleUrl: 'sort-header.css',\n  host: {\n    'class': 'mat-sort-header',\n    '(click)': '_toggleOnInteraction()',\n    '(keydown)': '_handleKeydown($event)',\n    '(mouseleave)': '_recentlyCleared.set(null)',\n    '[attr.aria-sort]': '_getAriaSortAttribute()',\n    '[class.mat-sort-header-disabled]': '_isDisabled()',\n  },\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n})\nexport class MatSortHeader implements MatSortable, OnDestroy, OnInit, AfterViewInit {\n  _intl = inject(MatSortHeaderIntl);\n  _sort = inject(MatSort, {optional: true})!;\n  _columnDef = inject<MatSortHeaderColumnDef>('MAT_SORT_HEADER_COLUMN_DEF' as any, {\n    optional: true,\n  });\n  private _changeDetectorRef = inject(ChangeDetectorRef);\n  private _focusMonitor = inject(FocusMonitor);\n  private _elementRef = inject<ElementRef<HTMLElement>>(ElementRef);\n  private _ariaDescriber = inject(AriaDescriber, {optional: true});\n  private _renderChanges: Subscription | undefined;\n  protected _animationModule = inject(ANIMATION_MODULE_TYPE, {optional: true});\n\n  /**\n   * Indicates which state was just cleared from the sort header.\n   * Will be reset on the next interaction. Used for coordinating animations.\n   */\n  protected _recentlyCleared = signal<SortDirection | null>(null);\n\n  /**\n   * The element with role=\"button\" inside this component's view. We need this\n   * in order to apply a description with AriaDescriber.\n   */\n  private _sortButton: HTMLElement;\n\n  /**\n   * ID of this sort header. If used within the context of a CdkColumnDef, this will default to\n   * the column's name.\n   */\n  @Input('mat-sort-header') id: string;\n\n  /** Sets the position of the arrow that displays when sorted. */\n  @Input() arrowPosition: SortHeaderArrowPosition = 'after';\n\n  /** Overrides the sort start value of the containing MatSort for this MatSortable. */\n  @Input() start: SortDirection;\n\n  /** whether the sort header is disabled. */\n  @Input({transform: booleanAttribute})\n  disabled: boolean = false;\n\n  /**\n   * Description applied to MatSortHeader's button element with aria-describedby. This text should\n   * describe the action that will occur when the user clicks the sort header.\n   */\n  @Input()\n  get sortActionDescription(): string {\n    return this._sortActionDescription;\n  }\n  set sortActionDescription(value: string) {\n    this._updateSortActionDescription(value);\n  }\n  // Default the action description to \"Sort\" because it's better than nothing.\n  // Without a description, the button's label comes from the sort header text content,\n  // which doesn't give any indication that it performs a sorting operation.\n  private _sortActionDescription: string = 'Sort';\n\n  /** Overrides the disable clear value of the containing MatSort for this MatSortable. */\n  @Input({transform: booleanAttribute})\n  disableClear: boolean;\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    inject(_CdkPrivateStyleLoader).load(_StructuralStylesLoader);\n    const defaultOptions = inject<MatSortDefaultOptions>(MAT_SORT_DEFAULT_OPTIONS, {\n      optional: true,\n    });\n\n    // Note that we use a string token for the `_columnDef`, because the value is provided both by\n    // `material/table` and `cdk/table` and we can't have the CDK depending on Material,\n    // and we want to avoid having the sort header depending on the CDK table because\n    // of this single reference.\n    if (!this._sort && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getSortHeaderNotContainedWithinSortError();\n    }\n\n    if (defaultOptions?.arrowPosition) {\n      this.arrowPosition = defaultOptions?.arrowPosition;\n    }\n  }\n\n  ngOnInit() {\n    if (!this.id && this._columnDef) {\n      this.id = this._columnDef.name;\n    }\n\n    this._sort.register(this);\n    this._renderChanges = merge(this._sort._stateChanges, this._sort.sortChange).subscribe(() =>\n      this._changeDetectorRef.markForCheck(),\n    );\n    this._sortButton = this._elementRef.nativeElement.querySelector('.mat-sort-header-container')!;\n    this._updateSortActionDescription(this._sortActionDescription);\n  }\n\n  ngAfterViewInit() {\n    // We use the focus monitor because we also want to style\n    // things differently based on the focus origin.\n    this._focusMonitor\n      .monitor(this._elementRef, true)\n      .subscribe(() => this._recentlyCleared.set(null));\n  }\n\n  ngOnDestroy() {\n    this._focusMonitor.stopMonitoring(this._elementRef);\n    this._sort.deregister(this);\n    this._renderChanges?.unsubscribe();\n\n    if (this._sortButton) {\n      this._ariaDescriber?.removeDescription(this._sortButton, this._sortActionDescription);\n    }\n  }\n\n  /** Triggers the sort on this sort header and removes the indicator hint. */\n  _toggleOnInteraction() {\n    if (!this._isDisabled()) {\n      const wasSorted = this._isSorted();\n      const prevDirection = this._sort.direction;\n      this._sort.sort(this);\n      this._recentlyCleared.set(wasSorted && !this._isSorted() ? prevDirection : null);\n    }\n  }\n\n  _handleKeydown(event: KeyboardEvent) {\n    if (event.keyCode === SPACE || event.keyCode === ENTER) {\n      event.preventDefault();\n      this._toggleOnInteraction();\n    }\n  }\n\n  /** Whether this MatSortHeader is currently sorted in either ascending or descending order. */\n  _isSorted() {\n    return (\n      this._sort.active == this.id &&\n      (this._sort.direction === 'asc' || this._sort.direction === 'desc')\n    );\n  }\n\n  _isDisabled() {\n    return this._sort.disabled || this.disabled;\n  }\n\n  /**\n   * Gets the aria-sort attribute that should be applied to this sort header. If this header\n   * is not sorted, returns null so that the attribute is removed from the host element. Aria spec\n   * says that the aria-sort property should only be present on one header at a time, so removing\n   * ensures this is true.\n   */\n  _getAriaSortAttribute() {\n    if (!this._isSorted()) {\n      return 'none';\n    }\n\n    return this._sort.direction == 'asc' ? 'ascending' : 'descending';\n  }\n\n  /** Whether the arrow inside the sort header should be rendered. */\n  _renderArrow() {\n    return !this._isDisabled() || this._isSorted();\n  }\n\n  private _updateSortActionDescription(newDescription: string) {\n    // We use AriaDescriber for the sort button instead of setting an `aria-label` because some\n    // screen readers (notably VoiceOver) will read both the column header *and* the button's label\n    // for every *cell* in the table, creating a lot of unnecessary noise.\n\n    // If _sortButton is undefined, the component hasn't been initialized yet so there's\n    // nothing to update in the DOM.\n    if (this._sortButton) {\n      // removeDescription will no-op if there is no existing message.\n      // TODO(jelbourn): remove optional chaining when AriaDescriber is required.\n      this._ariaDescriber?.removeDescription(this._sortButton, this._sortActionDescription);\n      this._ariaDescriber?.describe(this._sortButton, newDescription);\n    }\n\n    this._sortActionDescription = newDescription;\n  }\n}\n", "<!--\n  We set the `tabindex` on an element inside the table header, rather than the header itself,\n  because of a bug in NVDA where having a `tabindex` on a `th` breaks keyboard navigation in the\n  table (see https://github.com/nvaccess/nvda/issues/7718). This allows for the header to both\n  be focusable, and have screen readers read out its `aria-sort` state. We prefer this approach\n  over having a button with an `aria-label` inside the header, because the button's `aria-label`\n  will be read out as the user is navigating the table's cell (see #13012).\n\n  The approach is based off of: https://dequeuniversity.com/library/aria/tables/sf-sortable-grid\n-->\n<div class=\"mat-sort-header-container mat-focus-indicator\"\n     [class.mat-sort-header-sorted]=\"_isSorted()\"\n     [class.mat-sort-header-position-before]=\"arrowPosition === 'before'\"\n     [class.mat-sort-header-descending]=\"this._sort.direction === 'desc'\"\n     [class.mat-sort-header-ascending]=\"this._sort.direction === 'asc'\"\n     [class.mat-sort-header-recently-cleared-ascending]=\"_recentlyCleared() === 'asc'\"\n     [class.mat-sort-header-recently-cleared-descending]=\"_recentlyCleared() === 'desc'\"\n     [class.mat-sort-header-animations-disabled]=\"_animationModule === 'NoopAnimations'\"\n     [attr.tabindex]=\"_isDisabled() ? null : 0\"\n     [attr.role]=\"_isDisabled() ? null : 'button'\">\n\n  <!--\n    TODO(crisbeto): this div isn't strictly necessary, but we have to keep it due to a large\n    number of screenshot diff failures. It should be removed eventually. Note that the difference\n    isn't visible with a shorter header, but once it breaks up into multiple lines, this element\n    causes it to be center-aligned, whereas removing it will keep the text to the left.\n  -->\n  <div class=\"mat-sort-header-content\">\n    <ng-content></ng-content>\n  </div>\n\n  <!-- Disable animations while a current animation is running -->\n  @if (_renderArrow()) {\n    <div class=\"mat-sort-header-arrow\">\n      <svg viewBox=\"0 -960 960 960\" focusable=\"false\" aria-hidden=\"true\">\n        <path d=\"M440-240v-368L296-464l-56-56 240-240 240 240-56 56-144-144v368h-80Z\"/>\n      </svg>\n    </div>\n  }\n</div>\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {NgModule} from '@angular/core';\nimport {MatSortHeader} from './sort-header';\nimport {MatSort} from './sort';\nimport {MAT_SORT_HEADER_INTL_PROVIDER} from './sort-header-intl';\nimport {MatCommonModule} from '../core';\n\n@NgModule({\n  imports: [MatCommonModule, MatSort, MatSortHeader],\n  exports: [MatSort, MatSortHeader],\n  providers: [MAT_SORT_HEADER_INTL_PROVIDER],\n})\nexport class MatSortModule {}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/**\n * Animations used by MatSort.\n * @docs-private\n * @deprecated No longer being used, to be removed.\n * @breaking-change 21.0.0\n */\nexport const matSortAnimations: {\n  readonly indicator: any;\n  readonly leftPointer: any;\n  readonly rightPointer: any;\n  readonly arrowOpacity: any;\n  readonly arrowPosition: any;\n  readonly allowChildren: any;\n} = {\n  // Represents:\n  // trigger('indicator', [\n  //   state('active-asc, asc', style({transform: 'translateY(0px)'})),\n  //   // 10px is the height of the sort indicator, minus the width of the pointers\n  //   state('active-desc, desc', style({transform: 'translateY(10px)'})),\n  //   transition('active-asc <=> active-desc', animate(SORT_ANIMATION_TRANSITION)),\n  // ])\n\n  /** Animation that moves the sort indicator. */\n  indicator: {\n    type: 7,\n    name: 'indicator',\n    definitions: [\n      {\n        type: 0,\n        name: 'active-asc, asc',\n        styles: {type: 6, styles: {transform: 'translateY(0px)'}, offset: null},\n      },\n      {\n        type: 0,\n        name: 'active-desc, desc',\n        styles: {type: 6, styles: {transform: 'translateY(10px)'}, offset: null},\n      },\n      {\n        type: 1,\n        expr: 'active-asc <=> active-desc',\n        animation: {type: 4, styles: null, timings: '225ms cubic-bezier(0.4,0.0,0.2,1)'},\n        options: null,\n      },\n    ],\n    options: {},\n  },\n\n  // Represents:\n  // trigger('leftPointer', [\n  //   state('active-asc, asc', style({transform: 'rotate(-45deg)'})),\n  //   state('active-desc, desc', style({transform: 'rotate(45deg)'})),\n  //   transition('active-asc <=> active-desc', animate(SORT_ANIMATION_TRANSITION)),\n  // ])\n\n  /** Animation that rotates the left pointer of the indicator based on the sorting direction. */\n  leftPointer: {\n    type: 7,\n    name: 'leftPointer',\n    definitions: [\n      {\n        type: 0,\n        name: 'active-asc, asc',\n        styles: {type: 6, styles: {transform: 'rotate(-45deg)'}, offset: null},\n      },\n      {\n        type: 0,\n        name: 'active-desc, desc',\n        styles: {type: 6, styles: {transform: 'rotate(45deg)'}, offset: null},\n      },\n      {\n        type: 1,\n        expr: 'active-asc <=> active-desc',\n        animation: {type: 4, styles: null, timings: '225ms cubic-bezier(0.4,0.0,0.2,1)'},\n        options: null,\n      },\n    ],\n    options: {},\n  },\n\n  // Represents:\n  // trigger('rightPointer', [\n  //   state('active-asc, asc', style({transform: 'rotate(45deg)'})),\n  //   state('active-desc, desc', style({transform: 'rotate(-45deg)'})),\n  //   transition('active-asc <=> active-desc', animate(SORT_ANIMATION_TRANSITION)),\n  // ])\n\n  /** Animation that rotates the right pointer of the indicator based on the sorting direction. */\n  rightPointer: {\n    type: 7,\n    name: 'rightPointer',\n    definitions: [\n      {\n        type: 0,\n        name: 'active-asc, asc',\n        styles: {type: 6, styles: {transform: 'rotate(45deg)'}, offset: null},\n      },\n      {\n        type: 0,\n        name: 'active-desc, desc',\n        styles: {type: 6, styles: {transform: 'rotate(-45deg)'}, offset: null},\n      },\n      {\n        type: 1,\n        expr: 'active-asc <=> active-desc',\n        animation: {type: 4, styles: null, timings: '225ms cubic-bezier(0.4,0.0,0.2,1)'},\n        options: null,\n      },\n    ],\n    options: {},\n  },\n\n  // Represents:\n  // trigger('arrowOpacity', [\n  //   state('desc-to-active, asc-to-active, active', style({opacity: 1})),\n  //   state('desc-to-hint, asc-to-hint, hint', style({opacity: 0.54})),\n  //   state(\n  //     'hint-to-desc, active-to-desc, desc, hint-to-asc, active-to-asc, asc, void',\n  //     style({opacity: 0}),\n  //   ),\n  //   // Transition between all states except for immediate transitions\n  //   transition('* => asc, * => desc, * => active, * => hint, * => void', animate('0ms')),\n  //   transition('* <=> *', animate(SORT_ANIMATION_TRANSITION)),\n  // ])\n\n  /** Animation that controls the arrow opacity. */\n  arrowOpacity: {\n    type: 7,\n    name: 'arrowOpacity',\n    definitions: [\n      {\n        type: 0,\n        name: 'desc-to-active, asc-to-active, active',\n        styles: {type: 6, styles: {'opacity': 1}, offset: null},\n      },\n      {\n        type: 0,\n        name: 'desc-to-hint, asc-to-hint, hint',\n        styles: {type: 6, styles: {'opacity': 0.54}, offset: null},\n      },\n      {\n        type: 0,\n        name: 'hint-to-desc, active-to-desc, desc, hint-to-asc, active-to-asc, asc, void',\n        styles: {type: 6, styles: {'opacity': 0}, offset: null},\n      },\n      {\n        type: 1,\n        expr: '* => asc, * => desc, * => active, * => hint, * => void',\n        animation: {type: 4, styles: null, timings: '0ms'},\n        options: null,\n      },\n      {\n        type: 1,\n        expr: '* <=> *',\n        animation: {type: 4, styles: null, timings: '225ms cubic-bezier(0.4,0.0,0.2,1)'},\n        options: null,\n      },\n    ],\n    options: {},\n  },\n\n  // Represents:\n  // trigger('arrowPosition', [\n  //   // Hidden Above => Hint Center\n  //   transition(\n  //     '* => desc-to-hint, * => desc-to-active',\n  //     animate(\n  //       SORT_ANIMATION_TRANSITION,\n  //       keyframes([style({transform: 'translateY(-25%)'}), style({transform: 'translateY(0)'})]),\n  //     ),\n  //   ),\n  //   // Hint Center => Hidden Below\n  //   transition(\n  //     '* => hint-to-desc, * => active-to-desc',\n  //     animate(\n  //       SORT_ANIMATION_TRANSITION,\n  //       keyframes([style({transform: 'translateY(0)'}), style({transform: 'translateY(25%)'})]),\n  //     ),\n  //   ),\n  //   // Hidden Below => Hint Center\n  //   transition(\n  //     '* => asc-to-hint, * => asc-to-active',\n  //     animate(\n  //       SORT_ANIMATION_TRANSITION,\n  //       keyframes([style({transform: 'translateY(25%)'}), style({transform: 'translateY(0)'})]),\n  //     ),\n  //   ),\n  //   // Hint Center => Hidden Above\n  //   transition(\n  //     '* => hint-to-asc, * => active-to-asc',\n  //     animate(\n  //       SORT_ANIMATION_TRANSITION,\n  //       keyframes([style({transform: 'translateY(0)'}), style({transform: 'translateY(-25%)'})]),\n  //     ),\n  //   ),\n  //   state(\n  //     'desc-to-hint, asc-to-hint, hint, desc-to-active, asc-to-active, active',\n  //     style({transform: 'translateY(0)'}),\n  //   ),\n  //   state('hint-to-desc, active-to-desc, desc', style({transform: 'translateY(-25%)'})),\n  //   state('hint-to-asc, active-to-asc, asc', style({transform: 'translateY(25%)'})),\n  // ])\n\n  /**\n   * Animation for the translation of the arrow as a whole. States are separated into two\n   * groups: ones with animations and others that are immediate. Immediate states are asc, desc,\n   * peek, and active. The other states define a specific animation (source-to-destination)\n   * and are determined as a function of their prev user-perceived state and what the next state\n   * should be.\n   */\n  arrowPosition: {\n    type: 7,\n    name: 'arrowPosition',\n    definitions: [\n      {\n        type: 1,\n        expr: '* => desc-to-hint, * => desc-to-active',\n        animation: {\n          type: 4,\n          styles: {\n            type: 5,\n            'steps': [\n              {type: 6, styles: {transform: 'translateY(-25%)'}, offset: null},\n              {type: 6, styles: {transform: 'translateY(0)'}, offset: null},\n            ],\n          },\n          timings: '225ms cubic-bezier(0.4,0.0,0.2,1)',\n        },\n        options: null,\n      },\n      {\n        type: 1,\n        expr: '* => hint-to-desc, * => active-to-desc',\n        animation: {\n          type: 4,\n          styles: {\n            type: 5,\n            'steps': [\n              {type: 6, styles: {transform: 'translateY(0)'}, offset: null},\n              {type: 6, styles: {transform: 'translateY(25%)'}, offset: null},\n            ],\n          },\n          timings: '225ms cubic-bezier(0.4,0.0,0.2,1)',\n        },\n        options: null,\n      },\n      {\n        type: 1,\n        expr: '* => asc-to-hint, * => asc-to-active',\n        animation: {\n          type: 4,\n          styles: {\n            type: 5,\n            'steps': [\n              {type: 6, styles: {transform: 'translateY(25%)'}, offset: null},\n              {type: 6, styles: {transform: 'translateY(0)'}, offset: null},\n            ],\n          },\n          timings: '225ms cubic-bezier(0.4,0.0,0.2,1)',\n        },\n        options: null,\n      },\n      {\n        type: 1,\n        expr: '* => hint-to-asc, * => active-to-asc',\n        animation: {\n          type: 4,\n          styles: {\n            type: 5,\n            'steps': [\n              {type: 6, styles: {transform: 'translateY(0)'}, offset: null},\n              {type: 6, styles: {transform: 'translateY(-25%)'}, offset: null},\n            ],\n          },\n          timings: '225ms cubic-bezier(0.4,0.0,0.2,1)',\n        },\n        options: null,\n      },\n      {\n        type: 0,\n        name: 'desc-to-hint, asc-to-hint, hint, desc-to-active, asc-to-active, active',\n        styles: {type: 6, styles: {transform: 'translateY(0)'}, offset: null},\n      },\n      {\n        type: 0,\n        name: 'hint-to-desc, active-to-desc, desc',\n        styles: {type: 6, styles: {transform: 'translateY(-25%)'}, offset: null},\n      },\n      {\n        type: 0,\n        name: 'hint-to-asc, active-to-asc, asc',\n        styles: {type: 6, styles: {transform: 'translateY(25%)'}, offset: null},\n      },\n    ],\n    options: {},\n  },\n\n  // Represents:\n  // trigger('allowChildren', [\n  //   transition('* <=> *', [query('@*', animateChild(), {optional: true})]),\n  // ])\n\n  /** Necessary trigger that calls animate on children animations. */\n  allowChildren: {\n    type: 7,\n    name: 'allowChildren',\n    definitions: [\n      {\n        type: 1,\n        expr: '* <=> *',\n        animation: [\n          {\n            type: 11,\n            selector: '@*',\n            animation: {type: 9, options: null},\n            options: {optional: true},\n          },\n        ],\n        options: null,\n      },\n    ],\n    options: {},\n  },\n};\n"], "names": [], "mappings": ";;;;;;;;;;AAQA;AACM,SAAU,+BAA+B,CAAC,EAAU,EAAA;AACxD,IAAA,OAAO,KAAK,CAAC,CAAA,+CAAA,EAAkD,EAAE,CAAA,EAAA,CAAI,CAAC;AACxE;AAEA;SACgB,wCAAwC,GAAA;AACtD,IAAA,OAAO,KAAK,CAAC,CAAkF,gFAAA,CAAA,CAAC;AAClG;AAEA;SACgB,2BAA2B,GAAA;AACzC,IAAA,OAAO,KAAK,CAAC,CAAkD,gDAAA,CAAA,CAAC;AAClE;AAEA;AACM,SAAU,4BAA4B,CAAC,SAAiB,EAAA;AAC5D,IAAA,OAAO,KAAK,CAAC,CAAA,EAAG,SAAS,CAAA,iDAAA,CAAmD,CAAC;AAC/E;;ACmCA;MACa,wBAAwB,GAAG,IAAI,cAAc,CACxD,0BAA0B;AAG5B;MAQa,OAAO,CAAA;AAwDR,IAAA,eAAA;AAvDF,IAAA,kBAAkB,GAAG,IAAI,aAAa,CAAO,CAAC,CAAC;;AAGvD,IAAA,SAAS,GAAG,IAAI,GAAG,EAAuB;;AAGjC,IAAA,aAAa,GAAG,IAAI,OAAO,EAAQ;;AAGpB,IAAA,MAAM;AAE9B;;;AAGG;IACoB,KAAK,GAAkB,KAAK;;AAGnD,IAAA,IACI,SAAS,GAAA;QACX,OAAO,IAAI,CAAC,UAAU;;IAExB,IAAI,SAAS,CAAC,SAAwB,EAAA;AACpC,QAAA,IACE,SAAS;AACT,YAAA,SAAS,KAAK,KAAK;AACnB,YAAA,SAAS,KAAK,MAAM;aACnB,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAC/C;AACA,YAAA,MAAM,4BAA4B,CAAC,SAAS,CAAC;;AAE/C,QAAA,IAAI,CAAC,UAAU,GAAG,SAAS;;IAErB,UAAU,GAAkB,EAAE;AAEtC;;;AAGG;AAEH,IAAA,YAAY;;IAIZ,QAAQ,GAAY,KAAK;;AAGS,IAAA,UAAU,GAAuB,IAAI,YAAY,EAAQ;;AAG3F,IAAA,WAAW,GAAqB,IAAI,CAAC,kBAAkB;AAEvD,IAAA,WAAA,CAGU,eAAuC,EAAA;QAAvC,IAAe,CAAA,eAAA,GAAf,eAAe;;AAGzB;;;AAGG;AACH,IAAA,QAAQ,CAAC,QAAqB,EAAA;AAC5B,QAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;AACjD,YAAA,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE;gBAChB,MAAM,2BAA2B,EAAE;;YAGrC,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE;AACnC,gBAAA,MAAM,+BAA+B,CAAC,QAAQ,CAAC,EAAE,CAAC;;;QAItD,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,EAAE,QAAQ,CAAC;;AAG3C;;;AAGG;AACH,IAAA,UAAU,CAAC,QAAqB,EAAA;QAC9B,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC;;;AAIpC,IAAA,IAAI,CAAC,QAAqB,EAAA;QACxB,IAAI,IAAI,CAAC,MAAM,IAAI,QAAQ,CAAC,EAAE,EAAE;AAC9B,YAAA,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC,EAAE;AACzB,YAAA,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK;;aACxD;YACL,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC;;AAGtD,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,SAAS,EAAE,IAAI,CAAC,SAAS,EAAC,CAAC;;;AAIxE,IAAA,oBAAoB,CAAC,QAAqB,EAAA;QACxC,IAAI,CAAC,QAAQ,EAAE;AACb,YAAA,OAAO,EAAE;;;AAIX,QAAA,MAAM,YAAY,GAChB,QAAQ,EAAE,YAAY,IAAI,IAAI,CAAC,YAAY,IAAI,CAAC,CAAC,IAAI,CAAC,eAAe,EAAE,YAAY;AACrF,QAAA,IAAI,kBAAkB,GAAG,qBAAqB,CAAC,QAAQ,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,EAAE,YAAY,CAAC;;AAG1F,QAAA,IAAI,kBAAkB,GAAG,kBAAkB,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC;AACvE,QAAA,IAAI,kBAAkB,IAAI,kBAAkB,CAAC,MAAM,EAAE;YACnD,kBAAkB,GAAG,CAAC;;AAExB,QAAA,OAAO,kBAAkB,CAAC,kBAAkB,CAAC;;IAG/C,QAAQ,GAAA;AACN,QAAA,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE;;IAGhC,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE;;IAG3B,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE;AAC7B,QAAA,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE;;AA9HzB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,OAAO,kBAuDR,wBAAwB,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAvDvB,OAAO,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,EAAA,MAAA,EAAA,CAAA,eAAA,EAAA,QAAA,CAAA,EAAA,KAAA,EAAA,CAAA,cAAA,EAAA,OAAA,CAAA,EAAA,SAAA,EAAA,CAAA,kBAAA,EAAA,WAAA,CAAA,EAAA,YAAA,EAAA,CAAA,qBAAA,EAAA,cAAA,EAwC+B,gBAAgB,CAAA,EAAA,QAAA,EAAA,CAAA,iBAAA,EAAA,UAAA,EAIpB,gBAAgB,CAAA,EAAA,EAAA,OAAA,EAAA,EAAA,UAAA,EAAA,eAAA,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,UAAA,EAAA,EAAA,QAAA,EAAA,CAAA,SAAA,CAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FA5ClD,OAAO,EAAA,UAAA,EAAA,CAAA;kBAPnB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,WAAW;AACrB,oBAAA,QAAQ,EAAE,SAAS;AACnB,oBAAA,IAAI,EAAE;AACJ,wBAAA,OAAO,EAAE,UAAU;AACpB,qBAAA;AACF,iBAAA;;0BAuDI;;0BACA,MAAM;2BAAC,wBAAwB;yCA7CV,MAAM,EAAA,CAAA;sBAA7B,KAAK;uBAAC,eAAe;gBAMC,KAAK,EAAA,CAAA;sBAA3B,KAAK;uBAAC,cAAc;gBAIjB,SAAS,EAAA,CAAA;sBADZ,KAAK;uBAAC,kBAAkB;gBAsBzB,YAAY,EAAA,CAAA;sBADX,KAAK;AAAC,gBAAA,IAAA,EAAA,CAAA,EAAC,KAAK,EAAE,qBAAqB,EAAE,SAAS,EAAE,gBAAgB,EAAC;gBAKlE,QAAQ,EAAA,CAAA;sBADP,KAAK;AAAC,gBAAA,IAAA,EAAA,CAAA,EAAC,KAAK,EAAE,iBAAiB,EAAE,SAAS,EAAE,gBAAgB,EAAC;gBAI5B,UAAU,EAAA,CAAA;sBAA3C,MAAM;uBAAC,eAAe;;AAkFzB;AACA,SAAS,qBAAqB,CAAC,KAAoB,EAAE,YAAqB,EAAA;AACxE,IAAA,IAAI,SAAS,GAAoB,CAAC,KAAK,EAAE,MAAM,CAAC;AAChD,IAAA,IAAI,KAAK,IAAI,MAAM,EAAE;QACnB,SAAS,CAAC,OAAO,EAAE;;IAErB,IAAI,CAAC,YAAY,EAAE;AACjB,QAAA,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC;;AAGpB,IAAA,OAAO,SAAS;AAClB;;AC5MA;;;AAGG;MAEU,iBAAiB,CAAA;AAC5B;;;AAGG;AACM,IAAA,OAAO,GAAkB,IAAI,OAAO,EAAQ;uGAL1C,iBAAiB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;AAAjB,IAAA,OAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,iBAAiB,cADL,MAAM,EAAA,CAAA;;2FAClB,iBAAiB,EAAA,UAAA,EAAA,CAAA;kBAD7B,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC;;AAShC;;;;AAIG;AACG,SAAU,qCAAqC,CAAC,UAA6B,EAAA;AACjF,IAAA,OAAO,UAAU,IAAI,IAAI,iBAAiB,EAAE;AAC9C;AAEA;;;;AAIG;AACU,MAAA,6BAA6B,GAAG;;AAE3C,IAAA,OAAO,EAAE,iBAAiB;AAC1B,IAAA,IAAI,EAAE,CAAC,CAAC,IAAI,QAAQ,EAAE,EAAE,IAAI,QAAQ,EAAE,EAAE,iBAAiB,CAAC,CAAC;AAC3D,IAAA,UAAU,EAAE,qCAAqC;;;AC0BnD;;;;;;;;AAQG;MAiBU,aAAa,CAAA;AACxB,IAAA,KAAK,GAAG,MAAM,CAAC,iBAAiB,CAAC;IACjC,KAAK,GAAG,MAAM,CAAC,OAAO,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAE;AAC1C,IAAA,UAAU,GAAG,MAAM,CAAyB,4BAAmC,EAAE;AAC/E,QAAA,QAAQ,EAAE,IAAI;AACf,KAAA,CAAC;AACM,IAAA,kBAAkB,GAAG,MAAM,CAAC,iBAAiB,CAAC;AAC9C,IAAA,aAAa,GAAG,MAAM,CAAC,YAAY,CAAC;AACpC,IAAA,WAAW,GAAG,MAAM,CAA0B,UAAU,CAAC;IACzD,cAAc,GAAG,MAAM,CAAC,aAAa,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;AACxD,IAAA,cAAc;IACZ,gBAAgB,GAAG,MAAM,CAAC,qBAAqB,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;AAE5E;;;AAGG;AACO,IAAA,gBAAgB,GAAG,MAAM,CAAuB,IAAI,CAAC;AAE/D;;;AAGG;AACK,IAAA,WAAW;AAEnB;;;AAGG;AACuB,IAAA,EAAE;;IAGnB,aAAa,GAA4B,OAAO;;AAGhD,IAAA,KAAK;;IAId,QAAQ,GAAY,KAAK;AAEzB;;;AAGG;AACH,IAAA,IACI,qBAAqB,GAAA;QACvB,OAAO,IAAI,CAAC,sBAAsB;;IAEpC,IAAI,qBAAqB,CAAC,KAAa,EAAA;AACrC,QAAA,IAAI,CAAC,4BAA4B,CAAC,KAAK,CAAC;;;;;IAKlC,sBAAsB,GAAW,MAAM;;AAI/C,IAAA,YAAY;AAIZ,IAAA,WAAA,GAAA;QACE,MAAM,CAAC,sBAAsB,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC;AAC5D,QAAA,MAAM,cAAc,GAAG,MAAM,CAAwB,wBAAwB,EAAE;AAC7E,YAAA,QAAQ,EAAE,IAAI;AACf,SAAA,CAAC;;;;;AAMF,QAAA,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAAE;YAClE,MAAM,wCAAwC,EAAE;;AAGlD,QAAA,IAAI,cAAc,EAAE,aAAa,EAAE;AACjC,YAAA,IAAI,CAAC,aAAa,GAAG,cAAc,EAAE,aAAa;;;IAItD,QAAQ,GAAA;QACN,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,UAAU,EAAE;YAC/B,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI;;AAGhC,QAAA,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC;AACzB,QAAA,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,SAAS,CAAC,MACrF,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CACvC;AACD,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,aAAa,CAAC,4BAA4B,CAAE;AAC9F,QAAA,IAAI,CAAC,4BAA4B,CAAC,IAAI,CAAC,sBAAsB,CAAC;;IAGhE,eAAe,GAAA;;;AAGb,QAAA,IAAI,CAAC;AACF,aAAA,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI;AAC9B,aAAA,SAAS,CAAC,MAAM,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;;IAGrD,WAAW,GAAA;QACT,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,IAAI,CAAC,WAAW,CAAC;AACnD,QAAA,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC;AAC3B,QAAA,IAAI,CAAC,cAAc,EAAE,WAAW,EAAE;AAElC,QAAA,IAAI,IAAI,CAAC,WAAW,EAAE;AACpB,YAAA,IAAI,CAAC,cAAc,EAAE,iBAAiB,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,sBAAsB,CAAC;;;;IAKzF,oBAAoB,GAAA;AAClB,QAAA,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE;AACvB,YAAA,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,EAAE;AAClC,YAAA,MAAM,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS;AAC1C,YAAA,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC;YACrB,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,GAAG,aAAa,GAAG,IAAI,CAAC;;;AAIpF,IAAA,cAAc,CAAC,KAAoB,EAAA;AACjC,QAAA,IAAI,KAAK,CAAC,OAAO,KAAK,KAAK,IAAI,KAAK,CAAC,OAAO,KAAK,KAAK,EAAE;YACtD,KAAK,CAAC,cAAc,EAAE;YACtB,IAAI,CAAC,oBAAoB,EAAE;;;;IAK/B,SAAS,GAAA;QACP,QACE,IAAI,CAAC,KAAK,CAAC,MAAM,IAAI,IAAI,CAAC,EAAE;AAC5B,aAAC,IAAI,CAAC,KAAK,CAAC,SAAS,KAAK,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,KAAK,MAAM,CAAC;;IAIvE,WAAW,GAAA;QACT,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ;;AAG7C;;;;;AAKG;IACH,qBAAqB,GAAA;AACnB,QAAA,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE;AACrB,YAAA,OAAO,MAAM;;AAGf,QAAA,OAAO,IAAI,CAAC,KAAK,CAAC,SAAS,IAAI,KAAK,GAAG,WAAW,GAAG,YAAY;;;IAInE,YAAY,GAAA;QACV,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,IAAI,CAAC,SAAS,EAAE;;AAGxC,IAAA,4BAA4B,CAAC,cAAsB,EAAA;;;;;;AAOzD,QAAA,IAAI,IAAI,CAAC,WAAW,EAAE;;;AAGpB,YAAA,IAAI,CAAC,cAAc,EAAE,iBAAiB,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,sBAAsB,CAAC;YACrF,IAAI,CAAC,cAAc,EAAE,QAAQ,CAAC,IAAI,CAAC,WAAW,EAAE,cAAc,CAAC;;AAGjE,QAAA,IAAI,CAAC,sBAAsB,GAAG,cAAc;;uGA/KnC,aAAa,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAb,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,aAAa,EAsCL,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,mBAAA,EAAA,MAAA,EAAA,EAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,IAAA,CAAA,EAAA,aAAA,EAAA,eAAA,EAAA,KAAA,EAAA,OAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAAA,gBAAgB,CAoBhB,EAAA,qBAAA,EAAA,uBAAA,EAAA,YAAA,EAAA,CAAA,cAAA,EAAA,cAAA,EAAA,gBAAgB,0VCvJrC,qrEAwCA,EAAA,MAAA,EAAA,CAAA,g0EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FDqDa,aAAa,EAAA,UAAA,EAAA,CAAA;kBAhBzB,SAAS;+BACE,mBAAmB,EAAA,QAAA,EACnB,eAAe,EAGnB,IAAA,EAAA;AACJ,wBAAA,OAAO,EAAE,iBAAiB;AAC1B,wBAAA,SAAS,EAAE,wBAAwB;AACnC,wBAAA,WAAW,EAAE,wBAAwB;AACrC,wBAAA,cAAc,EAAE,4BAA4B;AAC5C,wBAAA,kBAAkB,EAAE,yBAAyB;AAC7C,wBAAA,kCAAkC,EAAE,eAAe;AACpD,qBAAA,EAAA,aAAA,EACc,iBAAiB,CAAC,IAAI,EACpB,eAAA,EAAA,uBAAuB,CAAC,MAAM,EAAA,QAAA,EAAA,qrEAAA,EAAA,MAAA,EAAA,CAAA,g0EAAA,CAAA,EAAA;wDA+BrB,EAAE,EAAA,CAAA;sBAA3B,KAAK;uBAAC,iBAAiB;gBAGf,aAAa,EAAA,CAAA;sBAArB;gBAGQ,KAAK,EAAA,CAAA;sBAAb;gBAID,QAAQ,EAAA,CAAA;sBADP,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBAQhC,qBAAqB,EAAA,CAAA;sBADxB;gBAcD,YAAY,EAAA,CAAA;sBADX,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;;;MEpIzB,aAAa,CAAA;uGAAb,aAAa,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;wGAAb,aAAa,EAAA,OAAA,EAAA,CAJd,eAAe,EAAE,OAAO,EAAE,aAAa,CAAA,EAAA,OAAA,EAAA,CACvC,OAAO,EAAE,aAAa,CAAA,EAAA,CAAA;AAGrB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,aAAa,EAFb,SAAA,EAAA,CAAC,6BAA6B,CAAC,YAFhC,eAAe,CAAA,EAAA,CAAA;;2FAId,aAAa,EAAA,UAAA,EAAA,CAAA;kBALzB,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,OAAO,EAAE,CAAC,eAAe,EAAE,OAAO,EAAE,aAAa,CAAC;AAClD,oBAAA,OAAO,EAAE,CAAC,OAAO,EAAE,aAAa,CAAC;oBACjC,SAAS,EAAE,CAAC,6BAA6B,CAAC;AAC3C,iBAAA;;;ACVD;;;;;AAKG;AACU,MAAA,iBAAiB,GAO1B;;;;;;;;;AAUF,IAAA,SAAS,EAAE;AACT,QAAA,IAAI,EAAE,CAAC;AACP,QAAA,IAAI,EAAE,WAAW;AACjB,QAAA,WAAW,EAAE;AACX,YAAA;AACE,gBAAA,IAAI,EAAE,CAAC;AACP,gBAAA,IAAI,EAAE,iBAAiB;AACvB,gBAAA,MAAM,EAAE,EAAC,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,EAAC,SAAS,EAAE,iBAAiB,EAAC,EAAE,MAAM,EAAE,IAAI,EAAC;AACxE,aAAA;AACD,YAAA;AACE,gBAAA,IAAI,EAAE,CAAC;AACP,gBAAA,IAAI,EAAE,mBAAmB;AACzB,gBAAA,MAAM,EAAE,EAAC,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,EAAC,SAAS,EAAE,kBAAkB,EAAC,EAAE,MAAM,EAAE,IAAI,EAAC;AACzE,aAAA;AACD,YAAA;AACE,gBAAA,IAAI,EAAE,CAAC;AACP,gBAAA,IAAI,EAAE,4BAA4B;AAClC,gBAAA,SAAS,EAAE,EAAC,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,mCAAmC,EAAC;AAChF,gBAAA,OAAO,EAAE,IAAI;AACd,aAAA;AACF,SAAA;AACD,QAAA,OAAO,EAAE,EAAE;AACZ,KAAA;;;;;;;;AAUD,IAAA,WAAW,EAAE;AACX,QAAA,IAAI,EAAE,CAAC;AACP,QAAA,IAAI,EAAE,aAAa;AACnB,QAAA,WAAW,EAAE;AACX,YAAA;AACE,gBAAA,IAAI,EAAE,CAAC;AACP,gBAAA,IAAI,EAAE,iBAAiB;AACvB,gBAAA,MAAM,EAAE,EAAC,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,EAAC,SAAS,EAAE,gBAAgB,EAAC,EAAE,MAAM,EAAE,IAAI,EAAC;AACvE,aAAA;AACD,YAAA;AACE,gBAAA,IAAI,EAAE,CAAC;AACP,gBAAA,IAAI,EAAE,mBAAmB;AACzB,gBAAA,MAAM,EAAE,EAAC,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,EAAC,SAAS,EAAE,eAAe,EAAC,EAAE,MAAM,EAAE,IAAI,EAAC;AACtE,aAAA;AACD,YAAA;AACE,gBAAA,IAAI,EAAE,CAAC;AACP,gBAAA,IAAI,EAAE,4BAA4B;AAClC,gBAAA,SAAS,EAAE,EAAC,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,mCAAmC,EAAC;AAChF,gBAAA,OAAO,EAAE,IAAI;AACd,aAAA;AACF,SAAA;AACD,QAAA,OAAO,EAAE,EAAE;AACZ,KAAA;;;;;;;;AAUD,IAAA,YAAY,EAAE;AACZ,QAAA,IAAI,EAAE,CAAC;AACP,QAAA,IAAI,EAAE,cAAc;AACpB,QAAA,WAAW,EAAE;AACX,YAAA;AACE,gBAAA,IAAI,EAAE,CAAC;AACP,gBAAA,IAAI,EAAE,iBAAiB;AACvB,gBAAA,MAAM,EAAE,EAAC,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,EAAC,SAAS,EAAE,eAAe,EAAC,EAAE,MAAM,EAAE,IAAI,EAAC;AACtE,aAAA;AACD,YAAA;AACE,gBAAA,IAAI,EAAE,CAAC;AACP,gBAAA,IAAI,EAAE,mBAAmB;AACzB,gBAAA,MAAM,EAAE,EAAC,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,EAAC,SAAS,EAAE,gBAAgB,EAAC,EAAE,MAAM,EAAE,IAAI,EAAC;AACvE,aAAA;AACD,YAAA;AACE,gBAAA,IAAI,EAAE,CAAC;AACP,gBAAA,IAAI,EAAE,4BAA4B;AAClC,gBAAA,SAAS,EAAE,EAAC,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,mCAAmC,EAAC;AAChF,gBAAA,OAAO,EAAE,IAAI;AACd,aAAA;AACF,SAAA;AACD,QAAA,OAAO,EAAE,EAAE;AACZ,KAAA;;;;;;;;;;;;;;AAgBD,IAAA,YAAY,EAAE;AACZ,QAAA,IAAI,EAAE,CAAC;AACP,QAAA,IAAI,EAAE,cAAc;AACpB,QAAA,WAAW,EAAE;AACX,YAAA;AACE,gBAAA,IAAI,EAAE,CAAC;AACP,gBAAA,IAAI,EAAE,uCAAuC;AAC7C,gBAAA,MAAM,EAAE,EAAC,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,EAAC,SAAS,EAAE,CAAC,EAAC,EAAE,MAAM,EAAE,IAAI,EAAC;AACxD,aAAA;AACD,YAAA;AACE,gBAAA,IAAI,EAAE,CAAC;AACP,gBAAA,IAAI,EAAE,iCAAiC;AACvC,gBAAA,MAAM,EAAE,EAAC,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,EAAC,SAAS,EAAE,IAAI,EAAC,EAAE,MAAM,EAAE,IAAI,EAAC;AAC3D,aAAA;AACD,YAAA;AACE,gBAAA,IAAI,EAAE,CAAC;AACP,gBAAA,IAAI,EAAE,2EAA2E;AACjF,gBAAA,MAAM,EAAE,EAAC,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,EAAC,SAAS,EAAE,CAAC,EAAC,EAAE,MAAM,EAAE,IAAI,EAAC;AACxD,aAAA;AACD,YAAA;AACE,gBAAA,IAAI,EAAE,CAAC;AACP,gBAAA,IAAI,EAAE,wDAAwD;AAC9D,gBAAA,SAAS,EAAE,EAAC,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAC;AAClD,gBAAA,OAAO,EAAE,IAAI;AACd,aAAA;AACD,YAAA;AACE,gBAAA,IAAI,EAAE,CAAC;AACP,gBAAA,IAAI,EAAE,SAAS;AACf,gBAAA,SAAS,EAAE,EAAC,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,mCAAmC,EAAC;AAChF,gBAAA,OAAO,EAAE,IAAI;AACd,aAAA;AACF,SAAA;AACD,QAAA,OAAO,EAAE,EAAE;AACZ,KAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4CD;;;;;;AAMG;AACH,IAAA,aAAa,EAAE;AACb,QAAA,IAAI,EAAE,CAAC;AACP,QAAA,IAAI,EAAE,eAAe;AACrB,QAAA,WAAW,EAAE;AACX,YAAA;AACE,gBAAA,IAAI,EAAE,CAAC;AACP,gBAAA,IAAI,EAAE,wCAAwC;AAC9C,gBAAA,SAAS,EAAE;AACT,oBAAA,IAAI,EAAE,CAAC;AACP,oBAAA,MAAM,EAAE;AACN,wBAAA,IAAI,EAAE,CAAC;AACP,wBAAA,OAAO,EAAE;AACP,4BAAA,EAAC,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,EAAC,SAAS,EAAE,kBAAkB,EAAC,EAAE,MAAM,EAAE,IAAI,EAAC;AAChE,4BAAA,EAAC,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,EAAC,SAAS,EAAE,eAAe,EAAC,EAAE,MAAM,EAAE,IAAI,EAAC;AAC9D,yBAAA;AACF,qBAAA;AACD,oBAAA,OAAO,EAAE,mCAAmC;AAC7C,iBAAA;AACD,gBAAA,OAAO,EAAE,IAAI;AACd,aAAA;AACD,YAAA;AACE,gBAAA,IAAI,EAAE,CAAC;AACP,gBAAA,IAAI,EAAE,wCAAwC;AAC9C,gBAAA,SAAS,EAAE;AACT,oBAAA,IAAI,EAAE,CAAC;AACP,oBAAA,MAAM,EAAE;AACN,wBAAA,IAAI,EAAE,CAAC;AACP,wBAAA,OAAO,EAAE;AACP,4BAAA,EAAC,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,EAAC,SAAS,EAAE,eAAe,EAAC,EAAE,MAAM,EAAE,IAAI,EAAC;AAC7D,4BAAA,EAAC,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,EAAC,SAAS,EAAE,iBAAiB,EAAC,EAAE,MAAM,EAAE,IAAI,EAAC;AAChE,yBAAA;AACF,qBAAA;AACD,oBAAA,OAAO,EAAE,mCAAmC;AAC7C,iBAAA;AACD,gBAAA,OAAO,EAAE,IAAI;AACd,aAAA;AACD,YAAA;AACE,gBAAA,IAAI,EAAE,CAAC;AACP,gBAAA,IAAI,EAAE,sCAAsC;AAC5C,gBAAA,SAAS,EAAE;AACT,oBAAA,IAAI,EAAE,CAAC;AACP,oBAAA,MAAM,EAAE;AACN,wBAAA,IAAI,EAAE,CAAC;AACP,wBAAA,OAAO,EAAE;AACP,4BAAA,EAAC,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,EAAC,SAAS,EAAE,iBAAiB,EAAC,EAAE,MAAM,EAAE,IAAI,EAAC;AAC/D,4BAAA,EAAC,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,EAAC,SAAS,EAAE,eAAe,EAAC,EAAE,MAAM,EAAE,IAAI,EAAC;AAC9D,yBAAA;AACF,qBAAA;AACD,oBAAA,OAAO,EAAE,mCAAmC;AAC7C,iBAAA;AACD,gBAAA,OAAO,EAAE,IAAI;AACd,aAAA;AACD,YAAA;AACE,gBAAA,IAAI,EAAE,CAAC;AACP,gBAAA,IAAI,EAAE,sCAAsC;AAC5C,gBAAA,SAAS,EAAE;AACT,oBAAA,IAAI,EAAE,CAAC;AACP,oBAAA,MAAM,EAAE;AACN,wBAAA,IAAI,EAAE,CAAC;AACP,wBAAA,OAAO,EAAE;AACP,4BAAA,EAAC,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,EAAC,SAAS,EAAE,eAAe,EAAC,EAAE,MAAM,EAAE,IAAI,EAAC;AAC7D,4BAAA,EAAC,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,EAAC,SAAS,EAAE,kBAAkB,EAAC,EAAE,MAAM,EAAE,IAAI,EAAC;AACjE,yBAAA;AACF,qBAAA;AACD,oBAAA,OAAO,EAAE,mCAAmC;AAC7C,iBAAA;AACD,gBAAA,OAAO,EAAE,IAAI;AACd,aAAA;AACD,YAAA;AACE,gBAAA,IAAI,EAAE,CAAC;AACP,gBAAA,IAAI,EAAE,wEAAwE;AAC9E,gBAAA,MAAM,EAAE,EAAC,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,EAAC,SAAS,EAAE,eAAe,EAAC,EAAE,MAAM,EAAE,IAAI,EAAC;AACtE,aAAA;AACD,YAAA;AACE,gBAAA,IAAI,EAAE,CAAC;AACP,gBAAA,IAAI,EAAE,oCAAoC;AAC1C,gBAAA,MAAM,EAAE,EAAC,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,EAAC,SAAS,EAAE,kBAAkB,EAAC,EAAE,MAAM,EAAE,IAAI,EAAC;AACzE,aAAA;AACD,YAAA;AACE,gBAAA,IAAI,EAAE,CAAC;AACP,gBAAA,IAAI,EAAE,iCAAiC;AACvC,gBAAA,MAAM,EAAE,EAAC,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,EAAC,SAAS,EAAE,iBAAiB,EAAC,EAAE,MAAM,EAAE,IAAI,EAAC;AACxE,aAAA;AACF,SAAA;AACD,QAAA,OAAO,EAAE,EAAE;AACZ,KAAA;;;;;;AAQD,IAAA,aAAa,EAAE;AACb,QAAA,IAAI,EAAE,CAAC;AACP,QAAA,IAAI,EAAE,eAAe;AACrB,QAAA,WAAW,EAAE;AACX,YAAA;AACE,gBAAA,IAAI,EAAE,CAAC;AACP,gBAAA,IAAI,EAAE,SAAS;AACf,gBAAA,SAAS,EAAE;AACT,oBAAA;AACE,wBAAA,IAAI,EAAE,EAAE;AACR,wBAAA,QAAQ,EAAE,IAAI;wBACd,SAAS,EAAE,EAAC,IAAI,EAAE,CAAC,EAAE,OAAO,EAAE,IAAI,EAAC;AACnC,wBAAA,OAAO,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC;AAC1B,qBAAA;AACF,iBAAA;AACD,gBAAA,OAAO,EAAE,IAAI;AACd,aAAA;AACF,SAAA;AACD,QAAA,OAAO,EAAE,EAAE;AACZ,KAAA;;;;;"}