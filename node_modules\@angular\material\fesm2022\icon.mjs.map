{"version": 3, "file": "icon.mjs", "sources": ["../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/icon/icon.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/icon/icon-module.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {DOCUMENT} from '@angular/common';\nimport {\n  AfterViewChecked,\n  booleanAttribute,\n  ChangeDetectionStrategy,\n  Component,\n  ElementRef,\n  ErrorHandler,\n  inject,\n  InjectionToken,\n  Input,\n  OnDestroy,\n  OnInit,\n  ViewEncapsulation,\n  HostAttributeToken,\n} from '@angular/core';\nimport {ThemePalette} from '../core';\nimport {Subscription} from 'rxjs';\nimport {take} from 'rxjs/operators';\n\nimport {MatIconRegistry} from './icon-registry';\n\n/** Default options for `mat-icon`.  */\nexport interface MatIconDefaultOptions {\n  /**\n   * Theme color of the icon. This API is supported in M2 themes only, it\n   * has no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/icon/styling.\n   *\n   * For information on applying color variants in M3, see\n   * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n   */\n  color?: ThemePalette;\n  /** <PERSON>ont set that the icon is a part of. */\n  fontSet?: string;\n}\n\n/** Injection token to be used to override the default options for `mat-icon`. */\nexport const MAT_ICON_DEFAULT_OPTIONS = new InjectionToken<MatIconDefaultOptions>(\n  'MAT_ICON_DEFAULT_OPTIONS',\n);\n\n/**\n * Injection token used to provide the current location to `MatIcon`.\n * Used to handle server-side rendering and to stub out during unit tests.\n * @docs-private\n */\nexport const MAT_ICON_LOCATION = new InjectionToken<MatIconLocation>('mat-icon-location', {\n  providedIn: 'root',\n  factory: MAT_ICON_LOCATION_FACTORY,\n});\n\n/**\n * Stubbed out location for `MatIcon`.\n * @docs-private\n */\nexport interface MatIconLocation {\n  getPathname: () => string;\n}\n\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nexport function MAT_ICON_LOCATION_FACTORY(): MatIconLocation {\n  const _document = inject(DOCUMENT);\n  const _location = _document ? _document.location : null;\n\n  return {\n    // Note that this needs to be a function, rather than a property, because Angular\n    // will only resolve it once, but we want the current path on each call.\n    getPathname: () => (_location ? _location.pathname + _location.search : ''),\n  };\n}\n\n/** SVG attributes that accept a FuncIRI (e.g. `url(<something>)`). */\nconst funcIriAttributes = [\n  'clip-path',\n  'color-profile',\n  'src',\n  'cursor',\n  'fill',\n  'filter',\n  'marker',\n  'marker-start',\n  'marker-mid',\n  'marker-end',\n  'mask',\n  'stroke',\n];\n\n/** Selector that can be used to find all elements that are using a `FuncIRI`. */\nconst funcIriAttributeSelector = funcIriAttributes.map(attr => `[${attr}]`).join(', ');\n\n/** Regex that can be used to extract the id out of a FuncIRI. */\nconst funcIriPattern = /^url\\(['\"]?#(.*?)['\"]?\\)$/;\n\n/**\n * Component to display an icon. It can be used in the following ways:\n *\n * - Specify the svgIcon input to load an SVG icon from a URL previously registered with the\n *   addSvgIcon, addSvgIconInNamespace, addSvgIconSet, or addSvgIconSetInNamespace methods of\n *   MatIconRegistry. If the svgIcon value contains a colon it is assumed to be in the format\n *   \"[namespace]:[name]\", if not the value will be the name of an icon in the default namespace.\n *   Examples:\n *     `<mat-icon svgIcon=\"left-arrow\"></mat-icon>\n *     <mat-icon svgIcon=\"animals:cat\"></mat-icon>`\n *\n * - Use a font ligature as an icon by putting the ligature text in the `fontIcon` attribute or the\n *   content of the `<mat-icon>` component. If you register a custom font class, don't forget to also\n *   include the special class `mat-ligature-font`. It is recommended to use the attribute alternative\n *   to prevent the ligature text to be selectable and to appear in search engine results.\n *   By default, the Material icons font is used as described at\n *   http://google.github.io/material-design-icons/#icon-font-for-the-web. You can specify an\n *   alternate font by setting the fontSet input to either the CSS class to apply to use the\n *   desired font, or to an alias previously registered with MatIconRegistry.registerFontClassAlias.\n *   Examples:\n *     `<mat-icon fontIcon=\"home\"></mat-icon>\n *     <mat-icon>home</mat-icon>\n *     <mat-icon fontSet=\"myfont\" fontIcon=\"sun\"></mat-icon>\n *     <mat-icon fontSet=\"myfont\">sun</mat-icon>`\n *\n * - Specify a font glyph to be included via CSS rules by setting the fontSet input to specify the\n *   font, and the fontIcon input to specify the icon. Typically the fontIcon will specify a\n *   CSS class which causes the glyph to be displayed via a :before selector, as in\n *   https://fontawesome-v4.github.io/examples/\n *   Example:\n *     `<mat-icon fontSet=\"fa\" fontIcon=\"alarm\"></mat-icon>`\n */\n@Component({\n  template: '<ng-content></ng-content>',\n  selector: 'mat-icon',\n  exportAs: 'matIcon',\n  styleUrl: 'icon.css',\n  host: {\n    'role': 'img',\n    'class': 'mat-icon notranslate',\n    '[class]': 'color ? \"mat-\" + color : \"\"',\n    '[attr.data-mat-icon-type]': '_usingFontIcon() ? \"font\" : \"svg\"',\n    '[attr.data-mat-icon-name]': '_svgName || fontIcon',\n    '[attr.data-mat-icon-namespace]': '_svgNamespace || fontSet',\n    '[attr.fontIcon]': '_usingFontIcon() ? fontIcon : null',\n    '[class.mat-icon-inline]': 'inline',\n    '[class.mat-icon-no-color]': 'color !== \"primary\" && color !== \"accent\" && color !== \"warn\"',\n  },\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n})\nexport class MatIcon implements OnInit, AfterViewChecked, OnDestroy {\n  readonly _elementRef = inject<ElementRef<HTMLElement>>(ElementRef);\n  private _iconRegistry = inject(MatIconRegistry);\n  private _location = inject<MatIconLocation>(MAT_ICON_LOCATION);\n  private readonly _errorHandler = inject(ErrorHandler);\n  private _defaultColor: ThemePalette;\n\n  /**\n   * Theme color of the icon. This API is supported in M2 themes only, it\n   * has no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/icon/styling.\n   *\n   * For information on applying color variants in M3, see\n   * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n   */\n  @Input()\n  get color() {\n    return this._color || this._defaultColor;\n  }\n  set color(value: string | null | undefined) {\n    this._color = value;\n  }\n  private _color: string | null | undefined;\n\n  /**\n   * Whether the icon should be inlined, automatically sizing the icon to match the font size of\n   * the element the icon is contained in.\n   */\n  @Input({transform: booleanAttribute})\n  inline: boolean = false;\n\n  /** Name of the icon in the SVG icon set. */\n  @Input()\n  get svgIcon(): string {\n    return this._svgIcon;\n  }\n  set svgIcon(value: string) {\n    if (value !== this._svgIcon) {\n      if (value) {\n        this._updateSvgIcon(value);\n      } else if (this._svgIcon) {\n        this._clearSvgElement();\n      }\n      this._svgIcon = value;\n    }\n  }\n  private _svgIcon: string;\n\n  /** Font set that the icon is a part of. */\n  @Input()\n  get fontSet(): string {\n    return this._fontSet;\n  }\n  set fontSet(value: string) {\n    const newValue = this._cleanupFontValue(value);\n\n    if (newValue !== this._fontSet) {\n      this._fontSet = newValue;\n      this._updateFontIconClasses();\n    }\n  }\n  private _fontSet: string;\n\n  /** Name of an icon within a font set. */\n  @Input()\n  get fontIcon(): string {\n    return this._fontIcon;\n  }\n  set fontIcon(value: string) {\n    const newValue = this._cleanupFontValue(value);\n\n    if (newValue !== this._fontIcon) {\n      this._fontIcon = newValue;\n      this._updateFontIconClasses();\n    }\n  }\n  private _fontIcon: string;\n\n  private _previousFontSetClass: string[] = [];\n  private _previousFontIconClass: string;\n\n  _svgName: string | null;\n  _svgNamespace: string | null;\n\n  /** Keeps track of the current page path. */\n  private _previousPath?: string;\n\n  /** Keeps track of the elements and attributes that we've prefixed with the current path. */\n  private _elementsWithExternalReferences?: Map<Element, {name: string; value: string}[]>;\n\n  /** Subscription to the current in-progress SVG icon request. */\n  private _currentIconFetch = Subscription.EMPTY;\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    const ariaHidden = inject(new HostAttributeToken('aria-hidden'), {optional: true});\n    const defaults = inject<MatIconDefaultOptions>(MAT_ICON_DEFAULT_OPTIONS, {optional: true});\n\n    if (defaults) {\n      if (defaults.color) {\n        this.color = this._defaultColor = defaults.color;\n      }\n\n      if (defaults.fontSet) {\n        this.fontSet = defaults.fontSet;\n      }\n    }\n\n    // If the user has not explicitly set aria-hidden, mark the icon as hidden, as this is\n    // the right thing to do for the majority of icon use-cases.\n    if (!ariaHidden) {\n      this._elementRef.nativeElement.setAttribute('aria-hidden', 'true');\n    }\n  }\n\n  /**\n   * Splits an svgIcon binding value into its icon set and icon name components.\n   * Returns a 2-element array of [(icon set), (icon name)].\n   * The separator for the two fields is ':'. If there is no separator, an empty\n   * string is returned for the icon set and the entire value is returned for\n   * the icon name. If the argument is falsy, returns an array of two empty strings.\n   * Throws an error if the name contains two or more ':' separators.\n   * Examples:\n   *   `'social:cake' -> ['social', 'cake']\n   *   'penguin' -> ['', 'penguin']\n   *   null -> ['', '']\n   *   'a:b:c' -> (throws Error)`\n   */\n  private _splitIconName(iconName: string): [string, string] {\n    if (!iconName) {\n      return ['', ''];\n    }\n    const parts = iconName.split(':');\n    switch (parts.length) {\n      case 1:\n        return ['', parts[0]]; // Use default namespace.\n      case 2:\n        return <[string, string]>parts;\n      default:\n        throw Error(`Invalid icon name: \"${iconName}\"`); // TODO: add an ngDevMode check\n    }\n  }\n\n  ngOnInit() {\n    // Update font classes because ngOnChanges won't be called if none of the inputs are present,\n    // e.g. <mat-icon>arrow</mat-icon> In this case we need to add a CSS class for the default font.\n    this._updateFontIconClasses();\n  }\n\n  ngAfterViewChecked() {\n    const cachedElements = this._elementsWithExternalReferences;\n\n    if (cachedElements && cachedElements.size) {\n      const newPath = this._location.getPathname();\n\n      // We need to check whether the URL has changed on each change detection since\n      // the browser doesn't have an API that will let us react on link clicks and\n      // we can't depend on the Angular router. The references need to be updated,\n      // because while most browsers don't care whether the URL is correct after\n      // the first render, Safari will break if the user navigates to a different\n      // page and the SVG isn't re-rendered.\n      if (newPath !== this._previousPath) {\n        this._previousPath = newPath;\n        this._prependPathToReferences(newPath);\n      }\n    }\n  }\n\n  ngOnDestroy() {\n    this._currentIconFetch.unsubscribe();\n\n    if (this._elementsWithExternalReferences) {\n      this._elementsWithExternalReferences.clear();\n    }\n  }\n\n  _usingFontIcon(): boolean {\n    return !this.svgIcon;\n  }\n\n  private _setSvgElement(svg: SVGElement) {\n    this._clearSvgElement();\n\n    // Note: we do this fix here, rather than the icon registry, because the\n    // references have to point to the URL at the time that the icon was created.\n    const path = this._location.getPathname();\n    this._previousPath = path;\n    this._cacheChildrenWithExternalReferences(svg);\n    this._prependPathToReferences(path);\n    this._elementRef.nativeElement.appendChild(svg);\n  }\n\n  private _clearSvgElement() {\n    const layoutElement: HTMLElement = this._elementRef.nativeElement;\n    let childCount = layoutElement.childNodes.length;\n\n    if (this._elementsWithExternalReferences) {\n      this._elementsWithExternalReferences.clear();\n    }\n\n    // Remove existing non-element child nodes and SVGs, and add the new SVG element. Note that\n    // we can't use innerHTML, because IE will throw if the element has a data binding.\n    while (childCount--) {\n      const child = layoutElement.childNodes[childCount];\n\n      // 1 corresponds to Node.ELEMENT_NODE. We remove all non-element nodes in order to get rid\n      // of any loose text nodes, as well as any SVG elements in order to remove any old icons.\n      if (child.nodeType !== 1 || child.nodeName.toLowerCase() === 'svg') {\n        child.remove();\n      }\n    }\n  }\n\n  private _updateFontIconClasses() {\n    if (!this._usingFontIcon()) {\n      return;\n    }\n\n    const elem: HTMLElement = this._elementRef.nativeElement;\n    const fontSetClasses = (\n      this.fontSet\n        ? this._iconRegistry.classNameForFontAlias(this.fontSet).split(/ +/)\n        : this._iconRegistry.getDefaultFontSetClass()\n    ).filter(className => className.length > 0);\n\n    this._previousFontSetClass.forEach(className => elem.classList.remove(className));\n    fontSetClasses.forEach(className => elem.classList.add(className));\n    this._previousFontSetClass = fontSetClasses;\n\n    if (\n      this.fontIcon !== this._previousFontIconClass &&\n      !fontSetClasses.includes('mat-ligature-font')\n    ) {\n      if (this._previousFontIconClass) {\n        elem.classList.remove(this._previousFontIconClass);\n      }\n      if (this.fontIcon) {\n        elem.classList.add(this.fontIcon);\n      }\n      this._previousFontIconClass = this.fontIcon;\n    }\n  }\n\n  /**\n   * Cleans up a value to be used as a fontIcon or fontSet.\n   * Since the value ends up being assigned as a CSS class, we\n   * have to trim the value and omit space-separated values.\n   */\n  private _cleanupFontValue(value: string) {\n    return typeof value === 'string' ? value.trim().split(' ')[0] : value;\n  }\n\n  /**\n   * Prepends the current path to all elements that have an attribute pointing to a `FuncIRI`\n   * reference. This is required because WebKit browsers require references to be prefixed with\n   * the current path, if the page has a `base` tag.\n   */\n  private _prependPathToReferences(path: string) {\n    const elements = this._elementsWithExternalReferences;\n\n    if (elements) {\n      elements.forEach((attrs, element) => {\n        attrs.forEach(attr => {\n          element.setAttribute(attr.name, `url('${path}#${attr.value}')`);\n        });\n      });\n    }\n  }\n\n  /**\n   * Caches the children of an SVG element that have `url()`\n   * references that we need to prefix with the current path.\n   */\n  private _cacheChildrenWithExternalReferences(element: SVGElement) {\n    const elementsWithFuncIri = element.querySelectorAll(funcIriAttributeSelector);\n    const elements = (this._elementsWithExternalReferences =\n      this._elementsWithExternalReferences || new Map());\n\n    for (let i = 0; i < elementsWithFuncIri.length; i++) {\n      funcIriAttributes.forEach(attr => {\n        const elementWithReference = elementsWithFuncIri[i];\n        const value = elementWithReference.getAttribute(attr);\n        const match = value ? value.match(funcIriPattern) : null;\n\n        if (match) {\n          let attributes = elements.get(elementWithReference);\n\n          if (!attributes) {\n            attributes = [];\n            elements.set(elementWithReference, attributes);\n          }\n\n          attributes!.push({name: attr, value: match[1]});\n        }\n      });\n    }\n  }\n\n  /** Sets a new SVG icon with a particular name. */\n  private _updateSvgIcon(rawName: string | undefined) {\n    this._svgNamespace = null;\n    this._svgName = null;\n    this._currentIconFetch.unsubscribe();\n\n    if (rawName) {\n      const [namespace, iconName] = this._splitIconName(rawName);\n\n      if (namespace) {\n        this._svgNamespace = namespace;\n      }\n\n      if (iconName) {\n        this._svgName = iconName;\n      }\n\n      this._currentIconFetch = this._iconRegistry\n        .getNamedSvgIcon(iconName, namespace)\n        .pipe(take(1))\n        .subscribe(\n          svg => this._setSvgElement(svg),\n          (err: Error) => {\n            const errorMessage = `Error retrieving icon ${namespace}:${iconName}! ${err.message}`;\n            this._errorHandler.handleError(new Error(errorMessage));\n          },\n        );\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {NgModule} from '@angular/core';\nimport {MatCommonModule} from '../core';\nimport {MatIcon} from './icon';\n\n@NgModule({\n  imports: [MatCommonModule, MatIcon],\n  exports: [MatIcon, MatCommonModule],\n})\nexport class MatIconModule {}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AA4CA;MACa,wBAAwB,GAAG,IAAI,cAAc,CACxD,0BAA0B;AAG5B;;;;AAIG;MACU,iBAAiB,GAAG,IAAI,cAAc,CAAkB,mBAAmB,EAAE;AACxF,IAAA,UAAU,EAAE,MAAM;AAClB,IAAA,OAAO,EAAE,yBAAyB;AACnC,CAAA;AAUD;;;;AAIG;SACa,yBAAyB,GAAA;AACvC,IAAA,MAAM,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC;AAClC,IAAA,MAAM,SAAS,GAAG,SAAS,GAAG,SAAS,CAAC,QAAQ,GAAG,IAAI;IAEvD,OAAO;;;QAGL,WAAW,EAAE,OAAO,SAAS,GAAG,SAAS,CAAC,QAAQ,GAAG,SAAS,CAAC,MAAM,GAAG,EAAE,CAAC;KAC5E;AACH;AAEA;AACA,MAAM,iBAAiB,GAAG;IACxB,WAAW;IACX,eAAe;IACf,KAAK;IACL,QAAQ;IACR,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,cAAc;IACd,YAAY;IACZ,YAAY;IACZ,MAAM;IACN,QAAQ;CACT;AAED;AACA,MAAM,wBAAwB,GAAG,iBAAiB,CAAC,GAAG,CAAC,IAAI,IAAI,CAAI,CAAA,EAAA,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;AAEtF;AACA,MAAM,cAAc,GAAG,2BAA2B;AAElD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+BG;MAoBU,OAAO,CAAA;AACT,IAAA,WAAW,GAAG,MAAM,CAA0B,UAAU,CAAC;AAC1D,IAAA,aAAa,GAAG,MAAM,CAAC,eAAe,CAAC;AACvC,IAAA,SAAS,GAAG,MAAM,CAAkB,iBAAiB,CAAC;AAC7C,IAAA,aAAa,GAAG,MAAM,CAAC,YAAY,CAAC;AAC7C,IAAA,aAAa;AAErB;;;;;;AAMG;AACH,IAAA,IACI,KAAK,GAAA;AACP,QAAA,OAAO,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,aAAa;;IAE1C,IAAI,KAAK,CAAC,KAAgC,EAAA;AACxC,QAAA,IAAI,CAAC,MAAM,GAAG,KAAK;;AAEb,IAAA,MAAM;AAEd;;;AAGG;IAEH,MAAM,GAAY,KAAK;;AAGvB,IAAA,IACI,OAAO,GAAA;QACT,OAAO,IAAI,CAAC,QAAQ;;IAEtB,IAAI,OAAO,CAAC,KAAa,EAAA;AACvB,QAAA,IAAI,KAAK,KAAK,IAAI,CAAC,QAAQ,EAAE;YAC3B,IAAI,KAAK,EAAE;AACT,gBAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC;;AACrB,iBAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;gBACxB,IAAI,CAAC,gBAAgB,EAAE;;AAEzB,YAAA,IAAI,CAAC,QAAQ,GAAG,KAAK;;;AAGjB,IAAA,QAAQ;;AAGhB,IAAA,IACI,OAAO,GAAA;QACT,OAAO,IAAI,CAAC,QAAQ;;IAEtB,IAAI,OAAO,CAAC,KAAa,EAAA;QACvB,MAAM,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC;AAE9C,QAAA,IAAI,QAAQ,KAAK,IAAI,CAAC,QAAQ,EAAE;AAC9B,YAAA,IAAI,CAAC,QAAQ,GAAG,QAAQ;YACxB,IAAI,CAAC,sBAAsB,EAAE;;;AAGzB,IAAA,QAAQ;;AAGhB,IAAA,IACI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,SAAS;;IAEvB,IAAI,QAAQ,CAAC,KAAa,EAAA;QACxB,MAAM,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC;AAE9C,QAAA,IAAI,QAAQ,KAAK,IAAI,CAAC,SAAS,EAAE;AAC/B,YAAA,IAAI,CAAC,SAAS,GAAG,QAAQ;YACzB,IAAI,CAAC,sBAAsB,EAAE;;;AAGzB,IAAA,SAAS;IAET,qBAAqB,GAAa,EAAE;AACpC,IAAA,sBAAsB;AAE9B,IAAA,QAAQ;AACR,IAAA,aAAa;;AAGL,IAAA,aAAa;;AAGb,IAAA,+BAA+B;;AAG/B,IAAA,iBAAiB,GAAG,YAAY,CAAC,KAAK;AAI9C,IAAA,WAAA,GAAA;AACE,QAAA,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI,kBAAkB,CAAC,aAAa,CAAC,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;AAClF,QAAA,MAAM,QAAQ,GAAG,MAAM,CAAwB,wBAAwB,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;QAE1F,IAAI,QAAQ,EAAE;AACZ,YAAA,IAAI,QAAQ,CAAC,KAAK,EAAE;gBAClB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,aAAa,GAAG,QAAQ,CAAC,KAAK;;AAGlD,YAAA,IAAI,QAAQ,CAAC,OAAO,EAAE;AACpB,gBAAA,IAAI,CAAC,OAAO,GAAG,QAAQ,CAAC,OAAO;;;;;QAMnC,IAAI,CAAC,UAAU,EAAE;YACf,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC;;;AAItE;;;;;;;;;;;;AAYG;AACK,IAAA,cAAc,CAAC,QAAgB,EAAA;QACrC,IAAI,CAAC,QAAQ,EAAE;AACb,YAAA,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC;;QAEjB,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC;AACjC,QAAA,QAAQ,KAAK,CAAC,MAAM;AAClB,YAAA,KAAK,CAAC;gBACJ,OAAO,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AACxB,YAAA,KAAK,CAAC;AACJ,gBAAA,OAAyB,KAAK;AAChC,YAAA;gBACE,MAAM,KAAK,CAAC,CAAuB,oBAAA,EAAA,QAAQ,GAAG,CAAC,CAAC;;;IAItD,QAAQ,GAAA;;;QAGN,IAAI,CAAC,sBAAsB,EAAE;;IAG/B,kBAAkB,GAAA;AAChB,QAAA,MAAM,cAAc,GAAG,IAAI,CAAC,+BAA+B;AAE3D,QAAA,IAAI,cAAc,IAAI,cAAc,CAAC,IAAI,EAAE;YACzC,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE;;;;;;;AAQ5C,YAAA,IAAI,OAAO,KAAK,IAAI,CAAC,aAAa,EAAE;AAClC,gBAAA,IAAI,CAAC,aAAa,GAAG,OAAO;AAC5B,gBAAA,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC;;;;IAK5C,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,iBAAiB,CAAC,WAAW,EAAE;AAEpC,QAAA,IAAI,IAAI,CAAC,+BAA+B,EAAE;AACxC,YAAA,IAAI,CAAC,+BAA+B,CAAC,KAAK,EAAE;;;IAIhD,cAAc,GAAA;AACZ,QAAA,OAAO,CAAC,IAAI,CAAC,OAAO;;AAGd,IAAA,cAAc,CAAC,GAAe,EAAA;QACpC,IAAI,CAAC,gBAAgB,EAAE;;;QAIvB,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE;AACzC,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI;AACzB,QAAA,IAAI,CAAC,oCAAoC,CAAC,GAAG,CAAC;AAC9C,QAAA,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC;QACnC,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,WAAW,CAAC,GAAG,CAAC;;IAGzC,gBAAgB,GAAA;AACtB,QAAA,MAAM,aAAa,GAAgB,IAAI,CAAC,WAAW,CAAC,aAAa;AACjE,QAAA,IAAI,UAAU,GAAG,aAAa,CAAC,UAAU,CAAC,MAAM;AAEhD,QAAA,IAAI,IAAI,CAAC,+BAA+B,EAAE;AACxC,YAAA,IAAI,CAAC,+BAA+B,CAAC,KAAK,EAAE;;;;QAK9C,OAAO,UAAU,EAAE,EAAE;YACnB,MAAM,KAAK,GAAG,aAAa,CAAC,UAAU,CAAC,UAAU,CAAC;;;AAIlD,YAAA,IAAI,KAAK,CAAC,QAAQ,KAAK,CAAC,IAAI,KAAK,CAAC,QAAQ,CAAC,WAAW,EAAE,KAAK,KAAK,EAAE;gBAClE,KAAK,CAAC,MAAM,EAAE;;;;IAKZ,sBAAsB,GAAA;AAC5B,QAAA,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE;YAC1B;;AAGF,QAAA,MAAM,IAAI,GAAgB,IAAI,CAAC,WAAW,CAAC,aAAa;AACxD,QAAA,MAAM,cAAc,GAAG,CACrB,IAAI,CAAC;AACH,cAAE,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,IAAI;cACjE,IAAI,CAAC,aAAa,CAAC,sBAAsB,EAAE,EAC/C,MAAM,CAAC,SAAS,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;AAE3C,QAAA,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;AACjF,QAAA,cAAc,CAAC,OAAO,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;AAClE,QAAA,IAAI,CAAC,qBAAqB,GAAG,cAAc;AAE3C,QAAA,IACE,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,sBAAsB;AAC7C,YAAA,CAAC,cAAc,CAAC,QAAQ,CAAC,mBAAmB,CAAC,EAC7C;AACA,YAAA,IAAI,IAAI,CAAC,sBAAsB,EAAE;gBAC/B,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,sBAAsB,CAAC;;AAEpD,YAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;gBACjB,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC;;AAEnC,YAAA,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,QAAQ;;;AAI/C;;;;AAIG;AACK,IAAA,iBAAiB,CAAC,KAAa,EAAA;QACrC,OAAO,OAAO,KAAK,KAAK,QAAQ,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK;;AAGvE;;;;AAIG;AACK,IAAA,wBAAwB,CAAC,IAAY,EAAA;AAC3C,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,+BAA+B;QAErD,IAAI,QAAQ,EAAE;YACZ,QAAQ,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,OAAO,KAAI;AAClC,gBAAA,KAAK,CAAC,OAAO,CAAC,IAAI,IAAG;AACnB,oBAAA,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,EAAE,CAAQ,KAAA,EAAA,IAAI,IAAI,IAAI,CAAC,KAAK,CAAA,EAAA,CAAI,CAAC;AACjE,iBAAC,CAAC;AACJ,aAAC,CAAC;;;AAIN;;;AAGG;AACK,IAAA,oCAAoC,CAAC,OAAmB,EAAA;QAC9D,MAAM,mBAAmB,GAAG,OAAO,CAAC,gBAAgB,CAAC,wBAAwB,CAAC;AAC9E,QAAA,MAAM,QAAQ,IAAI,IAAI,CAAC,+BAA+B;AACpD,YAAA,IAAI,CAAC,+BAA+B,IAAI,IAAI,GAAG,EAAE,CAAC;AAEpD,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,mBAAmB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACnD,YAAA,iBAAiB,CAAC,OAAO,CAAC,IAAI,IAAG;AAC/B,gBAAA,MAAM,oBAAoB,GAAG,mBAAmB,CAAC,CAAC,CAAC;gBACnD,MAAM,KAAK,GAAG,oBAAoB,CAAC,YAAY,CAAC,IAAI,CAAC;AACrD,gBAAA,MAAM,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,cAAc,CAAC,GAAG,IAAI;gBAExD,IAAI,KAAK,EAAE;oBACT,IAAI,UAAU,GAAG,QAAQ,CAAC,GAAG,CAAC,oBAAoB,CAAC;oBAEnD,IAAI,CAAC,UAAU,EAAE;wBACf,UAAU,GAAG,EAAE;AACf,wBAAA,QAAQ,CAAC,GAAG,CAAC,oBAAoB,EAAE,UAAU,CAAC;;AAGhD,oBAAA,UAAW,CAAC,IAAI,CAAC,EAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC,EAAC,CAAC;;AAEnD,aAAC,CAAC;;;;AAKE,IAAA,cAAc,CAAC,OAA2B,EAAA;AAChD,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI;AACzB,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI;AACpB,QAAA,IAAI,CAAC,iBAAiB,CAAC,WAAW,EAAE;QAEpC,IAAI,OAAO,EAAE;AACX,YAAA,MAAM,CAAC,SAAS,EAAE,QAAQ,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC;YAE1D,IAAI,SAAS,EAAE;AACb,gBAAA,IAAI,CAAC,aAAa,GAAG,SAAS;;YAGhC,IAAI,QAAQ,EAAE;AACZ,gBAAA,IAAI,CAAC,QAAQ,GAAG,QAAQ;;AAG1B,YAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;AAC3B,iBAAA,eAAe,CAAC,QAAQ,EAAE,SAAS;AACnC,iBAAA,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AACZ,iBAAA,SAAS,CACR,GAAG,IAAI,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,EAC/B,CAAC,GAAU,KAAI;gBACb,MAAM,YAAY,GAAG,CAAA,sBAAA,EAAyB,SAAS,CAAA,CAAA,EAAI,QAAQ,CAAA,EAAA,EAAK,GAAG,CAAC,OAAO,CAAA,CAAE;gBACrF,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC;AACzD,aAAC,CACF;;;uGApUI,OAAO,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAP,OAAO,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,UAAA,EAAA,MAAA,EAAA,EAAA,KAAA,EAAA,OAAA,EAAA,MAAA,EAAA,CAAA,QAAA,EAAA,QAAA,EA2BC,gBAAgB,CAAA,EAAA,OAAA,EAAA,SAAA,EAAA,OAAA,EAAA,SAAA,EAAA,QAAA,EAAA,UAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,MAAA,EAAA,KAAA,EAAA,EAAA,UAAA,EAAA,EAAA,OAAA,EAAA,iCAAA,EAAA,yBAAA,EAAA,uCAAA,EAAA,yBAAA,EAAA,sBAAA,EAAA,8BAAA,EAAA,0BAAA,EAAA,eAAA,EAAA,oCAAA,EAAA,uBAAA,EAAA,QAAA,EAAA,yBAAA,EAAA,qEAAA,EAAA,EAAA,cAAA,EAAA,sBAAA,EAAA,EAAA,QAAA,EAAA,CAAA,SAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EA7CzB,2BAA2B,EAAA,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,+3BAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAkB1B,OAAO,EAAA,UAAA,EAAA,CAAA;kBAnBnB,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,2BAA2B,EAC3B,QAAA,EAAA,UAAU,EACV,QAAA,EAAA,SAAS,EAEb,IAAA,EAAA;AACJ,wBAAA,MAAM,EAAE,KAAK;AACb,wBAAA,OAAO,EAAE,sBAAsB;AAC/B,wBAAA,SAAS,EAAE,6BAA6B;AACxC,wBAAA,2BAA2B,EAAE,mCAAmC;AAChE,wBAAA,2BAA2B,EAAE,sBAAsB;AACnD,wBAAA,gCAAgC,EAAE,0BAA0B;AAC5D,wBAAA,iBAAiB,EAAE,oCAAoC;AACvD,wBAAA,yBAAyB,EAAE,QAAQ;AACnC,wBAAA,2BAA2B,EAAE,+DAA+D;AAC7F,qBAAA,EAAA,aAAA,EACc,iBAAiB,CAAC,IAAI,EACpB,eAAA,EAAA,uBAAuB,CAAC,MAAM,EAAA,MAAA,EAAA,CAAA,+3BAAA,CAAA,EAAA;wDAiB3C,KAAK,EAAA,CAAA;sBADR;gBAcD,MAAM,EAAA,CAAA;sBADL,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBAKhC,OAAO,EAAA,CAAA;sBADV;gBAkBG,OAAO,EAAA,CAAA;sBADV;gBAgBG,QAAQ,EAAA,CAAA;sBADX;;;MC3MU,aAAa,CAAA;uGAAb,aAAa,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;AAAb,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,aAAa,YAHd,eAAe,EAAE,OAAO,CACxB,EAAA,OAAA,EAAA,CAAA,OAAO,EAAE,eAAe,CAAA,EAAA,CAAA;wGAEvB,aAAa,EAAA,OAAA,EAAA,CAHd,eAAe,EACN,eAAe,CAAA,EAAA,CAAA;;2FAEvB,aAAa,EAAA,UAAA,EAAA,CAAA;kBAJzB,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,OAAO,EAAE,CAAC,eAAe,EAAE,OAAO,CAAC;AACnC,oBAAA,OAAO,EAAE,CAAC,OAAO,EAAE,eAAe,CAAC;AACpC,iBAAA;;;;;"}