{"version": 3, "file": "chips.mjs", "sources": ["../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/chips/tokens.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/chips/chip-action.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/chips/chip-icons.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/chips/chip.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/chips/chip.html", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/chips/chip-option.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/chips/chip-option.html", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/chips/chip-edit-input.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/chips/chip-row.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/chips/chip-row.html", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/chips/chip-set.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/chips/chip-listbox.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/chips/chip-grid.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/chips/chip-input.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/chips/module.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {ENTER} from '@angular/cdk/keycodes';\nimport {InjectionToken} from '@angular/core';\n\n/** Default options, for the chips module, that can be overridden. */\nexport interface MatChipsDefaultOptions {\n  /** The list of key codes that will trigger a chipEnd event. */\n  separatorKeyCodes: readonly number[] | ReadonlySet<number>;\n\n  /** Whether icon indicators should be hidden for single-selection. */\n  hideSingleSelectionIndicator?: boolean;\n}\n\n/** Injection token to be used to override the default options for the chips module. */\nexport const MAT_CHIPS_DEFAULT_OPTIONS = new InjectionToken<MatChipsDefaultOptions>(\n  'mat-chips-default-options',\n  {\n    providedIn: 'root',\n    factory: () => ({\n      separatorKeyCodes: [ENTER],\n    }),\n  },\n);\n\n/**\n * Injection token that can be used to reference instances of `MatChipAvatar`. It serves as\n * alternative token to the actual `MatChipAvatar` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nexport const MAT_CHIP_AVATAR = new InjectionToken('MatChipAvatar');\n\n/**\n * Injection token that can be used to reference instances of `MatChipTrailingIcon`. It serves as\n * alternative token to the actual `MatChipTrailingIcon` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nexport const MAT_CHIP_TRAILING_ICON = new InjectionToken('MatChipTrailingIcon');\n\n/**\n * Injection token that can be used to reference instances of `MatChipRemove`. It serves as\n * alternative token to the actual `MatChipRemove` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nexport const MAT_CHIP_REMOVE = new InjectionToken('MatChipRemove');\n\n/**\n * Injection token used to avoid a circular dependency between the `MatChip` and `MatChipAction`.\n */\nexport const MAT_CHIP = new InjectionToken('MatChip');\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  Directive,\n  ElementRef,\n  Input,\n  booleanAttribute,\n  numberAttribute,\n  inject,\n} from '@angular/core';\nimport {ENTER, SPACE} from '@angular/cdk/keycodes';\nimport {MAT_CHIP} from './tokens';\nimport {_CdkPrivateStyleLoader} from '@angular/cdk/private';\nimport {_StructuralStylesLoader} from '../core';\n\n/**\n * Section within a chip.\n * @docs-private\n */\n@Directive({\n  selector: '[matChipAction]',\n  host: {\n    'class': 'mdc-evolution-chip__action mat-mdc-chip-action',\n    '[class.mdc-evolution-chip__action--primary]': '_isPrimary',\n    '[class.mdc-evolution-chip__action--presentational]': '!isInteractive',\n    '[class.mdc-evolution-chip__action--trailing]': '!_isPrimary',\n    '[attr.tabindex]': '_getTabindex()',\n    '[attr.disabled]': '_getDisabledAttribute()',\n    '[attr.aria-disabled]': 'disabled',\n    '(click)': '_handleClick($event)',\n    '(keydown)': '_handleKeydown($event)',\n  },\n})\nexport class MatChipAction {\n  _elementRef = inject<ElementRef<HTMLElement>>(ElementRef);\n  protected _parentChip = inject<{\n    _handlePrimaryActionInteraction(): void;\n    remove(): void;\n    disabled: boolean;\n    _isEditing?: boolean;\n  }>(MAT_CHIP);\n\n  /** Whether the action is interactive. */\n  @Input() isInteractive = true;\n\n  /** Whether this is the primary action in the chip. */\n  _isPrimary = true;\n\n  /** Whether the action is disabled. */\n  @Input({transform: booleanAttribute})\n  get disabled(): boolean {\n    return this._disabled || this._parentChip?.disabled || false;\n  }\n  set disabled(value: boolean) {\n    this._disabled = value;\n  }\n  private _disabled = false;\n\n  /** Tab index of the action. */\n  @Input({\n    transform: (value: unknown) => (value == null ? -1 : numberAttribute(value)),\n  })\n  tabIndex: number = -1;\n\n  /**\n   * Private API to allow focusing this chip when it is disabled.\n   */\n  @Input()\n  private _allowFocusWhenDisabled = false;\n\n  /**\n   * Determine the value of the disabled attribute for this chip action.\n   */\n  protected _getDisabledAttribute(): string | null {\n    // When this chip action is disabled and focusing disabled chips is not permitted, return empty\n    // string to indicate that disabled attribute should be included.\n    return this.disabled && !this._allowFocusWhenDisabled ? '' : null;\n  }\n\n  /**\n   * Determine the value of the tabindex attribute for this chip action.\n   */\n  protected _getTabindex(): string | null {\n    return (this.disabled && !this._allowFocusWhenDisabled) || !this.isInteractive\n      ? null\n      : this.tabIndex.toString();\n  }\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    inject(_CdkPrivateStyleLoader).load(_StructuralStylesLoader);\n    if (this._elementRef.nativeElement.nodeName === 'BUTTON') {\n      this._elementRef.nativeElement.setAttribute('type', 'button');\n    }\n  }\n\n  focus() {\n    this._elementRef.nativeElement.focus();\n  }\n\n  _handleClick(event: MouseEvent) {\n    if (!this.disabled && this.isInteractive && this._isPrimary) {\n      event.preventDefault();\n      this._parentChip._handlePrimaryActionInteraction();\n    }\n  }\n\n  _handleKeydown(event: KeyboardEvent) {\n    if (\n      (event.keyCode === ENTER || event.keyCode === SPACE) &&\n      !this.disabled &&\n      this.isInteractive &&\n      this._isPrimary &&\n      !this._parentChip._isEditing\n    ) {\n      event.preventDefault();\n      this._parentChip._handlePrimaryActionInteraction();\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {ENTER, SPACE} from '@angular/cdk/keycodes';\nimport {Directive} from '@angular/core';\nimport {MatChipAction} from './chip-action';\nimport {MAT_CHIP_AVATAR, MAT_CHIP_REMOVE, MAT_CHIP_TRAILING_ICON} from './tokens';\n\n/** Avatar image within a chip. */\n@Directive({\n  selector: 'mat-chip-avatar, [matChipAvatar]',\n  host: {\n    'class': 'mat-mdc-chip-avatar mdc-evolution-chip__icon mdc-evolution-chip__icon--primary',\n    'role': 'img',\n  },\n  providers: [{provide: MAT_CHIP_AVATAR, useExisting: MatChipAvatar}],\n})\nexport class MatChipAvatar {}\n\n/** Non-interactive trailing icon in a chip. */\n@Directive({\n  selector: 'mat-chip-trailing-icon, [matChipTrailingIcon]',\n  host: {\n    'class':\n      'mat-mdc-chip-trailing-icon mdc-evolution-chip__icon mdc-evolution-chip__icon--trailing',\n    'aria-hidden': 'true',\n  },\n  providers: [{provide: MAT_CHIP_TRAILING_ICON, useExisting: MatChipTrailingIcon}],\n})\nexport class MatChipTrailingIcon extends MatChipAction {\n  /**\n   * MDC considers all trailing actions as a remove icon,\n   * but we support non-interactive trailing icons.\n   */\n  override isInteractive = false;\n\n  override _isPrimary = false;\n}\n\n/**\n * Directive to remove the parent chip when the trailing icon is clicked or\n * when the ENTER key is pressed on it.\n *\n * Recommended for use with the Material Design \"cancel\" icon\n * available at https://material.io/icons/#ic_cancel.\n *\n * Example:\n *\n * ```\n * <mat-chip>\n *   <mat-icon matChipRemove>cancel</mat-icon>\n * </mat-chip>\n * ```\n */\n\n@Directive({\n  selector: '[matChipRemove]',\n  host: {\n    'class':\n      'mat-mdc-chip-remove mat-mdc-chip-trailing-icon mat-focus-indicator ' +\n      'mdc-evolution-chip__icon mdc-evolution-chip__icon--trailing',\n    'role': 'button',\n    '[attr.aria-hidden]': 'null',\n  },\n  providers: [{provide: MAT_CHIP_REMOVE, useExisting: MatChipRemove}],\n})\nexport class MatChipRemove extends MatChipAction {\n  override _isPrimary = false;\n\n  override _handleClick(event: MouseEvent): void {\n    if (!this.disabled) {\n      event.stopPropagation();\n      event.preventDefault();\n      this._parentChip.remove();\n    }\n  }\n\n  override _handleKeydown(event: KeyboardEvent) {\n    if ((event.keyCode === ENTER || event.keyCode === SPACE) && !this.disabled) {\n      event.stopPropagation();\n      event.preventDefault();\n      this._parentChip.remove();\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {FocusMonitor, _IdGenerator} from '@angular/cdk/a11y';\nimport {BACKSPACE, DELETE} from '@angular/cdk/keycodes';\nimport {_CdkPrivateStyleLoader, _VisuallyHiddenLoader} from '@angular/cdk/private';\nimport {DOCUMENT} from '@angular/common';\nimport {\n  ANIMATION_MODULE_TYPE,\n  AfterContentInit,\n  AfterViewInit,\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  ContentChild,\n  ContentChildren,\n  DoCheck,\n  ElementRef,\n  EventEmitter,\n  Injector,\n  Input,\n  NgZone,\n  OnDestroy,\n  OnInit,\n  Output,\n  QueryList,\n  ViewChild,\n  ViewEncapsulation,\n  booleanAttribute,\n  inject,\n} from '@angular/core';\nimport {\n  MAT_RIPPLE_GLOBAL_OPTIONS,\n  MatRippleLoader,\n  RippleGlobalOptions,\n  _StructuralStylesLoader,\n} from '../core';\nimport {Subject, Subscription, merge} from 'rxjs';\nimport {MatChipAction} from './chip-action';\nimport {MatChipAvatar, MatChipRemove, MatChipTrailingIcon} from './chip-icons';\nimport {MAT_CHIP, MAT_CHIP_AVATAR, MAT_CHIP_REMOVE, MAT_CHIP_TRAILING_ICON} from './tokens';\n\n/** Represents an event fired on an individual `mat-chip`. */\nexport interface MatChipEvent {\n  /** The chip the event was fired on. */\n  chip: MatChip;\n}\n\n/**\n * Material design styled Chip base component. Used inside the MatChipSet component.\n *\n * Extended by MatChipOption and MatChipRow for different interaction patterns.\n */\n@Component({\n  selector: 'mat-basic-chip, [mat-basic-chip], mat-chip, [mat-chip]',\n  exportAs: 'matChip',\n  templateUrl: 'chip.html',\n  styleUrl: 'chip.css',\n  host: {\n    'class': 'mat-mdc-chip',\n    '[class]': '\"mat-\" + (color || \"primary\")',\n    '[class.mdc-evolution-chip]': '!_isBasicChip',\n    '[class.mdc-evolution-chip--disabled]': 'disabled',\n    '[class.mdc-evolution-chip--with-trailing-action]': '_hasTrailingIcon()',\n    '[class.mdc-evolution-chip--with-primary-graphic]': 'leadingIcon',\n    '[class.mdc-evolution-chip--with-primary-icon]': 'leadingIcon',\n    '[class.mdc-evolution-chip--with-avatar]': 'leadingIcon',\n    '[class.mat-mdc-chip-with-avatar]': 'leadingIcon',\n    '[class.mat-mdc-chip-highlighted]': 'highlighted',\n    '[class.mat-mdc-chip-disabled]': 'disabled',\n    '[class.mat-mdc-basic-chip]': '_isBasicChip',\n    '[class.mat-mdc-standard-chip]': '!_isBasicChip',\n    '[class.mat-mdc-chip-with-trailing-icon]': '_hasTrailingIcon()',\n    '[class._mat-animation-noopable]': '_animationsDisabled',\n    '[id]': 'id',\n    '[attr.role]': 'role',\n    '[attr.aria-label]': 'ariaLabel',\n    '(keydown)': '_handleKeydown($event)',\n  },\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  providers: [{provide: MAT_CHIP, useExisting: MatChip}],\n  imports: [MatChipAction],\n})\nexport class MatChip implements OnInit, AfterViewInit, AfterContentInit, DoCheck, OnDestroy {\n  _changeDetectorRef = inject(ChangeDetectorRef);\n  _elementRef = inject<ElementRef<HTMLElement>>(ElementRef);\n  protected _ngZone = inject(NgZone);\n  private _focusMonitor = inject(FocusMonitor);\n  private _globalRippleOptions = inject<RippleGlobalOptions>(MAT_RIPPLE_GLOBAL_OPTIONS, {\n    optional: true,\n  });\n\n  protected _document = inject(DOCUMENT);\n\n  /** Emits when the chip is focused. */\n  readonly _onFocus = new Subject<MatChipEvent>();\n\n  /** Emits when the chip is blurred. */\n  readonly _onBlur = new Subject<MatChipEvent>();\n\n  /** Whether this chip is a basic (unstyled) chip. */\n  _isBasicChip: boolean;\n\n  /** Role for the root of the chip. */\n  @Input() role: string | null = null;\n\n  /** Whether the chip has focus. */\n  private _hasFocusInternal = false;\n\n  /** Whether moving focus into the chip is pending. */\n  private _pendingFocus: boolean;\n\n  /** Subscription to changes in the chip's actions. */\n  private _actionChanges: Subscription | undefined;\n\n  /** Whether animations for the chip are enabled. */\n  _animationsDisabled: boolean;\n\n  /** All avatars present in the chip. */\n  @ContentChildren(MAT_CHIP_AVATAR, {descendants: true})\n  protected _allLeadingIcons: QueryList<MatChipAvatar>;\n\n  /** All trailing icons present in the chip. */\n  @ContentChildren(MAT_CHIP_TRAILING_ICON, {descendants: true})\n  protected _allTrailingIcons: QueryList<MatChipTrailingIcon>;\n\n  /** All remove icons present in the chip. */\n  @ContentChildren(MAT_CHIP_REMOVE, {descendants: true})\n  protected _allRemoveIcons: QueryList<MatChipRemove>;\n\n  _hasFocus() {\n    return this._hasFocusInternal;\n  }\n\n  /** A unique id for the chip. If none is supplied, it will be auto-generated. */\n  @Input() id: string = inject(_IdGenerator).getId('mat-mdc-chip-');\n\n  // TODO(#26104): Consider deprecating and using `_computeAriaAccessibleName` instead.\n  // `ariaLabel` may be unnecessary, and `_computeAriaAccessibleName` only supports\n  // datepicker's use case.\n  /** ARIA label for the content of the chip. */\n  @Input('aria-label') ariaLabel: string | null = null;\n\n  // TODO(#26104): Consider deprecating and using `_computeAriaAccessibleName` instead.\n  // `ariaDescription` may be unnecessary, and `_computeAriaAccessibleName` only supports\n  // datepicker's use case.\n  /** ARIA description for the content of the chip. */\n  @Input('aria-description') ariaDescription: string | null = null;\n\n  /** Id of a span that contains this chip's aria description. */\n  _ariaDescriptionId = `${this.id}-aria-description`;\n\n  /** Whether the chip list is disabled. */\n  _chipListDisabled: boolean = false;\n\n  private _textElement!: HTMLElement;\n\n  /**\n   * The value of the chip. Defaults to the content inside\n   * the `mat-mdc-chip-action-label` element.\n   */\n  @Input()\n  get value(): any {\n    return this._value !== undefined ? this._value : this._textElement.textContent!.trim();\n  }\n  set value(value: any) {\n    this._value = value;\n  }\n  protected _value: any;\n\n  // TODO: should be typed as `ThemePalette` but internal apps pass in arbitrary strings.\n  /**\n   * Theme color of the chip. This API is supported in M2 themes only, it has no\n   * effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/chips/styling.\n   *\n   * For information on applying color variants in M3, see\n   * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n   */\n  @Input() color?: string | null;\n\n  /**\n   * Determines whether or not the chip displays the remove styling and emits (removed) events.\n   */\n  @Input({transform: booleanAttribute})\n  removable: boolean = true;\n\n  /**\n   * Colors the chip for emphasis as if it were selected.\n   */\n  @Input({transform: booleanAttribute})\n  highlighted: boolean = false;\n\n  /** Whether the ripple effect is disabled or not. */\n  @Input({transform: booleanAttribute})\n  disableRipple: boolean = false;\n\n  /** Whether the chip is disabled. */\n  @Input({transform: booleanAttribute})\n  get disabled(): boolean {\n    return this._disabled || this._chipListDisabled;\n  }\n  set disabled(value: boolean) {\n    this._disabled = value;\n  }\n  private _disabled = false;\n\n  /** Emitted when a chip is to be removed. */\n  @Output() readonly removed: EventEmitter<MatChipEvent> = new EventEmitter<MatChipEvent>();\n\n  /** Emitted when the chip is destroyed. */\n  @Output() readonly destroyed: EventEmitter<MatChipEvent> = new EventEmitter<MatChipEvent>();\n\n  /** The unstyled chip selector for this component. */\n  protected basicChipAttrName = 'mat-basic-chip';\n\n  /** The chip's leading icon. */\n  @ContentChild(MAT_CHIP_AVATAR) leadingIcon: MatChipAvatar;\n\n  /** The chip's trailing icon. */\n  @ContentChild(MAT_CHIP_TRAILING_ICON) trailingIcon: MatChipTrailingIcon;\n\n  /** The chip's trailing remove icon. */\n  @ContentChild(MAT_CHIP_REMOVE) removeIcon: MatChipRemove;\n\n  /** Action receiving the primary set of user interactions. */\n  @ViewChild(MatChipAction) primaryAction: MatChipAction;\n\n  /**\n   * Handles the lazy creation of the MatChip ripple.\n   * Used to improve initial load time of large applications.\n   */\n  private _rippleLoader: MatRippleLoader = inject(MatRippleLoader);\n\n  protected _injector = inject(Injector);\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    const styleLoader = inject(_CdkPrivateStyleLoader);\n    styleLoader.load(_StructuralStylesLoader);\n    styleLoader.load(_VisuallyHiddenLoader);\n    const animationMode = inject(ANIMATION_MODULE_TYPE, {optional: true});\n    this._animationsDisabled = animationMode === 'NoopAnimations';\n    this._monitorFocus();\n\n    this._rippleLoader?.configureRipple(this._elementRef.nativeElement, {\n      className: 'mat-mdc-chip-ripple',\n      disabled: this._isRippleDisabled(),\n    });\n  }\n\n  ngOnInit() {\n    // This check needs to happen in `ngOnInit` so the overridden value of\n    // `basicChipAttrName` coming from base classes can be picked up.\n    const element = this._elementRef.nativeElement;\n    this._isBasicChip =\n      element.hasAttribute(this.basicChipAttrName) ||\n      element.tagName.toLowerCase() === this.basicChipAttrName;\n  }\n\n  ngAfterViewInit() {\n    this._textElement = this._elementRef.nativeElement.querySelector('.mat-mdc-chip-action-label')!;\n\n    if (this._pendingFocus) {\n      this._pendingFocus = false;\n      this.focus();\n    }\n  }\n\n  ngAfterContentInit(): void {\n    // Since the styling depends on the presence of some\n    // actions, we have to mark for check on changes.\n    this._actionChanges = merge(\n      this._allLeadingIcons.changes,\n      this._allTrailingIcons.changes,\n      this._allRemoveIcons.changes,\n    ).subscribe(() => this._changeDetectorRef.markForCheck());\n  }\n\n  ngDoCheck(): void {\n    this._rippleLoader.setDisabled(this._elementRef.nativeElement, this._isRippleDisabled());\n  }\n\n  ngOnDestroy() {\n    this._focusMonitor.stopMonitoring(this._elementRef);\n    this._rippleLoader?.destroyRipple(this._elementRef.nativeElement);\n    this._actionChanges?.unsubscribe();\n    this.destroyed.emit({chip: this});\n    this.destroyed.complete();\n  }\n\n  /**\n   * Allows for programmatic removal of the chip.\n   *\n   * Informs any listeners of the removal request. Does not remove the chip from the DOM.\n   */\n  remove(): void {\n    if (this.removable) {\n      this.removed.emit({chip: this});\n    }\n  }\n\n  /** Whether or not the ripple should be disabled. */\n  _isRippleDisabled(): boolean {\n    return (\n      this.disabled ||\n      this.disableRipple ||\n      this._animationsDisabled ||\n      this._isBasicChip ||\n      !!this._globalRippleOptions?.disabled\n    );\n  }\n\n  /** Returns whether the chip has a trailing icon. */\n  _hasTrailingIcon() {\n    return !!(this.trailingIcon || this.removeIcon);\n  }\n\n  /** Handles keyboard events on the chip. */\n  _handleKeydown(event: KeyboardEvent) {\n    // Ignore backspace events where the user is holding down the key\n    // so that we don't accidentally remove too many chips.\n    if ((event.keyCode === BACKSPACE && !event.repeat) || event.keyCode === DELETE) {\n      event.preventDefault();\n      this.remove();\n    }\n  }\n\n  /** Allows for programmatic focusing of the chip. */\n  focus(): void {\n    if (!this.disabled) {\n      // If `focus` is called before `ngAfterViewInit`, we won't have access to the primary action.\n      // This can happen if the consumer tries to focus a chip immediately after it is added.\n      // Queue the method to be called again on init.\n      if (this.primaryAction) {\n        this.primaryAction.focus();\n      } else {\n        this._pendingFocus = true;\n      }\n    }\n  }\n\n  /** Gets the action that contains a specific target node. */\n  _getSourceAction(target: Node): MatChipAction | undefined {\n    return this._getActions().find(action => {\n      const element = action._elementRef.nativeElement;\n      return element === target || element.contains(target);\n    });\n  }\n\n  /** Gets all of the actions within the chip. */\n  _getActions(): MatChipAction[] {\n    const result: MatChipAction[] = [];\n\n    if (this.primaryAction) {\n      result.push(this.primaryAction);\n    }\n\n    if (this.removeIcon) {\n      result.push(this.removeIcon);\n    }\n\n    if (this.trailingIcon) {\n      result.push(this.trailingIcon);\n    }\n\n    return result;\n  }\n\n  /** Handles interactions with the primary action of the chip. */\n  _handlePrimaryActionInteraction() {\n    // Empty here, but is overwritten in child classes.\n  }\n\n  /** Starts the focus monitoring process on the chip. */\n  private _monitorFocus() {\n    this._focusMonitor.monitor(this._elementRef, true).subscribe(origin => {\n      const hasFocus = origin !== null;\n\n      if (hasFocus !== this._hasFocusInternal) {\n        this._hasFocusInternal = hasFocus;\n\n        if (hasFocus) {\n          this._onFocus.next({chip: this});\n        } else {\n          // When animations are enabled, Angular may end up removing the chip from the DOM a little\n          // earlier than usual, causing it to be blurred and throwing off the logic in the chip list\n          // that moves focus to the next item. To work around the issue, we defer marking the chip\n          // as not focused until after the next render.\n          this._changeDetectorRef.markForCheck();\n          setTimeout(() => this._ngZone.run(() => this._onBlur.next({chip: this})));\n        }\n      }\n    });\n  }\n}\n", "<span class=\"mat-mdc-chip-focus-overlay\"></span>\n\n<span class=\"mdc-evolution-chip__cell mdc-evolution-chip__cell--primary\">\n  <span matChipAction [isInteractive]=\"false\">\n    @if (leadingIcon) {\n      <span class=\"mdc-evolution-chip__graphic mat-mdc-chip-graphic\">\n        <ng-content select=\"mat-chip-avatar, [matChipAvatar]\"></ng-content>\n      </span>\n    }\n    <span class=\"mdc-evolution-chip__text-label mat-mdc-chip-action-label\">\n      <ng-content></ng-content>\n      <span class=\"mat-mdc-chip-primary-focus-indicator mat-focus-indicator\"></span>\n    </span>\n  </span>\n</span>\n\n@if (_hasTrailingIcon()) {\n  <span class=\"mdc-evolution-chip__cell mdc-evolution-chip__cell--trailing\">\n    <ng-content select=\"mat-chip-trailing-icon,[matChipRemove],[matChipTrailingIcon]\"></ng-content>\n  </span>\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  ChangeDetectionStrategy,\n  Component,\n  EventEmitter,\n  Input,\n  Output,\n  ViewEncapsulation,\n  OnInit,\n  inject,\n  booleanAttribute,\n} from '@angular/core';\nimport {MatChip} from './chip';\nimport {MAT_CHIP, MAT_CHIPS_DEFAULT_OPTIONS} from './tokens';\nimport {MatChipAction} from './chip-action';\n\n/** Event object emitted by MatChipOption when selected or deselected. */\nexport class MatChipSelectionChange {\n  constructor(\n    /** Reference to the chip that emitted the event. */\n    public source: MatChipOption,\n    /** Whether the chip that emitted the event is selected. */\n    public selected: boolean,\n    /** Whether the selection change was a result of a user interaction. */\n    public isUserInput = false,\n  ) {}\n}\n\n/**\n * An extension of the MatChip component that supports chip selection. Used with MatChipListbox.\n *\n * Unlike other chips, the user can focus on disabled chip options inside a MatChipListbox. The\n * user cannot click disabled chips.\n */\n@Component({\n  selector: 'mat-basic-chip-option, [mat-basic-chip-option], mat-chip-option, [mat-chip-option]',\n  templateUrl: 'chip-option.html',\n  styleUrl: 'chip.css',\n  host: {\n    'class': 'mat-mdc-chip mat-mdc-chip-option',\n    '[class.mdc-evolution-chip]': '!_isBasicChip',\n    '[class.mdc-evolution-chip--filter]': '!_isBasicChip',\n    '[class.mdc-evolution-chip--selectable]': '!_isBasicChip',\n    '[class.mat-mdc-chip-selected]': 'selected',\n    '[class.mat-mdc-chip-multiple]': '_chipListMultiple',\n    '[class.mat-mdc-chip-disabled]': 'disabled',\n    '[class.mat-mdc-chip-with-avatar]': 'leadingIcon',\n    '[class.mdc-evolution-chip--disabled]': 'disabled',\n    '[class.mdc-evolution-chip--selected]': 'selected',\n    // This class enables the transition on the checkmark. Usually MDC adds it when selection\n    // starts and removes it once the animation is finished. We don't need to go through all\n    // the trouble, because we only care about the selection animation. MDC needs to do it,\n    // because they also have an exit animation that we don't care about.\n    '[class.mdc-evolution-chip--selecting]': '!_animationsDisabled',\n    '[class.mdc-evolution-chip--with-trailing-action]': '_hasTrailingIcon()',\n    '[class.mdc-evolution-chip--with-primary-icon]': 'leadingIcon',\n    '[class.mdc-evolution-chip--with-primary-graphic]': '_hasLeadingGraphic()',\n    '[class.mdc-evolution-chip--with-avatar]': 'leadingIcon',\n    '[class.mat-mdc-chip-highlighted]': 'highlighted',\n    '[class.mat-mdc-chip-with-trailing-icon]': '_hasTrailingIcon()',\n    '[attr.tabindex]': 'null',\n    '[attr.aria-label]': 'null',\n    '[attr.aria-description]': 'null',\n    '[attr.role]': 'role',\n    '[id]': 'id',\n  },\n  providers: [\n    {provide: MatChip, useExisting: MatChipOption},\n    {provide: MAT_CHIP, useExisting: MatChipOption},\n  ],\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  imports: [MatChipAction],\n})\nexport class MatChipOption extends MatChip implements OnInit {\n  /** Default chip options. */\n  private _defaultOptions = inject(MAT_CHIPS_DEFAULT_OPTIONS, {optional: true});\n\n  /** Whether the chip list is selectable. */\n  chipListSelectable: boolean = true;\n\n  /** Whether the chip list is in multi-selection mode. */\n  _chipListMultiple: boolean = false;\n\n  /** Whether the chip list hides single-selection indicator. */\n  _chipListHideSingleSelectionIndicator: boolean =\n    this._defaultOptions?.hideSingleSelectionIndicator ?? false;\n\n  /**\n   * Whether or not the chip is selectable.\n   *\n   * When a chip is not selectable, changes to its selected state are always\n   * ignored. By default an option chip is selectable, and it becomes\n   * non-selectable if its parent chip list is not selectable.\n   */\n  @Input({transform: booleanAttribute})\n  get selectable(): boolean {\n    return this._selectable && this.chipListSelectable;\n  }\n  set selectable(value: boolean) {\n    this._selectable = value;\n    this._changeDetectorRef.markForCheck();\n  }\n  protected _selectable: boolean = true;\n\n  /** Whether the chip is selected. */\n  @Input({transform: booleanAttribute})\n  get selected(): boolean {\n    return this._selected;\n  }\n  set selected(value: boolean) {\n    this._setSelectedState(value, false, true);\n  }\n  private _selected = false;\n\n  /**\n   * The ARIA selected applied to the chip. Conforms to WAI ARIA best practices for listbox\n   * interaction patterns.\n   *\n   * From [WAI ARIA Listbox authoring practices guide](\n   * https://www.w3.org/WAI/ARIA/apg/patterns/listbox/):\n   *  \"If any options are selected, each selected option has either aria-selected or aria-checked\n   *  set to true. All options that are selectable but not selected have either aria-selected or\n   *  aria-checked set to false.\"\n   *\n   * Set `aria-selected=\"false\"` on not-selected listbox options that are selectable to fix\n   * VoiceOver reading every option as \"selected\" (#25736).\n   */\n  get ariaSelected(): string | null {\n    return this.selectable ? this.selected.toString() : null;\n  }\n\n  /** The unstyled chip selector for this component. */\n  protected override basicChipAttrName = 'mat-basic-chip-option';\n\n  /** Emitted when the chip is selected or deselected. */\n  @Output() readonly selectionChange: EventEmitter<MatChipSelectionChange> =\n    new EventEmitter<MatChipSelectionChange>();\n\n  override ngOnInit() {\n    super.ngOnInit();\n    this.role = 'presentation';\n  }\n\n  /** Selects the chip. */\n  select(): void {\n    this._setSelectedState(true, false, true);\n  }\n\n  /** Deselects the chip. */\n  deselect(): void {\n    this._setSelectedState(false, false, true);\n  }\n\n  /** Selects this chip and emits userInputSelection event */\n  selectViaInteraction(): void {\n    this._setSelectedState(true, true, true);\n  }\n\n  /** Toggles the current selected state of this chip. */\n  toggleSelected(isUserInput: boolean = false): boolean {\n    this._setSelectedState(!this.selected, isUserInput, true);\n    return this.selected;\n  }\n\n  override _handlePrimaryActionInteraction() {\n    if (!this.disabled) {\n      // Interacting with the primary action implies that the chip already has focus, however\n      // there's a bug in Safari where focus ends up lingering on the previous chip (see #27544).\n      // We work around it by explicitly focusing the primary action of the current chip.\n      this.focus();\n\n      if (this.selectable) {\n        this.toggleSelected(true);\n      }\n    }\n  }\n\n  _hasLeadingGraphic() {\n    if (this.leadingIcon) {\n      return true;\n    }\n\n    // The checkmark graphic communicates selected state for both single-select and multi-select.\n    // Include checkmark in single-select to fix a11y issue where selected state is communicated\n    // visually only using color (#25886).\n    return !this._chipListHideSingleSelectionIndicator || this._chipListMultiple;\n  }\n\n  _setSelectedState(isSelected: boolean, isUserInput: boolean, emitEvent: boolean) {\n    if (isSelected !== this.selected) {\n      this._selected = isSelected;\n\n      if (emitEvent) {\n        this.selectionChange.emit({\n          source: this,\n          isUserInput,\n          selected: this.selected,\n        });\n      }\n\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n}\n", "<span class=\"mat-mdc-chip-focus-overlay\"></span>\n\n<span class=\"mdc-evolution-chip__cell mdc-evolution-chip__cell--primary\">\n  <button\n    matChipAction\n    [_allowFocusWhenDisabled]=\"true\"\n    [attr.aria-selected]=\"ariaSelected\"\n    [attr.aria-label]=\"ariaLabel\"\n    [attr.aria-describedby]=\"_ariaDescriptionId\"\n    role=\"option\">\n    @if (_hasLeadingGraphic()) {\n      <span class=\"mdc-evolution-chip__graphic mat-mdc-chip-graphic\">\n        <ng-content select=\"mat-chip-avatar, [matChipAvatar]\"></ng-content>\n        <span class=\"mdc-evolution-chip__checkmark\">\n          <svg\n            class=\"mdc-evolution-chip__checkmark-svg\"\n            viewBox=\"-2 -3 30 30\"\n            focusable=\"false\"\n            aria-hidden=\"true\">\n            <path class=\"mdc-evolution-chip__checkmark-path\"\n                  fill=\"none\" stroke=\"currentColor\" d=\"M1.73,12.91 8.1,19.28 22.79,4.59\" />\n          </svg>\n        </span>\n      </span>\n    }\n    <span class=\"mdc-evolution-chip__text-label mat-mdc-chip-action-label\">\n      <ng-content></ng-content>\n      <span class=\"mat-mdc-chip-primary-focus-indicator mat-focus-indicator\"></span>\n    </span>\n  </button>\n</span>\n\n@if (_hasTrailingIcon()) {\n  <span class=\"mdc-evolution-chip__cell mdc-evolution-chip__cell--trailing\">\n    <ng-content select=\"mat-chip-trailing-icon,[matChipRemove],[matChipTrailingIcon]\"></ng-content>\n  </span>\n}\n\n<span class=\"cdk-visually-hidden\" [id]=\"_ariaDescriptionId\">{{ariaDescription}}</span>\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Directive, ElementRef, inject} from '@angular/core';\nimport {DOCUMENT} from '@angular/common';\n\n/**\n * A directive that makes a span editable and exposes functions to modify and retrieve the\n * element's contents.\n */\n@Directive({\n  selector: 'span[matChipEditInput]',\n  host: {\n    'class': 'mat-chip-edit-input',\n    'role': 'textbox',\n    'tabindex': '-1',\n    'contenteditable': 'true',\n  },\n})\nexport class MatChipEditInput {\n  private readonly _elementRef = inject(ElementRef);\n  private readonly _document = inject(DOCUMENT);\n\n  constructor(...args: unknown[]);\n  constructor() {}\n\n  initialize(initialValue: string) {\n    this.getNativeElement().focus();\n    this.setValue(initialValue);\n  }\n\n  getNativeElement(): HTMLElement {\n    return this._elementRef.nativeElement;\n  }\n\n  setValue(value: string) {\n    this.getNativeElement().textContent = value;\n    this._moveCursorToEndOfInput();\n  }\n\n  getValue(): string {\n    return this.getNativeElement().textContent || '';\n  }\n\n  private _moveCursorToEndOfInput() {\n    const range = this._document.createRange();\n    range.selectNodeContents(this.getNativeElement());\n    range.collapse(false);\n    const sel = window.getSelection()!;\n    sel.removeAllRanges();\n    sel.addRange(range);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {ENTER} from '@angular/cdk/keycodes';\nimport {\n  AfterViewInit,\n  ChangeDetectionStrategy,\n  Component,\n  ContentChild,\n  EventEmitter,\n  Input,\n  Output,\n  ViewChild,\n  ViewEncapsulation,\n  afterNextRender,\n} from '@angular/core';\nimport {takeUntil} from 'rxjs/operators';\nimport {MatChip, MatChipEvent} from './chip';\nimport {MatChipAction} from './chip-action';\nimport {MatChipEditInput} from './chip-edit-input';\nimport {MAT_CHIP} from './tokens';\n\n/** Represents an event fired on an individual `mat-chip` when it is edited. */\nexport interface MatChipEditedEvent extends MatChipEvent {\n  /** The final edit value. */\n  value: string;\n}\n\n/**\n * An extension of the MatChip component used with MatChipGrid and\n * the matChipInputFor directive.\n */\n@Component({\n  selector: 'mat-chip-row, [mat-chip-row], mat-basic-chip-row, [mat-basic-chip-row]',\n  templateUrl: 'chip-row.html',\n  styleUrl: 'chip.css',\n  host: {\n    'class': 'mat-mdc-chip mat-mdc-chip-row mdc-evolution-chip',\n    '[class.mat-mdc-chip-with-avatar]': 'leadingIcon',\n    '[class.mat-mdc-chip-disabled]': 'disabled',\n    '[class.mat-mdc-chip-editing]': '_isEditing',\n    '[class.mat-mdc-chip-editable]': 'editable',\n    '[class.mdc-evolution-chip--disabled]': 'disabled',\n    '[class.mdc-evolution-chip--with-trailing-action]': '_hasTrailingIcon()',\n    '[class.mdc-evolution-chip--with-primary-graphic]': 'leadingIcon',\n    '[class.mdc-evolution-chip--with-primary-icon]': 'leadingIcon',\n    '[class.mdc-evolution-chip--with-avatar]': 'leadingIcon',\n    '[class.mat-mdc-chip-highlighted]': 'highlighted',\n    '[class.mat-mdc-chip-with-trailing-icon]': '_hasTrailingIcon()',\n    '[id]': 'id',\n    // Has to have a negative tabindex in order to capture\n    // focus and redirect it to the primary action.\n    '[attr.tabindex]': 'disabled ? null : -1',\n    '[attr.aria-label]': 'null',\n    '[attr.aria-description]': 'null',\n    '[attr.role]': 'role',\n    '(focus)': '_handleFocus()',\n    '(dblclick)': '_handleDoubleclick($event)',\n  },\n  providers: [\n    {provide: MatChip, useExisting: MatChipRow},\n    {provide: MAT_CHIP, useExisting: MatChipRow},\n  ],\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  imports: [MatChipAction, MatChipEditInput],\n})\nexport class MatChipRow extends MatChip implements AfterViewInit {\n  protected override basicChipAttrName = 'mat-basic-chip-row';\n\n  /**\n   * The editing action has to be triggered in a timeout. While we're waiting on it, a blur\n   * event might occur which will interrupt the editing. This flag is used to avoid interruptions\n   * while the editing action is being initialized.\n   */\n  private _editStartPending = false;\n\n  @Input() editable: boolean = false;\n\n  /** Emitted when the chip is edited. */\n  @Output() readonly edited: EventEmitter<MatChipEditedEvent> =\n    new EventEmitter<MatChipEditedEvent>();\n\n  /** The default chip edit input that is used if none is projected into this chip row. */\n  @ViewChild(MatChipEditInput) defaultEditInput?: MatChipEditInput;\n\n  /** The projected chip edit input. */\n  @ContentChild(MatChipEditInput) contentEditInput?: MatChipEditInput;\n\n  _isEditing = false;\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    super();\n\n    this.role = 'row';\n    this._onBlur.pipe(takeUntil(this.destroyed)).subscribe(() => {\n      if (this._isEditing && !this._editStartPending) {\n        this._onEditFinish();\n      }\n    });\n  }\n\n  override _hasTrailingIcon() {\n    // The trailing icon is hidden while editing.\n    return !this._isEditing && super._hasTrailingIcon();\n  }\n\n  /** Sends focus to the first gridcell when the user clicks anywhere inside the chip. */\n  _handleFocus() {\n    if (!this._isEditing && !this.disabled) {\n      this.focus();\n    }\n  }\n\n  override _handleKeydown(event: KeyboardEvent): void {\n    if (event.keyCode === ENTER && !this.disabled) {\n      if (this._isEditing) {\n        event.preventDefault();\n        this._onEditFinish();\n      } else if (this.editable) {\n        this._startEditing(event);\n      }\n    } else if (this._isEditing) {\n      // Stop the event from reaching the chip set in order to avoid navigating.\n      event.stopPropagation();\n    } else {\n      super._handleKeydown(event);\n    }\n  }\n\n  _handleDoubleclick(event: MouseEvent) {\n    if (!this.disabled && this.editable) {\n      this._startEditing(event);\n    }\n  }\n\n  private _startEditing(event: Event) {\n    if (\n      !this.primaryAction ||\n      (this.removeIcon && this._getSourceAction(event.target as Node) === this.removeIcon)\n    ) {\n      return;\n    }\n\n    // The value depends on the DOM so we need to extract it before we flip the flag.\n    const value = this.value;\n\n    this._isEditing = this._editStartPending = true;\n\n    // Defer initializing the input until after it has been added to the DOM.\n    afterNextRender(\n      () => {\n        this._getEditInput().initialize(value);\n        this._editStartPending = false;\n      },\n      {injector: this._injector},\n    );\n  }\n\n  private _onEditFinish() {\n    this._isEditing = this._editStartPending = false;\n    this.edited.emit({chip: this, value: this._getEditInput().getValue()});\n\n    // If the edit input is still focused or focus was returned to the body after it was destroyed,\n    // return focus to the chip contents.\n    if (\n      this._document.activeElement === this._getEditInput().getNativeElement() ||\n      this._document.activeElement === this._document.body\n    ) {\n      this.primaryAction.focus();\n    }\n  }\n\n  override _isRippleDisabled(): boolean {\n    return super._isRippleDisabled() || this._isEditing;\n  }\n\n  /**\n   * Gets the projected chip edit input, or the default input if none is projected in. One of these\n   * two values is guaranteed to be defined.\n   */\n  private _getEditInput(): MatChipEditInput {\n    return this.contentEditInput || this.defaultEditInput!;\n  }\n}\n", "@if (!_isEditing) {\n  <span class=\"mat-mdc-chip-focus-overlay\"></span>\n}\n\n<span class=\"mdc-evolution-chip__cell mdc-evolution-chip__cell--primary\" role=\"gridcell\"\n    matChipAction\n    [disabled]=\"disabled\"\n    [attr.aria-label]=\"ariaLabel\"\n    [attr.aria-describedby]=\"_ariaDescriptionId\">\n  @if (leadingIcon) {\n    <span class=\"mdc-evolution-chip__graphic mat-mdc-chip-graphic\">\n      <ng-content select=\"mat-chip-avatar, [matChipAvatar]\"></ng-content>\n    </span>\n  }\n\n  <span class=\"mdc-evolution-chip__text-label mat-mdc-chip-action-label\">\n    @if (_isEditing) {\n      @if (contentEditInput) {\n        <ng-content select=\"[matChipEditInput]\"></ng-content>\n      } @else {\n        <span matChipEditInput></span>\n      }\n    } @else {\n      <ng-content></ng-content>\n    }\n\n    <span class=\"mat-mdc-chip-primary-focus-indicator mat-focus-indicator\" aria-hidden=\"true\"></span>\n  </span>\n</span>\n\n@if (_hasTrailingIcon()) {\n  <span\n    class=\"mdc-evolution-chip__cell mdc-evolution-chip__cell--trailing\"\n    role=\"gridcell\">\n    <ng-content select=\"mat-chip-trailing-icon,[matChipRemove],[matChipTrailingIcon]\"></ng-content>\n  </span>\n}\n\n<span class=\"cdk-visually-hidden\" [id]=\"_ariaDescriptionId\">{{ariaDescription}}</span>\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {FocusKeyManager} from '@angular/cdk/a11y';\nimport {Directionality} from '@angular/cdk/bidi';\nimport {\n  AfterViewInit,\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  ContentChildren,\n  ElementRef,\n  Input,\n  OnDestroy,\n  QueryList,\n  ViewEncapsulation,\n  booleanAttribute,\n  numberAttribute,\n  inject,\n} from '@angular/core';\nimport {Observable, Subject, merge} from 'rxjs';\nimport {startWith, switchMap, takeUntil} from 'rxjs/operators';\nimport {MatChip, MatChipEvent} from './chip';\nimport {MatChipAction} from './chip-action';\n\n/**\n * Basic container component for the MatChip component.\n *\n * Extended by MatChipListbox and MatChipGrid for different interaction patterns.\n */\n@Component({\n  selector: 'mat-chip-set',\n  template: `\n    <div class=\"mdc-evolution-chip-set__chips\" role=\"presentation\">\n      <ng-content></ng-content>\n    </div>\n  `,\n  styleUrl: 'chip-set.css',\n  host: {\n    'class': 'mat-mdc-chip-set mdc-evolution-chip-set',\n    '(keydown)': '_handleKeydown($event)',\n    '[attr.role]': 'role',\n  },\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n})\nexport class MatChipSet implements AfterViewInit, OnDestroy {\n  protected _elementRef = inject<ElementRef<HTMLElement>>(ElementRef);\n  protected _changeDetectorRef = inject(ChangeDetectorRef);\n  private _dir = inject(Directionality, {optional: true});\n\n  /** Index of the last destroyed chip that had focus. */\n  private _lastDestroyedFocusedChipIndex: number | null = null;\n\n  /** Used to manage focus within the chip list. */\n  protected _keyManager: FocusKeyManager<MatChipAction>;\n\n  /** Subject that emits when the component has been destroyed. */\n  protected _destroyed = new Subject<void>();\n\n  /** Role to use if it hasn't been overwritten by the user. */\n  protected _defaultRole = 'presentation';\n\n  /** Combined stream of all of the child chips' focus events. */\n  get chipFocusChanges(): Observable<MatChipEvent> {\n    return this._getChipStream(chip => chip._onFocus);\n  }\n\n  /** Combined stream of all of the child chips' destroy events. */\n  get chipDestroyedChanges(): Observable<MatChipEvent> {\n    return this._getChipStream(chip => chip.destroyed);\n  }\n\n  /** Combined stream of all of the child chips' remove events. */\n  get chipRemovedChanges(): Observable<MatChipEvent> {\n    return this._getChipStream(chip => chip.removed);\n  }\n\n  /** Whether the chip set is disabled. */\n  @Input({transform: booleanAttribute})\n  get disabled(): boolean {\n    return this._disabled;\n  }\n  set disabled(value: boolean) {\n    this._disabled = value;\n    this._syncChipsState();\n  }\n  protected _disabled: boolean = false;\n\n  /** Whether the chip list contains chips or not. */\n  get empty(): boolean {\n    return !this._chips || this._chips.length === 0;\n  }\n\n  /** The ARIA role applied to the chip set. */\n  @Input()\n  get role(): string | null {\n    if (this._explicitRole) {\n      return this._explicitRole;\n    }\n\n    return this.empty ? null : this._defaultRole;\n  }\n\n  /** Tabindex of the chip set. */\n  @Input({\n    transform: (value: unknown) => (value == null ? 0 : numberAttribute(value)),\n  })\n  tabIndex: number = 0;\n\n  set role(value: string | null) {\n    this._explicitRole = value;\n  }\n  private _explicitRole: string | null = null;\n\n  /** Whether any of the chips inside of this chip-set has focus. */\n  get focused(): boolean {\n    return this._hasFocusedChip();\n  }\n\n  /** The chips that are part of this chip set. */\n  @ContentChildren(MatChip, {\n    // We need to use `descendants: true`, because Ivy will no longer match\n    // indirect descendants if it's left as false.\n    descendants: true,\n  })\n  _chips: QueryList<MatChip>;\n\n  /** Flat list of all the actions contained within the chips. */\n  _chipActions = new QueryList<MatChipAction>();\n\n  constructor(...args: unknown[]);\n  constructor() {}\n\n  ngAfterViewInit() {\n    this._setUpFocusManagement();\n    this._trackChipSetChanges();\n    this._trackDestroyedFocusedChip();\n  }\n\n  ngOnDestroy() {\n    this._keyManager?.destroy();\n    this._chipActions.destroy();\n    this._destroyed.next();\n    this._destroyed.complete();\n  }\n\n  /** Checks whether any of the chips is focused. */\n  protected _hasFocusedChip() {\n    return this._chips && this._chips.some(chip => chip._hasFocus());\n  }\n\n  /** Syncs the chip-set's state with the individual chips. */\n  protected _syncChipsState() {\n    this._chips?.forEach(chip => {\n      chip._chipListDisabled = this._disabled;\n      chip._changeDetectorRef.markForCheck();\n    });\n  }\n\n  /** Dummy method for subclasses to override. Base chip set cannot be focused. */\n  focus() {}\n\n  /** Handles keyboard events on the chip set. */\n  _handleKeydown(event: KeyboardEvent) {\n    if (this._originatesFromChip(event)) {\n      this._keyManager.onKeydown(event);\n    }\n  }\n\n  /**\n   * Utility to ensure all indexes are valid.\n   *\n   * @param index The index to be checked.\n   * @returns True if the index is valid for our list of chips.\n   */\n  protected _isValidIndex(index: number): boolean {\n    return index >= 0 && index < this._chips.length;\n  }\n\n  /**\n   * Removes the `tabindex` from the chip set and resets it back afterwards, allowing the\n   * user to tab out of it. This prevents the set from capturing focus and redirecting\n   * it back to the first chip, creating a focus trap, if it user tries to tab away.\n   */\n  protected _allowFocusEscape() {\n    const previous = this._elementRef.nativeElement.tabIndex;\n\n    if (previous !== -1) {\n      // Set the tabindex directly on the element, instead of going through\n      // the data binding, because we aren't guaranteed that change detection\n      // will run quickly enough to allow focus to escape.\n      this._elementRef.nativeElement.tabIndex = -1;\n\n      // Note that this needs to be a `setTimeout`, because a `Promise.resolve`\n      // doesn't allow enough time for the focus to escape.\n      setTimeout(() => (this._elementRef.nativeElement.tabIndex = previous));\n    }\n  }\n\n  /**\n   * Gets a stream of events from all the chips within the set.\n   * The stream will automatically incorporate any newly-added chips.\n   */\n  protected _getChipStream<T, C extends MatChip = MatChip>(\n    mappingFunction: (chip: C) => Observable<T>,\n  ): Observable<T> {\n    return this._chips.changes.pipe(\n      startWith(null),\n      switchMap(() => merge(...(this._chips as QueryList<C>).map(mappingFunction))),\n    );\n  }\n\n  /** Checks whether an event comes from inside a chip element. */\n  protected _originatesFromChip(event: Event): boolean {\n    let currentElement = event.target as HTMLElement | null;\n\n    while (currentElement && currentElement !== this._elementRef.nativeElement) {\n      if (currentElement.classList.contains('mat-mdc-chip')) {\n        return true;\n      }\n      currentElement = currentElement.parentElement;\n    }\n    return false;\n  }\n\n  /** Sets up the chip set's focus management logic. */\n  private _setUpFocusManagement() {\n    // Create a flat `QueryList` containing the actions of all of the chips.\n    // This allows us to navigate both within the chip and move to the next/previous\n    // one using the existing `ListKeyManager`.\n    this._chips.changes.pipe(startWith(this._chips)).subscribe((chips: QueryList<MatChip>) => {\n      const actions: MatChipAction[] = [];\n      chips.forEach(chip => chip._getActions().forEach(action => actions.push(action)));\n      this._chipActions.reset(actions);\n      this._chipActions.notifyOnChanges();\n    });\n\n    this._keyManager = new FocusKeyManager(this._chipActions)\n      .withVerticalOrientation()\n      .withHorizontalOrientation(this._dir ? this._dir.value : 'ltr')\n      .withHomeAndEnd()\n      .skipPredicate(action => this._skipPredicate(action));\n\n    // Keep the manager active index in sync so that navigation picks\n    // up from the current chip if the user clicks into the list directly.\n    this.chipFocusChanges.pipe(takeUntil(this._destroyed)).subscribe(({chip}) => {\n      const action = chip._getSourceAction(document.activeElement as Element);\n\n      if (action) {\n        this._keyManager.updateActiveItem(action);\n      }\n    });\n\n    this._dir?.change\n      .pipe(takeUntil(this._destroyed))\n      .subscribe(direction => this._keyManager.withHorizontalOrientation(direction));\n  }\n\n  /**\n   * Determines if key manager should avoid putting a given chip action in the tab index. Skip\n   * non-interactive and disabled actions since the user can't do anything with them.\n   */\n  protected _skipPredicate(action: MatChipAction): boolean {\n    // Skip chips that the user cannot interact with. `mat-chip-set` does not permit focusing disabled\n    // chips.\n    return !action.isInteractive || action.disabled;\n  }\n\n  /** Listens to changes in the chip set and syncs up the state of the individual chips. */\n  private _trackChipSetChanges() {\n    this._chips.changes.pipe(startWith(null), takeUntil(this._destroyed)).subscribe(() => {\n      if (this.disabled) {\n        // Since this happens after the content has been\n        // checked, we need to defer it to the next tick.\n        Promise.resolve().then(() => this._syncChipsState());\n      }\n\n      this._redirectDestroyedChipFocus();\n    });\n  }\n\n  /** Starts tracking the destroyed chips in order to capture the focused one. */\n  private _trackDestroyedFocusedChip() {\n    this.chipDestroyedChanges.pipe(takeUntil(this._destroyed)).subscribe((event: MatChipEvent) => {\n      const chipArray = this._chips.toArray();\n      const chipIndex = chipArray.indexOf(event.chip);\n\n      // If the focused chip is destroyed, save its index so that we can move focus to the next\n      // chip. We only save the index here, rather than move the focus immediately, because we want\n      // to wait until the chip is removed from the chip list before focusing the next one. This\n      // allows us to keep focus on the same index if the chip gets swapped out.\n      if (this._isValidIndex(chipIndex) && event.chip._hasFocus()) {\n        this._lastDestroyedFocusedChipIndex = chipIndex;\n      }\n    });\n  }\n\n  /**\n   * Finds the next appropriate chip to move focus to,\n   * if the currently-focused chip is destroyed.\n   */\n  private _redirectDestroyedChipFocus() {\n    if (this._lastDestroyedFocusedChipIndex == null) {\n      return;\n    }\n\n    if (this._chips.length) {\n      const newIndex = Math.min(this._lastDestroyedFocusedChipIndex, this._chips.length - 1);\n      const chipToFocus = this._chips.toArray()[newIndex];\n\n      if (chipToFocus.disabled) {\n        // If we're down to one disabled chip, move focus back to the set.\n        if (this._chips.length === 1) {\n          this.focus();\n        } else {\n          this._keyManager.setPreviousItemActive();\n        }\n      } else {\n        chipToFocus.focus();\n      }\n    } else {\n      this.focus();\n    }\n\n    this._lastDestroyedFocusedChipIndex = null;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  AfterContentInit,\n  booleanAttribute,\n  ChangeDetectionStrategy,\n  Component,\n  ContentChildren,\n  EventEmitter,\n  forwardRef,\n  inject,\n  Input,\n  OnDestroy,\n  Output,\n  QueryList,\n  ViewEncapsulation,\n} from '@angular/core';\nimport {ControlValueAccessor, NG_VALUE_ACCESSOR} from '@angular/forms';\nimport {Observable} from 'rxjs';\nimport {startWith, takeUntil} from 'rxjs/operators';\nimport {TAB} from '@angular/cdk/keycodes';\nimport {MatChip, MatChipEvent} from './chip';\nimport {MatChipOption, MatChipSelectionChange} from './chip-option';\nimport {MatChipSet} from './chip-set';\nimport {MatChipAction} from './chip-action';\nimport {MAT_CHIPS_DEFAULT_OPTIONS} from './tokens';\n\n/** Change event object that is emitted when the chip listbox value has changed. */\nexport class MatChipListboxChange {\n  constructor(\n    /** Chip listbox that emitted the event. */\n    public source: MatChipListbox,\n    /** Value of the chip listbox when the event was emitted. */\n    public value: any,\n  ) {}\n}\n\n/**\n * Provider Expression that allows mat-chip-listbox to register as a ControlValueAccessor.\n * This allows it to support [(ngModel)].\n * @docs-private\n */\nexport const MAT_CHIP_LISTBOX_CONTROL_VALUE_ACCESSOR: any = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => MatChipListbox),\n  multi: true,\n};\n\n/**\n * An extension of the MatChipSet component that supports chip selection.\n * Used with MatChipOption chips.\n */\n@Component({\n  selector: 'mat-chip-listbox',\n  template: `\n    <div class=\"mdc-evolution-chip-set__chips\" role=\"presentation\">\n      <ng-content></ng-content>\n    </div>\n  `,\n  styleUrl: 'chip-set.css',\n  host: {\n    'class': 'mdc-evolution-chip-set mat-mdc-chip-listbox',\n    '[attr.role]': 'role',\n    '[tabIndex]': '(disabled || empty) ? -1 : tabIndex',\n    '[attr.aria-required]': 'role ? required : null',\n    '[attr.aria-disabled]': 'disabled.toString()',\n    '[attr.aria-multiselectable]': 'multiple',\n    '[attr.aria-orientation]': 'ariaOrientation',\n    '[class.mat-mdc-chip-list-disabled]': 'disabled',\n    '[class.mat-mdc-chip-list-required]': 'required',\n    '(focus)': 'focus()',\n    '(blur)': '_blur()',\n    '(keydown)': '_keydown($event)',\n  },\n  providers: [MAT_CHIP_LISTBOX_CONTROL_VALUE_ACCESSOR],\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n})\nexport class MatChipListbox\n  extends MatChipSet\n  implements AfterContentInit, OnDestroy, ControlValueAccessor\n{\n  /**\n   * Function when touched. Set as part of ControlValueAccessor implementation.\n   * @docs-private\n   */\n  _onTouched = () => {};\n\n  /**\n   * Function when changed. Set as part of ControlValueAccessor implementation.\n   * @docs-private\n   */\n  _onChange: (value: any) => void = () => {};\n\n  // TODO: MDC uses `grid` here\n  protected override _defaultRole = 'listbox';\n\n  /** Default chip options. */\n  private _defaultOptions = inject(MAT_CHIPS_DEFAULT_OPTIONS, {optional: true});\n\n  /** Whether the user should be allowed to select multiple chips. */\n  @Input({transform: booleanAttribute})\n  get multiple(): boolean {\n    return this._multiple;\n  }\n  set multiple(value: boolean) {\n    this._multiple = value;\n    this._syncListboxProperties();\n  }\n  private _multiple: boolean = false;\n\n  /** The array of selected chips inside the chip listbox. */\n  get selected(): MatChipOption[] | MatChipOption {\n    const selectedChips = this._chips.toArray().filter(chip => chip.selected);\n    return this.multiple ? selectedChips : selectedChips[0];\n  }\n\n  /** Orientation of the chip list. */\n  @Input('aria-orientation') ariaOrientation: 'horizontal' | 'vertical' = 'horizontal';\n\n  /**\n   * Whether or not this chip listbox is selectable.\n   *\n   * When a chip listbox is not selectable, the selected states for all\n   * the chips inside the chip listbox are always ignored.\n   */\n  @Input({transform: booleanAttribute})\n  get selectable(): boolean {\n    return this._selectable;\n  }\n  set selectable(value: boolean) {\n    this._selectable = value;\n    this._syncListboxProperties();\n  }\n  protected _selectable: boolean = true;\n\n  /**\n   * A function to compare the option values with the selected values. The first argument\n   * is a value from an option. The second is a value from the selection. A boolean\n   * should be returned.\n   */\n  @Input() compareWith: (o1: any, o2: any) => boolean = (o1: any, o2: any) => o1 === o2;\n\n  /** Whether this chip listbox is required. */\n  @Input({transform: booleanAttribute})\n  required: boolean = false;\n\n  /** Whether checkmark indicator for single-selection options is hidden. */\n  @Input({transform: booleanAttribute})\n  get hideSingleSelectionIndicator(): boolean {\n    return this._hideSingleSelectionIndicator;\n  }\n  set hideSingleSelectionIndicator(value: boolean) {\n    this._hideSingleSelectionIndicator = value;\n    this._syncListboxProperties();\n  }\n  private _hideSingleSelectionIndicator: boolean =\n    this._defaultOptions?.hideSingleSelectionIndicator ?? false;\n\n  /** Combined stream of all of the child chips' selection change events. */\n  get chipSelectionChanges(): Observable<MatChipSelectionChange> {\n    return this._getChipStream<MatChipSelectionChange, MatChipOption>(chip => chip.selectionChange);\n  }\n\n  /** Combined stream of all of the child chips' blur events. */\n  get chipBlurChanges(): Observable<MatChipEvent> {\n    return this._getChipStream(chip => chip._onBlur);\n  }\n\n  /** The value of the listbox, which is the combined value of the selected chips. */\n  @Input()\n  get value(): any {\n    return this._value;\n  }\n  set value(value: any) {\n    if (this._chips && this._chips.length) {\n      this._setSelectionByValue(value, false);\n    }\n    this._value = value;\n  }\n  protected _value: any;\n\n  /** Event emitted when the selected chip listbox value has been changed by the user. */\n  @Output() readonly change: EventEmitter<MatChipListboxChange> =\n    new EventEmitter<MatChipListboxChange>();\n\n  @ContentChildren(MatChipOption, {\n    // We need to use `descendants: true`, because Ivy will no longer match\n    // indirect descendants if it's left as false.\n    descendants: true,\n  })\n  // We need an initializer here to avoid a TS error. The value will be set in `ngAfterViewInit`.\n  override _chips: QueryList<MatChipOption> = undefined!;\n\n  ngAfterContentInit() {\n    this._chips.changes.pipe(startWith(null), takeUntil(this._destroyed)).subscribe(() => {\n      if (this.value !== undefined) {\n        Promise.resolve().then(() => {\n          this._setSelectionByValue(this.value, false);\n        });\n      }\n      // Update listbox selectable/multiple properties on chips\n      this._syncListboxProperties();\n    });\n\n    this.chipBlurChanges.pipe(takeUntil(this._destroyed)).subscribe(() => this._blur());\n    this.chipSelectionChanges.pipe(takeUntil(this._destroyed)).subscribe(event => {\n      if (!this.multiple) {\n        this._chips.forEach(chip => {\n          if (chip !== event.source) {\n            chip._setSelectedState(false, false, false);\n          }\n        });\n      }\n\n      if (event.isUserInput) {\n        this._propagateChanges();\n      }\n    });\n  }\n\n  /**\n   * Focuses the first selected chip in this chip listbox, or the first non-disabled chip when there\n   * are no selected chips.\n   */\n  override focus(): void {\n    if (this.disabled) {\n      return;\n    }\n\n    const firstSelectedChip = this._getFirstSelectedChip();\n\n    if (firstSelectedChip && !firstSelectedChip.disabled) {\n      firstSelectedChip.focus();\n    } else if (this._chips.length > 0) {\n      this._keyManager.setFirstItemActive();\n    } else {\n      this._elementRef.nativeElement.focus();\n    }\n  }\n\n  /**\n   * Implemented as part of ControlValueAccessor.\n   * @docs-private\n   */\n  writeValue(value: any): void {\n    if (value != null) {\n      this.value = value;\n    } else {\n      this.value = undefined;\n    }\n  }\n\n  /**\n   * Implemented as part of ControlValueAccessor.\n   * @docs-private\n   */\n  registerOnChange(fn: (value: any) => void): void {\n    this._onChange = fn;\n  }\n\n  /**\n   * Implemented as part of ControlValueAccessor.\n   * @docs-private\n   */\n  registerOnTouched(fn: () => void): void {\n    this._onTouched = fn;\n  }\n\n  /**\n   * Implemented as part of ControlValueAccessor.\n   * @docs-private\n   */\n  setDisabledState(isDisabled: boolean): void {\n    this.disabled = isDisabled;\n  }\n\n  /** Selects all chips with value. */\n  _setSelectionByValue(value: any, isUserInput: boolean = true) {\n    this._clearSelection();\n\n    if (Array.isArray(value)) {\n      value.forEach(currentValue => this._selectValue(currentValue, isUserInput));\n    } else {\n      this._selectValue(value, isUserInput);\n    }\n  }\n\n  /** When blurred, marks the field as touched when focus moved outside the chip listbox. */\n  _blur() {\n    if (!this.disabled) {\n      // Wait to see if focus moves to an individual chip.\n      setTimeout(() => {\n        if (!this.focused) {\n          this._markAsTouched();\n        }\n      });\n    }\n  }\n\n  _keydown(event: KeyboardEvent) {\n    if (event.keyCode === TAB) {\n      super._allowFocusEscape();\n    }\n  }\n\n  /** Marks the field as touched */\n  private _markAsTouched() {\n    this._onTouched();\n    this._changeDetectorRef.markForCheck();\n  }\n\n  /** Emits change event to set the model value. */\n  private _propagateChanges(): void {\n    let valueToEmit: any = null;\n\n    if (Array.isArray(this.selected)) {\n      valueToEmit = this.selected.map(chip => chip.value);\n    } else {\n      valueToEmit = this.selected ? this.selected.value : undefined;\n    }\n    this._value = valueToEmit;\n    this.change.emit(new MatChipListboxChange(this, valueToEmit));\n    this._onChange(valueToEmit);\n    this._changeDetectorRef.markForCheck();\n  }\n\n  /**\n   * Deselects every chip in the listbox.\n   * @param skip Chip that should not be deselected.\n   */\n  private _clearSelection(skip?: MatChip): void {\n    this._chips.forEach(chip => {\n      if (chip !== skip) {\n        chip.deselect();\n      }\n    });\n  }\n\n  /**\n   * Finds and selects the chip based on its value.\n   * @returns Chip that has the corresponding value.\n   */\n  private _selectValue(value: any, isUserInput: boolean): MatChip | undefined {\n    const correspondingChip = this._chips.find(chip => {\n      return chip.value != null && this.compareWith(chip.value, value);\n    });\n\n    if (correspondingChip) {\n      isUserInput ? correspondingChip.selectViaInteraction() : correspondingChip.select();\n    }\n\n    return correspondingChip;\n  }\n\n  /** Syncs the chip-listbox selection state with the individual chips. */\n  private _syncListboxProperties() {\n    if (this._chips) {\n      // Defer setting the value in order to avoid the \"Expression\n      // has changed after it was checked\" errors from Angular.\n      Promise.resolve().then(() => {\n        this._chips.forEach(chip => {\n          chip._chipListMultiple = this.multiple;\n          chip.chipListSelectable = this._selectable;\n          chip._chipListHideSingleSelectionIndicator = this.hideSingleSelectionIndicator;\n          chip._changeDetectorRef.markForCheck();\n        });\n      });\n    }\n  }\n\n  /** Returns the first selected chip in this listbox, or undefined if no chips are selected. */\n  private _getFirstSelectedChip(): MatChipOption | undefined {\n    if (Array.isArray(this.selected)) {\n      return this.selected.length ? this.selected[0] : undefined;\n    } else {\n      return this.selected;\n    }\n  }\n\n  /**\n   * Determines if key manager should avoid putting a given chip action in the tab index. Skip\n   * non-interactive actions since the user can't do anything with them.\n   */\n  protected override _skipPredicate(action: MatChipAction): boolean {\n    // Override the skip predicate in the base class to avoid skipping disabled chips. Allow\n    // disabled chip options to receive focus to align with WAI ARIA recommendation. Normally WAI\n    // ARIA's instructions are to exclude disabled items from the tab order, but it makes a few\n    // exceptions for compound widgets.\n    //\n    // From [Developing a Keyboard Interface](\n    // https://www.w3.org/WAI/ARIA/apg/practices/keyboard-interface/):\n    //   \"For the following composite widget elements, keep them focusable when disabled: Options in a\n    //   Listbox...\"\n    return !action.isInteractive;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {DOWN_ARROW, hasModifier<PERSON><PERSON>, TAB, UP_ARROW} from '@angular/cdk/keycodes';\nimport {\n  AfterContentInit,\n  AfterViewInit,\n  booleanAttribute,\n  ChangeDetectionStrategy,\n  Component,\n  ContentChildren,\n  DoCheck,\n  EventEmitter,\n  Input,\n  OnDestroy,\n  Output,\n  QueryList,\n  ViewEncapsulation,\n  inject,\n} from '@angular/core';\nimport {\n  ControlValueAccessor,\n  FormGroupDirective,\n  NgControl,\n  NgForm,\n  Validators,\n} from '@angular/forms';\nimport {_ErrorStateTracker, ErrorStateMatcher} from '../core';\nimport {MatFormFieldControl} from '../form-field';\nimport {merge, Observable, Subject} from 'rxjs';\nimport {takeUntil} from 'rxjs/operators';\nimport {MatChipEvent} from './chip';\nimport {MatChipRow} from './chip-row';\nimport {MatChipSet} from './chip-set';\nimport {MatChipTextControl} from './chip-text-control';\n\n/** Change event object that is emitted when the chip grid value has changed. */\nexport class MatChipGridChange {\n  constructor(\n    /** Chip grid that emitted the event. */\n    public source: MatChipGrid,\n    /** Value of the chip grid when the event was emitted. */\n    public value: any,\n  ) {}\n}\n\n/**\n * An extension of the MatChipSet component used with MatChipRow chips and\n * the matChipInputFor directive.\n */\n@Component({\n  selector: 'mat-chip-grid',\n  template: `\n    <div class=\"mdc-evolution-chip-set__chips\" role=\"presentation\">\n      <ng-content></ng-content>\n    </div>\n  `,\n  styleUrl: 'chip-set.css',\n  host: {\n    'class': 'mat-mdc-chip-set mat-mdc-chip-grid mdc-evolution-chip-set',\n    '[attr.role]': 'role',\n    '[attr.tabindex]': '(disabled || (_chips && _chips.length === 0)) ? -1 : tabIndex',\n    '[attr.aria-disabled]': 'disabled.toString()',\n    '[attr.aria-invalid]': 'errorState',\n    '[class.mat-mdc-chip-list-disabled]': 'disabled',\n    '[class.mat-mdc-chip-list-invalid]': 'errorState',\n    '[class.mat-mdc-chip-list-required]': 'required',\n    '(focus)': 'focus()',\n    '(blur)': '_blur()',\n  },\n  providers: [{provide: MatFormFieldControl, useExisting: MatChipGrid}],\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n})\nexport class MatChipGrid\n  extends MatChipSet\n  implements\n    AfterContentInit,\n    AfterViewInit,\n    ControlValueAccessor,\n    DoCheck,\n    MatFormFieldControl<any>,\n    OnDestroy\n{\n  ngControl = inject(NgControl, {optional: true, self: true})!;\n\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  readonly controlType: string = 'mat-chip-grid';\n\n  /** The chip input to add more chips */\n  protected _chipInput: MatChipTextControl;\n\n  protected override _defaultRole = 'grid';\n  private _errorStateTracker: _ErrorStateTracker;\n\n  /**\n   * List of element ids to propagate to the chipInput's aria-describedby attribute.\n   */\n  private _ariaDescribedbyIds: string[] = [];\n\n  /**\n   * Function when touched. Set as part of ControlValueAccessor implementation.\n   * @docs-private\n   */\n  _onTouched = () => {};\n\n  /**\n   * Function when changed. Set as part of ControlValueAccessor implementation.\n   * @docs-private\n   */\n  _onChange: (value: any) => void = () => {};\n\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  @Input({transform: booleanAttribute})\n  override get disabled(): boolean {\n    return this.ngControl ? !!this.ngControl.disabled : this._disabled;\n  }\n  override set disabled(value: boolean) {\n    this._disabled = value;\n    this._syncChipsState();\n    this.stateChanges.next();\n  }\n\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  get id(): string {\n    return this._chipInput.id;\n  }\n\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  override get empty(): boolean {\n    return (\n      (!this._chipInput || this._chipInput.empty) && (!this._chips || this._chips.length === 0)\n    );\n  }\n\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  @Input()\n  get placeholder(): string {\n    return this._chipInput ? this._chipInput.placeholder : this._placeholder;\n  }\n  set placeholder(value: string) {\n    this._placeholder = value;\n    this.stateChanges.next();\n  }\n  protected _placeholder: string;\n\n  /** Whether any chips or the matChipInput inside of this chip-grid has focus. */\n  override get focused(): boolean {\n    return this._chipInput.focused || this._hasFocusedChip();\n  }\n\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  @Input({transform: booleanAttribute})\n  get required(): boolean {\n    return this._required ?? this.ngControl?.control?.hasValidator(Validators.required) ?? false;\n  }\n  set required(value: boolean) {\n    this._required = value;\n    this.stateChanges.next();\n  }\n  protected _required: boolean | undefined;\n\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  get shouldLabelFloat(): boolean {\n    return !this.empty || this.focused;\n  }\n\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  @Input()\n  get value(): any {\n    return this._value;\n  }\n  set value(value: any) {\n    this._value = value;\n  }\n  protected _value: any[] = [];\n\n  /** An object used to control when error messages are shown. */\n  @Input()\n  get errorStateMatcher() {\n    return this._errorStateTracker.matcher;\n  }\n  set errorStateMatcher(value: ErrorStateMatcher) {\n    this._errorStateTracker.matcher = value;\n  }\n\n  /** Combined stream of all of the child chips' blur events. */\n  get chipBlurChanges(): Observable<MatChipEvent> {\n    return this._getChipStream(chip => chip._onBlur);\n  }\n\n  /** Emits when the chip grid value has been changed by the user. */\n  @Output() readonly change: EventEmitter<MatChipGridChange> =\n    new EventEmitter<MatChipGridChange>();\n\n  /**\n   * Emits whenever the raw value of the chip-grid changes. This is here primarily\n   * to facilitate the two-way binding for the `value` input.\n   * @docs-private\n   */\n  @Output() readonly valueChange: EventEmitter<any> = new EventEmitter<any>();\n\n  @ContentChildren(MatChipRow, {\n    // We need to use `descendants: true`, because Ivy will no longer match\n    // indirect descendants if it's left as false.\n    descendants: true,\n  })\n  // We need an initializer here to avoid a TS error. The value will be set in `ngAfterViewInit`.\n  override _chips: QueryList<MatChipRow> = undefined!;\n\n  /**\n   * Emits whenever the component state changes and should cause the parent\n   * form-field to update. Implemented as part of `MatFormFieldControl`.\n   * @docs-private\n   */\n  readonly stateChanges = new Subject<void>();\n\n  /** Whether the chip grid is in an error state. */\n  get errorState() {\n    return this._errorStateTracker.errorState;\n  }\n  set errorState(value: boolean) {\n    this._errorStateTracker.errorState = value;\n  }\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    super();\n\n    const parentForm = inject(NgForm, {optional: true});\n    const parentFormGroup = inject(FormGroupDirective, {optional: true});\n    const defaultErrorStateMatcher = inject(ErrorStateMatcher);\n\n    if (this.ngControl) {\n      this.ngControl.valueAccessor = this;\n    }\n\n    this._errorStateTracker = new _ErrorStateTracker(\n      defaultErrorStateMatcher,\n      this.ngControl,\n      parentFormGroup,\n      parentForm,\n      this.stateChanges,\n    );\n  }\n\n  ngAfterContentInit() {\n    this.chipBlurChanges.pipe(takeUntil(this._destroyed)).subscribe(() => {\n      this._blur();\n      this.stateChanges.next();\n    });\n\n    merge(this.chipFocusChanges, this._chips.changes)\n      .pipe(takeUntil(this._destroyed))\n      .subscribe(() => this.stateChanges.next());\n  }\n\n  override ngAfterViewInit() {\n    super.ngAfterViewInit();\n\n    if (!this._chipInput && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error('mat-chip-grid must be used in combination with matChipInputFor.');\n    }\n  }\n\n  ngDoCheck() {\n    if (this.ngControl) {\n      // We need to re-evaluate this on every change detection cycle, because there are some\n      // error triggers that we can't subscribe to (e.g. parent form submissions). This means\n      // that whatever logic is in here has to be super lean or we risk destroying the performance.\n      this.updateErrorState();\n    }\n  }\n\n  override ngOnDestroy() {\n    super.ngOnDestroy();\n    this.stateChanges.complete();\n  }\n\n  /** Associates an HTML input element with this chip grid. */\n  registerInput(inputElement: MatChipTextControl): void {\n    this._chipInput = inputElement;\n    this._chipInput.setDescribedByIds(this._ariaDescribedbyIds);\n  }\n\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  onContainerClick(event: MouseEvent) {\n    if (!this.disabled && !this._originatesFromChip(event)) {\n      this.focus();\n    }\n  }\n\n  /**\n   * Focuses the first chip in this chip grid, or the associated input when there\n   * are no eligible chips.\n   */\n  override focus(): void {\n    if (this.disabled || this._chipInput.focused) {\n      return;\n    }\n\n    if (!this._chips.length || this._chips.first.disabled) {\n      // Delay until the next tick, because this can cause a \"changed after checked\"\n      // error if the input does something on focus (e.g. opens an autocomplete).\n      Promise.resolve().then(() => this._chipInput.focus());\n    } else {\n      const activeItem = this._keyManager.activeItem;\n\n      if (activeItem) {\n        activeItem.focus();\n      } else {\n        this._keyManager.setFirstItemActive();\n      }\n    }\n\n    this.stateChanges.next();\n  }\n\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  setDescribedByIds(ids: string[]) {\n    // We must keep this up to date to handle the case where ids are set\n    // before the chip input is registered.\n    this._ariaDescribedbyIds = ids;\n    this._chipInput?.setDescribedByIds(ids);\n  }\n\n  /**\n   * Implemented as part of ControlValueAccessor.\n   * @docs-private\n   */\n  writeValue(value: any): void {\n    // The user is responsible for creating the child chips, so we just store the value.\n    this._value = value;\n  }\n\n  /**\n   * Implemented as part of ControlValueAccessor.\n   * @docs-private\n   */\n  registerOnChange(fn: (value: any) => void): void {\n    this._onChange = fn;\n  }\n\n  /**\n   * Implemented as part of ControlValueAccessor.\n   * @docs-private\n   */\n  registerOnTouched(fn: () => void): void {\n    this._onTouched = fn;\n  }\n\n  /**\n   * Implemented as part of ControlValueAccessor.\n   * @docs-private\n   */\n  setDisabledState(isDisabled: boolean): void {\n    this.disabled = isDisabled;\n    this.stateChanges.next();\n  }\n\n  /** Refreshes the error state of the chip grid. */\n  updateErrorState() {\n    this._errorStateTracker.updateErrorState();\n  }\n\n  /** When blurred, mark the field as touched when focus moved outside the chip grid. */\n  _blur() {\n    if (!this.disabled) {\n      // Check whether the focus moved to chip input.\n      // If the focus is not moved to chip input, mark the field as touched. If the focus moved\n      // to chip input, do nothing.\n      // Timeout is needed to wait for the focus() event trigger on chip input.\n      setTimeout(() => {\n        if (!this.focused) {\n          this._propagateChanges();\n          this._markAsTouched();\n        }\n      });\n    }\n  }\n\n  /**\n   * Removes the `tabindex` from the chip grid and resets it back afterwards, allowing the\n   * user to tab out of it. This prevents the grid from capturing focus and redirecting\n   * it back to the first chip, creating a focus trap, if it user tries to tab away.\n   */\n  protected override _allowFocusEscape() {\n    if (!this._chipInput.focused) {\n      super._allowFocusEscape();\n    }\n  }\n\n  /** Handles custom keyboard events. */\n  override _handleKeydown(event: KeyboardEvent) {\n    const keyCode = event.keyCode;\n    const activeItem = this._keyManager.activeItem;\n\n    if (keyCode === TAB) {\n      if (\n        this._chipInput.focused &&\n        hasModifierKey(event, 'shiftKey') &&\n        this._chips.length &&\n        !this._chips.last.disabled\n      ) {\n        event.preventDefault();\n\n        if (activeItem) {\n          this._keyManager.setActiveItem(activeItem);\n        } else {\n          this._focusLastChip();\n        }\n      } else {\n        // Use the super method here since it doesn't check for the input\n        // focused state. This allows focus to escape if there's only one\n        // disabled chip left in the list.\n        super._allowFocusEscape();\n      }\n    } else if (!this._chipInput.focused) {\n      // The up and down arrows are supposed to navigate between the individual rows in the grid.\n      // We do this by filtering the actions down to the ones that have the same `_isPrimary`\n      // flag as the active action and moving focus between them ourseles instead of delegating\n      // to the key manager. For more information, see #29359 and:\n      // https://www.w3.org/WAI/ARIA/apg/patterns/grid/examples/layout-grids/#ex2_label\n      if ((keyCode === UP_ARROW || keyCode === DOWN_ARROW) && activeItem) {\n        const eligibleActions = this._chipActions.filter(\n          action => action._isPrimary === activeItem._isPrimary && !this._skipPredicate(action),\n        );\n        const currentIndex = eligibleActions.indexOf(activeItem);\n        const delta = event.keyCode === UP_ARROW ? -1 : 1;\n\n        event.preventDefault();\n        if (currentIndex > -1 && this._isValidIndex(currentIndex + delta)) {\n          this._keyManager.setActiveItem(eligibleActions[currentIndex + delta]);\n        }\n      } else {\n        super._handleKeydown(event);\n      }\n    }\n\n    this.stateChanges.next();\n  }\n\n  _focusLastChip() {\n    if (this._chips.length) {\n      this._chips.last.focus();\n    }\n  }\n\n  /** Emits change event to set the model value. */\n  private _propagateChanges(): void {\n    const valueToEmit = this._chips.length ? this._chips.toArray().map(chip => chip.value) : [];\n    this._value = valueToEmit;\n    this.change.emit(new MatChipGridChange(this, valueToEmit));\n    this.valueChange.emit(valueToEmit);\n    this._onChange(valueToEmit);\n    this._changeDetectorRef.markForCheck();\n  }\n\n  /** Mark the field as touched */\n  private _markAsTouched() {\n    this._onTouched();\n    this._changeDetectorRef.markForCheck();\n    this.stateChanges.next();\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {BACKSPACE, hasModifierKey} from '@angular/cdk/keycodes';\nimport {\n  Directive,\n  ElementRef,\n  EventEmitter,\n  Input,\n  OnChanges,\n  OnDestroy,\n  Output,\n  booleanAttribute,\n  inject,\n} from '@angular/core';\nimport {_IdGenerator} from '@angular/cdk/a11y';\nimport {MatFormField, MAT_FORM_FIELD} from '../form-field';\nimport {MatChipsDefaultOptions, MAT_CHIPS_DEFAULT_OPTIONS} from './tokens';\nimport {MatChipGrid} from './chip-grid';\nimport {MatChipTextControl} from './chip-text-control';\n\n/** Represents an input event on a `matChipInput`. */\nexport interface MatChipInputEvent {\n  /**\n   * The native `<input>` element that the event is being fired for.\n   * @deprecated Use `MatChipInputEvent#chipInput.inputElement` instead.\n   * @breaking-change 13.0.0 This property will be removed.\n   */\n  input: HTMLInputElement;\n\n  /** The value of the input. */\n  value: string;\n\n  /** Reference to the chip input that emitted the event. */\n  chipInput: MatChipInput;\n}\n\n/**\n * Directive that adds chip-specific behaviors to an input element inside `<mat-form-field>`.\n * May be placed inside or outside of a `<mat-chip-grid>`.\n */\n@Directive({\n  selector: 'input[matChipInputFor]',\n  exportAs: 'matChipInput, matChipInputFor',\n  host: {\n    // TODO: eventually we should remove `mat-input-element` from here since it comes from the\n    // non-MDC version of the input. It's currently being kept for backwards compatibility, because\n    // the MDC chips were landed initially with it.\n    'class': 'mat-mdc-chip-input mat-mdc-input-element mdc-text-field__input mat-input-element',\n    '(keydown)': '_keydown($event)',\n    '(blur)': '_blur()',\n    '(focus)': '_focus()',\n    '(input)': '_onInput()',\n    '[id]': 'id',\n    '[attr.disabled]': 'disabled || null',\n    '[attr.placeholder]': 'placeholder || null',\n    '[attr.aria-invalid]': '_chipGrid && _chipGrid.ngControl ? _chipGrid.ngControl.invalid : null',\n    '[attr.aria-required]': '_chipGrid && _chipGrid.required || null',\n    '[attr.required]': '_chipGrid && _chipGrid.required || null',\n  },\n})\nexport class MatChipInput implements MatChipTextControl, OnChanges, OnDestroy {\n  protected _elementRef = inject<ElementRef<HTMLInputElement>>(ElementRef);\n\n  /** Whether the control is focused. */\n  focused: boolean = false;\n\n  /** Register input for chip list */\n  @Input('matChipInputFor')\n  get chipGrid(): MatChipGrid {\n    return this._chipGrid;\n  }\n  set chipGrid(value: MatChipGrid) {\n    if (value) {\n      this._chipGrid = value;\n      this._chipGrid.registerInput(this);\n    }\n  }\n  private _chipGrid: MatChipGrid;\n\n  /**\n   * Whether or not the chipEnd event will be emitted when the input is blurred.\n   */\n  @Input({alias: 'matChipInputAddOnBlur', transform: booleanAttribute})\n  addOnBlur: boolean = false;\n\n  /**\n   * The list of key codes that will trigger a chipEnd event.\n   *\n   * Defaults to `[ENTER]`.\n   */\n  @Input('matChipInputSeparatorKeyCodes')\n  separatorKeyCodes: readonly number[] | ReadonlySet<number>;\n\n  /** Emitted when a chip is to be added. */\n  @Output('matChipInputTokenEnd')\n  readonly chipEnd: EventEmitter<MatChipInputEvent> = new EventEmitter<MatChipInputEvent>();\n\n  /** The input's placeholder text. */\n  @Input() placeholder: string = '';\n\n  /** Unique id for the input. */\n  @Input() id: string = inject(_IdGenerator).getId('mat-mdc-chip-list-input-');\n\n  /** Whether the input is disabled. */\n  @Input({transform: booleanAttribute})\n  get disabled(): boolean {\n    return this._disabled || (this._chipGrid && this._chipGrid.disabled);\n  }\n  set disabled(value: boolean) {\n    this._disabled = value;\n  }\n  private _disabled: boolean = false;\n\n  /** Whether the input is empty. */\n  get empty(): boolean {\n    return !this.inputElement.value;\n  }\n\n  /** The native input element to which this directive is attached. */\n  readonly inputElement!: HTMLInputElement;\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    const defaultOptions = inject<MatChipsDefaultOptions>(MAT_CHIPS_DEFAULT_OPTIONS);\n    const formField = inject<MatFormField>(MAT_FORM_FIELD, {optional: true});\n\n    this.inputElement = this._elementRef.nativeElement as HTMLInputElement;\n    this.separatorKeyCodes = defaultOptions.separatorKeyCodes;\n\n    if (formField) {\n      this.inputElement.classList.add('mat-mdc-form-field-input-control');\n    }\n  }\n\n  ngOnChanges() {\n    this._chipGrid.stateChanges.next();\n  }\n\n  ngOnDestroy(): void {\n    this.chipEnd.complete();\n  }\n\n  /** Utility method to make host definition/tests more clear. */\n  _keydown(event: KeyboardEvent) {\n    if (this.empty && event.keyCode === BACKSPACE) {\n      // Ignore events where the user is holding down backspace\n      // so that we don't accidentally remove too many chips.\n      if (!event.repeat) {\n        this._chipGrid._focusLastChip();\n      }\n      event.preventDefault();\n    } else {\n      this._emitChipEnd(event);\n    }\n  }\n\n  /** Checks to see if the blur should emit the (chipEnd) event. */\n  _blur() {\n    if (this.addOnBlur) {\n      this._emitChipEnd();\n    }\n    this.focused = false;\n    // Blur the chip list if it is not focused\n    if (!this._chipGrid.focused) {\n      this._chipGrid._blur();\n    }\n    this._chipGrid.stateChanges.next();\n  }\n\n  _focus() {\n    this.focused = true;\n    this._chipGrid.stateChanges.next();\n  }\n\n  /** Checks to see if the (chipEnd) event needs to be emitted. */\n  _emitChipEnd(event?: KeyboardEvent) {\n    if (!event || (this._isSeparatorKey(event) && !event.repeat)) {\n      this.chipEnd.emit({\n        input: this.inputElement,\n        value: this.inputElement.value,\n        chipInput: this,\n      });\n\n      event?.preventDefault();\n    }\n  }\n\n  _onInput() {\n    // Let chip list know whenever the value changes.\n    this._chipGrid.stateChanges.next();\n  }\n\n  /** Focuses the input. */\n  focus(): void {\n    this.inputElement.focus();\n  }\n\n  /** Clears the input */\n  clear(): void {\n    this.inputElement.value = '';\n  }\n\n  setDescribedByIds(ids: string[]): void {\n    const element = this._elementRef.nativeElement;\n\n    // Set the value directly in the DOM since this binding\n    // is prone to \"changed after checked\" errors.\n    if (ids.length) {\n      element.setAttribute('aria-describedby', ids.join(' '));\n    } else {\n      element.removeAttribute('aria-describedby');\n    }\n  }\n\n  /** Checks whether a keycode is one of the configured separators. */\n  private _isSeparatorKey(event: KeyboardEvent) {\n    return !hasModifierKey(event) && new Set(this.separatorKeyCodes).has(event.keyCode);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {ENTER} from '@angular/cdk/keycodes';\nimport {NgModule} from '@angular/core';\nimport {<PERSON><PERSON>r<PERSON>tateMatcher, MatCommonModule, MatRippleModule} from '../core';\nimport {MatChip} from './chip';\nimport {MAT_CHIPS_DEFAULT_OPTIONS, MatChipsDefaultOptions} from './tokens';\nimport {MatChipEditInput} from './chip-edit-input';\nimport {MatChipGrid} from './chip-grid';\nimport {MatChipAvatar, MatChipRemove, MatChipTrailingIcon} from './chip-icons';\nimport {MatChipInput} from './chip-input';\nimport {MatChipListbox} from './chip-listbox';\nimport {MatChipRow} from './chip-row';\nimport {MatChipOption} from './chip-option';\nimport {MatChipSet} from './chip-set';\nimport {MatChipAction} from './chip-action';\n\nconst CHIP_DECLARATIONS = [\n  MatChip,\n  MatChipAvatar,\n  MatChipEditInput,\n  MatChipGrid,\n  MatChipInput,\n  MatChipListbox,\n  MatChipOption,\n  MatChipRemove,\n  MatChipRow,\n  MatChipSet,\n  MatChipTrailingIcon,\n];\n\n@NgModule({\n  imports: [MatCommonModule, MatRippleModule, MatChipAction, CHIP_DECLARATIONS],\n  exports: [MatCommonModule, CHIP_DECLARATIONS],\n  providers: [\n    ErrorStateMatcher,\n    {\n      provide: MAT_CHIPS_DEFAULT_OPTIONS,\n      useValue: {\n        separatorKeyCodes: [ENTER],\n      } as MatChipsDefaultOptions,\n    },\n  ],\n})\nexport class MatChipsModule {}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAoBA;MACa,yBAAyB,GAAG,IAAI,cAAc,CACzD,2BAA2B,EAC3B;AACE,IAAA,UAAU,EAAE,MAAM;AAClB,IAAA,OAAO,EAAE,OAAO;QACd,iBAAiB,EAAE,CAAC,KAAK,CAAC;KAC3B,CAAC;AACH,CAAA;AAGH;;;;AAIG;MACU,eAAe,GAAG,IAAI,cAAc,CAAC,eAAe;AAEjE;;;;AAIG;MACU,sBAAsB,GAAG,IAAI,cAAc,CAAC,qBAAqB;AAE9E;;;;AAIG;MACU,eAAe,GAAG,IAAI,cAAc,CAAC,eAAe;AAEjE;;AAEG;MACU,QAAQ,GAAG,IAAI,cAAc,CAAC,SAAS;;AClCpD;;;AAGG;MAeU,aAAa,CAAA;AACxB,IAAA,WAAW,GAAG,MAAM,CAA0B,UAAU,CAAC;AAC/C,IAAA,WAAW,GAAG,MAAM,CAK3B,QAAQ,CAAC;;IAGH,aAAa,GAAG,IAAI;;IAG7B,UAAU,GAAG,IAAI;;AAGjB,IAAA,IACI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,WAAW,EAAE,QAAQ,IAAI,KAAK;;IAE9D,IAAI,QAAQ,CAAC,KAAc,EAAA;AACzB,QAAA,IAAI,CAAC,SAAS,GAAG,KAAK;;IAEhB,SAAS,GAAG,KAAK;;IAMzB,QAAQ,GAAW,CAAC,CAAC;AAErB;;AAEG;IAEK,uBAAuB,GAAG,KAAK;AAEvC;;AAEG;IACO,qBAAqB,GAAA;;;AAG7B,QAAA,OAAO,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,uBAAuB,GAAG,EAAE,GAAG,IAAI;;AAGnE;;AAEG;IACO,YAAY,GAAA;AACpB,QAAA,OAAO,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,uBAAuB,KAAK,CAAC,IAAI,CAAC;AAC/D,cAAE;AACF,cAAE,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE;;AAK9B,IAAA,WAAA,GAAA;QACE,MAAM,CAAC,sBAAsB,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC;QAC5D,IAAI,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,QAAQ,KAAK,QAAQ,EAAE;YACxD,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,YAAY,CAAC,MAAM,EAAE,QAAQ,CAAC;;;IAIjE,KAAK,GAAA;AACH,QAAA,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,KAAK,EAAE;;AAGxC,IAAA,YAAY,CAAC,KAAiB,EAAA;AAC5B,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,UAAU,EAAE;YAC3D,KAAK,CAAC,cAAc,EAAE;AACtB,YAAA,IAAI,CAAC,WAAW,CAAC,+BAA+B,EAAE;;;AAItD,IAAA,cAAc,CAAC,KAAoB,EAAA;AACjC,QAAA,IACE,CAAC,KAAK,CAAC,OAAO,KAAK,KAAK,IAAI,KAAK,CAAC,OAAO,KAAK,KAAK;YACnD,CAAC,IAAI,CAAC,QAAQ;AACd,YAAA,IAAI,CAAC,aAAa;AAClB,YAAA,IAAI,CAAC,UAAU;AACf,YAAA,CAAC,IAAI,CAAC,WAAW,CAAC,UAAU,EAC5B;YACA,KAAK,CAAC,cAAc,EAAE;AACtB,YAAA,IAAI,CAAC,WAAW,CAAC,+BAA+B,EAAE;;;uGApF3C,aAAa,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAb,aAAa,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,iBAAA,EAAA,MAAA,EAAA,EAAA,aAAA,EAAA,eAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAgBL,gBAAgB,CAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAWtB,CAAC,KAAc,MAAM,KAAK,IAAI,IAAI,GAAG,CAAC,CAAC,GAAG,eAAe,CAAC,KAAK,CAAC,CAAC,CAAA,EAAA,uBAAA,EAAA,yBAAA,EAAA,EAAA,IAAA,EAAA,EAAA,SAAA,EAAA,EAAA,OAAA,EAAA,sBAAA,EAAA,SAAA,EAAA,wBAAA,EAAA,EAAA,UAAA,EAAA,EAAA,2CAAA,EAAA,YAAA,EAAA,kDAAA,EAAA,gBAAA,EAAA,4CAAA,EAAA,aAAA,EAAA,eAAA,EAAA,gBAAA,EAAA,eAAA,EAAA,yBAAA,EAAA,oBAAA,EAAA,UAAA,EAAA,EAAA,cAAA,EAAA,gDAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FA3BnE,aAAa,EAAA,UAAA,EAAA,CAAA;kBAdzB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,iBAAiB;AAC3B,oBAAA,IAAI,EAAE;AACJ,wBAAA,OAAO,EAAE,gDAAgD;AACzD,wBAAA,6CAA6C,EAAE,YAAY;AAC3D,wBAAA,oDAAoD,EAAE,gBAAgB;AACtE,wBAAA,8CAA8C,EAAE,aAAa;AAC7D,wBAAA,iBAAiB,EAAE,gBAAgB;AACnC,wBAAA,iBAAiB,EAAE,yBAAyB;AAC5C,wBAAA,sBAAsB,EAAE,UAAU;AAClC,wBAAA,SAAS,EAAE,sBAAsB;AACjC,wBAAA,WAAW,EAAE,wBAAwB;AACtC,qBAAA;AACF,iBAAA;wDAWU,aAAa,EAAA,CAAA;sBAArB;gBAOG,QAAQ,EAAA,CAAA;sBADX,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBAapC,QAAQ,EAAA,CAAA;sBAHP,KAAK;AAAC,gBAAA,IAAA,EAAA,CAAA;wBACL,SAAS,EAAE,CAAC,KAAc,MAAM,KAAK,IAAI,IAAI,GAAG,CAAC,CAAC,GAAG,eAAe,CAAC,KAAK,CAAC,CAAC;AAC7E,qBAAA;gBAOO,uBAAuB,EAAA,CAAA;sBAD9B;;;AC5DH;MASa,aAAa,CAAA;uGAAb,aAAa,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAb,aAAa,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,kCAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,MAAA,EAAA,KAAA,EAAA,EAAA,cAAA,EAAA,gFAAA,EAAA,EAAA,SAAA,EAFb,CAAC,EAAC,OAAO,EAAE,eAAe,EAAE,WAAW,EAAE,aAAa,EAAC,CAAC,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAExD,aAAa,EAAA,UAAA,EAAA,CAAA;kBARzB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,kCAAkC;AAC5C,oBAAA,IAAI,EAAE;AACJ,wBAAA,OAAO,EAAE,gFAAgF;AACzF,wBAAA,MAAM,EAAE,KAAK;AACd,qBAAA;oBACD,SAAS,EAAE,CAAC,EAAC,OAAO,EAAE,eAAe,EAAE,WAAW,EAAe,aAAA,EAAC,CAAC;AACpE,iBAAA;;AAGD;AAUM,MAAO,mBAAoB,SAAQ,aAAa,CAAA;AACpD;;;AAGG;IACM,aAAa,GAAG,KAAK;IAErB,UAAU,GAAG,KAAK;uGAPhB,mBAAmB,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAnB,mBAAmB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,+CAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,aAAA,EAAA,MAAA,EAAA,EAAA,cAAA,EAAA,wFAAA,EAAA,EAAA,SAAA,EAFnB,CAAC,EAAC,OAAO,EAAE,sBAAsB,EAAE,WAAW,EAAE,mBAAmB,EAAC,CAAC,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAErE,mBAAmB,EAAA,UAAA,EAAA,CAAA;kBAT/B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,+CAA+C;AACzD,oBAAA,IAAI,EAAE;AACJ,wBAAA,OAAO,EACL,wFAAwF;AAC1F,wBAAA,aAAa,EAAE,MAAM;AACtB,qBAAA;oBACD,SAAS,EAAE,CAAC,EAAC,OAAO,EAAE,sBAAsB,EAAE,WAAW,EAAqB,mBAAA,EAAC,CAAC;AACjF,iBAAA;;AAWD;;;;;;;;;;;;;;AAcG;AAaG,MAAO,aAAc,SAAQ,aAAa,CAAA;IACrC,UAAU,GAAG,KAAK;AAElB,IAAA,YAAY,CAAC,KAAiB,EAAA;AACrC,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAClB,KAAK,CAAC,eAAe,EAAE;YACvB,KAAK,CAAC,cAAc,EAAE;AACtB,YAAA,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE;;;AAIpB,IAAA,cAAc,CAAC,KAAoB,EAAA;AAC1C,QAAA,IAAI,CAAC,KAAK,CAAC,OAAO,KAAK,KAAK,IAAI,KAAK,CAAC,OAAO,KAAK,KAAK,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE;YAC1E,KAAK,CAAC,eAAe,EAAE;YACvB,KAAK,CAAC,cAAc,EAAE;AACtB,YAAA,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE;;;uGAflB,aAAa,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAb,aAAa,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,iBAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,MAAA,EAAA,QAAA,EAAA,EAAA,UAAA,EAAA,EAAA,kBAAA,EAAA,MAAA,EAAA,EAAA,cAAA,EAAA,gIAAA,EAAA,EAAA,SAAA,EAFb,CAAC,EAAC,OAAO,EAAE,eAAe,EAAE,WAAW,EAAE,aAAa,EAAC,CAAC,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAExD,aAAa,EAAA,UAAA,EAAA,CAAA;kBAXzB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,iBAAiB;AAC3B,oBAAA,IAAI,EAAE;AACJ,wBAAA,OAAO,EACL,qEAAqE;4BACrE,6DAA6D;AAC/D,wBAAA,MAAM,EAAE,QAAQ;AAChB,wBAAA,oBAAoB,EAAE,MAAM;AAC7B,qBAAA;oBACD,SAAS,EAAE,CAAC,EAAC,OAAO,EAAE,eAAe,EAAE,WAAW,EAAe,aAAA,EAAC,CAAC;AACpE,iBAAA;;;ACjBD;;;;AAIG;MAgCU,OAAO,CAAA;AAClB,IAAA,kBAAkB,GAAG,MAAM,CAAC,iBAAiB,CAAC;AAC9C,IAAA,WAAW,GAAG,MAAM,CAA0B,UAAU,CAAC;AAC/C,IAAA,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;AAC1B,IAAA,aAAa,GAAG,MAAM,CAAC,YAAY,CAAC;AACpC,IAAA,oBAAoB,GAAG,MAAM,CAAsB,yBAAyB,EAAE;AACpF,QAAA,QAAQ,EAAE,IAAI;AACf,KAAA,CAAC;AAEQ,IAAA,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC;;AAG7B,IAAA,QAAQ,GAAG,IAAI,OAAO,EAAgB;;AAGtC,IAAA,OAAO,GAAG,IAAI,OAAO,EAAgB;;AAG9C,IAAA,YAAY;;IAGH,IAAI,GAAkB,IAAI;;IAG3B,iBAAiB,GAAG,KAAK;;AAGzB,IAAA,aAAa;;AAGb,IAAA,cAAc;;AAGtB,IAAA,mBAAmB;;AAIT,IAAA,gBAAgB;;AAIhB,IAAA,iBAAiB;;AAIjB,IAAA,eAAe;IAEzB,SAAS,GAAA;QACP,OAAO,IAAI,CAAC,iBAAiB;;;IAItB,EAAE,GAAW,MAAM,CAAC,YAAY,CAAC,CAAC,KAAK,CAAC,eAAe,CAAC;;;;;IAM5C,SAAS,GAAkB,IAAI;;;;;IAMzB,eAAe,GAAkB,IAAI;;AAGhE,IAAA,kBAAkB,GAAG,CAAG,EAAA,IAAI,CAAC,EAAE,mBAAmB;;IAGlD,iBAAiB,GAAY,KAAK;AAE1B,IAAA,YAAY;AAEpB;;;AAGG;AACH,IAAA,IACI,KAAK,GAAA;QACP,OAAO,IAAI,CAAC,MAAM,KAAK,SAAS,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,WAAY,CAAC,IAAI,EAAE;;IAExF,IAAI,KAAK,CAAC,KAAU,EAAA;AAClB,QAAA,IAAI,CAAC,MAAM,GAAG,KAAK;;AAEX,IAAA,MAAM;;AAGhB;;;;;;AAMG;AACM,IAAA,KAAK;AAEd;;AAEG;IAEH,SAAS,GAAY,IAAI;AAEzB;;AAEG;IAEH,WAAW,GAAY,KAAK;;IAI5B,aAAa,GAAY,KAAK;;AAG9B,IAAA,IACI,QAAQ,GAAA;AACV,QAAA,OAAO,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,iBAAiB;;IAEjD,IAAI,QAAQ,CAAC,KAAc,EAAA;AACzB,QAAA,IAAI,CAAC,SAAS,GAAG,KAAK;;IAEhB,SAAS,GAAG,KAAK;;AAGN,IAAA,OAAO,GAA+B,IAAI,YAAY,EAAgB;;AAGtE,IAAA,SAAS,GAA+B,IAAI,YAAY,EAAgB;;IAGjF,iBAAiB,GAAG,gBAAgB;;AAGf,IAAA,WAAW;;AAGJ,IAAA,YAAY;;AAGnB,IAAA,UAAU;;AAGf,IAAA,aAAa;AAEvC;;;AAGG;AACK,IAAA,aAAa,GAAoB,MAAM,CAAC,eAAe,CAAC;AAEtD,IAAA,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC;AAItC,IAAA,WAAA,GAAA;AACE,QAAA,MAAM,WAAW,GAAG,MAAM,CAAC,sBAAsB,CAAC;AAClD,QAAA,WAAW,CAAC,IAAI,CAAC,uBAAuB,CAAC;AACzC,QAAA,WAAW,CAAC,IAAI,CAAC,qBAAqB,CAAC;AACvC,QAAA,MAAM,aAAa,GAAG,MAAM,CAAC,qBAAqB,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;AACrE,QAAA,IAAI,CAAC,mBAAmB,GAAG,aAAa,KAAK,gBAAgB;QAC7D,IAAI,CAAC,aAAa,EAAE;QAEpB,IAAI,CAAC,aAAa,EAAE,eAAe,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE;AAClE,YAAA,SAAS,EAAE,qBAAqB;AAChC,YAAA,QAAQ,EAAE,IAAI,CAAC,iBAAiB,EAAE;AACnC,SAAA,CAAC;;IAGJ,QAAQ,GAAA;;;AAGN,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa;AAC9C,QAAA,IAAI,CAAC,YAAY;AACf,YAAA,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,iBAAiB,CAAC;gBAC5C,OAAO,CAAC,OAAO,CAAC,WAAW,EAAE,KAAK,IAAI,CAAC,iBAAiB;;IAG5D,eAAe,GAAA;AACb,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,aAAa,CAAC,4BAA4B,CAAE;AAE/F,QAAA,IAAI,IAAI,CAAC,aAAa,EAAE;AACtB,YAAA,IAAI,CAAC,aAAa,GAAG,KAAK;YAC1B,IAAI,CAAC,KAAK,EAAE;;;IAIhB,kBAAkB,GAAA;;;AAGhB,QAAA,IAAI,CAAC,cAAc,GAAG,KAAK,CACzB,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAC7B,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAC9B,IAAI,CAAC,eAAe,CAAC,OAAO,CAC7B,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;;IAG3D,SAAS,GAAA;AACP,QAAA,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,IAAI,CAAC,iBAAiB,EAAE,CAAC;;IAG1F,WAAW,GAAA;QACT,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,IAAI,CAAC,WAAW,CAAC;QACnD,IAAI,CAAC,aAAa,EAAE,aAAa,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC;AACjE,QAAA,IAAI,CAAC,cAAc,EAAE,WAAW,EAAE;QAClC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAC,IAAI,EAAE,IAAI,EAAC,CAAC;AACjC,QAAA,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE;;AAG3B;;;;AAIG;IACH,MAAM,GAAA;AACJ,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAC,IAAI,EAAE,IAAI,EAAC,CAAC;;;;IAKnC,iBAAiB,GAAA;QACf,QACE,IAAI,CAAC,QAAQ;AACb,YAAA,IAAI,CAAC,aAAa;AAClB,YAAA,IAAI,CAAC,mBAAmB;AACxB,YAAA,IAAI,CAAC,YAAY;AACjB,YAAA,CAAC,CAAC,IAAI,CAAC,oBAAoB,EAAE,QAAQ;;;IAKzC,gBAAgB,GAAA;QACd,OAAO,CAAC,EAAE,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,UAAU,CAAC;;;AAIjD,IAAA,cAAc,CAAC,KAAoB,EAAA;;;AAGjC,QAAA,IAAI,CAAC,KAAK,CAAC,OAAO,KAAK,SAAS,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,KAAK,CAAC,OAAO,KAAK,MAAM,EAAE;YAC9E,KAAK,CAAC,cAAc,EAAE;YACtB,IAAI,CAAC,MAAM,EAAE;;;;IAKjB,KAAK,GAAA;AACH,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;;;;AAIlB,YAAA,IAAI,IAAI,CAAC,aAAa,EAAE;AACtB,gBAAA,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE;;iBACrB;AACL,gBAAA,IAAI,CAAC,aAAa,GAAG,IAAI;;;;;AAM/B,IAAA,gBAAgB,CAAC,MAAY,EAAA;QAC3B,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,MAAM,IAAG;AACtC,YAAA,MAAM,OAAO,GAAG,MAAM,CAAC,WAAW,CAAC,aAAa;YAChD,OAAO,OAAO,KAAK,MAAM,IAAI,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC;AACvD,SAAC,CAAC;;;IAIJ,WAAW,GAAA;QACT,MAAM,MAAM,GAAoB,EAAE;AAElC,QAAA,IAAI,IAAI,CAAC,aAAa,EAAE;AACtB,YAAA,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC;;AAGjC,QAAA,IAAI,IAAI,CAAC,UAAU,EAAE;AACnB,YAAA,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC;;AAG9B,QAAA,IAAI,IAAI,CAAC,YAAY,EAAE;AACrB,YAAA,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC;;AAGhC,QAAA,OAAO,MAAM;;;IAIf,+BAA+B,GAAA;;;;IAKvB,aAAa,GAAA;AACnB,QAAA,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC,SAAS,CAAC,MAAM,IAAG;AACpE,YAAA,MAAM,QAAQ,GAAG,MAAM,KAAK,IAAI;AAEhC,YAAA,IAAI,QAAQ,KAAK,IAAI,CAAC,iBAAiB,EAAE;AACvC,gBAAA,IAAI,CAAC,iBAAiB,GAAG,QAAQ;gBAEjC,IAAI,QAAQ,EAAE;oBACZ,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAC,IAAI,EAAE,IAAI,EAAC,CAAC;;qBAC3B;;;;;AAKL,oBAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE;oBACtC,UAAU,CAAC,MAAM,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAC,IAAI,EAAE,IAAI,EAAC,CAAC,CAAC,CAAC;;;AAG/E,SAAC,CAAC;;uGAtTO,OAAO,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAP,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,OAAO,oSAoGC,gBAAgB,CAAA,EAAA,WAAA,EAAA,CAAA,aAAA,EAAA,aAAA,EAMhB,gBAAgB,CAAA,EAAA,aAAA,EAAA,CAAA,eAAA,EAAA,eAAA,EAIhB,gBAAgB,CAIhB,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAAA,gBAAgB,CArHxB,EAAA,EAAA,OAAA,EAAA,EAAA,OAAA,EAAA,SAAA,EAAA,SAAA,EAAA,WAAA,EAAA,EAAA,IAAA,EAAA,EAAA,SAAA,EAAA,EAAA,SAAA,EAAA,wBAAA,EAAA,EAAA,UAAA,EAAA,EAAA,OAAA,EAAA,mCAAA,EAAA,0BAAA,EAAA,eAAA,EAAA,oCAAA,EAAA,UAAA,EAAA,gDAAA,EAAA,oBAAA,EAAA,gDAAA,EAAA,aAAA,EAAA,6CAAA,EAAA,aAAA,EAAA,uCAAA,EAAA,aAAA,EAAA,gCAAA,EAAA,aAAA,EAAA,gCAAA,EAAA,aAAA,EAAA,6BAAA,EAAA,UAAA,EAAA,0BAAA,EAAA,cAAA,EAAA,6BAAA,EAAA,eAAA,EAAA,uCAAA,EAAA,oBAAA,EAAA,+BAAA,EAAA,qBAAA,EAAA,IAAA,EAAA,IAAA,EAAA,WAAA,EAAA,MAAA,EAAA,iBAAA,EAAA,WAAA,EAAA,EAAA,cAAA,EAAA,cAAA,EAAA,EAAA,SAAA,EAAA,CAAC,EAAC,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAE,OAAO,EAAC,CAAC,mEAwIxC,eAAe,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,cAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAGf,sBAAsB,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,YAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAGtB,eAAe,EAvGZ,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,kBAAA,EAAA,SAAA,EAAA,eAAe,EAIf,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,mBAAA,EAAA,SAAA,EAAA,sBAAsB,qEAItB,eAAe,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,eAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAkGrB,aAAa,ECvO1B,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,SAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,00BAqBA,y/hBDkEY,aAAa,EAAA,QAAA,EAAA,iBAAA,EAAA,MAAA,EAAA,CAAA,eAAA,EAAA,UAAA,EAAA,UAAA,EAAA,yBAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAEZ,OAAO,EAAA,UAAA,EAAA,CAAA;kBA/BnB,SAAS;+BACE,wDAAwD,EAAA,QAAA,EACxD,SAAS,EAGb,IAAA,EAAA;AACJ,wBAAA,OAAO,EAAE,cAAc;AACvB,wBAAA,SAAS,EAAE,+BAA+B;AAC1C,wBAAA,4BAA4B,EAAE,eAAe;AAC7C,wBAAA,sCAAsC,EAAE,UAAU;AAClD,wBAAA,kDAAkD,EAAE,oBAAoB;AACxE,wBAAA,kDAAkD,EAAE,aAAa;AACjE,wBAAA,+CAA+C,EAAE,aAAa;AAC9D,wBAAA,yCAAyC,EAAE,aAAa;AACxD,wBAAA,kCAAkC,EAAE,aAAa;AACjD,wBAAA,kCAAkC,EAAE,aAAa;AACjD,wBAAA,+BAA+B,EAAE,UAAU;AAC3C,wBAAA,4BAA4B,EAAE,cAAc;AAC5C,wBAAA,+BAA+B,EAAE,eAAe;AAChD,wBAAA,yCAAyC,EAAE,oBAAoB;AAC/D,wBAAA,iCAAiC,EAAE,qBAAqB;AACxD,wBAAA,MAAM,EAAE,IAAI;AACZ,wBAAA,aAAa,EAAE,MAAM;AACrB,wBAAA,mBAAmB,EAAE,WAAW;AAChC,wBAAA,WAAW,EAAE,wBAAwB;qBACtC,EACc,aAAA,EAAA,iBAAiB,CAAC,IAAI,EAAA,eAAA,EACpB,uBAAuB,CAAC,MAAM,aACpC,CAAC,EAAC,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAA,OAAS,EAAC,CAAC,EAAA,OAAA,EAC7C,CAAC,aAAa,CAAC,EAAA,QAAA,EAAA,00BAAA,EAAA,MAAA,EAAA,CAAA,i8hBAAA,CAAA,EAAA;wDAuBf,IAAI,EAAA,CAAA;sBAAZ;gBAgBS,gBAAgB,EAAA,CAAA;sBADzB,eAAe;AAAC,gBAAA,IAAA,EAAA,CAAA,eAAe,EAAE,EAAC,WAAW,EAAE,IAAI,EAAC;gBAK3C,iBAAiB,EAAA,CAAA;sBAD1B,eAAe;AAAC,gBAAA,IAAA,EAAA,CAAA,sBAAsB,EAAE,EAAC,WAAW,EAAE,IAAI,EAAC;gBAKlD,eAAe,EAAA,CAAA;sBADxB,eAAe;AAAC,gBAAA,IAAA,EAAA,CAAA,eAAe,EAAE,EAAC,WAAW,EAAE,IAAI,EAAC;gBAQ5C,EAAE,EAAA,CAAA;sBAAV;gBAMoB,SAAS,EAAA,CAAA;sBAA7B,KAAK;uBAAC,YAAY;gBAMQ,eAAe,EAAA,CAAA;sBAAzC,KAAK;uBAAC,kBAAkB;gBAerB,KAAK,EAAA,CAAA;sBADR;gBAiBQ,KAAK,EAAA,CAAA;sBAAb;gBAMD,SAAS,EAAA,CAAA;sBADR,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBAOpC,WAAW,EAAA,CAAA;sBADV,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBAKpC,aAAa,EAAA,CAAA;sBADZ,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBAKhC,QAAQ,EAAA,CAAA;sBADX,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBAUjB,OAAO,EAAA,CAAA;sBAAzB;gBAGkB,SAAS,EAAA,CAAA;sBAA3B;gBAM8B,WAAW,EAAA,CAAA;sBAAzC,YAAY;uBAAC,eAAe;gBAGS,YAAY,EAAA,CAAA;sBAAjD,YAAY;uBAAC,sBAAsB;gBAGL,UAAU,EAAA,CAAA;sBAAxC,YAAY;uBAAC,eAAe;gBAGH,aAAa,EAAA,CAAA;sBAAtC,SAAS;uBAAC,aAAa;;;AEhN1B;MACa,sBAAsB,CAAA;AAGxB,IAAA,MAAA;AAEA,IAAA,QAAA;AAEA,IAAA,WAAA;AANT,IAAA,WAAA;;IAES,MAAqB;;IAErB,QAAiB;;AAEjB,IAAA,WAAA,GAAc,KAAK,EAAA;QAJnB,IAAM,CAAA,MAAA,GAAN,MAAM;QAEN,IAAQ,CAAA,QAAA,GAAR,QAAQ;QAER,IAAW,CAAA,WAAA,GAAX,WAAW;;AAErB;AAED;;;;;AAKG;AAyCG,MAAO,aAAc,SAAQ,OAAO,CAAA;;IAEhC,eAAe,GAAG,MAAM,CAAC,yBAAyB,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;;IAG7E,kBAAkB,GAAY,IAAI;;IAGlC,iBAAiB,GAAY,KAAK;;IAGlC,qCAAqC,GACnC,IAAI,CAAC,eAAe,EAAE,4BAA4B,IAAI,KAAK;AAE7D;;;;;;AAMG;AACH,IAAA,IACI,UAAU,GAAA;AACZ,QAAA,OAAO,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,kBAAkB;;IAEpD,IAAI,UAAU,CAAC,KAAc,EAAA;AAC3B,QAAA,IAAI,CAAC,WAAW,GAAG,KAAK;AACxB,QAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE;;IAE9B,WAAW,GAAY,IAAI;;AAGrC,IAAA,IACI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,SAAS;;IAEvB,IAAI,QAAQ,CAAC,KAAc,EAAA;QACzB,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC;;IAEpC,SAAS,GAAG,KAAK;AAEzB;;;;;;;;;;;;AAYG;AACH,IAAA,IAAI,YAAY,GAAA;AACd,QAAA,OAAO,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,GAAG,IAAI;;;IAIvC,iBAAiB,GAAG,uBAAuB;;AAG3C,IAAA,eAAe,GAChC,IAAI,YAAY,EAA0B;IAEnC,QAAQ,GAAA;QACf,KAAK,CAAC,QAAQ,EAAE;AAChB,QAAA,IAAI,CAAC,IAAI,GAAG,cAAc;;;IAI5B,MAAM,GAAA;QACJ,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC;;;IAI3C,QAAQ,GAAA;QACN,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC;;;IAI5C,oBAAoB,GAAA;QAClB,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;;;IAI1C,cAAc,CAAC,cAAuB,KAAK,EAAA;AACzC,QAAA,IAAI,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC,QAAQ,EAAE,WAAW,EAAE,IAAI,CAAC;QACzD,OAAO,IAAI,CAAC,QAAQ;;IAGb,+BAA+B,GAAA;AACtC,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;;;;YAIlB,IAAI,CAAC,KAAK,EAAE;AAEZ,YAAA,IAAI,IAAI,CAAC,UAAU,EAAE;AACnB,gBAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC;;;;IAK/B,kBAAkB,GAAA;AAChB,QAAA,IAAI,IAAI,CAAC,WAAW,EAAE;AACpB,YAAA,OAAO,IAAI;;;;;QAMb,OAAO,CAAC,IAAI,CAAC,qCAAqC,IAAI,IAAI,CAAC,iBAAiB;;AAG9E,IAAA,iBAAiB,CAAC,UAAmB,EAAE,WAAoB,EAAE,SAAkB,EAAA;AAC7E,QAAA,IAAI,UAAU,KAAK,IAAI,CAAC,QAAQ,EAAE;AAChC,YAAA,IAAI,CAAC,SAAS,GAAG,UAAU;YAE3B,IAAI,SAAS,EAAE;AACb,gBAAA,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC;AACxB,oBAAA,MAAM,EAAE,IAAI;oBACZ,WAAW;oBACX,QAAQ,EAAE,IAAI,CAAC,QAAQ;AACxB,iBAAA,CAAC;;AAGJ,YAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE;;;uGA/H/B,aAAa,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAb,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,aAAa,EAqBL,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,oFAAA,EAAA,MAAA,EAAA,EAAA,UAAA,EAAA,CAAA,YAAA,EAAA,YAAA,EAAA,gBAAgB,CAWhB,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAAA,gBAAgB,CAxCxB,EAAA,EAAA,OAAA,EAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,0BAAA,EAAA,eAAA,EAAA,kCAAA,EAAA,eAAA,EAAA,sCAAA,EAAA,eAAA,EAAA,6BAAA,EAAA,UAAA,EAAA,6BAAA,EAAA,mBAAA,EAAA,6BAAA,EAAA,UAAA,EAAA,gCAAA,EAAA,aAAA,EAAA,oCAAA,EAAA,UAAA,EAAA,oCAAA,EAAA,UAAA,EAAA,qCAAA,EAAA,sBAAA,EAAA,gDAAA,EAAA,oBAAA,EAAA,6CAAA,EAAA,aAAA,EAAA,gDAAA,EAAA,sBAAA,EAAA,uCAAA,EAAA,aAAA,EAAA,gCAAA,EAAA,aAAA,EAAA,uCAAA,EAAA,oBAAA,EAAA,eAAA,EAAA,MAAA,EAAA,iBAAA,EAAA,MAAA,EAAA,uBAAA,EAAA,MAAA,EAAA,WAAA,EAAA,MAAA,EAAA,IAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,kCAAA,EAAA,EAAA,SAAA,EAAA;AACT,YAAA,EAAC,OAAO,EAAE,OAAO,EAAE,WAAW,EAAE,aAAa,EAAC;AAC9C,YAAA,EAAC,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAE,aAAa,EAAC;SAChD,EC5EH,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,ihDAuCA,y/hBDwCY,aAAa,EAAA,QAAA,EAAA,iBAAA,EAAA,MAAA,EAAA,CAAA,eAAA,EAAA,UAAA,EAAA,UAAA,EAAA,yBAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAEZ,aAAa,EAAA,UAAA,EAAA,CAAA;kBAxCzB,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,oFAAoF,EAGxF,IAAA,EAAA;AACJ,wBAAA,OAAO,EAAE,kCAAkC;AAC3C,wBAAA,4BAA4B,EAAE,eAAe;AAC7C,wBAAA,oCAAoC,EAAE,eAAe;AACrD,wBAAA,wCAAwC,EAAE,eAAe;AACzD,wBAAA,+BAA+B,EAAE,UAAU;AAC3C,wBAAA,+BAA+B,EAAE,mBAAmB;AACpD,wBAAA,+BAA+B,EAAE,UAAU;AAC3C,wBAAA,kCAAkC,EAAE,aAAa;AACjD,wBAAA,sCAAsC,EAAE,UAAU;AAClD,wBAAA,sCAAsC,EAAE,UAAU;;;;;AAKlD,wBAAA,uCAAuC,EAAE,sBAAsB;AAC/D,wBAAA,kDAAkD,EAAE,oBAAoB;AACxE,wBAAA,+CAA+C,EAAE,aAAa;AAC9D,wBAAA,kDAAkD,EAAE,sBAAsB;AAC1E,wBAAA,yCAAyC,EAAE,aAAa;AACxD,wBAAA,kCAAkC,EAAE,aAAa;AACjD,wBAAA,yCAAyC,EAAE,oBAAoB;AAC/D,wBAAA,iBAAiB,EAAE,MAAM;AACzB,wBAAA,mBAAmB,EAAE,MAAM;AAC3B,wBAAA,yBAAyB,EAAE,MAAM;AACjC,wBAAA,aAAa,EAAE,MAAM;AACrB,wBAAA,MAAM,EAAE,IAAI;qBACb,EACU,SAAA,EAAA;AACT,wBAAA,EAAC,OAAO,EAAE,OAAO,EAAE,WAAW,eAAe,EAAC;AAC9C,wBAAA,EAAC,OAAO,EAAE,QAAQ,EAAE,WAAW,eAAe,EAAC;qBAChD,EACc,aAAA,EAAA,iBAAiB,CAAC,IAAI,EACpB,eAAA,EAAA,uBAAuB,CAAC,MAAM,EAAA,OAAA,EACtC,CAAC,aAAa,CAAC,EAAA,QAAA,EAAA,ihDAAA,EAAA,MAAA,EAAA,CAAA,i8hBAAA,CAAA,EAAA;8BAwBpB,UAAU,EAAA,CAAA;sBADb,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBAYhC,QAAQ,EAAA,CAAA;sBADX,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBA8BjB,eAAe,EAAA,CAAA;sBAAjC;;;AEpIH;;;AAGG;MAUU,gBAAgB,CAAA;AACV,IAAA,WAAW,GAAG,MAAM,CAAC,UAAU,CAAC;AAChC,IAAA,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC;AAG7C,IAAA,WAAA,GAAA;AAEA,IAAA,UAAU,CAAC,YAAoB,EAAA;AAC7B,QAAA,IAAI,CAAC,gBAAgB,EAAE,CAAC,KAAK,EAAE;AAC/B,QAAA,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC;;IAG7B,gBAAgB,GAAA;AACd,QAAA,OAAO,IAAI,CAAC,WAAW,CAAC,aAAa;;AAGvC,IAAA,QAAQ,CAAC,KAAa,EAAA;AACpB,QAAA,IAAI,CAAC,gBAAgB,EAAE,CAAC,WAAW,GAAG,KAAK;QAC3C,IAAI,CAAC,uBAAuB,EAAE;;IAGhC,QAAQ,GAAA;QACN,OAAO,IAAI,CAAC,gBAAgB,EAAE,CAAC,WAAW,IAAI,EAAE;;IAG1C,uBAAuB,GAAA;QAC7B,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE;QAC1C,KAAK,CAAC,kBAAkB,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;AACjD,QAAA,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC;AACrB,QAAA,MAAM,GAAG,GAAG,MAAM,CAAC,YAAY,EAAG;QAClC,GAAG,CAAC,eAAe,EAAE;AACrB,QAAA,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC;;uGA/BV,gBAAgB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAhB,gBAAgB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,wBAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,MAAA,EAAA,SAAA,EAAA,UAAA,EAAA,IAAA,EAAA,iBAAA,EAAA,MAAA,EAAA,EAAA,cAAA,EAAA,qBAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAAhB,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAT5B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,wBAAwB;AAClC,oBAAA,IAAI,EAAE;AACJ,wBAAA,OAAO,EAAE,qBAAqB;AAC9B,wBAAA,MAAM,EAAE,SAAS;AACjB,wBAAA,UAAU,EAAE,IAAI;AAChB,wBAAA,iBAAiB,EAAE,MAAM;AAC1B,qBAAA;AACF,iBAAA;;;ACUD;;;AAGG;AAoCG,MAAO,UAAW,SAAQ,OAAO,CAAA;IAClB,iBAAiB,GAAG,oBAAoB;AAE3D;;;;AAIG;IACK,iBAAiB,GAAG,KAAK;IAExB,QAAQ,GAAY,KAAK;;AAGf,IAAA,MAAM,GACvB,IAAI,YAAY,EAAsB;;AAGX,IAAA,gBAAgB;;AAGb,IAAA,gBAAgB;IAEhD,UAAU,GAAG,KAAK;AAIlB,IAAA,WAAA,GAAA;AACE,QAAA,KAAK,EAAE;AAEP,QAAA,IAAI,CAAC,IAAI,GAAG,KAAK;AACjB,QAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,MAAK;YAC1D,IAAI,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE;gBAC9C,IAAI,CAAC,aAAa,EAAE;;AAExB,SAAC,CAAC;;IAGK,gBAAgB,GAAA;;QAEvB,OAAO,CAAC,IAAI,CAAC,UAAU,IAAI,KAAK,CAAC,gBAAgB,EAAE;;;IAIrD,YAAY,GAAA;QACV,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YACtC,IAAI,CAAC,KAAK,EAAE;;;AAIP,IAAA,cAAc,CAAC,KAAoB,EAAA;QAC1C,IAAI,KAAK,CAAC,OAAO,KAAK,KAAK,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AAC7C,YAAA,IAAI,IAAI,CAAC,UAAU,EAAE;gBACnB,KAAK,CAAC,cAAc,EAAE;gBACtB,IAAI,CAAC,aAAa,EAAE;;AACf,iBAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;AACxB,gBAAA,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC;;;AAEtB,aAAA,IAAI,IAAI,CAAC,UAAU,EAAE;;YAE1B,KAAK,CAAC,eAAe,EAAE;;aAClB;AACL,YAAA,KAAK,CAAC,cAAc,CAAC,KAAK,CAAC;;;AAI/B,IAAA,kBAAkB,CAAC,KAAiB,EAAA;QAClC,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,EAAE;AACnC,YAAA,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC;;;AAIrB,IAAA,aAAa,CAAC,KAAY,EAAA;QAChC,IACE,CAAC,IAAI,CAAC,aAAa;AACnB,aAAC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,MAAc,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,EACpF;YACA;;;AAIF,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK;QAExB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,iBAAiB,GAAG,IAAI;;QAG/C,eAAe,CACb,MAAK;YACH,IAAI,CAAC,aAAa,EAAE,CAAC,UAAU,CAAC,KAAK,CAAC;AACtC,YAAA,IAAI,CAAC,iBAAiB,GAAG,KAAK;SAC/B,EACD,EAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,EAAC,CAC3B;;IAGK,aAAa,GAAA;QACnB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,iBAAiB,GAAG,KAAK;QAChD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,aAAa,EAAE,CAAC,QAAQ,EAAE,EAAC,CAAC;;;AAItE,QAAA,IACE,IAAI,CAAC,SAAS,CAAC,aAAa,KAAK,IAAI,CAAC,aAAa,EAAE,CAAC,gBAAgB,EAAE;YACxE,IAAI,CAAC,SAAS,CAAC,aAAa,KAAK,IAAI,CAAC,SAAS,CAAC,IAAI,EACpD;AACA,YAAA,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE;;;IAIrB,iBAAiB,GAAA;QACxB,OAAO,KAAK,CAAC,iBAAiB,EAAE,IAAI,IAAI,CAAC,UAAU;;AAGrD;;;AAGG;IACK,aAAa,GAAA;AACnB,QAAA,OAAO,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,gBAAiB;;uGArH7C,UAAU,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAV,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,UAAU,EARV,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,wEAAA,EAAA,MAAA,EAAA,EAAA,QAAA,EAAA,UAAA,EAAA,EAAA,OAAA,EAAA,EAAA,MAAA,EAAA,QAAA,EAAA,EAAA,IAAA,EAAA,EAAA,SAAA,EAAA,EAAA,OAAA,EAAA,gBAAA,EAAA,UAAA,EAAA,4BAAA,EAAA,EAAA,UAAA,EAAA,EAAA,gCAAA,EAAA,aAAA,EAAA,6BAAA,EAAA,UAAA,EAAA,4BAAA,EAAA,YAAA,EAAA,6BAAA,EAAA,UAAA,EAAA,oCAAA,EAAA,UAAA,EAAA,gDAAA,EAAA,oBAAA,EAAA,gDAAA,EAAA,aAAA,EAAA,6CAAA,EAAA,aAAA,EAAA,uCAAA,EAAA,aAAA,EAAA,gCAAA,EAAA,aAAA,EAAA,uCAAA,EAAA,oBAAA,EAAA,IAAA,EAAA,IAAA,EAAA,eAAA,EAAA,sBAAA,EAAA,iBAAA,EAAA,MAAA,EAAA,uBAAA,EAAA,MAAA,EAAA,WAAA,EAAA,MAAA,EAAA,EAAA,cAAA,EAAA,kDAAA,EAAA,EAAA,SAAA,EAAA;AACT,YAAA,EAAC,OAAO,EAAE,OAAO,EAAE,WAAW,EAAE,UAAU,EAAC;AAC3C,YAAA,EAAC,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAE,UAAU,EAAC;SAC7C,EAyBa,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,kBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,gBAAgB,kGAHnB,gBAAgB,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,ECzF7B,sxCAuCA,ED+BY,MAAA,EAAA,CAAA,i8hBAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,aAAa,0IAAE,gBAAgB,EAAA,QAAA,EAAA,wBAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAE9B,UAAU,EAAA,UAAA,EAAA,CAAA;kBAnCtB,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,wEAAwE,EAG5E,IAAA,EAAA;AACJ,wBAAA,OAAO,EAAE,kDAAkD;AAC3D,wBAAA,kCAAkC,EAAE,aAAa;AACjD,wBAAA,+BAA+B,EAAE,UAAU;AAC3C,wBAAA,8BAA8B,EAAE,YAAY;AAC5C,wBAAA,+BAA+B,EAAE,UAAU;AAC3C,wBAAA,sCAAsC,EAAE,UAAU;AAClD,wBAAA,kDAAkD,EAAE,oBAAoB;AACxE,wBAAA,kDAAkD,EAAE,aAAa;AACjE,wBAAA,+CAA+C,EAAE,aAAa;AAC9D,wBAAA,yCAAyC,EAAE,aAAa;AACxD,wBAAA,kCAAkC,EAAE,aAAa;AACjD,wBAAA,yCAAyC,EAAE,oBAAoB;AAC/D,wBAAA,MAAM,EAAE,IAAI;;;AAGZ,wBAAA,iBAAiB,EAAE,sBAAsB;AACzC,wBAAA,mBAAmB,EAAE,MAAM;AAC3B,wBAAA,yBAAyB,EAAE,MAAM;AACjC,wBAAA,aAAa,EAAE,MAAM;AACrB,wBAAA,SAAS,EAAE,gBAAgB;AAC3B,wBAAA,YAAY,EAAE,4BAA4B;qBAC3C,EACU,SAAA,EAAA;AACT,wBAAA,EAAC,OAAO,EAAE,OAAO,EAAE,WAAW,YAAY,EAAC;AAC3C,wBAAA,EAAC,OAAO,EAAE,QAAQ,EAAE,WAAW,YAAY,EAAC;AAC7C,qBAAA,EAAA,aAAA,EACc,iBAAiB,CAAC,IAAI,EAAA,eAAA,EACpB,uBAAuB,CAAC,MAAM,EAAA,OAAA,EACtC,CAAC,aAAa,EAAE,gBAAgB,CAAC,EAAA,QAAA,EAAA,sxCAAA,EAAA,MAAA,EAAA,CAAA,i8hBAAA,CAAA,EAAA;wDAYjC,QAAQ,EAAA,CAAA;sBAAhB;gBAGkB,MAAM,EAAA,CAAA;sBAAxB;gBAI4B,gBAAgB,EAAA,CAAA;sBAA5C,SAAS;uBAAC,gBAAgB;gBAGK,gBAAgB,EAAA,CAAA;sBAA/C,YAAY;uBAAC,gBAAgB;;;AE9DhC;;;;AAIG;MAiBU,UAAU,CAAA;AACX,IAAA,WAAW,GAAG,MAAM,CAA0B,UAAU,CAAC;AACzD,IAAA,kBAAkB,GAAG,MAAM,CAAC,iBAAiB,CAAC;IAChD,IAAI,GAAG,MAAM,CAAC,cAAc,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;;IAG/C,8BAA8B,GAAkB,IAAI;;AAGlD,IAAA,WAAW;;AAGX,IAAA,UAAU,GAAG,IAAI,OAAO,EAAQ;;IAGhC,YAAY,GAAG,cAAc;;AAGvC,IAAA,IAAI,gBAAgB,GAAA;AAClB,QAAA,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC;;;AAInD,IAAA,IAAI,oBAAoB,GAAA;AACtB,QAAA,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,IAAI,IAAI,CAAC,SAAS,CAAC;;;AAIpD,IAAA,IAAI,kBAAkB,GAAA;AACpB,QAAA,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC;;;AAIlD,IAAA,IACI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,SAAS;;IAEvB,IAAI,QAAQ,CAAC,KAAc,EAAA;AACzB,QAAA,IAAI,CAAC,SAAS,GAAG,KAAK;QACtB,IAAI,CAAC,eAAe,EAAE;;IAEd,SAAS,GAAY,KAAK;;AAGpC,IAAA,IAAI,KAAK,GAAA;AACP,QAAA,OAAO,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC;;;AAIjD,IAAA,IACI,IAAI,GAAA;AACN,QAAA,IAAI,IAAI,CAAC,aAAa,EAAE;YACtB,OAAO,IAAI,CAAC,aAAa;;AAG3B,QAAA,OAAO,IAAI,CAAC,KAAK,GAAG,IAAI,GAAG,IAAI,CAAC,YAAY;;;IAO9C,QAAQ,GAAW,CAAC;IAEpB,IAAI,IAAI,CAAC,KAAoB,EAAA;AAC3B,QAAA,IAAI,CAAC,aAAa,GAAG,KAAK;;IAEpB,aAAa,GAAkB,IAAI;;AAG3C,IAAA,IAAI,OAAO,GAAA;AACT,QAAA,OAAO,IAAI,CAAC,eAAe,EAAE;;;AAS/B,IAAA,MAAM;;AAGN,IAAA,YAAY,GAAG,IAAI,SAAS,EAAiB;AAG7C,IAAA,WAAA,GAAA;IAEA,eAAe,GAAA;QACb,IAAI,CAAC,qBAAqB,EAAE;QAC5B,IAAI,CAAC,oBAAoB,EAAE;QAC3B,IAAI,CAAC,0BAA0B,EAAE;;IAGnC,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,WAAW,EAAE,OAAO,EAAE;AAC3B,QAAA,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE;AAC3B,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE;AACtB,QAAA,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE;;;IAIlB,eAAe,GAAA;AACvB,QAAA,OAAO,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;;;IAIxD,eAAe,GAAA;AACvB,QAAA,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,IAAI,IAAG;AAC1B,YAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,SAAS;AACvC,YAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE;AACxC,SAAC,CAAC;;;AAIJ,IAAA,KAAK;;AAGL,IAAA,cAAc,CAAC,KAAoB,EAAA;AACjC,QAAA,IAAI,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,EAAE;AACnC,YAAA,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,KAAK,CAAC;;;AAIrC;;;;;AAKG;AACO,IAAA,aAAa,CAAC,KAAa,EAAA;QACnC,OAAO,KAAK,IAAI,CAAC,IAAI,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM;;AAGjD;;;;AAIG;IACO,iBAAiB,GAAA;QACzB,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,QAAQ;AAExD,QAAA,IAAI,QAAQ,KAAK,CAAC,CAAC,EAAE;;;;YAInB,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,QAAQ,GAAG,CAAC,CAAC;;;AAI5C,YAAA,UAAU,CAAC,OAAO,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,QAAQ,GAAG,QAAQ,CAAC,CAAC;;;AAI1E;;;AAGG;AACO,IAAA,cAAc,CACtB,eAA2C,EAAA;AAE3C,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAC7B,SAAS,CAAC,IAAI,CAAC,EACf,SAAS,CAAC,MAAM,KAAK,CAAC,GAAI,IAAI,CAAC,MAAuB,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC,CAAC,CAC9E;;;AAIO,IAAA,mBAAmB,CAAC,KAAY,EAAA;AACxC,QAAA,IAAI,cAAc,GAAG,KAAK,CAAC,MAA4B;QAEvD,OAAO,cAAc,IAAI,cAAc,KAAK,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE;YAC1E,IAAI,cAAc,CAAC,SAAS,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE;AACrD,gBAAA,OAAO,IAAI;;AAEb,YAAA,cAAc,GAAG,cAAc,CAAC,aAAa;;AAE/C,QAAA,OAAO,KAAK;;;IAIN,qBAAqB,GAAA;;;;QAI3B,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,KAAyB,KAAI;YACvF,MAAM,OAAO,GAAoB,EAAE;YACnC,KAAK,CAAC,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;AACjF,YAAA,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,OAAO,CAAC;AAChC,YAAA,IAAI,CAAC,YAAY,CAAC,eAAe,EAAE;AACrC,SAAC,CAAC;QAEF,IAAI,CAAC,WAAW,GAAG,IAAI,eAAe,CAAC,IAAI,CAAC,YAAY;AACrD,aAAA,uBAAuB;AACvB,aAAA,yBAAyB,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,KAAK;AAC7D,aAAA,cAAc;AACd,aAAA,aAAa,CAAC,MAAM,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;;;QAIvD,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,EAAC,IAAI,EAAC,KAAI;YAC1E,MAAM,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,aAAwB,CAAC;YAEvE,IAAI,MAAM,EAAE;AACV,gBAAA,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,MAAM,CAAC;;AAE7C,SAAC,CAAC;QAEF,IAAI,CAAC,IAAI,EAAE;AACR,aAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC;AAC/B,aAAA,SAAS,CAAC,SAAS,IAAI,IAAI,CAAC,WAAW,CAAC,yBAAyB,CAAC,SAAS,CAAC,CAAC;;AAGlF;;;AAGG;AACO,IAAA,cAAc,CAAC,MAAqB,EAAA;;;QAG5C,OAAO,CAAC,MAAM,CAAC,aAAa,IAAI,MAAM,CAAC,QAAQ;;;IAIzC,oBAAoB,GAAA;QAC1B,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,MAAK;AACnF,YAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;;;AAGjB,gBAAA,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC;;YAGtD,IAAI,CAAC,2BAA2B,EAAE;AACpC,SAAC,CAAC;;;IAII,0BAA0B,GAAA;AAChC,QAAA,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,KAAmB,KAAI;YAC3F,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE;YACvC,MAAM,SAAS,GAAG,SAAS,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC;;;;;AAM/C,YAAA,IAAI,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE;AAC3D,gBAAA,IAAI,CAAC,8BAA8B,GAAG,SAAS;;AAEnD,SAAC,CAAC;;AAGJ;;;AAGG;IACK,2BAA2B,GAAA;AACjC,QAAA,IAAI,IAAI,CAAC,8BAA8B,IAAI,IAAI,EAAE;YAC/C;;AAGF,QAAA,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;AACtB,YAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,8BAA8B,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;YACtF,MAAM,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,QAAQ,CAAC;AAEnD,YAAA,IAAI,WAAW,CAAC,QAAQ,EAAE;;gBAExB,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;oBAC5B,IAAI,CAAC,KAAK,EAAE;;qBACP;AACL,oBAAA,IAAI,CAAC,WAAW,CAAC,qBAAqB,EAAE;;;iBAErC;gBACL,WAAW,CAAC,KAAK,EAAE;;;aAEhB;YACL,IAAI,CAAC,KAAK,EAAE;;AAGd,QAAA,IAAI,CAAC,8BAA8B,GAAG,IAAI;;uGAvRjC,UAAU,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAV,UAAU,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,cAAA,EAAA,MAAA,EAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAiCF,gBAAgB,CAAA,EAAA,IAAA,EAAA,MAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EA2BtB,CAAC,KAAc,MAAM,KAAK,IAAI,IAAI,GAAG,CAAC,GAAG,eAAe,CAAC,KAAK,CAAC,CAAC,CAe5D,EAAA,EAAA,IAAA,EAAA,EAAA,SAAA,EAAA,EAAA,SAAA,EAAA,wBAAA,EAAA,EAAA,UAAA,EAAA,EAAA,WAAA,EAAA,MAAA,EAAA,EAAA,cAAA,EAAA,yCAAA,EAAA,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,QAAA,EAAA,SAAA,EAAA,OAAO,EAzFd,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA;;;;AAIT,EAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,g3BAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAUU,UAAU,EAAA,UAAA,EAAA,CAAA;kBAhBtB,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,cAAc,EACd,QAAA,EAAA;;;;GAIT,EAEK,IAAA,EAAA;AACJ,wBAAA,OAAO,EAAE,yCAAyC;AAClD,wBAAA,WAAW,EAAE,wBAAwB;AACrC,wBAAA,aAAa,EAAE,MAAM;AACtB,qBAAA,EAAA,aAAA,EACc,iBAAiB,CAAC,IAAI,EACpB,eAAA,EAAA,uBAAuB,CAAC,MAAM,EAAA,MAAA,EAAA,CAAA,g3BAAA,CAAA,EAAA;wDAoC3C,QAAQ,EAAA,CAAA;sBADX,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBAiBhC,IAAI,EAAA,CAAA;sBADP;gBAaD,QAAQ,EAAA,CAAA;sBAHP,KAAK;AAAC,gBAAA,IAAA,EAAA,CAAA;wBACL,SAAS,EAAE,CAAC,KAAc,MAAM,KAAK,IAAI,IAAI,GAAG,CAAC,GAAG,eAAe,CAAC,KAAK,CAAC,CAAC;AAC5E,qBAAA;gBAmBD,MAAM,EAAA,CAAA;sBALL,eAAe;AAAC,gBAAA,IAAA,EAAA,CAAA,OAAO,EAAE;;;AAGxB,wBAAA,WAAW,EAAE,IAAI;AAClB,qBAAA;;;ACjGH;MACa,oBAAoB,CAAA;AAGtB,IAAA,MAAA;AAEA,IAAA,KAAA;AAJT,IAAA,WAAA;;IAES,MAAsB;;IAEtB,KAAU,EAAA;QAFV,IAAM,CAAA,MAAA,GAAN,MAAM;QAEN,IAAK,CAAA,KAAA,GAAL,KAAK;;AAEf;AAED;;;;AAIG;AACU,MAAA,uCAAuC,GAAQ;AAC1D,IAAA,OAAO,EAAE,iBAAiB;AAC1B,IAAA,WAAW,EAAE,UAAU,CAAC,MAAM,cAAc,CAAC;AAC7C,IAAA,KAAK,EAAE,IAAI;;AAGb;;;AAGG;AA2BG,MAAO,cACX,SAAQ,UAAU,CAAA;AAGlB;;;AAGG;AACH,IAAA,UAAU,GAAG,MAAK,GAAG;AAErB;;;AAGG;AACH,IAAA,SAAS,GAAyB,MAAK,GAAG;;IAGvB,YAAY,GAAG,SAAS;;IAGnC,eAAe,GAAG,MAAM,CAAC,yBAAyB,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;;AAG7E,IAAA,IACI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,SAAS;;IAEvB,IAAI,QAAQ,CAAC,KAAc,EAAA;AACzB,QAAA,IAAI,CAAC,SAAS,GAAG,KAAK;QACtB,IAAI,CAAC,sBAAsB,EAAE;;IAEvB,SAAS,GAAY,KAAK;;AAGlC,IAAA,IAAI,QAAQ,GAAA;AACV,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,MAAM,CAAC,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC;AACzE,QAAA,OAAO,IAAI,CAAC,QAAQ,GAAG,aAAa,GAAG,aAAa,CAAC,CAAC,CAAC;;;IAI9B,eAAe,GAA8B,YAAY;AAEpF;;;;;AAKG;AACH,IAAA,IACI,UAAU,GAAA;QACZ,OAAO,IAAI,CAAC,WAAW;;IAEzB,IAAI,UAAU,CAAC,KAAc,EAAA;AAC3B,QAAA,IAAI,CAAC,WAAW,GAAG,KAAK;QACxB,IAAI,CAAC,sBAAsB,EAAE;;IAErB,WAAW,GAAY,IAAI;AAErC;;;;AAIG;IACM,WAAW,GAAkC,CAAC,EAAO,EAAE,EAAO,KAAK,EAAE,KAAK,EAAE;;IAIrF,QAAQ,GAAY,KAAK;;AAGzB,IAAA,IACI,4BAA4B,GAAA;QAC9B,OAAO,IAAI,CAAC,6BAA6B;;IAE3C,IAAI,4BAA4B,CAAC,KAAc,EAAA;AAC7C,QAAA,IAAI,CAAC,6BAA6B,GAAG,KAAK;QAC1C,IAAI,CAAC,sBAAsB,EAAE;;IAEvB,6BAA6B,GACnC,IAAI,CAAC,eAAe,EAAE,4BAA4B,IAAI,KAAK;;AAG7D,IAAA,IAAI,oBAAoB,GAAA;AACtB,QAAA,OAAO,IAAI,CAAC,cAAc,CAAwC,IAAI,IAAI,IAAI,CAAC,eAAe,CAAC;;;AAIjG,IAAA,IAAI,eAAe,GAAA;AACjB,QAAA,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC;;;AAIlD,IAAA,IACI,KAAK,GAAA;QACP,OAAO,IAAI,CAAC,MAAM;;IAEpB,IAAI,KAAK,CAAC,KAAU,EAAA;QAClB,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;AACrC,YAAA,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,KAAK,CAAC;;AAEzC,QAAA,IAAI,CAAC,MAAM,GAAG,KAAK;;AAEX,IAAA,MAAM;;AAGG,IAAA,MAAM,GACvB,IAAI,YAAY,EAAwB;IAQjC,MAAM,GAA6B,SAAU;IAEtD,kBAAkB,GAAA;QAChB,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,MAAK;AACnF,YAAA,IAAI,IAAI,CAAC,KAAK,KAAK,SAAS,EAAE;AAC5B,gBAAA,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,MAAK;oBAC1B,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC;AAC9C,iBAAC,CAAC;;;YAGJ,IAAI,CAAC,sBAAsB,EAAE;AAC/B,SAAC,CAAC;QAEF,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,KAAK,EAAE,CAAC;AACnF,QAAA,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,KAAK,IAAG;AAC3E,YAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AAClB,gBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,IAAG;AACzB,oBAAA,IAAI,IAAI,KAAK,KAAK,CAAC,MAAM,EAAE;wBACzB,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;;AAE/C,iBAAC,CAAC;;AAGJ,YAAA,IAAI,KAAK,CAAC,WAAW,EAAE;gBACrB,IAAI,CAAC,iBAAiB,EAAE;;AAE5B,SAAC,CAAC;;AAGJ;;;AAGG;IACM,KAAK,GAAA;AACZ,QAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB;;AAGF,QAAA,MAAM,iBAAiB,GAAG,IAAI,CAAC,qBAAqB,EAAE;AAEtD,QAAA,IAAI,iBAAiB,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE;YACpD,iBAAiB,CAAC,KAAK,EAAE;;aACpB,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;AACjC,YAAA,IAAI,CAAC,WAAW,CAAC,kBAAkB,EAAE;;aAChC;AACL,YAAA,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,KAAK,EAAE;;;AAI1C;;;AAGG;AACH,IAAA,UAAU,CAAC,KAAU,EAAA;AACnB,QAAA,IAAI,KAAK,IAAI,IAAI,EAAE;AACjB,YAAA,IAAI,CAAC,KAAK,GAAG,KAAK;;aACb;AACL,YAAA,IAAI,CAAC,KAAK,GAAG,SAAS;;;AAI1B;;;AAGG;AACH,IAAA,gBAAgB,CAAC,EAAwB,EAAA;AACvC,QAAA,IAAI,CAAC,SAAS,GAAG,EAAE;;AAGrB;;;AAGG;AACH,IAAA,iBAAiB,CAAC,EAAc,EAAA;AAC9B,QAAA,IAAI,CAAC,UAAU,GAAG,EAAE;;AAGtB;;;AAGG;AACH,IAAA,gBAAgB,CAAC,UAAmB,EAAA;AAClC,QAAA,IAAI,CAAC,QAAQ,GAAG,UAAU;;;AAI5B,IAAA,oBAAoB,CAAC,KAAU,EAAE,WAAA,GAAuB,IAAI,EAAA;QAC1D,IAAI,CAAC,eAAe,EAAE;AAEtB,QAAA,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;AACxB,YAAA,KAAK,CAAC,OAAO,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE,WAAW,CAAC,CAAC;;aACtE;AACL,YAAA,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,WAAW,CAAC;;;;IAKzC,KAAK,GAAA;AACH,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;;YAElB,UAAU,CAAC,MAAK;AACd,gBAAA,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;oBACjB,IAAI,CAAC,cAAc,EAAE;;AAEzB,aAAC,CAAC;;;AAIN,IAAA,QAAQ,CAAC,KAAoB,EAAA;AAC3B,QAAA,IAAI,KAAK,CAAC,OAAO,KAAK,GAAG,EAAE;YACzB,KAAK,CAAC,iBAAiB,EAAE;;;;IAKrB,cAAc,GAAA;QACpB,IAAI,CAAC,UAAU,EAAE;AACjB,QAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE;;;IAIhC,iBAAiB,GAAA;QACvB,IAAI,WAAW,GAAQ,IAAI;QAE3B,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;AAChC,YAAA,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC;;aAC9C;AACL,YAAA,WAAW,GAAG,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,GAAG,SAAS;;AAE/D,QAAA,IAAI,CAAC,MAAM,GAAG,WAAW;AACzB,QAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,oBAAoB,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;AAC7D,QAAA,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC;AAC3B,QAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE;;AAGxC;;;AAGG;AACK,IAAA,eAAe,CAAC,IAAc,EAAA;AACpC,QAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,IAAG;AACzB,YAAA,IAAI,IAAI,KAAK,IAAI,EAAE;gBACjB,IAAI,CAAC,QAAQ,EAAE;;AAEnB,SAAC,CAAC;;AAGJ;;;AAGG;IACK,YAAY,CAAC,KAAU,EAAE,WAAoB,EAAA;QACnD,MAAM,iBAAiB,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,IAAG;AAChD,YAAA,OAAO,IAAI,CAAC,KAAK,IAAI,IAAI,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC;AAClE,SAAC,CAAC;QAEF,IAAI,iBAAiB,EAAE;AACrB,YAAA,WAAW,GAAG,iBAAiB,CAAC,oBAAoB,EAAE,GAAG,iBAAiB,CAAC,MAAM,EAAE;;AAGrF,QAAA,OAAO,iBAAiB;;;IAIlB,sBAAsB,GAAA;AAC5B,QAAA,IAAI,IAAI,CAAC,MAAM,EAAE;;;AAGf,YAAA,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,MAAK;AAC1B,gBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,IAAG;AACzB,oBAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,QAAQ;AACtC,oBAAA,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,WAAW;AAC1C,oBAAA,IAAI,CAAC,qCAAqC,GAAG,IAAI,CAAC,4BAA4B;AAC9E,oBAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE;AACxC,iBAAC,CAAC;AACJ,aAAC,CAAC;;;;IAKE,qBAAqB,GAAA;QAC3B,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;AAChC,YAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,SAAS;;aACrD;YACL,OAAO,IAAI,CAAC,QAAQ;;;AAIxB;;;AAGG;AACgB,IAAA,cAAc,CAAC,MAAqB,EAAA;;;;;;;;;;AAUrD,QAAA,OAAO,CAAC,MAAM,CAAC,aAAa;;uGA5TnB,cAAc,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAd,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,cAAc,EAuBN,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,kBAAA,EAAA,MAAA,EAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAAA,gBAAgB,CAyBhB,EAAA,eAAA,EAAA,CAAA,kBAAA,EAAA,iBAAA,CAAA,EAAA,UAAA,EAAA,CAAA,YAAA,EAAA,YAAA,EAAA,gBAAgB,kEAkBhB,gBAAgB,CAAA,EAAA,4BAAA,EAAA,CAAA,8BAAA,EAAA,8BAAA,EAIhB,gBAAgB,CAAA,EAAA,KAAA,EAAA,OAAA,EAAA,EAAA,OAAA,EAAA,EAAA,MAAA,EAAA,QAAA,EAAA,EAAA,IAAA,EAAA,EAAA,SAAA,EAAA,EAAA,OAAA,EAAA,SAAA,EAAA,MAAA,EAAA,SAAA,EAAA,SAAA,EAAA,kBAAA,EAAA,EAAA,UAAA,EAAA,EAAA,WAAA,EAAA,MAAA,EAAA,UAAA,EAAA,qCAAA,EAAA,oBAAA,EAAA,wBAAA,EAAA,oBAAA,EAAA,qBAAA,EAAA,2BAAA,EAAA,UAAA,EAAA,uBAAA,EAAA,iBAAA,EAAA,kCAAA,EAAA,UAAA,EAAA,kCAAA,EAAA,UAAA,EAAA,EAAA,cAAA,EAAA,6CAAA,EAAA,EAAA,SAAA,EA1ExB,CAAC,uCAAuC,CAAC,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,QAAA,EAAA,SAAA,EAgHnC,aAAa,EApIpB,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA;;;;AAIT,EAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,g3BAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAoBU,cAAc,EAAA,UAAA,EAAA,CAAA;kBA1B1B,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,kBAAkB,EAClB,QAAA,EAAA;;;;GAIT,EAEK,IAAA,EAAA;AACJ,wBAAA,OAAO,EAAE,6CAA6C;AACtD,wBAAA,aAAa,EAAE,MAAM;AACrB,wBAAA,YAAY,EAAE,qCAAqC;AACnD,wBAAA,sBAAsB,EAAE,wBAAwB;AAChD,wBAAA,sBAAsB,EAAE,qBAAqB;AAC7C,wBAAA,6BAA6B,EAAE,UAAU;AACzC,wBAAA,yBAAyB,EAAE,iBAAiB;AAC5C,wBAAA,oCAAoC,EAAE,UAAU;AAChD,wBAAA,oCAAoC,EAAE,UAAU;AAChD,wBAAA,SAAS,EAAE,SAAS;AACpB,wBAAA,QAAQ,EAAE,SAAS;AACnB,wBAAA,WAAW,EAAE,kBAAkB;qBAChC,EACU,SAAA,EAAA,CAAC,uCAAuC,CAAC,EACrC,aAAA,EAAA,iBAAiB,CAAC,IAAI,EAAA,eAAA,EACpB,uBAAuB,CAAC,MAAM,EAAA,MAAA,EAAA,CAAA,g3BAAA,CAAA,EAAA;8BA0B3C,QAAQ,EAAA,CAAA;sBADX,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBAiBT,eAAe,EAAA,CAAA;sBAAzC,KAAK;uBAAC,kBAAkB;gBASrB,UAAU,EAAA,CAAA;sBADb,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBAe3B,WAAW,EAAA,CAAA;sBAAnB;gBAID,QAAQ,EAAA,CAAA;sBADP,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBAKhC,4BAA4B,EAAA,CAAA;sBAD/B,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBAuBhC,KAAK,EAAA,CAAA;sBADR;gBAakB,MAAM,EAAA,CAAA;sBAAxB;gBASQ,MAAM,EAAA,CAAA;sBANd,eAAe;AAAC,gBAAA,IAAA,EAAA,CAAA,aAAa,EAAE;;;AAG9B,wBAAA,WAAW,EAAE,IAAI;AAClB,qBAAA;;;AC3JH;MACa,iBAAiB,CAAA;AAGnB,IAAA,MAAA;AAEA,IAAA,KAAA;AAJT,IAAA,WAAA;;IAES,MAAmB;;IAEnB,KAAU,EAAA;QAFV,IAAM,CAAA,MAAA,GAAN,MAAM;QAEN,IAAK,CAAA,KAAA,GAAL,KAAK;;AAEf;AAED;;;AAGG;AAyBG,MAAO,WACX,SAAQ,UAAU,CAAA;AASlB,IAAA,SAAS,GAAG,MAAM,CAAC,SAAS,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAC,CAAE;AAE5D;;;AAGG;IACM,WAAW,GAAW,eAAe;;AAGpC,IAAA,UAAU;IAED,YAAY,GAAG,MAAM;AAChC,IAAA,kBAAkB;AAE1B;;AAEG;IACK,mBAAmB,GAAa,EAAE;AAE1C;;;AAGG;AACH,IAAA,UAAU,GAAG,MAAK,GAAG;AAErB;;;AAGG;AACH,IAAA,SAAS,GAAyB,MAAK,GAAG;AAE1C;;;AAGG;AACH,IAAA,IACa,QAAQ,GAAA;AACnB,QAAA,OAAO,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,GAAG,IAAI,CAAC,SAAS;;IAEpE,IAAa,QAAQ,CAAC,KAAc,EAAA;AAClC,QAAA,IAAI,CAAC,SAAS,GAAG,KAAK;QACtB,IAAI,CAAC,eAAe,EAAE;AACtB,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE;;AAG1B;;;AAGG;AACH,IAAA,IAAI,EAAE,GAAA;AACJ,QAAA,OAAO,IAAI,CAAC,UAAU,CAAC,EAAE;;AAG3B;;;AAGG;AACH,IAAA,IAAa,KAAK,GAAA;AAChB,QAAA,QACE,CAAC,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,MAAM,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,CAAC;;AAI7F;;;AAGG;AACH,IAAA,IACI,WAAW,GAAA;AACb,QAAA,OAAO,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,GAAG,IAAI,CAAC,YAAY;;IAE1E,IAAI,WAAW,CAAC,KAAa,EAAA;AAC3B,QAAA,IAAI,CAAC,YAAY,GAAG,KAAK;AACzB,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE;;AAEhB,IAAA,YAAY;;AAGtB,IAAA,IAAa,OAAO,GAAA;QAClB,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,IAAI,IAAI,CAAC,eAAe,EAAE;;AAG1D;;;AAGG;AACH,IAAA,IACI,QAAQ,GAAA;AACV,QAAA,OAAO,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,EAAE,OAAO,EAAE,YAAY,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,KAAK;;IAE9F,IAAI,QAAQ,CAAC,KAAc,EAAA;AACzB,QAAA,IAAI,CAAC,SAAS,GAAG,KAAK;AACtB,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE;;AAEhB,IAAA,SAAS;AAEnB;;;AAGG;AACH,IAAA,IAAI,gBAAgB,GAAA;QAClB,OAAO,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,OAAO;;AAGpC;;;AAGG;AACH,IAAA,IACI,KAAK,GAAA;QACP,OAAO,IAAI,CAAC,MAAM;;IAEpB,IAAI,KAAK,CAAC,KAAU,EAAA;AAClB,QAAA,IAAI,CAAC,MAAM,GAAG,KAAK;;IAEX,MAAM,GAAU,EAAE;;AAG5B,IAAA,IACI,iBAAiB,GAAA;AACnB,QAAA,OAAO,IAAI,CAAC,kBAAkB,CAAC,OAAO;;IAExC,IAAI,iBAAiB,CAAC,KAAwB,EAAA;AAC5C,QAAA,IAAI,CAAC,kBAAkB,CAAC,OAAO,GAAG,KAAK;;;AAIzC,IAAA,IAAI,eAAe,GAAA;AACjB,QAAA,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC;;;AAI/B,IAAA,MAAM,GACvB,IAAI,YAAY,EAAqB;AAEvC;;;;AAIG;AACgB,IAAA,WAAW,GAAsB,IAAI,YAAY,EAAO;IAQlE,MAAM,GAA0B,SAAU;AAEnD;;;;AAIG;AACM,IAAA,YAAY,GAAG,IAAI,OAAO,EAAQ;;AAG3C,IAAA,IAAI,UAAU,GAAA;AACZ,QAAA,OAAO,IAAI,CAAC,kBAAkB,CAAC,UAAU;;IAE3C,IAAI,UAAU,CAAC,KAAc,EAAA;AAC3B,QAAA,IAAI,CAAC,kBAAkB,CAAC,UAAU,GAAG,KAAK;;AAK5C,IAAA,WAAA,GAAA;AACE,QAAA,KAAK,EAAE;AAEP,QAAA,MAAM,UAAU,GAAG,MAAM,CAAC,MAAM,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;AACnD,QAAA,MAAM,eAAe,GAAG,MAAM,CAAC,kBAAkB,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;AACpE,QAAA,MAAM,wBAAwB,GAAG,MAAM,CAAC,iBAAiB,CAAC;AAE1D,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;AAClB,YAAA,IAAI,CAAC,SAAS,CAAC,aAAa,GAAG,IAAI;;QAGrC,IAAI,CAAC,kBAAkB,GAAG,IAAI,kBAAkB,CAC9C,wBAAwB,EACxB,IAAI,CAAC,SAAS,EACd,eAAe,EACf,UAAU,EACV,IAAI,CAAC,YAAY,CAClB;;IAGH,kBAAkB,GAAA;AAChB,QAAA,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,MAAK;YACnE,IAAI,CAAC,KAAK,EAAE;AACZ,YAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE;AAC1B,SAAC,CAAC;QAEF,KAAK,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO;AAC7C,aAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC;aAC/B,SAAS,CAAC,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;;IAGrC,eAAe,GAAA;QACtB,KAAK,CAAC,eAAe,EAAE;AAEvB,QAAA,IAAI,CAAC,IAAI,CAAC,UAAU,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAAE;AACvE,YAAA,MAAM,KAAK,CAAC,iEAAiE,CAAC;;;IAIlF,SAAS,GAAA;AACP,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;;;;YAIlB,IAAI,CAAC,gBAAgB,EAAE;;;IAIlB,WAAW,GAAA;QAClB,KAAK,CAAC,WAAW,EAAE;AACnB,QAAA,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE;;;AAI9B,IAAA,aAAa,CAAC,YAAgC,EAAA;AAC5C,QAAA,IAAI,CAAC,UAAU,GAAG,YAAY;QAC9B,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,IAAI,CAAC,mBAAmB,CAAC;;AAG7D;;;AAGG;AACH,IAAA,gBAAgB,CAAC,KAAiB,EAAA;AAChC,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,EAAE;YACtD,IAAI,CAAC,KAAK,EAAE;;;AAIhB;;;AAGG;IACM,KAAK,GAAA;QACZ,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE;YAC5C;;AAGF,QAAA,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,EAAE;;;AAGrD,YAAA,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;;aAChD;AACL,YAAA,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,UAAU;YAE9C,IAAI,UAAU,EAAE;gBACd,UAAU,CAAC,KAAK,EAAE;;iBACb;AACL,gBAAA,IAAI,CAAC,WAAW,CAAC,kBAAkB,EAAE;;;AAIzC,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE;;AAG1B;;;AAGG;AACH,IAAA,iBAAiB,CAAC,GAAa,EAAA;;;AAG7B,QAAA,IAAI,CAAC,mBAAmB,GAAG,GAAG;AAC9B,QAAA,IAAI,CAAC,UAAU,EAAE,iBAAiB,CAAC,GAAG,CAAC;;AAGzC;;;AAGG;AACH,IAAA,UAAU,CAAC,KAAU,EAAA;;AAEnB,QAAA,IAAI,CAAC,MAAM,GAAG,KAAK;;AAGrB;;;AAGG;AACH,IAAA,gBAAgB,CAAC,EAAwB,EAAA;AACvC,QAAA,IAAI,CAAC,SAAS,GAAG,EAAE;;AAGrB;;;AAGG;AACH,IAAA,iBAAiB,CAAC,EAAc,EAAA;AAC9B,QAAA,IAAI,CAAC,UAAU,GAAG,EAAE;;AAGtB;;;AAGG;AACH,IAAA,gBAAgB,CAAC,UAAmB,EAAA;AAClC,QAAA,IAAI,CAAC,QAAQ,GAAG,UAAU;AAC1B,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE;;;IAI1B,gBAAgB,GAAA;AACd,QAAA,IAAI,CAAC,kBAAkB,CAAC,gBAAgB,EAAE;;;IAI5C,KAAK,GAAA;AACH,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;;;;;YAKlB,UAAU,CAAC,MAAK;AACd,gBAAA,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;oBACjB,IAAI,CAAC,iBAAiB,EAAE;oBACxB,IAAI,CAAC,cAAc,EAAE;;AAEzB,aAAC,CAAC;;;AAIN;;;;AAIG;IACgB,iBAAiB,GAAA;AAClC,QAAA,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE;YAC5B,KAAK,CAAC,iBAAiB,EAAE;;;;AAKpB,IAAA,cAAc,CAAC,KAAoB,EAAA;AAC1C,QAAA,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO;AAC7B,QAAA,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,UAAU;AAE9C,QAAA,IAAI,OAAO,KAAK,GAAG,EAAE;AACnB,YAAA,IACE,IAAI,CAAC,UAAU,CAAC,OAAO;AACvB,gBAAA,cAAc,CAAC,KAAK,EAAE,UAAU,CAAC;gBACjC,IAAI,CAAC,MAAM,CAAC,MAAM;gBAClB,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,EAC1B;gBACA,KAAK,CAAC,cAAc,EAAE;gBAEtB,IAAI,UAAU,EAAE;AACd,oBAAA,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,UAAU,CAAC;;qBACrC;oBACL,IAAI,CAAC,cAAc,EAAE;;;iBAElB;;;;gBAIL,KAAK,CAAC,iBAAiB,EAAE;;;AAEtB,aAAA,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE;;;;;;AAMnC,YAAA,IAAI,CAAC,OAAO,KAAK,QAAQ,IAAI,OAAO,KAAK,UAAU,KAAK,UAAU,EAAE;AAClE,gBAAA,MAAM,eAAe,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAC9C,MAAM,IAAI,MAAM,CAAC,UAAU,KAAK,UAAU,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CACtF;gBACD,MAAM,YAAY,GAAG,eAAe,CAAC,OAAO,CAAC,UAAU,CAAC;AACxD,gBAAA,MAAM,KAAK,GAAG,KAAK,CAAC,OAAO,KAAK,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC;gBAEjD,KAAK,CAAC,cAAc,EAAE;AACtB,gBAAA,IAAI,YAAY,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,aAAa,CAAC,YAAY,GAAG,KAAK,CAAC,EAAE;AACjE,oBAAA,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,eAAe,CAAC,YAAY,GAAG,KAAK,CAAC,CAAC;;;iBAElE;AACL,gBAAA,KAAK,CAAC,cAAc,CAAC,KAAK,CAAC;;;AAI/B,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE;;IAG1B,cAAc,GAAA;AACZ,QAAA,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;AACtB,YAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE;;;;IAKpB,iBAAiB,GAAA;AACvB,QAAA,MAAM,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,GAAG,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE;AAC3F,QAAA,IAAI,CAAC,MAAM,GAAG,WAAW;AACzB,QAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,iBAAiB,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;AAC1D,QAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC;AAClC,QAAA,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC;AAC3B,QAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE;;;IAIhC,cAAc,GAAA;QACpB,IAAI,CAAC,UAAU,EAAE;AACjB,QAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE;AACtC,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE;;uGAnaf,WAAW,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAX,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,WAAW,8FA6CH,gBAAgB,CAAA,EAAA,WAAA,EAAA,aAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAmDhB,gBAAgB,CApGxB,EAAA,KAAA,EAAA,OAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,EAAA,OAAA,EAAA,EAAA,MAAA,EAAA,QAAA,EAAA,WAAA,EAAA,aAAA,EAAA,EAAA,IAAA,EAAA,EAAA,SAAA,EAAA,EAAA,OAAA,EAAA,SAAA,EAAA,MAAA,EAAA,SAAA,EAAA,EAAA,UAAA,EAAA,EAAA,WAAA,EAAA,MAAA,EAAA,eAAA,EAAA,+DAAA,EAAA,oBAAA,EAAA,qBAAA,EAAA,mBAAA,EAAA,YAAA,EAAA,kCAAA,EAAA,UAAA,EAAA,iCAAA,EAAA,YAAA,EAAA,kCAAA,EAAA,UAAA,EAAA,EAAA,cAAA,EAAA,2DAAA,EAAA,EAAA,SAAA,EAAA,CAAC,EAAC,OAAO,EAAE,mBAAmB,EAAE,WAAW,EAAE,WAAW,EAAC,CAAC,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,QAAA,EAAA,SAAA,EA4JpD,UAAU,EA9KjB,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA;;;;AAIT,EAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,g3BAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAkBU,WAAW,EAAA,UAAA,EAAA,CAAA;kBAxBvB,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,eAAe,EACf,QAAA,EAAA;;;;GAIT,EAEK,IAAA,EAAA;AACJ,wBAAA,OAAO,EAAE,2DAA2D;AACpE,wBAAA,aAAa,EAAE,MAAM;AACrB,wBAAA,iBAAiB,EAAE,+DAA+D;AAClF,wBAAA,sBAAsB,EAAE,qBAAqB;AAC7C,wBAAA,qBAAqB,EAAE,YAAY;AACnC,wBAAA,oCAAoC,EAAE,UAAU;AAChD,wBAAA,mCAAmC,EAAE,YAAY;AACjD,wBAAA,oCAAoC,EAAE,UAAU;AAChD,wBAAA,SAAS,EAAE,SAAS;AACpB,wBAAA,QAAQ,EAAE,SAAS;AACpB,qBAAA,EAAA,SAAA,EACU,CAAC,EAAC,OAAO,EAAE,mBAAmB,EAAE,WAAW,EAAA,WAAa,EAAC,CAAC,iBACtD,iBAAiB,CAAC,IAAI,EACpB,eAAA,EAAA,uBAAuB,CAAC,MAAM,EAAA,MAAA,EAAA,CAAA,g3BAAA,CAAA,EAAA;wDAgDlC,QAAQ,EAAA,CAAA;sBADpB,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBAiChC,WAAW,EAAA,CAAA;sBADd;gBAoBG,QAAQ,EAAA,CAAA;sBADX,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBAuBhC,KAAK,EAAA,CAAA;sBADR;gBAWG,iBAAiB,EAAA,CAAA;sBADpB;gBAckB,MAAM,EAAA,CAAA;sBAAxB;gBAQkB,WAAW,EAAA,CAAA;sBAA7B;gBAQQ,MAAM,EAAA,CAAA;sBANd,eAAe;AAAC,gBAAA,IAAA,EAAA,CAAA,UAAU,EAAE;;;AAG3B,wBAAA,WAAW,EAAE,IAAI;AAClB,qBAAA;;;ACjMH;;;AAGG;MAqBU,YAAY,CAAA;AACb,IAAA,WAAW,GAAG,MAAM,CAA+B,UAAU,CAAC;;IAGxE,OAAO,GAAY,KAAK;;AAGxB,IAAA,IACI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,SAAS;;IAEvB,IAAI,QAAQ,CAAC,KAAkB,EAAA;QAC7B,IAAI,KAAK,EAAE;AACT,YAAA,IAAI,CAAC,SAAS,GAAG,KAAK;AACtB,YAAA,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,IAAI,CAAC;;;AAG9B,IAAA,SAAS;AAEjB;;AAEG;IAEH,SAAS,GAAY,KAAK;AAE1B;;;;AAIG;AAEH,IAAA,iBAAiB;;AAIR,IAAA,OAAO,GAAoC,IAAI,YAAY,EAAqB;;IAGhF,WAAW,GAAW,EAAE;;IAGxB,EAAE,GAAW,MAAM,CAAC,YAAY,CAAC,CAAC,KAAK,CAAC,0BAA0B,CAAC;;AAG5E,IAAA,IACI,QAAQ,GAAA;AACV,QAAA,OAAO,IAAI,CAAC,SAAS,KAAK,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;;IAEtE,IAAI,QAAQ,CAAC,KAAc,EAAA;AACzB,QAAA,IAAI,CAAC,SAAS,GAAG,KAAK;;IAEhB,SAAS,GAAY,KAAK;;AAGlC,IAAA,IAAI,KAAK,GAAA;AACP,QAAA,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK;;;AAIxB,IAAA,YAAY;AAIrB,IAAA,WAAA,GAAA;AACE,QAAA,MAAM,cAAc,GAAG,MAAM,CAAyB,yBAAyB,CAAC;AAChF,QAAA,MAAM,SAAS,GAAG,MAAM,CAAe,cAAc,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;QAExE,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,WAAW,CAAC,aAAiC;AACtE,QAAA,IAAI,CAAC,iBAAiB,GAAG,cAAc,CAAC,iBAAiB;QAEzD,IAAI,SAAS,EAAE;YACb,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,GAAG,CAAC,kCAAkC,CAAC;;;IAIvE,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,IAAI,EAAE;;IAGpC,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE;;;AAIzB,IAAA,QAAQ,CAAC,KAAoB,EAAA;QAC3B,IAAI,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,OAAO,KAAK,SAAS,EAAE;;;AAG7C,YAAA,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;AACjB,gBAAA,IAAI,CAAC,SAAS,CAAC,cAAc,EAAE;;YAEjC,KAAK,CAAC,cAAc,EAAE;;aACjB;AACL,YAAA,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC;;;;IAK5B,KAAK,GAAA;AACH,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,IAAI,CAAC,YAAY,EAAE;;AAErB,QAAA,IAAI,CAAC,OAAO,GAAG,KAAK;;AAEpB,QAAA,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE;AAC3B,YAAA,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE;;AAExB,QAAA,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,IAAI,EAAE;;IAGpC,MAAM,GAAA;AACJ,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI;AACnB,QAAA,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,IAAI,EAAE;;;AAIpC,IAAA,YAAY,CAAC,KAAqB,EAAA;AAChC,QAAA,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE;AAC5D,YAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;gBAChB,KAAK,EAAE,IAAI,CAAC,YAAY;AACxB,gBAAA,KAAK,EAAE,IAAI,CAAC,YAAY,CAAC,KAAK;AAC9B,gBAAA,SAAS,EAAE,IAAI;AAChB,aAAA,CAAC;YAEF,KAAK,EAAE,cAAc,EAAE;;;IAI3B,QAAQ,GAAA;;AAEN,QAAA,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,IAAI,EAAE;;;IAIpC,KAAK,GAAA;AACH,QAAA,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE;;;IAI3B,KAAK,GAAA;AACH,QAAA,IAAI,CAAC,YAAY,CAAC,KAAK,GAAG,EAAE;;AAG9B,IAAA,iBAAiB,CAAC,GAAa,EAAA;AAC7B,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa;;;AAI9C,QAAA,IAAI,GAAG,CAAC,MAAM,EAAE;AACd,YAAA,OAAO,CAAC,YAAY,CAAC,kBAAkB,EAAE,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;;aAClD;AACL,YAAA,OAAO,CAAC,eAAe,CAAC,kBAAkB,CAAC;;;;AAKvC,IAAA,eAAe,CAAC,KAAoB,EAAA;QAC1C,OAAO,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,IAAI,GAAG,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC;;uGA7J1E,YAAY,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAZ,YAAY,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,wBAAA,EAAA,MAAA,EAAA,EAAA,QAAA,EAAA,CAAA,iBAAA,EAAA,UAAA,CAAA,EAAA,SAAA,EAAA,CAAA,uBAAA,EAAA,WAAA,EAsB4B,gBAAgB,CAAA,EAAA,iBAAA,EAAA,CAAA,+BAAA,EAAA,mBAAA,CAAA,EAAA,WAAA,EAAA,aAAA,EAAA,EAAA,EAAA,IAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAsBhD,gBAAgB,CAAA,EAAA,EAAA,OAAA,EAAA,EAAA,OAAA,EAAA,sBAAA,EAAA,EAAA,IAAA,EAAA,EAAA,SAAA,EAAA,EAAA,SAAA,EAAA,kBAAA,EAAA,MAAA,EAAA,SAAA,EAAA,OAAA,EAAA,UAAA,EAAA,OAAA,EAAA,YAAA,EAAA,EAAA,UAAA,EAAA,EAAA,IAAA,EAAA,IAAA,EAAA,eAAA,EAAA,kBAAA,EAAA,kBAAA,EAAA,qBAAA,EAAA,mBAAA,EAAA,uEAAA,EAAA,oBAAA,EAAA,yCAAA,EAAA,eAAA,EAAA,yCAAA,EAAA,EAAA,cAAA,EAAA,kFAAA,EAAA,EAAA,QAAA,EAAA,CAAA,cAAA,EAAA,iBAAA,CAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FA5CxB,YAAY,EAAA,UAAA,EAAA,CAAA;kBApBxB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,wBAAwB;AAClC,oBAAA,QAAQ,EAAE,+BAA+B;AACzC,oBAAA,IAAI,EAAE;;;;AAIJ,wBAAA,OAAO,EAAE,kFAAkF;AAC3F,wBAAA,WAAW,EAAE,kBAAkB;AAC/B,wBAAA,QAAQ,EAAE,SAAS;AACnB,wBAAA,SAAS,EAAE,UAAU;AACrB,wBAAA,SAAS,EAAE,YAAY;AACvB,wBAAA,MAAM,EAAE,IAAI;AACZ,wBAAA,iBAAiB,EAAE,kBAAkB;AACrC,wBAAA,oBAAoB,EAAE,qBAAqB;AAC3C,wBAAA,qBAAqB,EAAE,uEAAuE;AAC9F,wBAAA,sBAAsB,EAAE,yCAAyC;AACjE,wBAAA,iBAAiB,EAAE,yCAAyC;AAC7D,qBAAA;AACF,iBAAA;wDASK,QAAQ,EAAA,CAAA;sBADX,KAAK;uBAAC,iBAAiB;gBAgBxB,SAAS,EAAA,CAAA;sBADR,KAAK;AAAC,gBAAA,IAAA,EAAA,CAAA,EAAC,KAAK,EAAE,uBAAuB,EAAE,SAAS,EAAE,gBAAgB,EAAC;gBASpE,iBAAiB,EAAA,CAAA;sBADhB,KAAK;uBAAC,+BAA+B;gBAK7B,OAAO,EAAA,CAAA;sBADf,MAAM;uBAAC,sBAAsB;gBAIrB,WAAW,EAAA,CAAA;sBAAnB;gBAGQ,EAAE,EAAA,CAAA;sBAAV;gBAIG,QAAQ,EAAA,CAAA;sBADX,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;;;ACvFtC,MAAM,iBAAiB,GAAG;IACxB,OAAO;IACP,aAAa;IACb,gBAAgB;IAChB,WAAW;IACX,YAAY;IACZ,cAAc;IACd,aAAa;IACb,aAAa;IACb,UAAU;IACV,UAAU;IACV,mBAAmB;CACpB;MAeY,cAAc,CAAA;uGAAd,cAAc,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;AAAd,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,cAAc,YAZf,eAAe,EAAE,eAAe,EAAE,aAAa,EAdzD,OAAO;YACP,aAAa;YACb,gBAAgB;YAChB,WAAW;YACX,YAAY;YACZ,cAAc;YACd,aAAa;YACb,aAAa;YACb,UAAU;YACV,UAAU;YACV,mBAAmB,CAAA,EAAA,OAAA,EAAA,CAKT,eAAe,EAfzB,OAAO;YACP,aAAa;YACb,gBAAgB;YAChB,WAAW;YACX,YAAY;YACZ,cAAc;YACd,aAAa;YACb,aAAa;YACb,UAAU;YACV,UAAU;YACV,mBAAmB,CAAA,EAAA,CAAA;AAgBR,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,cAAc,EAVd,SAAA,EAAA;YACT,iBAAiB;AACjB,YAAA;AACE,gBAAA,OAAO,EAAE,yBAAyB;AAClC,gBAAA,QAAQ,EAAE;oBACR,iBAAiB,EAAE,CAAC,KAAK,CAAC;AACD,iBAAA;AAC5B,aAAA;AACF,SAAA,EAAA,OAAA,EAAA,CAVS,eAAe,EAAE,eAAe,EAChC,eAAe,CAAA,EAAA,CAAA;;2FAWd,cAAc,EAAA,UAAA,EAAA,CAAA;kBAb1B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACR,OAAO,EAAE,CAAC,eAAe,EAAE,eAAe,EAAE,aAAa,EAAE,iBAAiB,CAAC;AAC7E,oBAAA,OAAO,EAAE,CAAC,eAAe,EAAE,iBAAiB,CAAC;AAC7C,oBAAA,SAAS,EAAE;wBACT,iBAAiB;AACjB,wBAAA;AACE,4BAAA,OAAO,EAAE,yBAAyB;AAClC,4BAAA,QAAQ,EAAE;gCACR,iBAAiB,EAAE,CAAC,KAAK,CAAC;AACD,6BAAA;AAC5B,yBAAA;AACF,qBAAA;AACF,iBAAA;;;;;"}