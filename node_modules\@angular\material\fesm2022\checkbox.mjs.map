{"version": 3, "file": "checkbox.mjs", "sources": ["../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/checkbox/checkbox-config.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/checkbox/checkbox.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/checkbox/checkbox.html", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/checkbox/checkbox-required-validator.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/checkbox/module.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\nimport {InjectionToken} from '@angular/core';\nimport {ThemePalette} from '../core';\n\n/** Default `mat-checkbox` options that can be overridden. */\nexport interface MatCheckboxDefaultOptions {\n  /**\n   * Default theme color of the checkbox. This API is supported in M2 themes\n   * only, it has no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/checkbox/styling.\n   *\n   * For information on applying color variants in M3, see\n   * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n   */\n  color?: ThemePalette;\n\n  /** Default checkbox click action for checkboxes. */\n  clickAction?: MatCheckboxClickAction;\n\n  /** Whether disabled checkboxes should be interactive. */\n  disabledInteractive?: boolean;\n}\n\n/** Injection token to be used to override the default options for `mat-checkbox`. */\nexport const MAT_CHECKBOX_DEFAULT_OPTIONS = new InjectionToken<MatCheckboxDefaultOptions>(\n  'mat-checkbox-default-options',\n  {\n    providedIn: 'root',\n    factory: MAT_CHECKBOX_DEFAULT_OPTIONS_FACTORY,\n  },\n);\n\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nexport function MAT_CHECKBOX_DEFAULT_OPTIONS_FACTORY(): MatCheckboxDefaultOptions {\n  return {\n    color: 'accent',\n    clickAction: 'check-indeterminate',\n    disabledInteractive: false,\n  };\n}\n\n/**\n * Checkbox click action when user click on input element.\n * noop: Do not toggle checked or indeterminate.\n * check: Only toggle checked status, ignore indeterminate.\n * check-indeterminate: Toggle checked status, set indeterminate to false. Default behavior.\n * undefined: Same as `check-indeterminate`.\n */\nexport type MatCheckboxClickAction = 'noop' | 'check' | 'check-indeterminate' | undefined;\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {_IdGenerator, FocusableOption} from '@angular/cdk/a11y';\nimport {\n  ANIMATION_MODULE_TYPE,\n  AfterViewInit,\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  ElementRef,\n  EventEmitter,\n  Input,\n  NgZone,\n  OnChanges,\n  Output,\n  SimpleChanges,\n  ViewChild,\n  ViewEncapsulation,\n  booleanAttribute,\n  forwardRef,\n  numberAttribute,\n  inject,\n  HostAttributeToken,\n} from '@angular/core';\nimport {\n  AbstractControl,\n  ControlValueAccessor,\n  NG_VALIDATORS,\n  NG_VALUE_ACCESSOR,\n  ValidationErrors,\n  Validator,\n} from '@angular/forms';\nimport {MatRipple, _MatInternalFormField, _StructuralStylesLoader} from '../core';\nimport {\n  MAT_CHECKBOX_DEFAULT_OPTIONS,\n  MAT_CHECKBOX_DEFAULT_OPTIONS_FACTORY,\n  MatCheckboxDefaultOptions,\n} from './checkbox-config';\nimport {_CdkPrivateStyleLoader} from '@angular/cdk/private';\n\n/**\n * Represents the different states that require custom transitions between them.\n * @docs-private\n */\nexport enum TransitionCheckState {\n  /** The initial state of the component before any user interaction. */\n  Init,\n  /** The state representing the component when it's becoming checked. */\n  Checked,\n  /** The state representing the component when it's becoming unchecked. */\n  Unchecked,\n  /** The state representing the component when it's becoming indeterminate. */\n  Indeterminate,\n}\n\n/**\n * @deprecated Will stop being exported.\n * @breaking-change 19.0.0\n */\nexport const MAT_CHECKBOX_CONTROL_VALUE_ACCESSOR: any = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => MatCheckbox),\n  multi: true,\n};\n\n/** Change event object emitted by checkbox. */\nexport class MatCheckboxChange {\n  /** The source checkbox of the event. */\n  source: MatCheckbox;\n  /** The new `checked` value of the checkbox. */\n  checked: boolean;\n}\n\n// Default checkbox configuration.\nconst defaults = MAT_CHECKBOX_DEFAULT_OPTIONS_FACTORY();\n\n@Component({\n  selector: 'mat-checkbox',\n  templateUrl: 'checkbox.html',\n  styleUrl: 'checkbox.css',\n  host: {\n    'class': 'mat-mdc-checkbox',\n    '[attr.tabindex]': 'null',\n    '[attr.aria-label]': 'null',\n    '[attr.aria-labelledby]': 'null',\n    '[class._mat-animation-noopable]': `_animationMode === 'NoopAnimations'`,\n    '[class.mdc-checkbox--disabled]': 'disabled',\n    '[id]': 'id',\n    // Add classes that users can use to more easily target disabled or checked checkboxes.\n    '[class.mat-mdc-checkbox-disabled]': 'disabled',\n    '[class.mat-mdc-checkbox-checked]': 'checked',\n    '[class.mat-mdc-checkbox-disabled-interactive]': 'disabledInteractive',\n    '[class]': 'color ? \"mat-\" + color : \"mat-accent\"',\n  },\n  providers: [\n    MAT_CHECKBOX_CONTROL_VALUE_ACCESSOR,\n    {\n      provide: NG_VALIDATORS,\n      useExisting: MatCheckbox,\n      multi: true,\n    },\n  ],\n  exportAs: 'matCheckbox',\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  imports: [MatRipple, _MatInternalFormField],\n})\nexport class MatCheckbox\n  implements AfterViewInit, OnChanges, ControlValueAccessor, Validator, FocusableOption\n{\n  _elementRef = inject<ElementRef<HTMLElement>>(ElementRef);\n  private _changeDetectorRef = inject(ChangeDetectorRef);\n  private _ngZone = inject(NgZone);\n  _animationMode? = inject(ANIMATION_MODULE_TYPE, {optional: true});\n  private _options = inject<MatCheckboxDefaultOptions>(MAT_CHECKBOX_DEFAULT_OPTIONS, {\n    optional: true,\n  });\n\n  /** Focuses the checkbox. */\n  focus() {\n    this._inputElement.nativeElement.focus();\n  }\n\n  /** Creates the change event that will be emitted by the checkbox. */\n  protected _createChangeEvent(isChecked: boolean) {\n    const event = new MatCheckboxChange();\n    event.source = this;\n    event.checked = isChecked;\n    return event;\n  }\n\n  /** Gets the element on which to add the animation CSS classes. */\n  protected _getAnimationTargetElement() {\n    return this._inputElement?.nativeElement;\n  }\n\n  /** CSS classes to add when transitioning between the different checkbox states. */\n  protected _animationClasses = {\n    uncheckedToChecked: 'mdc-checkbox--anim-unchecked-checked',\n    uncheckedToIndeterminate: 'mdc-checkbox--anim-unchecked-indeterminate',\n    checkedToUnchecked: 'mdc-checkbox--anim-checked-unchecked',\n    checkedToIndeterminate: 'mdc-checkbox--anim-checked-indeterminate',\n    indeterminateToChecked: 'mdc-checkbox--anim-indeterminate-checked',\n    indeterminateToUnchecked: 'mdc-checkbox--anim-indeterminate-unchecked',\n  };\n\n  /**\n   * Attached to the aria-label attribute of the host element. In most cases, aria-labelledby will\n   * take precedence so this may be omitted.\n   */\n  @Input('aria-label') ariaLabel: string = '';\n\n  /**\n   * Users can specify the `aria-labelledby` attribute which will be forwarded to the input element\n   */\n  @Input('aria-labelledby') ariaLabelledby: string | null = null;\n\n  /** The 'aria-describedby' attribute is read after the element's label and field type. */\n  @Input('aria-describedby') ariaDescribedby: string;\n\n  /**\n   * Users can specify the `aria-expanded` attribute which will be forwarded to the input element\n   */\n  @Input({alias: 'aria-expanded', transform: booleanAttribute}) ariaExpanded: boolean;\n\n  /**\n   * Users can specify the `aria-controls` attribute which will be forwarded to the input element\n   */\n  @Input('aria-controls') ariaControls: string;\n\n  /** Users can specify the `aria-owns` attribute which will be forwarded to the input element */\n  @Input('aria-owns') ariaOwns: string;\n\n  private _uniqueId: string;\n\n  /** A unique id for the checkbox input. If none is supplied, it will be auto-generated. */\n  @Input() id: string;\n\n  /** Returns the unique id for the visual hidden input. */\n  get inputId(): string {\n    return `${this.id || this._uniqueId}-input`;\n  }\n\n  /** Whether the checkbox is required. */\n  @Input({transform: booleanAttribute}) required: boolean;\n\n  /** Whether the label should appear after or before the checkbox. Defaults to 'after' */\n  @Input() labelPosition: 'before' | 'after' = 'after';\n\n  /** Name value will be applied to the input element if present */\n  @Input() name: string | null = null;\n\n  /** Event emitted when the checkbox's `checked` value changes. */\n  @Output() readonly change = new EventEmitter<MatCheckboxChange>();\n\n  /** Event emitted when the checkbox's `indeterminate` value changes. */\n  @Output() readonly indeterminateChange: EventEmitter<boolean> = new EventEmitter<boolean>();\n\n  /** The value attribute of the native input element */\n  @Input() value: string;\n\n  /** Whether the checkbox has a ripple. */\n  @Input({transform: booleanAttribute}) disableRipple: boolean;\n\n  /** The native `<input type=\"checkbox\">` element */\n  @ViewChild('input') _inputElement: ElementRef<HTMLInputElement>;\n\n  /** The native `<label>` element */\n  @ViewChild('label') _labelElement: ElementRef<HTMLInputElement>;\n\n  /** Tabindex for the checkbox. */\n  @Input({transform: (value: unknown) => (value == null ? undefined : numberAttribute(value))})\n  tabIndex: number;\n\n  // TODO(crisbeto): this should be a ThemePalette, but some internal apps were abusing\n  // the lack of type checking previously and assigning random strings.\n  /**\n   * Theme color of the checkbox. This API is supported in M2 themes only, it\n   * has no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/checkbox/styling.\n   *\n   * For information on applying color variants in M3, see\n   * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n   */\n  @Input() color: string | undefined;\n\n  /** Whether the checkbox should remain interactive when it is disabled. */\n  @Input({transform: booleanAttribute})\n  disabledInteractive: boolean;\n\n  /**\n   * Called when the checkbox is blurred. Needed to properly implement ControlValueAccessor.\n   * @docs-private\n   */\n  _onTouched: () => any = () => {};\n\n  private _currentAnimationClass: string = '';\n  private _currentCheckState: TransitionCheckState = TransitionCheckState.Init;\n  private _controlValueAccessorChangeFn: (value: any) => void = () => {};\n  private _validatorChangeFn = () => {};\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    inject(_CdkPrivateStyleLoader).load(_StructuralStylesLoader);\n    const tabIndex = inject(new HostAttributeToken('tabindex'), {optional: true});\n    this._options = this._options || defaults;\n    this.color = this._options.color || defaults.color;\n    this.tabIndex = tabIndex == null ? 0 : parseInt(tabIndex) || 0;\n    this.id = this._uniqueId = inject(_IdGenerator).getId('mat-mdc-checkbox-');\n    this.disabledInteractive = this._options?.disabledInteractive ?? false;\n  }\n\n  ngOnChanges(changes: SimpleChanges) {\n    if (changes['required']) {\n      this._validatorChangeFn();\n    }\n  }\n\n  ngAfterViewInit() {\n    this._syncIndeterminate(this._indeterminate);\n  }\n\n  /** Whether the checkbox is checked. */\n  @Input({transform: booleanAttribute})\n  get checked(): boolean {\n    return this._checked;\n  }\n  set checked(value: boolean) {\n    if (value != this.checked) {\n      this._checked = value;\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  private _checked: boolean = false;\n\n  /** Whether the checkbox is disabled. */\n  @Input({transform: booleanAttribute})\n  get disabled(): boolean {\n    return this._disabled;\n  }\n  set disabled(value: boolean) {\n    if (value !== this.disabled) {\n      this._disabled = value;\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  private _disabled: boolean = false;\n\n  /**\n   * Whether the checkbox is indeterminate. This is also known as \"mixed\" mode and can be used to\n   * represent a checkbox with three states, e.g. a checkbox that represents a nested list of\n   * checkable items. Note that whenever checkbox is manually clicked, indeterminate is immediately\n   * set to false.\n   */\n  @Input({transform: booleanAttribute})\n  get indeterminate(): boolean {\n    return this._indeterminate;\n  }\n  set indeterminate(value: boolean) {\n    const changed = value != this._indeterminate;\n    this._indeterminate = value;\n\n    if (changed) {\n      if (this._indeterminate) {\n        this._transitionCheckState(TransitionCheckState.Indeterminate);\n      } else {\n        this._transitionCheckState(\n          this.checked ? TransitionCheckState.Checked : TransitionCheckState.Unchecked,\n        );\n      }\n      this.indeterminateChange.emit(this._indeterminate);\n    }\n\n    this._syncIndeterminate(this._indeterminate);\n  }\n  private _indeterminate: boolean = false;\n\n  _isRippleDisabled() {\n    return this.disableRipple || this.disabled;\n  }\n\n  /** Method being called whenever the label text changes. */\n  _onLabelTextChange() {\n    // Since the event of the `cdkObserveContent` directive runs outside of the zone, the checkbox\n    // component will be only marked for check, but no actual change detection runs automatically.\n    // Instead of going back into the zone in order to trigger a change detection which causes\n    // *all* components to be checked (if explicitly marked or not using OnPush), we only trigger\n    // an explicit change detection for the checkbox view and its children.\n    this._changeDetectorRef.detectChanges();\n  }\n\n  // Implemented as part of ControlValueAccessor.\n  writeValue(value: any) {\n    this.checked = !!value;\n  }\n\n  // Implemented as part of ControlValueAccessor.\n  registerOnChange(fn: (value: any) => void) {\n    this._controlValueAccessorChangeFn = fn;\n  }\n\n  // Implemented as part of ControlValueAccessor.\n  registerOnTouched(fn: any) {\n    this._onTouched = fn;\n  }\n\n  // Implemented as part of ControlValueAccessor.\n  setDisabledState(isDisabled: boolean) {\n    this.disabled = isDisabled;\n  }\n\n  // Implemented as a part of Validator.\n  validate(control: AbstractControl<boolean>): ValidationErrors | null {\n    return this.required && control.value !== true ? {'required': true} : null;\n  }\n\n  // Implemented as a part of Validator.\n  registerOnValidatorChange(fn: () => void): void {\n    this._validatorChangeFn = fn;\n  }\n\n  private _transitionCheckState(newState: TransitionCheckState) {\n    let oldState = this._currentCheckState;\n    let element = this._getAnimationTargetElement();\n\n    if (oldState === newState || !element) {\n      return;\n    }\n    if (this._currentAnimationClass) {\n      element.classList.remove(this._currentAnimationClass);\n    }\n\n    this._currentAnimationClass = this._getAnimationClassForCheckStateTransition(\n      oldState,\n      newState,\n    );\n    this._currentCheckState = newState;\n\n    if (this._currentAnimationClass.length > 0) {\n      element.classList.add(this._currentAnimationClass);\n\n      // Remove the animation class to avoid animation when the checkbox is moved between containers\n      const animationClass = this._currentAnimationClass;\n\n      this._ngZone.runOutsideAngular(() => {\n        setTimeout(() => {\n          element!.classList.remove(animationClass);\n        }, 1000);\n      });\n    }\n  }\n\n  private _emitChangeEvent() {\n    this._controlValueAccessorChangeFn(this.checked);\n    this.change.emit(this._createChangeEvent(this.checked));\n\n    // Assigning the value again here is redundant, but we have to do it in case it was\n    // changed inside the `change` listener which will cause the input to be out of sync.\n    if (this._inputElement) {\n      this._inputElement.nativeElement.checked = this.checked;\n    }\n  }\n\n  /** Toggles the `checked` state of the checkbox. */\n  toggle(): void {\n    this.checked = !this.checked;\n    this._controlValueAccessorChangeFn(this.checked);\n  }\n\n  protected _handleInputClick() {\n    const clickAction = this._options?.clickAction;\n\n    // If resetIndeterminate is false, and the current state is indeterminate, do nothing on click\n    if (!this.disabled && clickAction !== 'noop') {\n      // When user manually click on the checkbox, `indeterminate` is set to false.\n      if (this.indeterminate && clickAction !== 'check') {\n        Promise.resolve().then(() => {\n          this._indeterminate = false;\n          this.indeterminateChange.emit(this._indeterminate);\n        });\n      }\n\n      this._checked = !this._checked;\n      this._transitionCheckState(\n        this._checked ? TransitionCheckState.Checked : TransitionCheckState.Unchecked,\n      );\n\n      // Emit our custom change event if the native input emitted one.\n      // It is important to only emit it, if the native input triggered one, because\n      // we don't want to trigger a change event, when the `checked` variable changes for example.\n      this._emitChangeEvent();\n    } else if (\n      (this.disabled && this.disabledInteractive) ||\n      (!this.disabled && clickAction === 'noop')\n    ) {\n      // Reset native input when clicked with noop. The native checkbox becomes checked after\n      // click, reset it to be align with `checked` value of `mat-checkbox`.\n      this._inputElement.nativeElement.checked = this.checked;\n      this._inputElement.nativeElement.indeterminate = this.indeterminate;\n    }\n  }\n\n  _onInteractionEvent(event: Event) {\n    // We always have to stop propagation on the change event.\n    // Otherwise the change event, from the input element, will bubble up and\n    // emit its event object to the `change` output.\n    event.stopPropagation();\n  }\n\n  _onBlur() {\n    // When a focused element becomes disabled, the browser *immediately* fires a blur event.\n    // Angular does not expect events to be raised during change detection, so any state change\n    // (such as a form control's 'ng-touched') will cause a changed-after-checked error.\n    // See https://github.com/angular/angular/issues/17793. To work around this, we defer\n    // telling the form control it has been touched until the next tick.\n    Promise.resolve().then(() => {\n      this._onTouched();\n      this._changeDetectorRef.markForCheck();\n    });\n  }\n\n  private _getAnimationClassForCheckStateTransition(\n    oldState: TransitionCheckState,\n    newState: TransitionCheckState,\n  ): string {\n    // Don't transition if animations are disabled.\n    if (this._animationMode === 'NoopAnimations') {\n      return '';\n    }\n\n    switch (oldState) {\n      case TransitionCheckState.Init:\n        // Handle edge case where user interacts with checkbox that does not have [(ngModel)] or\n        // [checked] bound to it.\n        if (newState === TransitionCheckState.Checked) {\n          return this._animationClasses.uncheckedToChecked;\n        } else if (newState == TransitionCheckState.Indeterminate) {\n          return this._checked\n            ? this._animationClasses.checkedToIndeterminate\n            : this._animationClasses.uncheckedToIndeterminate;\n        }\n        break;\n      case TransitionCheckState.Unchecked:\n        return newState === TransitionCheckState.Checked\n          ? this._animationClasses.uncheckedToChecked\n          : this._animationClasses.uncheckedToIndeterminate;\n      case TransitionCheckState.Checked:\n        return newState === TransitionCheckState.Unchecked\n          ? this._animationClasses.checkedToUnchecked\n          : this._animationClasses.checkedToIndeterminate;\n      case TransitionCheckState.Indeterminate:\n        return newState === TransitionCheckState.Checked\n          ? this._animationClasses.indeterminateToChecked\n          : this._animationClasses.indeterminateToUnchecked;\n    }\n\n    return '';\n  }\n\n  /**\n   * Syncs the indeterminate value with the checkbox DOM node.\n   *\n   * We sync `indeterminate` directly on the DOM node, because in Ivy the check for whether a\n   * property is supported on an element boils down to `if (propName in element)`. Domino's\n   * HTMLInputElement doesn't have an `indeterminate` property so Ivy will warn during\n   * server-side rendering.\n   */\n  private _syncIndeterminate(value: boolean) {\n    const nativeCheckbox = this._inputElement;\n\n    if (nativeCheckbox) {\n      nativeCheckbox.nativeElement.indeterminate = value;\n    }\n  }\n\n  _onInputClick() {\n    this._handleInputClick();\n  }\n\n  _onTouchTargetClick() {\n    this._handleInputClick();\n\n    if (!this.disabled) {\n      // Normally the input should be focused already, but if the click\n      // comes from the touch target, then we might have to focus it ourselves.\n      this._inputElement.nativeElement.focus();\n    }\n  }\n\n  /**\n   *  Prevent click events that come from the `<label/>` element from bubbling. This prevents the\n   *  click handler on the host from triggering twice when clicking on the `<label/>` element. After\n   *  the click event on the `<label/>` propagates, the browsers dispatches click on the associated\n   *  `<input/>`. By preventing clicks on the label by bubbling, we ensure only one click event\n   *  bubbles when the label is clicked.\n   */\n  _preventBubblingFromLabel(event: MouseEvent) {\n    if (!!event.target && this._labelElement.nativeElement.contains(event.target as HTMLElement)) {\n      event.stopPropagation();\n    }\n  }\n}\n", "<div mat-internal-form-field [labelPosition]=\"labelPosition\" (click)=\"_preventBubblingFromLabel($event)\">\n  <div #checkbox class=\"mdc-checkbox\">\n    <!-- Render this element first so the input is on top. -->\n    <div class=\"mat-mdc-checkbox-touch-target\" (click)=\"_onTouchTargetClick()\"></div>\n    <input #input\n           type=\"checkbox\"\n           class=\"mdc-checkbox__native-control\"\n           [class.mdc-checkbox--selected]=\"checked\"\n           [attr.aria-label]=\"ariaLabel || null\"\n           [attr.aria-labelledby]=\"ariaLabelledby\"\n           [attr.aria-describedby]=\"ariaDescribedby\"\n           [attr.aria-checked]=\"indeterminate ? 'mixed' : null\"\n           [attr.aria-controls]=\"ariaControls\"\n           [attr.aria-disabled]=\"disabled && disabledInteractive ? true : null\"\n           [attr.aria-expanded]=\"ariaExpanded\"\n           [attr.aria-owns]=\"ariaOwns\"\n           [attr.name]=\"name\"\n           [attr.value]=\"value\"\n           [checked]=\"checked\"\n           [indeterminate]=\"indeterminate\"\n           [disabled]=\"disabled && !disabledInteractive\"\n           [id]=\"inputId\"\n           [required]=\"required\"\n           [tabIndex]=\"disabled && !disabledInteractive ? -1 : tabIndex\"\n           (blur)=\"_onBlur()\"\n           (click)=\"_onInputClick()\"\n           (change)=\"_onInteractionEvent($event)\"/>\n    <div class=\"mdc-checkbox__ripple\"></div>\n    <div class=\"mdc-checkbox__background\">\n      <svg class=\"mdc-checkbox__checkmark\"\n           focusable=\"false\"\n           viewBox=\"0 0 24 24\"\n           aria-hidden=\"true\">\n        <path class=\"mdc-checkbox__checkmark-path\"\n              fill=\"none\"\n              d=\"M1.73,12.91 8.1,19.28 22.79,4.59\"/>\n      </svg>\n      <div class=\"mdc-checkbox__mixedmark\"></div>\n    </div>\n    <div class=\"mat-mdc-checkbox-ripple mat-focus-indicator\" mat-ripple\n      [matRippleTrigger]=\"checkbox\"\n      [matRippleDisabled]=\"disableRipple || disabled\"\n      [matRippleCentered]=\"true\"></div>\n  </div>\n  <!--\n    Avoid putting a click handler on the <label/> to fix duplicate navigation stop on Talk Back\n    (#14385). Putting a click handler on the <label/> caused this bug because the browser produced\n    an unnecessary accessibility tree node.\n  -->\n  <label class=\"mdc-label\" #label [for]=\"inputId\">\n    <ng-content></ng-content>\n  </label>\n</div>\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Directive, forwardRef, Provider} from '@angular/core';\nimport {CheckboxRequiredValidator, NG_VALIDATORS} from '@angular/forms';\n\n/**\n * @deprecated No longer used, `MatCheckbox` implements required validation directly.\n * @breaking-change 19.0.0\n */\nexport const MAT_CHECKBOX_REQUIRED_VALIDATOR: Provider = {\n  provide: NG_VALIDATORS,\n  useExisting: forwardRef(() => MatCheckboxRequiredValidator),\n  multi: true,\n};\n\n/**\n * Validator for Material checkbox's required attribute in template-driven checkbox.\n * Current CheckboxRequiredValidator only work with `input type=checkbox` and does not\n * work with `mat-checkbox`.\n *\n * @deprecated No longer used, `MatCheckbox` implements required validation directly.\n * @breaking-change 19.0.0\n */\n@Directive({\n  selector: `mat-checkbox[required][formControlName],\n             mat-checkbox[required][formControl], mat-checkbox[required][ngModel]`,\n  providers: [MAT_CHECKBOX_REQUIRED_VALIDATOR],\n})\nexport class MatCheckboxRequiredValidator extends CheckboxRequiredValidator {}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {NgModule} from '@angular/core';\nimport {MatCommonModule} from '../core';\nimport {MatCheckbox} from './checkbox';\nimport {MatCheckboxRequiredValidator} from './checkbox-required-validator';\n\n/**\n * @deprecated No longer used, `MatCheckbox` implements required validation directly.\n * @breaking-change 19.0.0\n */\n@NgModule({\n  imports: [MatCheckboxRequiredValidator],\n  exports: [MatCheckboxRequiredValidator],\n})\nexport class _MatCheckboxRequiredValidatorModule {}\n\n@NgModule({\n  imports: [MatCheckbox, MatCommonModule],\n  exports: [MatCheckbox, MatCommonModule],\n})\nexport class MatCheckboxModule {}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AA4BA;MACa,4BAA4B,GAAG,IAAI,cAAc,CAC5D,8BAA8B,EAC9B;AACE,IAAA,UAAU,EAAE,MAAM;AAClB,IAAA,OAAO,EAAE,oCAAoC;AAC9C,CAAA;AAGH;;;;AAIG;SACa,oCAAoC,GAAA;IAClD,OAAO;AACL,QAAA,KAAK,EAAE,QAAQ;AACf,QAAA,WAAW,EAAE,qBAAqB;AAClC,QAAA,mBAAmB,EAAE,KAAK;KAC3B;AACH;;ACFA;;;AAGG;IACS;AAAZ,CAAA,UAAY,oBAAoB,EAAA;;AAE9B,IAAA,oBAAA,CAAA,oBAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAI;;AAEJ,IAAA,oBAAA,CAAA,oBAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,GAAA,SAAO;;AAEP,IAAA,oBAAA,CAAA,oBAAA,CAAA,WAAA,CAAA,GAAA,CAAA,CAAA,GAAA,WAAS;;AAET,IAAA,oBAAA,CAAA,oBAAA,CAAA,eAAA,CAAA,GAAA,CAAA,CAAA,GAAA,eAAa;AACf,CAAC,EATW,oBAAoB,KAApB,oBAAoB,GAS/B,EAAA,CAAA,CAAA;AAED;;;AAGG;AACU,MAAA,mCAAmC,GAAQ;AACtD,IAAA,OAAO,EAAE,iBAAiB;AAC1B,IAAA,WAAW,EAAE,UAAU,CAAC,MAAM,WAAW,CAAC;AAC1C,IAAA,KAAK,EAAE,IAAI;;AAGb;MACa,iBAAiB,CAAA;;AAE5B,IAAA,MAAM;;AAEN,IAAA,OAAO;AACR;AAED;AACA,MAAM,QAAQ,GAAG,oCAAoC,EAAE;MAiC1C,WAAW,CAAA;AAGtB,IAAA,WAAW,GAAG,MAAM,CAA0B,UAAU,CAAC;AACjD,IAAA,kBAAkB,GAAG,MAAM,CAAC,iBAAiB,CAAC;AAC9C,IAAA,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;IAChC,cAAc,GAAI,MAAM,CAAC,qBAAqB,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;AACzD,IAAA,QAAQ,GAAG,MAAM,CAA4B,4BAA4B,EAAE;AACjF,QAAA,QAAQ,EAAE,IAAI;AACf,KAAA,CAAC;;IAGF,KAAK,GAAA;AACH,QAAA,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,KAAK,EAAE;;;AAIhC,IAAA,kBAAkB,CAAC,SAAkB,EAAA;AAC7C,QAAA,MAAM,KAAK,GAAG,IAAI,iBAAiB,EAAE;AACrC,QAAA,KAAK,CAAC,MAAM,GAAG,IAAI;AACnB,QAAA,KAAK,CAAC,OAAO,GAAG,SAAS;AACzB,QAAA,OAAO,KAAK;;;IAIJ,0BAA0B,GAAA;AAClC,QAAA,OAAO,IAAI,CAAC,aAAa,EAAE,aAAa;;;AAIhC,IAAA,iBAAiB,GAAG;AAC5B,QAAA,kBAAkB,EAAE,sCAAsC;AAC1D,QAAA,wBAAwB,EAAE,4CAA4C;AACtE,QAAA,kBAAkB,EAAE,sCAAsC;AAC1D,QAAA,sBAAsB,EAAE,0CAA0C;AAClE,QAAA,sBAAsB,EAAE,0CAA0C;AAClE,QAAA,wBAAwB,EAAE,4CAA4C;KACvE;AAED;;;AAGG;IACkB,SAAS,GAAW,EAAE;AAE3C;;AAEG;IACuB,cAAc,GAAkB,IAAI;;AAGnC,IAAA,eAAe;AAE1C;;AAEG;AAC2D,IAAA,YAAY;AAE1E;;AAEG;AACqB,IAAA,YAAY;;AAGhB,IAAA,QAAQ;AAEpB,IAAA,SAAS;;AAGR,IAAA,EAAE;;AAGX,IAAA,IAAI,OAAO,GAAA;QACT,OAAO,CAAA,EAAG,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,SAAS,CAAA,MAAA,CAAQ;;;AAIP,IAAA,QAAQ;;IAGrC,aAAa,GAAuB,OAAO;;IAG3C,IAAI,GAAkB,IAAI;;AAGhB,IAAA,MAAM,GAAG,IAAI,YAAY,EAAqB;;AAG9C,IAAA,mBAAmB,GAA0B,IAAI,YAAY,EAAW;;AAGlF,IAAA,KAAK;;AAGwB,IAAA,aAAa;;AAG/B,IAAA,aAAa;;AAGb,IAAA,aAAa;;AAIjC,IAAA,QAAQ;;;AAIR;;;;;;AAMG;AACM,IAAA,KAAK;;AAId,IAAA,mBAAmB;AAEnB;;;AAGG;AACH,IAAA,UAAU,GAAc,MAAK,GAAG;IAExB,sBAAsB,GAAW,EAAE;AACnC,IAAA,kBAAkB,GAAyB,oBAAoB,CAAC,IAAI;AACpE,IAAA,6BAA6B,GAAyB,MAAK,GAAG;AAC9D,IAAA,kBAAkB,GAAG,MAAK,GAAG;AAIrC,IAAA,WAAA,GAAA;QACE,MAAM,CAAC,sBAAsB,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC;AAC5D,QAAA,MAAM,QAAQ,GAAG,MAAM,CAAC,IAAI,kBAAkB,CAAC,UAAU,CAAC,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;QAC7E,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,IAAI,QAAQ;AACzC,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,IAAI,QAAQ,CAAC,KAAK;AAClD,QAAA,IAAI,CAAC,QAAQ,GAAG,QAAQ,IAAI,IAAI,GAAG,CAAC,GAAG,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC;AAC9D,QAAA,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC,YAAY,CAAC,CAAC,KAAK,CAAC,mBAAmB,CAAC;QAC1E,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,QAAQ,EAAE,mBAAmB,IAAI,KAAK;;AAGxE,IAAA,WAAW,CAAC,OAAsB,EAAA;AAChC,QAAA,IAAI,OAAO,CAAC,UAAU,CAAC,EAAE;YACvB,IAAI,CAAC,kBAAkB,EAAE;;;IAI7B,eAAe,GAAA;AACb,QAAA,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,cAAc,CAAC;;;AAI9C,IAAA,IACI,OAAO,GAAA;QACT,OAAO,IAAI,CAAC,QAAQ;;IAEtB,IAAI,OAAO,CAAC,KAAc,EAAA;AACxB,QAAA,IAAI,KAAK,IAAI,IAAI,CAAC,OAAO,EAAE;AACzB,YAAA,IAAI,CAAC,QAAQ,GAAG,KAAK;AACrB,YAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE;;;IAGlC,QAAQ,GAAY,KAAK;;AAGjC,IAAA,IACI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,SAAS;;IAEvB,IAAI,QAAQ,CAAC,KAAc,EAAA;AACzB,QAAA,IAAI,KAAK,KAAK,IAAI,CAAC,QAAQ,EAAE;AAC3B,YAAA,IAAI,CAAC,SAAS,GAAG,KAAK;AACtB,YAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE;;;IAGlC,SAAS,GAAY,KAAK;AAElC;;;;;AAKG;AACH,IAAA,IACI,aAAa,GAAA;QACf,OAAO,IAAI,CAAC,cAAc;;IAE5B,IAAI,aAAa,CAAC,KAAc,EAAA;AAC9B,QAAA,MAAM,OAAO,GAAG,KAAK,IAAI,IAAI,CAAC,cAAc;AAC5C,QAAA,IAAI,CAAC,cAAc,GAAG,KAAK;QAE3B,IAAI,OAAO,EAAE;AACX,YAAA,IAAI,IAAI,CAAC,cAAc,EAAE;AACvB,gBAAA,IAAI,CAAC,qBAAqB,CAAC,oBAAoB,CAAC,aAAa,CAAC;;iBACzD;AACL,gBAAA,IAAI,CAAC,qBAAqB,CACxB,IAAI,CAAC,OAAO,GAAG,oBAAoB,CAAC,OAAO,GAAG,oBAAoB,CAAC,SAAS,CAC7E;;YAEH,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC;;AAGpD,QAAA,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,cAAc,CAAC;;IAEtC,cAAc,GAAY,KAAK;IAEvC,iBAAiB,GAAA;AACf,QAAA,OAAO,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,QAAQ;;;IAI5C,kBAAkB,GAAA;;;;;;AAMhB,QAAA,IAAI,CAAC,kBAAkB,CAAC,aAAa,EAAE;;;AAIzC,IAAA,UAAU,CAAC,KAAU,EAAA;AACnB,QAAA,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC,KAAK;;;AAIxB,IAAA,gBAAgB,CAAC,EAAwB,EAAA;AACvC,QAAA,IAAI,CAAC,6BAA6B,GAAG,EAAE;;;AAIzC,IAAA,iBAAiB,CAAC,EAAO,EAAA;AACvB,QAAA,IAAI,CAAC,UAAU,GAAG,EAAE;;;AAItB,IAAA,gBAAgB,CAAC,UAAmB,EAAA;AAClC,QAAA,IAAI,CAAC,QAAQ,GAAG,UAAU;;;AAI5B,IAAA,QAAQ,CAAC,OAAiC,EAAA;QACxC,OAAO,IAAI,CAAC,QAAQ,IAAI,OAAO,CAAC,KAAK,KAAK,IAAI,GAAG,EAAC,UAAU,EAAE,IAAI,EAAC,GAAG,IAAI;;;AAI5E,IAAA,yBAAyB,CAAC,EAAc,EAAA;AACtC,QAAA,IAAI,CAAC,kBAAkB,GAAG,EAAE;;AAGtB,IAAA,qBAAqB,CAAC,QAA8B,EAAA;AAC1D,QAAA,IAAI,QAAQ,GAAG,IAAI,CAAC,kBAAkB;AACtC,QAAA,IAAI,OAAO,GAAG,IAAI,CAAC,0BAA0B,EAAE;AAE/C,QAAA,IAAI,QAAQ,KAAK,QAAQ,IAAI,CAAC,OAAO,EAAE;YACrC;;AAEF,QAAA,IAAI,IAAI,CAAC,sBAAsB,EAAE;YAC/B,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,sBAAsB,CAAC;;QAGvD,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,yCAAyC,CAC1E,QAAQ,EACR,QAAQ,CACT;AACD,QAAA,IAAI,CAAC,kBAAkB,GAAG,QAAQ;QAElC,IAAI,IAAI,CAAC,sBAAsB,CAAC,MAAM,GAAG,CAAC,EAAE;YAC1C,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,sBAAsB,CAAC;;AAGlD,YAAA,MAAM,cAAc,GAAG,IAAI,CAAC,sBAAsB;AAElD,YAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;gBAClC,UAAU,CAAC,MAAK;AACd,oBAAA,OAAQ,CAAC,SAAS,CAAC,MAAM,CAAC,cAAc,CAAC;iBAC1C,EAAE,IAAI,CAAC;AACV,aAAC,CAAC;;;IAIE,gBAAgB,GAAA;AACtB,QAAA,IAAI,CAAC,6BAA6B,CAAC,IAAI,CAAC,OAAO,CAAC;AAChD,QAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;;;AAIvD,QAAA,IAAI,IAAI,CAAC,aAAa,EAAE;YACtB,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO;;;;IAK3D,MAAM,GAAA;AACJ,QAAA,IAAI,CAAC,OAAO,GAAG,CAAC,IAAI,CAAC,OAAO;AAC5B,QAAA,IAAI,CAAC,6BAA6B,CAAC,IAAI,CAAC,OAAO,CAAC;;IAGxC,iBAAiB,GAAA;AACzB,QAAA,MAAM,WAAW,GAAG,IAAI,CAAC,QAAQ,EAAE,WAAW;;QAG9C,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,WAAW,KAAK,MAAM,EAAE;;YAE5C,IAAI,IAAI,CAAC,aAAa,IAAI,WAAW,KAAK,OAAO,EAAE;AACjD,gBAAA,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,MAAK;AAC1B,oBAAA,IAAI,CAAC,cAAc,GAAG,KAAK;oBAC3B,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC;AACpD,iBAAC,CAAC;;AAGJ,YAAA,IAAI,CAAC,QAAQ,GAAG,CAAC,IAAI,CAAC,QAAQ;AAC9B,YAAA,IAAI,CAAC,qBAAqB,CACxB,IAAI,CAAC,QAAQ,GAAG,oBAAoB,CAAC,OAAO,GAAG,oBAAoB,CAAC,SAAS,CAC9E;;;;YAKD,IAAI,CAAC,gBAAgB,EAAE;;aAClB,IACL,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,mBAAmB;aACzC,CAAC,IAAI,CAAC,QAAQ,IAAI,WAAW,KAAK,MAAM,CAAC,EAC1C;;;YAGA,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO;YACvD,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa;;;AAIvE,IAAA,mBAAmB,CAAC,KAAY,EAAA;;;;QAI9B,KAAK,CAAC,eAAe,EAAE;;IAGzB,OAAO,GAAA;;;;;;AAML,QAAA,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,MAAK;YAC1B,IAAI,CAAC,UAAU,EAAE;AACjB,YAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE;AACxC,SAAC,CAAC;;IAGI,yCAAyC,CAC/C,QAA8B,EAC9B,QAA8B,EAAA;;AAG9B,QAAA,IAAI,IAAI,CAAC,cAAc,KAAK,gBAAgB,EAAE;AAC5C,YAAA,OAAO,EAAE;;QAGX,QAAQ,QAAQ;YACd,KAAK,oBAAoB,CAAC,IAAI;;;AAG5B,gBAAA,IAAI,QAAQ,KAAK,oBAAoB,CAAC,OAAO,EAAE;AAC7C,oBAAA,OAAO,IAAI,CAAC,iBAAiB,CAAC,kBAAkB;;AAC3C,qBAAA,IAAI,QAAQ,IAAI,oBAAoB,CAAC,aAAa,EAAE;oBACzD,OAAO,IAAI,CAAC;AACV,0BAAE,IAAI,CAAC,iBAAiB,CAAC;AACzB,0BAAE,IAAI,CAAC,iBAAiB,CAAC,wBAAwB;;gBAErD;YACF,KAAK,oBAAoB,CAAC,SAAS;AACjC,gBAAA,OAAO,QAAQ,KAAK,oBAAoB,CAAC;AACvC,sBAAE,IAAI,CAAC,iBAAiB,CAAC;AACzB,sBAAE,IAAI,CAAC,iBAAiB,CAAC,wBAAwB;YACrD,KAAK,oBAAoB,CAAC,OAAO;AAC/B,gBAAA,OAAO,QAAQ,KAAK,oBAAoB,CAAC;AACvC,sBAAE,IAAI,CAAC,iBAAiB,CAAC;AACzB,sBAAE,IAAI,CAAC,iBAAiB,CAAC,sBAAsB;YACnD,KAAK,oBAAoB,CAAC,aAAa;AACrC,gBAAA,OAAO,QAAQ,KAAK,oBAAoB,CAAC;AACvC,sBAAE,IAAI,CAAC,iBAAiB,CAAC;AACzB,sBAAE,IAAI,CAAC,iBAAiB,CAAC,wBAAwB;;AAGvD,QAAA,OAAO,EAAE;;AAGX;;;;;;;AAOG;AACK,IAAA,kBAAkB,CAAC,KAAc,EAAA;AACvC,QAAA,MAAM,cAAc,GAAG,IAAI,CAAC,aAAa;QAEzC,IAAI,cAAc,EAAE;AAClB,YAAA,cAAc,CAAC,aAAa,CAAC,aAAa,GAAG,KAAK;;;IAItD,aAAa,GAAA;QACX,IAAI,CAAC,iBAAiB,EAAE;;IAG1B,mBAAmB,GAAA;QACjB,IAAI,CAAC,iBAAiB,EAAE;AAExB,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;;;AAGlB,YAAA,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,KAAK,EAAE;;;AAI5C;;;;;;AAMG;AACH,IAAA,yBAAyB,CAAC,KAAiB,EAAA;AACzC,QAAA,IAAI,CAAC,CAAC,KAAK,CAAC,MAAM,IAAI,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAqB,CAAC,EAAE;YAC5F,KAAK,CAAC,eAAe,EAAE;;;uGA/ahB,WAAW,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAX,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,WAAW,mQAwDqB,gBAAgB,CAAA,EAAA,YAAA,EAAA,CAAA,eAAA,EAAA,cAAA,CAAA,EAAA,QAAA,EAAA,CAAA,WAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAqBxC,gBAAgB,CAAA,EAAA,aAAA,EAAA,eAAA,EAAA,IAAA,EAAA,MAAA,EAAA,KAAA,EAAA,OAAA,EAAA,aAAA,EAAA,CAAA,eAAA,EAAA,eAAA,EAkBhB,gBAAgB,CAShB,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAAA,CAAC,KAAc,MAAM,KAAK,IAAI,IAAI,GAAG,SAAS,GAAG,eAAe,CAAC,KAAK,CAAC,CAAC,CAexE,EAAA,KAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,CAAA,qBAAA,EAAA,qBAAA,EAAA,gBAAgB,mCAqChB,gBAAgB,CAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAahB,gBAAgB,CAAA,EAAA,aAAA,EAAA,CAAA,eAAA,EAAA,eAAA,EAkBhB,gBAAgB,CAxMxB,EAAA,EAAA,OAAA,EAAA,EAAA,MAAA,EAAA,QAAA,EAAA,mBAAA,EAAA,qBAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,eAAA,EAAA,MAAA,EAAA,iBAAA,EAAA,MAAA,EAAA,sBAAA,EAAA,MAAA,EAAA,+BAAA,EAAA,qCAAA,EAAA,8BAAA,EAAA,UAAA,EAAA,IAAA,EAAA,IAAA,EAAA,iCAAA,EAAA,UAAA,EAAA,gCAAA,EAAA,SAAA,EAAA,6CAAA,EAAA,qBAAA,EAAA,OAAA,EAAA,2CAAA,EAAA,EAAA,cAAA,EAAA,kBAAA,EAAA,EAAA,SAAA,EAAA;YACT,mCAAmC;AACnC,YAAA;AACE,gBAAA,OAAO,EAAE,aAAa;AACtB,gBAAA,WAAW,EAAE,WAAW;AACxB,gBAAA,KAAK,EAAE,IAAI;AACZ,aAAA;AACF,SAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,eAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,OAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,eAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,OAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,aAAA,CAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EC3GH,04EAqDA,EAAA,MAAA,EAAA,CAAA,y6fAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,ED0DY,SAAS,EAAA,QAAA,EAAA,2BAAA,EAAA,MAAA,EAAA,CAAA,gBAAA,EAAA,oBAAA,EAAA,mBAAA,EAAA,iBAAA,EAAA,oBAAA,EAAA,mBAAA,EAAA,kBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,WAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAE,qBAAqB,EAAA,QAAA,EAAA,8BAAA,EAAA,MAAA,EAAA,CAAA,eAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAE/B,WAAW,EAAA,UAAA,EAAA,CAAA;kBA/BvB,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,cAAc,EAGlB,IAAA,EAAA;AACJ,wBAAA,OAAO,EAAE,kBAAkB;AAC3B,wBAAA,iBAAiB,EAAE,MAAM;AACzB,wBAAA,mBAAmB,EAAE,MAAM;AAC3B,wBAAA,wBAAwB,EAAE,MAAM;AAChC,wBAAA,iCAAiC,EAAE,CAAqC,mCAAA,CAAA;AACxE,wBAAA,gCAAgC,EAAE,UAAU;AAC5C,wBAAA,MAAM,EAAE,IAAI;;AAEZ,wBAAA,mCAAmC,EAAE,UAAU;AAC/C,wBAAA,kCAAkC,EAAE,SAAS;AAC7C,wBAAA,+CAA+C,EAAE,qBAAqB;AACtE,wBAAA,SAAS,EAAE,uCAAuC;qBACnD,EACU,SAAA,EAAA;wBACT,mCAAmC;AACnC,wBAAA;AACE,4BAAA,OAAO,EAAE,aAAa;AACtB,4BAAA,WAAW,EAAa,WAAA;AACxB,4BAAA,KAAK,EAAE,IAAI;AACZ,yBAAA;AACF,qBAAA,EAAA,QAAA,EACS,aAAa,EAAA,aAAA,EACR,iBAAiB,CAAC,IAAI,EACpB,eAAA,EAAA,uBAAuB,CAAC,MAAM,EACtC,OAAA,EAAA,CAAC,SAAS,EAAE,qBAAqB,CAAC,EAAA,QAAA,EAAA,04EAAA,EAAA,MAAA,EAAA,CAAA,y6fAAA,CAAA,EAAA;wDA6CtB,SAAS,EAAA,CAAA;sBAA7B,KAAK;uBAAC,YAAY;gBAKO,cAAc,EAAA,CAAA;sBAAvC,KAAK;uBAAC,iBAAiB;gBAGG,eAAe,EAAA,CAAA;sBAAzC,KAAK;uBAAC,kBAAkB;gBAKqC,YAAY,EAAA,CAAA;sBAAzE,KAAK;AAAC,gBAAA,IAAA,EAAA,CAAA,EAAC,KAAK,EAAE,eAAe,EAAE,SAAS,EAAE,gBAAgB,EAAC;gBAKpC,YAAY,EAAA,CAAA;sBAAnC,KAAK;uBAAC,eAAe;gBAGF,QAAQ,EAAA,CAAA;sBAA3B,KAAK;uBAAC,WAAW;gBAKT,EAAE,EAAA,CAAA;sBAAV;gBAQqC,QAAQ,EAAA,CAAA;sBAA7C,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBAG3B,aAAa,EAAA,CAAA;sBAArB;gBAGQ,IAAI,EAAA,CAAA;sBAAZ;gBAGkB,MAAM,EAAA,CAAA;sBAAxB;gBAGkB,mBAAmB,EAAA,CAAA;sBAArC;gBAGQ,KAAK,EAAA,CAAA;sBAAb;gBAGqC,aAAa,EAAA,CAAA;sBAAlD,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBAGhB,aAAa,EAAA,CAAA;sBAAhC,SAAS;uBAAC,OAAO;gBAGE,aAAa,EAAA,CAAA;sBAAhC,SAAS;uBAAC,OAAO;gBAIlB,QAAQ,EAAA,CAAA;sBADP,KAAK;uBAAC,EAAC,SAAS,EAAE,CAAC,KAAc,MAAM,KAAK,IAAI,IAAI,GAAG,SAAS,GAAG,eAAe,CAAC,KAAK,CAAC,CAAC,EAAC;gBAYnF,KAAK,EAAA,CAAA;sBAAb;gBAID,mBAAmB,EAAA,CAAA;sBADlB,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBAsChC,OAAO,EAAA,CAAA;sBADV,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBAchC,QAAQ,EAAA,CAAA;sBADX,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBAmBhC,aAAa,EAAA,CAAA;sBADhB,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;;;AEjStC;;;AAGG;AACU,MAAA,+BAA+B,GAAa;AACvD,IAAA,OAAO,EAAE,aAAa;AACtB,IAAA,WAAW,EAAE,UAAU,CAAC,MAAM,4BAA4B,CAAC;AAC3D,IAAA,KAAK,EAAE,IAAI;;AAGb;;;;;;;AAOG;AAMG,MAAO,4BAA6B,SAAQ,yBAAyB,CAAA;uGAA9D,4BAA4B,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAA5B,4BAA4B,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,6HAAA,EAAA,SAAA,EAF5B,CAAC,+BAA+B,CAAC,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAEjC,4BAA4B,EAAA,UAAA,EAAA,CAAA;kBALxC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,CAAA;AACsE,iFAAA,CAAA;oBAChF,SAAS,EAAE,CAAC,+BAA+B,CAAC;AAC7C,iBAAA;;;ACpBD;;;AAGG;MAKU,mCAAmC,CAAA;uGAAnC,mCAAmC,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;wGAAnC,mCAAmC,EAAA,OAAA,EAAA,CAHpC,4BAA4B,CAAA,EAAA,OAAA,EAAA,CAC5B,4BAA4B,CAAA,EAAA,CAAA;wGAE3B,mCAAmC,EAAA,CAAA;;2FAAnC,mCAAmC,EAAA,UAAA,EAAA,CAAA;kBAJ/C,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACR,OAAO,EAAE,CAAC,4BAA4B,CAAC;oBACvC,OAAO,EAAE,CAAC,4BAA4B,CAAC;AACxC,iBAAA;;MAOY,iBAAiB,CAAA;uGAAjB,iBAAiB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;AAAjB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,iBAAiB,YAHlB,WAAW,EAAE,eAAe,CAC5B,EAAA,OAAA,EAAA,CAAA,WAAW,EAAE,eAAe,CAAA,EAAA,CAAA;AAE3B,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,iBAAiB,EAHlB,OAAA,EAAA,CAAA,WAAW,EAAE,eAAe,EACf,eAAe,CAAA,EAAA,CAAA;;2FAE3B,iBAAiB,EAAA,UAAA,EAAA,CAAA;kBAJ7B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,OAAO,EAAE,CAAC,WAAW,EAAE,eAAe,CAAC;AACvC,oBAAA,OAAO,EAAE,CAAC,WAAW,EAAE,eAAe,CAAC;AACxC,iBAAA;;;;;"}