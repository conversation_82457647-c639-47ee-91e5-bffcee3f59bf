{"version": 3, "file": "button-toggle.mjs", "sources": ["../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/button-toggle/button-toggle.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/button-toggle/button-toggle.html", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/button-toggle/button-toggle-module.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {_IdGenerator, FocusMonitor} from '@angular/cdk/a11y';\nimport {Direction, Directionality} from '@angular/cdk/bidi';\nimport {SelectionModel} from '@angular/cdk/collections';\nimport {DOWN_ARROW, ENTER, LEFT_ARROW, RIGHT_ARROW, SPACE, UP_ARROW} from '@angular/cdk/keycodes';\nimport {_CdkPrivateStyleLoader} from '@angular/cdk/private';\nimport {\n  AfterContentInit,\n  AfterViewInit,\n  ANIMATION_MODULE_TYPE,\n  booleanAttribute,\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  ContentChildren,\n  Directive,\n  ElementRef,\n  EventEmitter,\n  forwardRef,\n  HostAttributeToken,\n  inject,\n  InjectionToken,\n  Input,\n  OnDestroy,\n  OnInit,\n  Output,\n  QueryList,\n  ViewChild,\n  ViewEncapsulation,\n} from '@angular/core';\nimport {ControlValueAccessor, NG_VALUE_ACCESSOR} from '@angular/forms';\nimport {_StructuralStylesLoader, MatPseudoCheckbox, MatRipple} from '../core';\n\n/**\n * @deprecated No longer used.\n * @breaking-change 11.0.0\n */\nexport type ToggleType = 'checkbox' | 'radio';\n\n/** Possible appearance styles for the button toggle. */\nexport type MatButtonToggleAppearance = 'legacy' | 'standard';\n\n/**\n * Represents the default options for the button toggle that can be configured\n * using the `MAT_BUTTON_TOGGLE_DEFAULT_OPTIONS` injection token.\n */\nexport interface MatButtonToggleDefaultOptions {\n  /**\n   * Default appearance to be used by button toggles. Can be overridden by explicitly\n   * setting an appearance on a button toggle or group.\n   */\n  appearance?: MatButtonToggleAppearance;\n  /** Whether icon indicators should be hidden for single-selection button toggle groups. */\n  hideSingleSelectionIndicator?: boolean;\n  /** Whether icon indicators should be hidden for multiple-selection button toggle groups. */\n  hideMultipleSelectionIndicator?: boolean;\n  /** Whether disabled toggle buttons should be interactive. */\n  disabledInteractive?: boolean;\n}\n\n/**\n * Injection token that can be used to configure the\n * default options for all button toggles within an app.\n */\nexport const MAT_BUTTON_TOGGLE_DEFAULT_OPTIONS = new InjectionToken<MatButtonToggleDefaultOptions>(\n  'MAT_BUTTON_TOGGLE_DEFAULT_OPTIONS',\n  {\n    providedIn: 'root',\n    factory: MAT_BUTTON_TOGGLE_GROUP_DEFAULT_OPTIONS_FACTORY,\n  },\n);\n\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nexport function MAT_BUTTON_TOGGLE_GROUP_DEFAULT_OPTIONS_FACTORY(): MatButtonToggleDefaultOptions {\n  return {\n    hideSingleSelectionIndicator: false,\n    hideMultipleSelectionIndicator: false,\n    disabledInteractive: false,\n  };\n}\n\n/**\n * Injection token that can be used to reference instances of `MatButtonToggleGroup`.\n * It serves as alternative token to the actual `MatButtonToggleGroup` class which\n * could cause unnecessary retention of the class and its component metadata.\n */\nexport const MAT_BUTTON_TOGGLE_GROUP = new InjectionToken<MatButtonToggleGroup>(\n  'MatButtonToggleGroup',\n);\n\n/**\n * Provider Expression that allows mat-button-toggle-group to register as a ControlValueAccessor.\n * This allows it to support [(ngModel)].\n * @docs-private\n */\nexport const MAT_BUTTON_TOGGLE_GROUP_VALUE_ACCESSOR: any = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => MatButtonToggleGroup),\n  multi: true,\n};\n\n/** Change event object emitted by button toggle. */\nexport class MatButtonToggleChange {\n  constructor(\n    /** The button toggle that emits the event. */\n    public source: MatButtonToggle,\n\n    /** The value assigned to the button toggle. */\n    public value: any,\n  ) {}\n}\n\n/** Exclusive selection button toggle group that behaves like a radio-button group. */\n@Directive({\n  selector: 'mat-button-toggle-group',\n  providers: [\n    MAT_BUTTON_TOGGLE_GROUP_VALUE_ACCESSOR,\n    {provide: MAT_BUTTON_TOGGLE_GROUP, useExisting: MatButtonToggleGroup},\n  ],\n  host: {\n    'class': 'mat-button-toggle-group',\n    '(keydown)': '_keydown($event)',\n    '[attr.role]': \"multiple ? 'group' : 'radiogroup'\",\n    '[attr.aria-disabled]': 'disabled',\n    '[class.mat-button-toggle-vertical]': 'vertical',\n    '[class.mat-button-toggle-group-appearance-standard]': 'appearance === \"standard\"',\n  },\n  exportAs: 'matButtonToggleGroup',\n})\nexport class MatButtonToggleGroup implements ControlValueAccessor, OnInit, AfterContentInit {\n  private _changeDetector = inject(ChangeDetectorRef);\n  private _dir = inject(Directionality, {optional: true});\n\n  private _multiple = false;\n  private _disabled = false;\n  private _disabledInteractive = false;\n  private _selectionModel: SelectionModel<MatButtonToggle>;\n\n  /**\n   * Reference to the raw value that the consumer tried to assign. The real\n   * value will exclude any values from this one that don't correspond to a\n   * toggle. Useful for the cases where the value is assigned before the toggles\n   * have been initialized or at the same that they're being swapped out.\n   */\n  private _rawValue: any;\n\n  /**\n   * The method to be called in order to update ngModel.\n   * Now `ngModel` binding is not supported in multiple selection mode.\n   */\n  _controlValueAccessorChangeFn: (value: any) => void = () => {};\n\n  /** onTouch function registered via registerOnTouch (ControlValueAccessor). */\n  _onTouched: () => any = () => {};\n\n  /** Child button toggle buttons. */\n  @ContentChildren(forwardRef(() => MatButtonToggle), {\n    // Note that this would technically pick up toggles\n    // from nested groups, but that's not a case that we support.\n    descendants: true,\n  })\n  _buttonToggles: QueryList<MatButtonToggle>;\n\n  /** The appearance for all the buttons in the group. */\n  @Input() appearance: MatButtonToggleAppearance;\n\n  /** `name` attribute for the underlying `input` element. */\n  @Input()\n  get name(): string {\n    return this._name;\n  }\n  set name(value: string) {\n    this._name = value;\n    this._markButtonsForCheck();\n  }\n  private _name = inject(_IdGenerator).getId('mat-button-toggle-group-');\n\n  /** Whether the toggle group is vertical. */\n  @Input({transform: booleanAttribute}) vertical: boolean;\n\n  /** Value of the toggle group. */\n  @Input()\n  get value(): any {\n    const selected = this._selectionModel ? this._selectionModel.selected : [];\n\n    if (this.multiple) {\n      return selected.map(toggle => toggle.value);\n    }\n\n    return selected[0] ? selected[0].value : undefined;\n  }\n  set value(newValue: any) {\n    this._setSelectionByValue(newValue);\n    this.valueChange.emit(this.value);\n  }\n\n  /**\n   * Event that emits whenever the value of the group changes.\n   * Used to facilitate two-way data binding.\n   * @docs-private\n   */\n  @Output() readonly valueChange = new EventEmitter<any>();\n\n  /** Selected button toggles in the group. */\n  get selected(): MatButtonToggle | MatButtonToggle[] {\n    const selected = this._selectionModel ? this._selectionModel.selected : [];\n    return this.multiple ? selected : selected[0] || null;\n  }\n\n  /** Whether multiple button toggles can be selected. */\n  @Input({transform: booleanAttribute})\n  get multiple(): boolean {\n    return this._multiple;\n  }\n  set multiple(value: boolean) {\n    this._multiple = value;\n    this._markButtonsForCheck();\n  }\n\n  /** Whether multiple button toggle group is disabled. */\n  @Input({transform: booleanAttribute})\n  get disabled(): boolean {\n    return this._disabled;\n  }\n  set disabled(value: boolean) {\n    this._disabled = value;\n    this._markButtonsForCheck();\n  }\n\n  /** Whether buttons in the group should be interactive while they're disabled. */\n  @Input({transform: booleanAttribute})\n  get disabledInteractive(): boolean {\n    return this._disabledInteractive;\n  }\n  set disabledInteractive(value: boolean) {\n    this._disabledInteractive = value;\n    this._markButtonsForCheck();\n  }\n\n  /** The layout direction of the toggle button group. */\n  get dir(): Direction {\n    return this._dir && this._dir.value === 'rtl' ? 'rtl' : 'ltr';\n  }\n\n  /** Event emitted when the group's value changes. */\n  @Output() readonly change: EventEmitter<MatButtonToggleChange> =\n    new EventEmitter<MatButtonToggleChange>();\n\n  /** Whether checkmark indicator for single-selection button toggle groups is hidden. */\n  @Input({transform: booleanAttribute})\n  get hideSingleSelectionIndicator(): boolean {\n    return this._hideSingleSelectionIndicator;\n  }\n  set hideSingleSelectionIndicator(value: boolean) {\n    this._hideSingleSelectionIndicator = value;\n    this._markButtonsForCheck();\n  }\n  private _hideSingleSelectionIndicator: boolean;\n\n  /** Whether checkmark indicator for multiple-selection button toggle groups is hidden. */\n  @Input({transform: booleanAttribute})\n  get hideMultipleSelectionIndicator(): boolean {\n    return this._hideMultipleSelectionIndicator;\n  }\n  set hideMultipleSelectionIndicator(value: boolean) {\n    this._hideMultipleSelectionIndicator = value;\n    this._markButtonsForCheck();\n  }\n  private _hideMultipleSelectionIndicator: boolean;\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    const defaultOptions = inject<MatButtonToggleDefaultOptions>(\n      MAT_BUTTON_TOGGLE_DEFAULT_OPTIONS,\n      {optional: true},\n    );\n\n    this.appearance =\n      defaultOptions && defaultOptions.appearance ? defaultOptions.appearance : 'standard';\n    this.hideSingleSelectionIndicator = defaultOptions?.hideSingleSelectionIndicator ?? false;\n    this.hideMultipleSelectionIndicator = defaultOptions?.hideMultipleSelectionIndicator ?? false;\n  }\n\n  ngOnInit() {\n    this._selectionModel = new SelectionModel<MatButtonToggle>(this.multiple, undefined, false);\n  }\n\n  ngAfterContentInit() {\n    this._selectionModel.select(...this._buttonToggles.filter(toggle => toggle.checked));\n    if (!this.multiple) {\n      this._initializeTabIndex();\n    }\n  }\n\n  /**\n   * Sets the model value. Implemented as part of ControlValueAccessor.\n   * @param value Value to be set to the model.\n   */\n  writeValue(value: any) {\n    this.value = value;\n    this._changeDetector.markForCheck();\n  }\n\n  // Implemented as part of ControlValueAccessor.\n  registerOnChange(fn: (value: any) => void) {\n    this._controlValueAccessorChangeFn = fn;\n  }\n\n  // Implemented as part of ControlValueAccessor.\n  registerOnTouched(fn: any) {\n    this._onTouched = fn;\n  }\n\n  // Implemented as part of ControlValueAccessor.\n  setDisabledState(isDisabled: boolean): void {\n    this.disabled = isDisabled;\n  }\n\n  /** Handle keydown event calling to single-select button toggle. */\n  protected _keydown(event: KeyboardEvent) {\n    if (this.multiple || this.disabled) {\n      return;\n    }\n\n    const target = event.target as HTMLButtonElement;\n    const buttonId = target.id;\n    const index = this._buttonToggles.toArray().findIndex(toggle => {\n      return toggle.buttonId === buttonId;\n    });\n\n    let nextButton: MatButtonToggle | null = null;\n    switch (event.keyCode) {\n      case SPACE:\n      case ENTER:\n        nextButton = this._buttonToggles.get(index) || null;\n        break;\n      case UP_ARROW:\n        nextButton = this._getNextButton(index, -1);\n        break;\n      case LEFT_ARROW:\n        nextButton = this._getNextButton(index, this.dir === 'ltr' ? -1 : 1);\n        break;\n      case DOWN_ARROW:\n        nextButton = this._getNextButton(index, 1);\n        break;\n      case RIGHT_ARROW:\n        nextButton = this._getNextButton(index, this.dir === 'ltr' ? 1 : -1);\n        break;\n      default:\n        return;\n    }\n\n    if (nextButton) {\n      event.preventDefault();\n      nextButton._onButtonClick();\n      nextButton.focus();\n    }\n  }\n\n  /** Dispatch change event with current selection and group value. */\n  _emitChangeEvent(toggle: MatButtonToggle): void {\n    const event = new MatButtonToggleChange(toggle, this.value);\n    this._rawValue = event.value;\n    this._controlValueAccessorChangeFn(event.value);\n    this.change.emit(event);\n  }\n\n  /**\n   * Syncs a button toggle's selected state with the model value.\n   * @param toggle Toggle to be synced.\n   * @param select Whether the toggle should be selected.\n   * @param isUserInput Whether the change was a result of a user interaction.\n   * @param deferEvents Whether to defer emitting the change events.\n   */\n  _syncButtonToggle(\n    toggle: MatButtonToggle,\n    select: boolean,\n    isUserInput = false,\n    deferEvents = false,\n  ) {\n    // Deselect the currently-selected toggle, if we're in single-selection\n    // mode and the button being toggled isn't selected at the moment.\n    if (!this.multiple && this.selected && !toggle.checked) {\n      (this.selected as MatButtonToggle).checked = false;\n    }\n\n    if (this._selectionModel) {\n      if (select) {\n        this._selectionModel.select(toggle);\n      } else {\n        this._selectionModel.deselect(toggle);\n      }\n    } else {\n      deferEvents = true;\n    }\n\n    // We need to defer in some cases in order to avoid \"changed after checked errors\", however\n    // the side-effect is that we may end up updating the model value out of sequence in others\n    // The `deferEvents` flag allows us to decide whether to do it on a case-by-case basis.\n    if (deferEvents) {\n      Promise.resolve().then(() => this._updateModelValue(toggle, isUserInput));\n    } else {\n      this._updateModelValue(toggle, isUserInput);\n    }\n  }\n\n  /** Checks whether a button toggle is selected. */\n  _isSelected(toggle: MatButtonToggle) {\n    return this._selectionModel && this._selectionModel.isSelected(toggle);\n  }\n\n  /** Determines whether a button toggle should be checked on init. */\n  _isPrechecked(toggle: MatButtonToggle) {\n    if (typeof this._rawValue === 'undefined') {\n      return false;\n    }\n\n    if (this.multiple && Array.isArray(this._rawValue)) {\n      return this._rawValue.some(value => toggle.value != null && value === toggle.value);\n    }\n\n    return toggle.value === this._rawValue;\n  }\n\n  /** Initializes the tabindex attribute using the radio pattern. */\n  private _initializeTabIndex() {\n    this._buttonToggles.forEach(toggle => {\n      toggle.tabIndex = -1;\n    });\n    if (this.selected) {\n      (this.selected as MatButtonToggle).tabIndex = 0;\n    } else {\n      for (let i = 0; i < this._buttonToggles.length; i++) {\n        const toggle = this._buttonToggles.get(i)!;\n\n        if (!toggle.disabled) {\n          toggle.tabIndex = 0;\n          break;\n        }\n      }\n    }\n    this._markButtonsForCheck();\n  }\n\n  /** Obtain the subsequent toggle to which the focus shifts. */\n  private _getNextButton(startIndex: number, offset: number): MatButtonToggle | null {\n    const items = this._buttonToggles;\n\n    for (let i = 1; i <= items.length; i++) {\n      const index = (startIndex + offset * i + items.length) % items.length;\n      const item = items.get(index);\n\n      if (item && !item.disabled) {\n        return item;\n      }\n    }\n\n    return null;\n  }\n\n  /** Updates the selection state of the toggles in the group based on a value. */\n  private _setSelectionByValue(value: any | any[]) {\n    this._rawValue = value;\n\n    if (!this._buttonToggles) {\n      return;\n    }\n\n    const toggles = this._buttonToggles.toArray();\n\n    if (this.multiple && value) {\n      if (!Array.isArray(value) && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw Error('Value must be an array in multiple-selection mode.');\n      }\n\n      this._clearSelection();\n      value.forEach((currentValue: any) => this._selectValue(currentValue, toggles));\n    } else {\n      this._clearSelection();\n      this._selectValue(value, toggles);\n    }\n\n    // In single selection mode we need at least one enabled toggle to always be focusable.\n    if (!this.multiple && toggles.every(toggle => toggle.tabIndex === -1)) {\n      for (const toggle of toggles) {\n        if (!toggle.disabled) {\n          toggle.tabIndex = 0;\n          break;\n        }\n      }\n    }\n  }\n\n  /** Clears the selected toggles. */\n  private _clearSelection() {\n    this._selectionModel.clear();\n    this._buttonToggles.forEach(toggle => {\n      toggle.checked = false;\n      // If the button toggle is in single select mode, initialize the tabIndex.\n      if (!this.multiple) {\n        toggle.tabIndex = -1;\n      }\n    });\n  }\n\n  /** Selects a value if there's a toggle that corresponds to it. */\n  private _selectValue(value: any, toggles: MatButtonToggle[]) {\n    for (const toggle of toggles) {\n      if (toggle.value === value) {\n        toggle.checked = true;\n        this._selectionModel.select(toggle);\n        if (!this.multiple) {\n          // If the button toggle is in single select mode, reset the tabIndex.\n          toggle.tabIndex = 0;\n        }\n        break;\n      }\n    }\n  }\n\n  /** Syncs up the group's value with the model and emits the change event. */\n  private _updateModelValue(toggle: MatButtonToggle, isUserInput: boolean) {\n    // Only emit the change event for user input.\n    if (isUserInput) {\n      this._emitChangeEvent(toggle);\n    }\n\n    // Note: we emit this one no matter whether it was a user interaction, because\n    // it is used by Angular to sync up the two-way data binding.\n    this.valueChange.emit(this.value);\n  }\n\n  /** Marks all of the child button toggles to be checked. */\n  private _markButtonsForCheck() {\n    this._buttonToggles?.forEach(toggle => toggle._markForCheck());\n  }\n}\n\n/** Single button inside of a toggle group. */\n@Component({\n  selector: 'mat-button-toggle',\n  templateUrl: 'button-toggle.html',\n  styleUrl: 'button-toggle.css',\n  encapsulation: ViewEncapsulation.None,\n  exportAs: 'matButtonToggle',\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  host: {\n    '[class.mat-button-toggle-standalone]': '!buttonToggleGroup',\n    '[class.mat-button-toggle-checked]': 'checked',\n    '[class.mat-button-toggle-disabled]': 'disabled',\n    '[class.mat-button-toggle-disabled-interactive]': 'disabledInteractive',\n    '[class.mat-button-toggle-appearance-standard]': 'appearance === \"standard\"',\n    'class': 'mat-button-toggle',\n    '[attr.aria-label]': 'null',\n    '[attr.aria-labelledby]': 'null',\n    '[attr.id]': 'id',\n    '[attr.name]': 'null',\n    '(focus)': 'focus()',\n    'role': 'presentation',\n  },\n  imports: [MatRipple, MatPseudoCheckbox],\n})\nexport class MatButtonToggle implements OnInit, AfterViewInit, OnDestroy {\n  private _changeDetectorRef = inject(ChangeDetectorRef);\n  private _elementRef = inject<ElementRef<HTMLElement>>(ElementRef);\n  private _focusMonitor = inject(FocusMonitor);\n  private _idGenerator = inject(_IdGenerator);\n  private _animationMode = inject(ANIMATION_MODULE_TYPE, {optional: true});\n  private _checked = false;\n\n  /**\n   * Attached to the aria-label attribute of the host element. In most cases, aria-labelledby will\n   * take precedence so this may be omitted.\n   */\n  @Input('aria-label') ariaLabel: string;\n\n  /**\n   * Users can specify the `aria-labelledby` attribute which will be forwarded to the input element\n   */\n  @Input('aria-labelledby') ariaLabelledby: string | null = null;\n\n  /** Underlying native `button` element. */\n  @ViewChild('button') _buttonElement: ElementRef<HTMLButtonElement>;\n\n  /** The parent button toggle group (exclusive selection). Optional. */\n  buttonToggleGroup: MatButtonToggleGroup;\n\n  /** Unique ID for the underlying `button` element. */\n  get buttonId(): string {\n    return `${this.id}-button`;\n  }\n\n  /** The unique ID for this button toggle. */\n  @Input() id: string;\n\n  /** HTML's 'name' attribute used to group radios for unique selection. */\n  @Input() name: string;\n\n  /** MatButtonToggleGroup reads this to assign its own value. */\n  @Input() value: any;\n\n  /** Tabindex of the toggle. */\n  @Input()\n  get tabIndex(): number | null {\n    return this._tabIndex;\n  }\n  set tabIndex(value: number | null) {\n    if (value !== this._tabIndex) {\n      this._tabIndex = value;\n      this._markForCheck();\n    }\n  }\n  private _tabIndex: number | null;\n\n  /** Whether ripples are disabled on the button toggle. */\n  @Input({transform: booleanAttribute}) disableRipple: boolean;\n\n  /** The appearance style of the button. */\n  @Input()\n  get appearance(): MatButtonToggleAppearance {\n    return this.buttonToggleGroup ? this.buttonToggleGroup.appearance : this._appearance;\n  }\n  set appearance(value: MatButtonToggleAppearance) {\n    this._appearance = value;\n  }\n  private _appearance: MatButtonToggleAppearance;\n\n  /** Whether the button is checked. */\n  @Input({transform: booleanAttribute})\n  get checked(): boolean {\n    return this.buttonToggleGroup ? this.buttonToggleGroup._isSelected(this) : this._checked;\n  }\n  set checked(value: boolean) {\n    if (value !== this._checked) {\n      this._checked = value;\n\n      if (this.buttonToggleGroup) {\n        this.buttonToggleGroup._syncButtonToggle(this, this._checked);\n      }\n\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n\n  /** Whether the button is disabled. */\n  @Input({transform: booleanAttribute})\n  get disabled(): boolean {\n    return this._disabled || (this.buttonToggleGroup && this.buttonToggleGroup.disabled);\n  }\n  set disabled(value: boolean) {\n    this._disabled = value;\n  }\n  private _disabled: boolean = false;\n\n  /** Whether the button should remain interactive when it is disabled. */\n  @Input({transform: booleanAttribute})\n  get disabledInteractive(): boolean {\n    return (\n      this._disabledInteractive ||\n      (this.buttonToggleGroup !== null && this.buttonToggleGroup.disabledInteractive)\n    );\n  }\n  set disabledInteractive(value: boolean) {\n    this._disabledInteractive = value;\n  }\n  private _disabledInteractive: boolean;\n\n  /** Event emitted when the group value changes. */\n  @Output() readonly change: EventEmitter<MatButtonToggleChange> =\n    new EventEmitter<MatButtonToggleChange>();\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    inject(_CdkPrivateStyleLoader).load(_StructuralStylesLoader);\n    const toggleGroup = inject<MatButtonToggleGroup>(MAT_BUTTON_TOGGLE_GROUP, {optional: true})!;\n    const defaultTabIndex = inject(new HostAttributeToken('tabindex'), {optional: true}) || '';\n    const defaultOptions = inject<MatButtonToggleDefaultOptions>(\n      MAT_BUTTON_TOGGLE_DEFAULT_OPTIONS,\n      {optional: true},\n    );\n\n    this._tabIndex = parseInt(defaultTabIndex) || 0;\n    this.buttonToggleGroup = toggleGroup;\n    this.appearance =\n      defaultOptions && defaultOptions.appearance ? defaultOptions.appearance : 'standard';\n    this.disabledInteractive = defaultOptions?.disabledInteractive ?? false;\n  }\n\n  ngOnInit() {\n    const group = this.buttonToggleGroup;\n    this.id = this.id || this._idGenerator.getId('mat-button-toggle-');\n\n    if (group) {\n      if (group._isPrechecked(this)) {\n        this.checked = true;\n      } else if (group._isSelected(this) !== this._checked) {\n        // As side effect of the circular dependency between the toggle group and the button,\n        // we may end up in a state where the button is supposed to be checked on init, but it\n        // isn't, because the checked value was assigned too early. This can happen when Ivy\n        // assigns the static input value before the `ngOnInit` has run.\n        group._syncButtonToggle(this, this._checked);\n      }\n    }\n  }\n\n  ngAfterViewInit() {\n    // This serves two purposes:\n    // 1. We don't want the animation to fire on the first render for pre-checked toggles so we\n    //    delay adding the class until the view is rendered.\n    // 2. We don't want animation if the `NoopAnimationsModule` is provided.\n    if (this._animationMode !== 'NoopAnimations') {\n      this._elementRef.nativeElement.classList.add('mat-button-toggle-animations-enabled');\n    }\n\n    this._focusMonitor.monitor(this._elementRef, true);\n  }\n\n  ngOnDestroy() {\n    const group = this.buttonToggleGroup;\n\n    this._focusMonitor.stopMonitoring(this._elementRef);\n\n    // Remove the toggle from the selection once it's destroyed. Needs to happen\n    // on the next tick in order to avoid \"changed after checked\" errors.\n    if (group && group._isSelected(this)) {\n      group._syncButtonToggle(this, false, false, true);\n    }\n  }\n\n  /** Focuses the button. */\n  focus(options?: FocusOptions): void {\n    this._buttonElement.nativeElement.focus(options);\n  }\n\n  /** Checks the button toggle due to an interaction with the underlying native button. */\n  _onButtonClick() {\n    if (this.disabled) {\n      return;\n    }\n\n    const newChecked = this.isSingleSelector() ? true : !this._checked;\n\n    if (newChecked !== this._checked) {\n      this._checked = newChecked;\n      if (this.buttonToggleGroup) {\n        this.buttonToggleGroup._syncButtonToggle(this, this._checked, true);\n        this.buttonToggleGroup._onTouched();\n      }\n    }\n\n    if (this.isSingleSelector()) {\n      const focusable = this.buttonToggleGroup._buttonToggles.find(toggle => {\n        return toggle.tabIndex === 0;\n      });\n      // Modify the tabindex attribute of the last focusable button toggle to -1.\n      if (focusable) {\n        focusable.tabIndex = -1;\n      }\n      // Modify the tabindex attribute of the presently selected button toggle to 0.\n      this.tabIndex = 0;\n    }\n\n    // Emit a change event when it's the single selector\n    this.change.emit(new MatButtonToggleChange(this, this.value));\n  }\n\n  /**\n   * Marks the button toggle as needing checking for change detection.\n   * This method is exposed because the parent button toggle group will directly\n   * update bound properties of the radio button.\n   */\n  _markForCheck() {\n    // When the group value changes, the button will not be notified.\n    // Use `markForCheck` to explicit update button toggle's status.\n    this._changeDetectorRef.markForCheck();\n  }\n\n  /** Gets the name that should be assigned to the inner DOM node. */\n  _getButtonName(): string | null {\n    if (this.isSingleSelector()) {\n      return this.buttonToggleGroup.name;\n    }\n    return this.name || null;\n  }\n\n  /** Whether the toggle is in single selection mode. */\n  isSingleSelector(): boolean {\n    return this.buttonToggleGroup && !this.buttonToggleGroup.multiple;\n  }\n}\n", "<button #button class=\"mat-button-toggle-button mat-focus-indicator\"\n        type=\"button\"\n        [id]=\"buttonId\"\n        [attr.role]=\"isSingleSelector() ? 'radio' : 'button'\"\n        [attr.tabindex]=\"disabled && !disabledInteractive ? -1 : tabIndex\"\n        [attr.aria-pressed]=\"!isSingleSelector() ? checked : null\"\n        [attr.aria-checked]=\"isSingleSelector() ? checked : null\"\n        [disabled]=\"(disabled && !disabledInteractive) || null\"\n        [attr.name]=\"_getButtonName()\"\n        [attr.aria-label]=\"ariaLabel\"\n        [attr.aria-labelledby]=\"ariaLabelledby\"\n        [attr.aria-disabled]=\"disabled && disabledInteractive ? 'true' : null\"\n        (click)=\"_onButtonClick()\">\n  @if (buttonToggleGroup && (\n    !buttonToggleGroup.multiple && !buttonToggleGroup.hideSingleSelectionIndicator ||\n    buttonToggleGroup.multiple && !buttonToggleGroup.hideMultipleSelectionIndicator)\n  ) {\n    <div class=\"mat-button-toggle-checkbox-wrapper\">\n      <mat-pseudo-checkbox\n        [disabled]=\"disabled\"\n        state=\"checked\"\n        aria-hidden=\"true\"\n        appearance=\"minimal\"/>\n    </div>\n  }\n\n  <span class=\"mat-button-toggle-label-content\">\n    <ng-content></ng-content>\n  </span>\n</button>\n\n<span class=\"mat-button-toggle-focus-overlay\"></span>\n<span class=\"mat-button-toggle-ripple\" matRipple\n     [matRippleTrigger]=\"button\"\n     [matRippleDisabled]=\"this.disableRipple || this.disabled\">\n</span>\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {NgModule} from '@angular/core';\nimport {MatCommonModule, MatRippleModule} from '../core';\nimport {MatButtonToggle, MatButtonToggleGroup} from './button-toggle';\n\n@NgModule({\n  imports: [MatCommonModule, MatRippleModule, MatButtonToggleGroup, MatButtonToggle],\n  exports: [MatCommonModule, MatButtonToggleGroup, MatButtonToggle],\n})\nexport class MatButtonToggleModule {}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAmEA;;;AAGG;MACU,iCAAiC,GAAG,IAAI,cAAc,CACjE,mCAAmC,EACnC;AACE,IAAA,UAAU,EAAE,MAAM;AAClB,IAAA,OAAO,EAAE,+CAA+C;AACzD,CAAA;AAGH;;;;AAIG;SACa,+CAA+C,GAAA;IAC7D,OAAO;AACL,QAAA,4BAA4B,EAAE,KAAK;AACnC,QAAA,8BAA8B,EAAE,KAAK;AACrC,QAAA,mBAAmB,EAAE,KAAK;KAC3B;AACH;AAEA;;;;AAIG;MACU,uBAAuB,GAAG,IAAI,cAAc,CACvD,sBAAsB;AAGxB;;;;AAIG;AACU,MAAA,sCAAsC,GAAQ;AACzD,IAAA,OAAO,EAAE,iBAAiB;AAC1B,IAAA,WAAW,EAAE,UAAU,CAAC,MAAM,oBAAoB,CAAC;AACnD,IAAA,KAAK,EAAE,IAAI;;AAGb;MACa,qBAAqB,CAAA;AAGvB,IAAA,MAAA;AAGA,IAAA,KAAA;AALT,IAAA,WAAA;;IAES,MAAuB;;IAGvB,KAAU,EAAA;QAHV,IAAM,CAAA,MAAA,GAAN,MAAM;QAGN,IAAK,CAAA,KAAA,GAAL,KAAK;;AAEf;AAED;MAiBa,oBAAoB,CAAA;AACvB,IAAA,eAAe,GAAG,MAAM,CAAC,iBAAiB,CAAC;IAC3C,IAAI,GAAG,MAAM,CAAC,cAAc,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;IAE/C,SAAS,GAAG,KAAK;IACjB,SAAS,GAAG,KAAK;IACjB,oBAAoB,GAAG,KAAK;AAC5B,IAAA,eAAe;AAEvB;;;;;AAKG;AACK,IAAA,SAAS;AAEjB;;;AAGG;AACH,IAAA,6BAA6B,GAAyB,MAAK,GAAG;;AAG9D,IAAA,UAAU,GAAc,MAAK,GAAG;;AAQhC,IAAA,cAAc;;AAGL,IAAA,UAAU;;AAGnB,IAAA,IACI,IAAI,GAAA;QACN,OAAO,IAAI,CAAC,KAAK;;IAEnB,IAAI,IAAI,CAAC,KAAa,EAAA;AACpB,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK;QAClB,IAAI,CAAC,oBAAoB,EAAE;;IAErB,KAAK,GAAG,MAAM,CAAC,YAAY,CAAC,CAAC,KAAK,CAAC,0BAA0B,CAAC;;AAGhC,IAAA,QAAQ;;AAG9C,IAAA,IACI,KAAK,GAAA;AACP,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC,QAAQ,GAAG,EAAE;AAE1E,QAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;AACjB,YAAA,OAAO,QAAQ,CAAC,GAAG,CAAC,MAAM,IAAI,MAAM,CAAC,KAAK,CAAC;;AAG7C,QAAA,OAAO,QAAQ,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,SAAS;;IAEpD,IAAI,KAAK,CAAC,QAAa,EAAA;AACrB,QAAA,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC;QACnC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC;;AAGnC;;;;AAIG;AACgB,IAAA,WAAW,GAAG,IAAI,YAAY,EAAO;;AAGxD,IAAA,IAAI,QAAQ,GAAA;AACV,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC,QAAQ,GAAG,EAAE;AAC1E,QAAA,OAAO,IAAI,CAAC,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAC,CAAC,CAAC,IAAI,IAAI;;;AAIvD,IAAA,IACI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,SAAS;;IAEvB,IAAI,QAAQ,CAAC,KAAc,EAAA;AACzB,QAAA,IAAI,CAAC,SAAS,GAAG,KAAK;QACtB,IAAI,CAAC,oBAAoB,EAAE;;;AAI7B,IAAA,IACI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,SAAS;;IAEvB,IAAI,QAAQ,CAAC,KAAc,EAAA;AACzB,QAAA,IAAI,CAAC,SAAS,GAAG,KAAK;QACtB,IAAI,CAAC,oBAAoB,EAAE;;;AAI7B,IAAA,IACI,mBAAmB,GAAA;QACrB,OAAO,IAAI,CAAC,oBAAoB;;IAElC,IAAI,mBAAmB,CAAC,KAAc,EAAA;AACpC,QAAA,IAAI,CAAC,oBAAoB,GAAG,KAAK;QACjC,IAAI,CAAC,oBAAoB,EAAE;;;AAI7B,IAAA,IAAI,GAAG,GAAA;AACL,QAAA,OAAO,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,KAAK,GAAG,KAAK,GAAG,KAAK;;;AAI5C,IAAA,MAAM,GACvB,IAAI,YAAY,EAAyB;;AAG3C,IAAA,IACI,4BAA4B,GAAA;QAC9B,OAAO,IAAI,CAAC,6BAA6B;;IAE3C,IAAI,4BAA4B,CAAC,KAAc,EAAA;AAC7C,QAAA,IAAI,CAAC,6BAA6B,GAAG,KAAK;QAC1C,IAAI,CAAC,oBAAoB,EAAE;;AAErB,IAAA,6BAA6B;;AAGrC,IAAA,IACI,8BAA8B,GAAA;QAChC,OAAO,IAAI,CAAC,+BAA+B;;IAE7C,IAAI,8BAA8B,CAAC,KAAc,EAAA;AAC/C,QAAA,IAAI,CAAC,+BAA+B,GAAG,KAAK;QAC5C,IAAI,CAAC,oBAAoB,EAAE;;AAErB,IAAA,+BAA+B;AAIvC,IAAA,WAAA,GAAA;AACE,QAAA,MAAM,cAAc,GAAG,MAAM,CAC3B,iCAAiC,EACjC,EAAC,QAAQ,EAAE,IAAI,EAAC,CACjB;AAED,QAAA,IAAI,CAAC,UAAU;AACb,YAAA,cAAc,IAAI,cAAc,CAAC,UAAU,GAAG,cAAc,CAAC,UAAU,GAAG,UAAU;QACtF,IAAI,CAAC,4BAA4B,GAAG,cAAc,EAAE,4BAA4B,IAAI,KAAK;QACzF,IAAI,CAAC,8BAA8B,GAAG,cAAc,EAAE,8BAA8B,IAAI,KAAK;;IAG/F,QAAQ,GAAA;AACN,QAAA,IAAI,CAAC,eAAe,GAAG,IAAI,cAAc,CAAkB,IAAI,CAAC,QAAQ,EAAE,SAAS,EAAE,KAAK,CAAC;;IAG7F,kBAAkB,GAAA;QAChB,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,OAAO,CAAC,CAAC;AACpF,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAClB,IAAI,CAAC,mBAAmB,EAAE;;;AAI9B;;;AAGG;AACH,IAAA,UAAU,CAAC,KAAU,EAAA;AACnB,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK;AAClB,QAAA,IAAI,CAAC,eAAe,CAAC,YAAY,EAAE;;;AAIrC,IAAA,gBAAgB,CAAC,EAAwB,EAAA;AACvC,QAAA,IAAI,CAAC,6BAA6B,GAAG,EAAE;;;AAIzC,IAAA,iBAAiB,CAAC,EAAO,EAAA;AACvB,QAAA,IAAI,CAAC,UAAU,GAAG,EAAE;;;AAItB,IAAA,gBAAgB,CAAC,UAAmB,EAAA;AAClC,QAAA,IAAI,CAAC,QAAQ,GAAG,UAAU;;;AAIlB,IAAA,QAAQ,CAAC,KAAoB,EAAA;QACrC,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,EAAE;YAClC;;AAGF,QAAA,MAAM,MAAM,GAAG,KAAK,CAAC,MAA2B;AAChD,QAAA,MAAM,QAAQ,GAAG,MAAM,CAAC,EAAE;AAC1B,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC,SAAS,CAAC,MAAM,IAAG;AAC7D,YAAA,OAAO,MAAM,CAAC,QAAQ,KAAK,QAAQ;AACrC,SAAC,CAAC;QAEF,IAAI,UAAU,GAA2B,IAAI;AAC7C,QAAA,QAAQ,KAAK,CAAC,OAAO;AACnB,YAAA,KAAK,KAAK;AACV,YAAA,KAAK,KAAK;gBACR,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,IAAI;gBACnD;AACF,YAAA,KAAK,QAAQ;gBACX,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;gBAC3C;AACF,YAAA,KAAK,UAAU;gBACb,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,KAAK,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;gBACpE;AACF,YAAA,KAAK,UAAU;gBACb,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC,CAAC;gBAC1C;AACF,YAAA,KAAK,WAAW;gBACd,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,KAAK,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;gBACpE;AACF,YAAA;gBACE;;QAGJ,IAAI,UAAU,EAAE;YACd,KAAK,CAAC,cAAc,EAAE;YACtB,UAAU,CAAC,cAAc,EAAE;YAC3B,UAAU,CAAC,KAAK,EAAE;;;;AAKtB,IAAA,gBAAgB,CAAC,MAAuB,EAAA;QACtC,MAAM,KAAK,GAAG,IAAI,qBAAqB,CAAC,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC;AAC3D,QAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,KAAK;AAC5B,QAAA,IAAI,CAAC,6BAA6B,CAAC,KAAK,CAAC,KAAK,CAAC;AAC/C,QAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC;;AAGzB;;;;;;AAMG;IACH,iBAAiB,CACf,MAAuB,EACvB,MAAe,EACf,WAAW,GAAG,KAAK,EACnB,WAAW,GAAG,KAAK,EAAA;;;AAInB,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE;AACrD,YAAA,IAAI,CAAC,QAA4B,CAAC,OAAO,GAAG,KAAK;;AAGpD,QAAA,IAAI,IAAI,CAAC,eAAe,EAAE;YACxB,IAAI,MAAM,EAAE;AACV,gBAAA,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,MAAM,CAAC;;iBAC9B;AACL,gBAAA,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,MAAM,CAAC;;;aAElC;YACL,WAAW,GAAG,IAAI;;;;;QAMpB,IAAI,WAAW,EAAE;AACf,YAAA,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;;aACpE;AACL,YAAA,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,WAAW,CAAC;;;;AAK/C,IAAA,WAAW,CAAC,MAAuB,EAAA;AACjC,QAAA,OAAO,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,MAAM,CAAC;;;AAIxE,IAAA,aAAa,CAAC,MAAuB,EAAA;AACnC,QAAA,IAAI,OAAO,IAAI,CAAC,SAAS,KAAK,WAAW,EAAE;AACzC,YAAA,OAAO,KAAK;;AAGd,QAAA,IAAI,IAAI,CAAC,QAAQ,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;YAClD,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,IAAI,MAAM,CAAC,KAAK,IAAI,IAAI,IAAI,KAAK,KAAK,MAAM,CAAC,KAAK,CAAC;;AAGrF,QAAA,OAAO,MAAM,CAAC,KAAK,KAAK,IAAI,CAAC,SAAS;;;IAIhC,mBAAmB,GAAA;AACzB,QAAA,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,MAAM,IAAG;AACnC,YAAA,MAAM,CAAC,QAAQ,GAAG,CAAC,CAAC;AACtB,SAAC,CAAC;AACF,QAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;AAChB,YAAA,IAAI,CAAC,QAA4B,CAAC,QAAQ,GAAG,CAAC;;aAC1C;AACL,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACnD,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,CAAE;AAE1C,gBAAA,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;AACpB,oBAAA,MAAM,CAAC,QAAQ,GAAG,CAAC;oBACnB;;;;QAIN,IAAI,CAAC,oBAAoB,EAAE;;;IAIrB,cAAc,CAAC,UAAkB,EAAE,MAAc,EAAA;AACvD,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc;AAEjC,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACtC,YAAA,MAAM,KAAK,GAAG,CAAC,UAAU,GAAG,MAAM,GAAG,CAAC,GAAG,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,MAAM;YACrE,MAAM,IAAI,GAAG,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC;AAE7B,YAAA,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AAC1B,gBAAA,OAAO,IAAI;;;AAIf,QAAA,OAAO,IAAI;;;AAIL,IAAA,oBAAoB,CAAC,KAAkB,EAAA;AAC7C,QAAA,IAAI,CAAC,SAAS,GAAG,KAAK;AAEtB,QAAA,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACxB;;QAGF,MAAM,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE;AAE7C,QAAA,IAAI,IAAI,CAAC,QAAQ,IAAI,KAAK,EAAE;AAC1B,YAAA,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAAE;AAC5E,gBAAA,MAAM,KAAK,CAAC,oDAAoD,CAAC;;YAGnE,IAAI,CAAC,eAAe,EAAE;AACtB,YAAA,KAAK,CAAC,OAAO,CAAC,CAAC,YAAiB,KAAK,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;;aACzE;YACL,IAAI,CAAC,eAAe,EAAE;AACtB,YAAA,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,OAAO,CAAC;;;QAInC,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,OAAO,CAAC,KAAK,CAAC,MAAM,IAAI,MAAM,CAAC,QAAQ,KAAK,CAAC,CAAC,CAAC,EAAE;AACrE,YAAA,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE;AAC5B,gBAAA,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;AACpB,oBAAA,MAAM,CAAC,QAAQ,GAAG,CAAC;oBACnB;;;;;;IAOA,eAAe,GAAA;AACrB,QAAA,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE;AAC5B,QAAA,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,MAAM,IAAG;AACnC,YAAA,MAAM,CAAC,OAAO,GAAG,KAAK;;AAEtB,YAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AAClB,gBAAA,MAAM,CAAC,QAAQ,GAAG,CAAC,CAAC;;AAExB,SAAC,CAAC;;;IAII,YAAY,CAAC,KAAU,EAAE,OAA0B,EAAA;AACzD,QAAA,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE;AAC5B,YAAA,IAAI,MAAM,CAAC,KAAK,KAAK,KAAK,EAAE;AAC1B,gBAAA,MAAM,CAAC,OAAO,GAAG,IAAI;AACrB,gBAAA,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,MAAM,CAAC;AACnC,gBAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;;AAElB,oBAAA,MAAM,CAAC,QAAQ,GAAG,CAAC;;gBAErB;;;;;IAME,iBAAiB,CAAC,MAAuB,EAAE,WAAoB,EAAA;;QAErE,IAAI,WAAW,EAAE;AACf,YAAA,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC;;;;QAK/B,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC;;;IAI3B,oBAAoB,GAAA;AAC1B,QAAA,IAAI,CAAC,cAAc,EAAE,OAAO,CAAC,MAAM,IAAI,MAAM,CAAC,aAAa,EAAE,CAAC;;uGAtZrD,oBAAoB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAApB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,oBAAoB,EAiDZ,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,yBAAA,EAAA,MAAA,EAAA,EAAA,UAAA,EAAA,YAAA,EAAA,IAAA,EAAA,MAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAAA,gBAAgB,CAgChB,EAAA,KAAA,EAAA,OAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAAA,gBAAgB,CAUhB,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAAA,gBAAgB,CAUhB,EAAA,mBAAA,EAAA,CAAA,qBAAA,EAAA,qBAAA,EAAA,gBAAgB,CAmBhB,EAAA,4BAAA,EAAA,CAAA,8BAAA,EAAA,8BAAA,EAAA,gBAAgB,CAWhB,EAAA,8BAAA,EAAA,CAAA,gCAAA,EAAA,gCAAA,EAAA,gBAAgB,CAjJxB,EAAA,EAAA,OAAA,EAAA,EAAA,WAAA,EAAA,aAAA,EAAA,MAAA,EAAA,QAAA,EAAA,EAAA,IAAA,EAAA,EAAA,SAAA,EAAA,EAAA,SAAA,EAAA,kBAAA,EAAA,EAAA,UAAA,EAAA,EAAA,WAAA,EAAA,mCAAA,EAAA,oBAAA,EAAA,UAAA,EAAA,kCAAA,EAAA,UAAA,EAAA,mDAAA,EAAA,6BAAA,EAAA,EAAA,cAAA,EAAA,yBAAA,EAAA,EAAA,SAAA,EAAA;YACT,sCAAsC;AACtC,YAAA,EAAC,OAAO,EAAE,uBAAuB,EAAE,WAAW,EAAE,oBAAoB,EAAC;AACtE,SAAA,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,gBAAA,EAAA,SAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAsCiC,eAAe,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,sBAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FA3BtC,oBAAoB,EAAA,UAAA,EAAA,CAAA;kBAhBhC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,yBAAyB;AACnC,oBAAA,SAAS,EAAE;wBACT,sCAAsC;AACtC,wBAAA,EAAC,OAAO,EAAE,uBAAuB,EAAE,WAAW,sBAAsB,EAAC;AACtE,qBAAA;AACD,oBAAA,IAAI,EAAE;AACJ,wBAAA,OAAO,EAAE,yBAAyB;AAClC,wBAAA,WAAW,EAAE,kBAAkB;AAC/B,wBAAA,aAAa,EAAE,mCAAmC;AAClD,wBAAA,sBAAsB,EAAE,UAAU;AAClC,wBAAA,oCAAoC,EAAE,UAAU;AAChD,wBAAA,qDAAqD,EAAE,2BAA2B;AACnF,qBAAA;AACD,oBAAA,QAAQ,EAAE,sBAAsB;AACjC,iBAAA;wDAiCC,cAAc,EAAA,CAAA;sBALb,eAAe;AAAC,gBAAA,IAAA,EAAA,CAAA,UAAU,CAAC,MAAM,eAAe,CAAC,EAAE;;;AAGlD,wBAAA,WAAW,EAAE,IAAI;AAClB,qBAAA;gBAIQ,UAAU,EAAA,CAAA;sBAAlB;gBAIG,IAAI,EAAA,CAAA;sBADP;gBAWqC,QAAQ,EAAA,CAAA;sBAA7C,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBAIhC,KAAK,EAAA,CAAA;sBADR;gBAoBkB,WAAW,EAAA,CAAA;sBAA7B;gBAUG,QAAQ,EAAA,CAAA;sBADX,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBAWhC,QAAQ,EAAA,CAAA;sBADX,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBAWhC,mBAAmB,EAAA,CAAA;sBADtB,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBAejB,MAAM,EAAA,CAAA;sBAAxB;gBAKG,4BAA4B,EAAA,CAAA;sBAD/B,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBAYhC,8BAA8B,EAAA,CAAA;sBADjC,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;;AAuRtC;MAwBa,eAAe,CAAA;AAClB,IAAA,kBAAkB,GAAG,MAAM,CAAC,iBAAiB,CAAC;AAC9C,IAAA,WAAW,GAAG,MAAM,CAA0B,UAAU,CAAC;AACzD,IAAA,aAAa,GAAG,MAAM,CAAC,YAAY,CAAC;AACpC,IAAA,YAAY,GAAG,MAAM,CAAC,YAAY,CAAC;IACnC,cAAc,GAAG,MAAM,CAAC,qBAAqB,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;IAChE,QAAQ,GAAG,KAAK;AAExB;;;AAGG;AACkB,IAAA,SAAS;AAE9B;;AAEG;IACuB,cAAc,GAAkB,IAAI;;AAGzC,IAAA,cAAc;;AAGnC,IAAA,iBAAiB;;AAGjB,IAAA,IAAI,QAAQ,GAAA;AACV,QAAA,OAAO,CAAG,EAAA,IAAI,CAAC,EAAE,SAAS;;;AAInB,IAAA,EAAE;;AAGF,IAAA,IAAI;;AAGJ,IAAA,KAAK;;AAGd,IAAA,IACI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,SAAS;;IAEvB,IAAI,QAAQ,CAAC,KAAoB,EAAA;AAC/B,QAAA,IAAI,KAAK,KAAK,IAAI,CAAC,SAAS,EAAE;AAC5B,YAAA,IAAI,CAAC,SAAS,GAAG,KAAK;YACtB,IAAI,CAAC,aAAa,EAAE;;;AAGhB,IAAA,SAAS;;AAGqB,IAAA,aAAa;;AAGnD,IAAA,IACI,UAAU,GAAA;AACZ,QAAA,OAAO,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC,UAAU,GAAG,IAAI,CAAC,WAAW;;IAEtF,IAAI,UAAU,CAAC,KAAgC,EAAA;AAC7C,QAAA,IAAI,CAAC,WAAW,GAAG,KAAK;;AAElB,IAAA,WAAW;;AAGnB,IAAA,IACI,OAAO,GAAA;QACT,OAAO,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,QAAQ;;IAE1F,IAAI,OAAO,CAAC,KAAc,EAAA;AACxB,QAAA,IAAI,KAAK,KAAK,IAAI,CAAC,QAAQ,EAAE;AAC3B,YAAA,IAAI,CAAC,QAAQ,GAAG,KAAK;AAErB,YAAA,IAAI,IAAI,CAAC,iBAAiB,EAAE;gBAC1B,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC;;AAG/D,YAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE;;;;AAK1C,IAAA,IACI,QAAQ,GAAA;AACV,QAAA,OAAO,IAAI,CAAC,SAAS,KAAK,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC;;IAEtF,IAAI,QAAQ,CAAC,KAAc,EAAA;AACzB,QAAA,IAAI,CAAC,SAAS,GAAG,KAAK;;IAEhB,SAAS,GAAY,KAAK;;AAGlC,IAAA,IACI,mBAAmB,GAAA;QACrB,QACE,IAAI,CAAC,oBAAoB;AACzB,aAAC,IAAI,CAAC,iBAAiB,KAAK,IAAI,IAAI,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,CAAC;;IAGnF,IAAI,mBAAmB,CAAC,KAAc,EAAA;AACpC,QAAA,IAAI,CAAC,oBAAoB,GAAG,KAAK;;AAE3B,IAAA,oBAAoB;;AAGT,IAAA,MAAM,GACvB,IAAI,YAAY,EAAyB;AAI3C,IAAA,WAAA,GAAA;QACE,MAAM,CAAC,sBAAsB,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC;AAC5D,QAAA,MAAM,WAAW,GAAG,MAAM,CAAuB,uBAAuB,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAE;AAC5F,QAAA,MAAM,eAAe,GAAG,MAAM,CAAC,IAAI,kBAAkB,CAAC,UAAU,CAAC,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC,IAAI,EAAE;AAC1F,QAAA,MAAM,cAAc,GAAG,MAAM,CAC3B,iCAAiC,EACjC,EAAC,QAAQ,EAAE,IAAI,EAAC,CACjB;QAED,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC,eAAe,CAAC,IAAI,CAAC;AAC/C,QAAA,IAAI,CAAC,iBAAiB,GAAG,WAAW;AACpC,QAAA,IAAI,CAAC,UAAU;AACb,YAAA,cAAc,IAAI,cAAc,CAAC,UAAU,GAAG,cAAc,CAAC,UAAU,GAAG,UAAU;QACtF,IAAI,CAAC,mBAAmB,GAAG,cAAc,EAAE,mBAAmB,IAAI,KAAK;;IAGzE,QAAQ,GAAA;AACN,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,iBAAiB;AACpC,QAAA,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,oBAAoB,CAAC;QAElE,IAAI,KAAK,EAAE;AACT,YAAA,IAAI,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE;AAC7B,gBAAA,IAAI,CAAC,OAAO,GAAG,IAAI;;iBACd,IAAI,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,QAAQ,EAAE;;;;;gBAKpD,KAAK,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC;;;;IAKlD,eAAe,GAAA;;;;;AAKb,QAAA,IAAI,IAAI,CAAC,cAAc,KAAK,gBAAgB,EAAE;YAC5C,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,SAAS,CAAC,GAAG,CAAC,sCAAsC,CAAC;;QAGtF,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC;;IAGpD,WAAW,GAAA;AACT,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,iBAAiB;QAEpC,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,IAAI,CAAC,WAAW,CAAC;;;QAInD,IAAI,KAAK,IAAI,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE;YACpC,KAAK,CAAC,iBAAiB,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC;;;;AAKrD,IAAA,KAAK,CAAC,OAAsB,EAAA;QAC1B,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,KAAK,CAAC,OAAO,CAAC;;;IAIlD,cAAc,GAAA;AACZ,QAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB;;AAGF,QAAA,MAAM,UAAU,GAAG,IAAI,CAAC,gBAAgB,EAAE,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,QAAQ;AAElE,QAAA,IAAI,UAAU,KAAK,IAAI,CAAC,QAAQ,EAAE;AAChC,YAAA,IAAI,CAAC,QAAQ,GAAG,UAAU;AAC1B,YAAA,IAAI,IAAI,CAAC,iBAAiB,EAAE;AAC1B,gBAAA,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC;AACnE,gBAAA,IAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE;;;AAIvC,QAAA,IAAI,IAAI,CAAC,gBAAgB,EAAE,EAAE;AAC3B,YAAA,MAAM,SAAS,GAAG,IAAI,CAAC,iBAAiB,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,IAAG;AACpE,gBAAA,OAAO,MAAM,CAAC,QAAQ,KAAK,CAAC;AAC9B,aAAC,CAAC;;YAEF,IAAI,SAAS,EAAE;AACb,gBAAA,SAAS,CAAC,QAAQ,GAAG,CAAC,CAAC;;;AAGzB,YAAA,IAAI,CAAC,QAAQ,GAAG,CAAC;;;AAInB,QAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,qBAAqB,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;;AAG/D;;;;AAIG;IACH,aAAa,GAAA;;;AAGX,QAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE;;;IAIxC,cAAc,GAAA;AACZ,QAAA,IAAI,IAAI,CAAC,gBAAgB,EAAE,EAAE;AAC3B,YAAA,OAAO,IAAI,CAAC,iBAAiB,CAAC,IAAI;;AAEpC,QAAA,OAAO,IAAI,CAAC,IAAI,IAAI,IAAI;;;IAI1B,gBAAgB,GAAA;QACd,OAAO,IAAI,CAAC,iBAAiB,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,QAAQ;;uGAlOxD,eAAe,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAf,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,eAAe,EAqDP,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,mBAAA,EAAA,MAAA,EAAA,EAAA,SAAA,EAAA,CAAA,YAAA,EAAA,WAAA,CAAA,EAAA,cAAA,EAAA,CAAA,iBAAA,EAAA,gBAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,KAAA,EAAA,OAAA,EAAA,QAAA,EAAA,UAAA,EAAA,aAAA,EAAA,CAAA,eAAA,EAAA,eAAA,EAAA,gBAAgB,CAahB,EAAA,UAAA,EAAA,YAAA,EAAA,OAAA,EAAA,CAAA,SAAA,EAAA,SAAA,EAAA,gBAAgB,CAiBhB,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAAA,gBAAgB,CAUhB,EAAA,mBAAA,EAAA,CAAA,qBAAA,EAAA,qBAAA,EAAA,gBAAgB,CC3pBrC,EAAA,EAAA,OAAA,EAAA,EAAA,MAAA,EAAA,QAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,MAAA,EAAA,cAAA,EAAA,EAAA,SAAA,EAAA,EAAA,OAAA,EAAA,SAAA,EAAA,EAAA,UAAA,EAAA,EAAA,oCAAA,EAAA,oBAAA,EAAA,iCAAA,EAAA,SAAA,EAAA,kCAAA,EAAA,UAAA,EAAA,8CAAA,EAAA,qBAAA,EAAA,6CAAA,EAAA,6BAAA,EAAA,iBAAA,EAAA,MAAA,EAAA,sBAAA,EAAA,MAAA,EAAA,SAAA,EAAA,IAAA,EAAA,WAAA,EAAA,MAAA,EAAA,EAAA,cAAA,EAAA,mBAAA,EAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,gBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,QAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,iBAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,w9CAoCA,EDwhBY,MAAA,EAAA,CAAA,iyUAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,SAAS,wPAAE,iBAAiB,EAAA,QAAA,EAAA,qBAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,UAAA,EAAA,YAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAE3B,eAAe,EAAA,UAAA,EAAA,CAAA;kBAvB3B,SAAS;+BACE,mBAAmB,EAAA,aAAA,EAGd,iBAAiB,CAAC,IAAI,EAAA,QAAA,EAC3B,iBAAiB,EACV,eAAA,EAAA,uBAAuB,CAAC,MAAM,EACzC,IAAA,EAAA;AACJ,wBAAA,sCAAsC,EAAE,oBAAoB;AAC5D,wBAAA,mCAAmC,EAAE,SAAS;AAC9C,wBAAA,oCAAoC,EAAE,UAAU;AAChD,wBAAA,gDAAgD,EAAE,qBAAqB;AACvE,wBAAA,+CAA+C,EAAE,2BAA2B;AAC5E,wBAAA,OAAO,EAAE,mBAAmB;AAC5B,wBAAA,mBAAmB,EAAE,MAAM;AAC3B,wBAAA,wBAAwB,EAAE,MAAM;AAChC,wBAAA,WAAW,EAAE,IAAI;AACjB,wBAAA,aAAa,EAAE,MAAM;AACrB,wBAAA,SAAS,EAAE,SAAS;AACpB,wBAAA,MAAM,EAAE,cAAc;AACvB,qBAAA,EAAA,OAAA,EACQ,CAAC,SAAS,EAAE,iBAAiB,CAAC,EAAA,QAAA,EAAA,w9CAAA,EAAA,MAAA,EAAA,CAAA,iyUAAA,CAAA,EAAA;wDAclB,SAAS,EAAA,CAAA;sBAA7B,KAAK;uBAAC,YAAY;gBAKO,cAAc,EAAA,CAAA;sBAAvC,KAAK;uBAAC,iBAAiB;gBAGH,cAAc,EAAA,CAAA;sBAAlC,SAAS;uBAAC,QAAQ;gBAWV,EAAE,EAAA,CAAA;sBAAV;gBAGQ,IAAI,EAAA,CAAA;sBAAZ;gBAGQ,KAAK,EAAA,CAAA;sBAAb;gBAIG,QAAQ,EAAA,CAAA;sBADX;gBAaqC,aAAa,EAAA,CAAA;sBAAlD,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBAIhC,UAAU,EAAA,CAAA;sBADb;gBAWG,OAAO,EAAA,CAAA;sBADV,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBAkBhC,QAAQ,EAAA,CAAA;sBADX,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBAWhC,mBAAmB,EAAA,CAAA;sBADtB,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBAajB,MAAM,EAAA,CAAA;sBAAxB;;;MExpBU,qBAAqB,CAAA;uGAArB,qBAAqB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;AAArB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,qBAAqB,EAHtB,OAAA,EAAA,CAAA,eAAe,EAAE,eAAe,EAAE,oBAAoB,EAAE,eAAe,CACvE,EAAA,OAAA,EAAA,CAAA,eAAe,EAAE,oBAAoB,EAAE,eAAe,CAAA,EAAA,CAAA;AAErD,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,qBAAqB,YAHtB,eAAe,EAAE,eAAe,EAAwB,eAAe,EACvE,eAAe,CAAA,EAAA,CAAA;;2FAEd,qBAAqB,EAAA,UAAA,EAAA,CAAA;kBAJjC,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACR,OAAO,EAAE,CAAC,eAAe,EAAE,eAAe,EAAE,oBAAoB,EAAE,eAAe,CAAC;AAClF,oBAAA,OAAO,EAAE,CAAC,eAAe,EAAE,oBAAoB,EAAE,eAAe,CAAC;AAClE,iBAAA;;;;;"}