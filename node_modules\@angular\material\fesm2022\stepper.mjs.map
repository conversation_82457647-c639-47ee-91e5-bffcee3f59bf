{"version": 3, "file": "stepper.mjs", "sources": ["../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/stepper/step-label.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/stepper/stepper-intl.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/stepper/step-header.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/stepper/step-header.html", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/stepper/stepper-icon.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/stepper/step-content.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/stepper/stepper.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/stepper/step.html", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/stepper/stepper.html", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/stepper/stepper-button.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/stepper/stepper-module.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/stepper/stepper-animations.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Directive} from '@angular/core';\nimport {CdkStepLabel} from '@angular/cdk/stepper';\n\n@Directive({\n  selector: '[matStepLabel]',\n})\nexport class MatStepLabel extends CdkStepLabel {}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Injectable, Optional, SkipSelf} from '@angular/core';\nimport {Subject} from 'rxjs';\n\n/** Stepper data that is required for internationalization. */\n@Injectable({providedIn: 'root'})\nexport class MatStepperIntl {\n  /**\n   * Stream that emits whenever the labels here are changed. Use this to notify\n   * components if the labels have changed after initialization.\n   */\n  readonly changes: Subject<void> = new Subject<void>();\n\n  /** Label that is rendered below optional steps. */\n  optionalLabel: string = 'Optional';\n\n  /** Label that is used to indicate step as completed to screen readers. */\n  completedLabel: string = 'Completed';\n\n  /** Label that is used to indicate step as editable to screen readers. */\n  editableLabel: string = 'Editable';\n}\n\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nexport function MAT_STEPPER_INTL_PROVIDER_FACTORY(parentIntl: MatStepperIntl) {\n  return parentIntl || new MatStepperIntl();\n}\n\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nexport const MAT_STEPPER_INTL_PROVIDER = {\n  provide: MatStepperIntl,\n  deps: [[new Optional(), new SkipSelf(), MatStepperIntl]],\n  useFactory: MAT_STEPPER_INTL_PROVIDER_FACTORY,\n};\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {FocusMonitor, FocusOrigin} from '@angular/cdk/a11y';\nimport {\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  Input,\n  OnDestroy,\n  ViewEncapsulation,\n  TemplateRef,\n  AfterViewInit,\n  inject,\n} from '@angular/core';\nimport {Subscription} from 'rxjs';\nimport {MatStepLabel} from './step-label';\nimport {MatStepperIntl} from './stepper-intl';\nimport {MatStepperIconContext} from './stepper-icon';\nimport {CdkStepHeader, StepState} from '@angular/cdk/stepper';\nimport {_StructuralStylesLoader, MatRipple, ThemePalette} from '../core';\nimport {MatIcon} from '../icon';\nimport {NgTemplateOutlet} from '@angular/common';\nimport {_CdkPrivateStyleLoader, _VisuallyHiddenLoader} from '@angular/cdk/private';\n\n@Component({\n  selector: 'mat-step-header',\n  templateUrl: 'step-header.html',\n  styleUrl: 'step-header.css',\n  host: {\n    'class': 'mat-step-header',\n    '[class]': '\"mat-\" + (color || \"primary\")',\n    'role': 'tab',\n  },\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  imports: [MatRipple, NgTemplateOutlet, MatIcon],\n})\nexport class MatStepHeader extends CdkStepHeader implements AfterViewInit, OnDestroy {\n  _intl = inject(MatStepperIntl);\n  private _focusMonitor = inject(FocusMonitor);\n\n  private _intlSubscription: Subscription;\n\n  /** State of the given step. */\n  @Input() state: StepState;\n\n  /** Label of the given step. */\n  @Input() label: MatStepLabel | string;\n\n  /** Error message to display when there's an error. */\n  @Input() errorMessage: string;\n\n  /** Overrides for the header icons, passed in via the stepper. */\n  @Input() iconOverrides: {[key: string]: TemplateRef<MatStepperIconContext>};\n\n  /** Index of the given step. */\n  @Input() index: number;\n\n  /** Whether the given step is selected. */\n  @Input() selected: boolean;\n\n  /** Whether the given step label is active. */\n  @Input() active: boolean;\n\n  /** Whether the given step is optional. */\n  @Input() optional: boolean;\n\n  /** Whether the ripple should be disabled. */\n  @Input() disableRipple: boolean;\n\n  /**\n   * Theme color of the step header. This API is supported in M2 themes only, it\n   * has no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/stepper/styling.\n   *\n   * For information on applying color variants in M3, see\n   * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n   */\n  @Input() color: ThemePalette;\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    super();\n\n    const styleLoader = inject(_CdkPrivateStyleLoader);\n    styleLoader.load(_StructuralStylesLoader);\n    styleLoader.load(_VisuallyHiddenLoader);\n    const changeDetectorRef = inject(ChangeDetectorRef);\n    this._intlSubscription = this._intl.changes.subscribe(() => changeDetectorRef.markForCheck());\n  }\n\n  ngAfterViewInit() {\n    this._focusMonitor.monitor(this._elementRef, true);\n  }\n\n  ngOnDestroy() {\n    this._intlSubscription.unsubscribe();\n    this._focusMonitor.stopMonitoring(this._elementRef);\n  }\n\n  /** Focuses the step header. */\n  override focus(origin?: FocusOrigin, options?: FocusOptions) {\n    if (origin) {\n      this._focusMonitor.focusVia(this._elementRef, origin, options);\n    } else {\n      this._elementRef.nativeElement.focus(options);\n    }\n  }\n\n  /** Returns string label of given step if it is a text label. */\n  _stringLabel(): string | null {\n    return this.label instanceof MatStepLabel ? null : this.label;\n  }\n\n  /** Returns MatStepLabel if the label of given step is a template label. */\n  _templateLabel(): MatStepLabel | null {\n    return this.label instanceof MatStepLabel ? this.label : null;\n  }\n\n  /** Returns the host HTML element. */\n  _getHostElement() {\n    return this._elementRef.nativeElement;\n  }\n\n  /** Template context variables that are exposed to the `matStepperIcon` instances. */\n  _getIconContext(): MatStepperIconContext {\n    return {\n      index: this.index,\n      active: this.active,\n      optional: this.optional,\n    };\n  }\n\n  _getDefaultTextForState(state: StepState): string {\n    if (state == 'number') {\n      return `${this.index + 1}`;\n    }\n    if (state == 'edit') {\n      return 'create';\n    }\n    if (state == 'error') {\n      return 'warning';\n    }\n    return state;\n  }\n}\n", "<div class=\"mat-step-header-ripple mat-focus-indicator\" matRipple\n     [matRippleTrigger]=\"_getHostElement()\"\n     [matRippleDisabled]=\"disableRipple\"></div>\n\n<div class=\"mat-step-icon-state-{{state}} mat-step-icon\" [class.mat-step-icon-selected]=\"selected\">\n  <div class=\"mat-step-icon-content\">\n    @if (iconOverrides && iconOverrides[state]) {\n      <ng-container\n        [ngTemplateOutlet]=\"iconOverrides[state]\"\n        [ngTemplateOutletContext]=\"_getIconContext()\"></ng-container>\n    } @else {\n      @switch (state) {\n        @case ('number') {\n          <span aria-hidden=\"true\">{{_getDefaultTextForState(state)}}</span>\n        }\n\n        @default {\n          @if (state === 'done') {\n            <span class=\"cdk-visually-hidden\">{{_intl.completedLabel}}</span>\n          } @else if (state === 'edit') {\n            <span class=\"cdk-visually-hidden\">{{_intl.editableLabel}}</span>\n          }\n\n          <mat-icon aria-hidden=\"true\">{{_getDefaultTextForState(state)}}</mat-icon>\n        }\n      }\n    }\n  </div>\n</div>\n<div class=\"mat-step-label\"\n     [class.mat-step-label-active]=\"active\"\n     [class.mat-step-label-selected]=\"selected\"\n     [class.mat-step-label-error]=\"state == 'error'\">\n  @if (_templateLabel(); as templateLabel) {\n    <!-- If there is a label template, use it. -->\n    <div class=\"mat-step-text-label\">\n      <ng-container [ngTemplateOutlet]=\"templateLabel.template\"></ng-container>\n    </div>\n  } @else if (_stringLabel()) {\n    <!-- If there is no label template, fall back to the text label. -->\n    <div class=\"mat-step-text-label\">{{label}}</div>\n  }\n\n  @if (optional && state != 'error') {\n    <div class=\"mat-step-optional\">{{_intl.optionalLabel}}</div>\n  }\n\n  @if (state === 'error') {\n    <div class=\"mat-step-sub-label-error\">{{errorMessage}}</div>\n  }\n</div>\n\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Directive, Input, TemplateRef, inject} from '@angular/core';\nimport {StepState} from '@angular/cdk/stepper';\n\n/** Template context available to an attached `matStepperIcon`. */\nexport interface MatStepperIconContext {\n  /** Index of the step. */\n  index: number;\n  /** Whether the step is currently active. */\n  active: boolean;\n  /** Whether the step is optional. */\n  optional: boolean;\n}\n\n/**\n * Template to be used to override the icons inside the step header.\n */\n@Directive({\n  selector: 'ng-template[matStepperIcon]',\n})\nexport class MatStepperIcon {\n  templateRef = inject<TemplateRef<MatStepperIconContext>>(TemplateRef);\n\n  /** Name of the icon to be overridden. */\n  @Input('matStepperIcon') name: StepState;\n\n  constructor(...args: unknown[]);\n  constructor() {}\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Directive, TemplateRef, inject} from '@angular/core';\n\n/**\n * Content for a `mat-step` that will be rendered lazily.\n */\n@Directive({\n  selector: 'ng-template[matStepContent]',\n})\nexport class MatStepContent {\n  _template = inject<TemplateRef<any>>(TemplateRef);\n\n  constructor(...args: unknown[]);\n  constructor() {}\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {CdkStep, CdkStepper} from '@angular/cdk/stepper';\nimport {\n  AfterContentInit,\n  AfterViewInit,\n  ANIMATION_MODULE_TYPE,\n  ChangeDetectionStrategy,\n  Component,\n  ContentChild,\n  ContentChildren,\n  ElementRef,\n  EventEmitter,\n  inject,\n  Input,\n  NgZone,\n  OnDestroy,\n  Output,\n  QueryList,\n  Renderer2,\n  signal,\n  TemplateRef,\n  ViewChildren,\n  ViewContainerRef,\n  ViewEncapsulation,\n} from '@angular/core';\nimport {NgTemplateOutlet} from '@angular/common';\nimport {AbstractControl, FormGroupDirective, NgForm} from '@angular/forms';\nimport {ErrorStateMatcher, ThemePalette} from '../core';\nimport {Platform} from '@angular/cdk/platform';\nimport {CdkPortalOutlet, TemplatePortal} from '@angular/cdk/portal';\nimport {Subscription} from 'rxjs';\nimport {takeUntil, map, startWith, switchMap} from 'rxjs/operators';\n\nimport {MatStepHeader} from './step-header';\nimport {MatStepLabel} from './step-label';\nimport {MatStepperIcon, MatStepperIconContext} from './stepper-icon';\nimport {MatStepContent} from './step-content';\n\n@Component({\n  selector: 'mat-step',\n  templateUrl: 'step.html',\n  providers: [\n    {provide: ErrorStateMatcher, useExisting: MatStep},\n    {provide: CdkStep, useExisting: MatStep},\n  ],\n  encapsulation: ViewEncapsulation.None,\n  exportAs: 'matStep',\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  imports: [CdkPortalOutlet],\n  host: {\n    'hidden': '', // Hide the steps so they don't affect the layout.\n  },\n})\nexport class MatStep extends CdkStep implements ErrorStateMatcher, AfterContentInit, OnDestroy {\n  private _errorStateMatcher = inject(ErrorStateMatcher, {skipSelf: true});\n  private _viewContainerRef = inject(ViewContainerRef);\n  private _isSelected = Subscription.EMPTY;\n\n  /** Content for step label given by `<ng-template matStepLabel>`. */\n  // We need an initializer here to avoid a TS error.\n  @ContentChild(MatStepLabel) override stepLabel: MatStepLabel = undefined!;\n\n  /**\n   * Theme color for the particular step. This API is supported in M2 themes\n   * only, it has no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/stepper/styling.\n   *\n   * For information on applying color variants in M3, see\n   * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n   */\n  @Input() color: ThemePalette;\n\n  /** Content that will be rendered lazily. */\n  @ContentChild(MatStepContent, {static: false}) _lazyContent: MatStepContent;\n\n  /** Currently-attached portal containing the lazy content. */\n  _portal: TemplatePortal;\n\n  ngAfterContentInit() {\n    this._isSelected = this._stepper.steps.changes\n      .pipe(\n        switchMap(() => {\n          return this._stepper.selectionChange.pipe(\n            map(event => event.selectedStep === this),\n            startWith(this._stepper.selected === this),\n          );\n        }),\n      )\n      .subscribe(isSelected => {\n        if (isSelected && this._lazyContent && !this._portal) {\n          this._portal = new TemplatePortal(this._lazyContent._template, this._viewContainerRef!);\n        }\n      });\n  }\n\n  ngOnDestroy() {\n    this._isSelected.unsubscribe();\n  }\n\n  /** Custom error state matcher that additionally checks for validity of interacted form. */\n  isErrorState(control: AbstractControl | null, form: FormGroupDirective | NgForm | null): boolean {\n    const originalErrorState = this._errorStateMatcher.isErrorState(control, form);\n\n    // Custom error state checks for the validity of form that is not submitted or touched\n    // since user can trigger a form change by calling for another step without directly\n    // interacting with the current form.\n    const customErrorState = !!(control && control.invalid && this.interacted);\n\n    return originalErrorState || customErrorState;\n  }\n}\n\n@Component({\n  selector: 'mat-stepper, mat-vertical-stepper, mat-horizontal-stepper, [matStepper]',\n  exportAs: 'matStepper, matVerticalStepper, matHorizontalStepper',\n  templateUrl: 'stepper.html',\n  styleUrl: 'stepper.css',\n  host: {\n    '[class.mat-stepper-horizontal]': 'orientation === \"horizontal\"',\n    '[class.mat-stepper-vertical]': 'orientation === \"vertical\"',\n    '[class.mat-stepper-label-position-end]':\n      'orientation === \"horizontal\" && labelPosition == \"end\"',\n    '[class.mat-stepper-label-position-bottom]':\n      'orientation === \"horizontal\" && labelPosition == \"bottom\"',\n    '[class.mat-stepper-header-position-bottom]': 'headerPosition === \"bottom\"',\n    '[class.mat-stepper-animating]': '_isAnimating()',\n    '[style.--mat-stepper-animation-duration]': '_getAnimationDuration()',\n    '[attr.aria-orientation]': 'orientation',\n    'role': 'tablist',\n  },\n  providers: [{provide: CdkStepper, useExisting: MatStepper}],\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  imports: [NgTemplateOutlet, MatStepHeader],\n})\nexport class MatStepper extends CdkStepper implements AfterViewInit, AfterContentInit, OnDestroy {\n  private _ngZone = inject(NgZone);\n  private _renderer = inject(Renderer2);\n  private _animationsModule = inject(ANIMATION_MODULE_TYPE, {optional: true});\n  private _cleanupTransition: (() => void) | undefined;\n  protected _isAnimating = signal(false);\n\n  /** The list of step headers of the steps in the stepper. */\n  @ViewChildren(MatStepHeader) override _stepHeader: QueryList<MatStepHeader> = undefined!;\n\n  /** Elements hosting the step animations. */\n  @ViewChildren('animatedContainer') _animatedContainers: QueryList<ElementRef>;\n\n  /** Full list of steps inside the stepper, including inside nested steppers. */\n  @ContentChildren(MatStep, {descendants: true}) override _steps: QueryList<MatStep> = undefined!;\n\n  /** Steps that belong to the current stepper, excluding ones from nested steppers. */\n  override readonly steps: QueryList<MatStep> = new QueryList<MatStep>();\n\n  /** Custom icon overrides passed in by the consumer. */\n  @ContentChildren(MatStepperIcon, {descendants: true}) _icons: QueryList<MatStepperIcon>;\n\n  /** Event emitted when the current step is done transitioning in. */\n  @Output() readonly animationDone: EventEmitter<void> = new EventEmitter<void>();\n\n  /** Whether ripples should be disabled for the step headers. */\n  @Input() disableRipple: boolean;\n\n  /**\n   * Theme color for all of the steps in stepper. This API is supported in M2\n   * themes only, it has no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/stepper/styling.\n   *\n   * For information on applying color variants in M3, see\n   * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n   */\n  @Input() color: ThemePalette;\n\n  /**\n   * Whether the label should display in bottom or end position.\n   * Only applies in the `horizontal` orientation.\n   */\n  @Input()\n  labelPosition: 'bottom' | 'end' = 'end';\n\n  /**\n   * Position of the stepper's header.\n   * Only applies in the `horizontal` orientation.\n   */\n  @Input()\n  headerPosition: 'top' | 'bottom' = 'top';\n\n  /** Consumer-specified template-refs to be used to override the header icons. */\n  _iconOverrides: Record<string, TemplateRef<MatStepperIconContext>> = {};\n\n  /** Duration for the animation. Will be normalized to milliseconds if no units are set. */\n  @Input()\n  get animationDuration(): string {\n    return this._animationDuration;\n  }\n  set animationDuration(value: string) {\n    this._animationDuration = /^\\d+$/.test(value) ? value + 'ms' : value;\n  }\n  private _animationDuration = '';\n\n  /** Whether the stepper is rendering on the server. */\n  protected _isServer: boolean = !inject(Platform).isBrowser;\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    super();\n\n    const elementRef = inject<ElementRef<HTMLElement>>(ElementRef);\n    const nodeName = elementRef.nativeElement.nodeName.toLowerCase();\n    this.orientation = nodeName === 'mat-vertical-stepper' ? 'vertical' : 'horizontal';\n  }\n\n  override ngAfterContentInit() {\n    super.ngAfterContentInit();\n    this._icons.forEach(({name, templateRef}) => (this._iconOverrides[name] = templateRef));\n\n    // Mark the component for change detection whenever the content children query changes\n    this.steps.changes.pipe(takeUntil(this._destroyed)).subscribe(() => this._stateChanged());\n\n    // Transition events won't fire if animations are disabled so we simulate them.\n    this.selectedIndexChange.pipe(takeUntil(this._destroyed)).subscribe(() => {\n      const duration = this._getAnimationDuration();\n      if (duration === '0ms' || duration === '0s') {\n        this._onAnimationDone();\n      } else {\n        this._isAnimating.set(true);\n      }\n    });\n\n    this._ngZone.runOutsideAngular(() => {\n      if (this._animationsModule !== 'NoopAnimations') {\n        setTimeout(() => {\n          // Delay enabling the animations so we don't animate the initial state.\n          this._elementRef.nativeElement.classList.add('mat-stepper-animations-enabled');\n\n          // Bind this outside the zone since it fires for all transitions inside the stepper.\n          this._cleanupTransition = this._renderer.listen(\n            this._elementRef.nativeElement,\n            'transitionend',\n            this._handleTransitionend,\n          );\n        }, 200);\n      }\n    });\n  }\n\n  override ngAfterViewInit(): void {\n    super.ngAfterViewInit();\n\n    // Prior to #30314 the stepper had animation `done` events bound to each animated container.\n    // The animations module was firing them on initialization and for each subsequent animation.\n    // Since the events were bound in the template, it had the unintended side-effect of triggering\n    // change detection as well. It appears that this side-effect ended up being load-bearing,\n    // because it was ensuring that the content elements (e.g. `matStepLabel`) that are defined\n    // in sub-components actually get picked up in a timely fashion. This subscription simulates\n    // the same change detection by using `queueMicrotask` similarly to the animations module.\n    if (typeof queueMicrotask === 'function') {\n      let hasEmittedInitial = false;\n      this._animatedContainers.changes\n        .pipe(startWith(null), takeUntil(this._destroyed))\n        .subscribe(() =>\n          queueMicrotask(() => {\n            // Simulate the initial `animationDone` event\n            // that gets emitted by the animations module.\n            if (!hasEmittedInitial) {\n              hasEmittedInitial = true;\n              this.animationDone.emit();\n            }\n\n            this._stateChanged();\n          }),\n        );\n    }\n  }\n\n  override ngOnDestroy(): void {\n    super.ngOnDestroy();\n    this._cleanupTransition?.();\n  }\n\n  _stepIsNavigable(index: number, step: MatStep): boolean {\n    return step.completed || this.selectedIndex === index || !this.linear;\n  }\n\n  _getAnimationDuration() {\n    if (this._animationsModule === 'NoopAnimations') {\n      return '0ms';\n    }\n\n    if (this.animationDuration) {\n      return this.animationDuration;\n    }\n\n    return this.orientation === 'horizontal' ? '500ms' : '225ms';\n  }\n\n  private _handleTransitionend = (event: TransitionEvent) => {\n    const target = event.target as HTMLElement | null;\n\n    if (!target) {\n      return;\n    }\n\n    // Because we bind a single `transitionend` handler on the host node and because transition\n    // events bubble, we have to filter down to only the active step so don't emit events too\n    // often. We check the orientation and `property` name first to reduce the amount of times\n    // we need to check the DOM.\n    const isHorizontalActiveElement =\n      this.orientation === 'horizontal' &&\n      event.propertyName === 'transform' &&\n      target.classList.contains('mat-horizontal-stepper-content-current');\n    const isVerticalActiveElement =\n      this.orientation === 'vertical' &&\n      event.propertyName === 'grid-template-rows' &&\n      target.classList.contains('mat-vertical-content-container-active');\n\n    // Finally we need to ensure that the animated element is a direct descendant,\n    // rather than one coming from a nested stepper.\n    const shouldEmit =\n      (isHorizontalActiveElement || isVerticalActiveElement) &&\n      this._animatedContainers.find(ref => ref.nativeElement === target);\n\n    if (shouldEmit) {\n      this._onAnimationDone();\n    }\n  };\n\n  private _onAnimationDone() {\n    this._isAnimating.set(false);\n    this.animationDone.emit();\n  }\n}\n", "<ng-template>\n  <ng-content></ng-content>\n  <ng-template [cdkPortalOutlet]=\"_portal\"></ng-template>\n</ng-template>\n", "<!--\n  We need to project the content somewhere to avoid hydration errors. Some observations:\n  1. This is only necessary on the server.\n  2. We get a hydration error if there aren't any nodes after the `ng-content`.\n  3. We get a hydration error if `ng-content` is wrapped in another element.\n-->\n@if (_isServer) {\n  <ng-content/>\n}\n\n@switch (orientation) {\n  @case ('horizontal') {\n    <div class=\"mat-horizontal-stepper-wrapper\">\n      <div class=\"mat-horizontal-stepper-header-container\">\n        @for (step of steps; track step) {\n          <ng-container\n            [ngTemplateOutlet]=\"stepTemplate\"\n            [ngTemplateOutletContext]=\"{step, i: $index}\"/>\n          @if (!$last) {\n            <div class=\"mat-stepper-horizontal-line\"></div>\n          }\n        }\n      </div>\n\n      <div class=\"mat-horizontal-content-container\">\n        @for (step of steps; track step) {\n          <div\n            #animatedContainer\n            class=\"mat-horizontal-stepper-content\"\n            role=\"tabpanel\"\n            [id]=\"_getStepContentId($index)\"\n            [attr.aria-labelledby]=\"_getStepLabelId($index)\"\n            [class]=\"'mat-horizontal-stepper-content-' + _getAnimationDirection($index)\"\n            [attr.inert]=\"selectedIndex === $index ? null : ''\">\n            <ng-container [ngTemplateOutlet]=\"step.content\"/>\n          </div>\n        }\n      </div>\n    </div>\n  }\n\n  @case ('vertical') {\n    @for (step of steps; track step) {\n      <div class=\"mat-step\">\n        <ng-container\n          [ngTemplateOutlet]=\"stepTemplate\"\n          [ngTemplateOutletContext]=\"{step, i: $index}\"/>\n        <div\n          #animatedContainer\n          class=\"mat-vertical-content-container\"\n          [class.mat-stepper-vertical-line]=\"!$last\"\n          [class.mat-vertical-content-container-active]=\"selectedIndex === $index\"\n          [attr.inert]=\"selectedIndex === $index ? null : ''\">\n          <div class=\"mat-vertical-stepper-content\"\n            role=\"tabpanel\"\n            [id]=\"_getStepContentId($index)\"\n            [attr.aria-labelledby]=\"_getStepLabelId($index)\">\n            <div class=\"mat-vertical-content\">\n              <ng-container [ngTemplateOutlet]=\"step.content\"/>\n            </div>\n          </div>\n        </div>\n      </div>\n    }\n  }\n}\n\n<!-- Common step templating -->\n<ng-template let-step=\"step\" let-i=\"i\" #stepTemplate>\n  <mat-step-header\n    [class.mat-horizontal-stepper-header]=\"orientation === 'horizontal'\"\n    [class.mat-vertical-stepper-header]=\"orientation === 'vertical'\"\n    (click)=\"step.select()\"\n    (keydown)=\"_onKeydown($event)\"\n    [tabIndex]=\"_getFocusIndex() === i ? 0 : -1\"\n    [id]=\"_getStepLabelId(i)\"\n    [attr.aria-posinset]=\"i + 1\"\n    [attr.aria-setsize]=\"steps.length\"\n    [attr.aria-controls]=\"_getStepContentId(i)\"\n    [attr.aria-selected]=\"selectedIndex == i\"\n    [attr.aria-label]=\"step.ariaLabel || null\"\n    [attr.aria-labelledby]=\"(!step.ariaLabel && step.ariaLabelledby) ? step.ariaLabelledby : null\"\n    [attr.aria-disabled]=\"_stepIsNavigable(i, step) ? null : true\"\n    [index]=\"i\"\n    [state]=\"_getIndicatorType(i, step.state)\"\n    [label]=\"step.stepLabel || step.label\"\n    [selected]=\"selectedIndex === i\"\n    [active]=\"_stepIsNavigable(i, step)\"\n    [optional]=\"step.optional\"\n    [errorMessage]=\"step.errorMessage\"\n    [iconOverrides]=\"_iconOverrides\"\n    [disableRipple]=\"disableRipple || !_stepIsNavigable(i, step)\"\n    [color]=\"step.color || color\"/>\n</ng-template>\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {CdkStepperNext, CdkStepperPrevious} from '@angular/cdk/stepper';\nimport {Directive} from '@angular/core';\n\n/** <PERSON><PERSON> that moves to the next step in a stepper workflow. */\n@Directive({\n  selector: 'button[matStepperNext]',\n  host: {\n    'class': 'mat-stepper-next',\n    '[type]': 'type',\n  },\n})\nexport class MatStepperNext extends CdkStepperNext {}\n\n/** Button that moves to the previous step in a stepper workflow. */\n@Directive({\n  selector: 'button[matStepperPrevious]',\n  host: {\n    'class': 'mat-stepper-previous',\n    '[type]': 'type',\n  },\n})\nexport class MatStepperPrevious extends CdkStepperPrevious {}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {PortalModule} from '@angular/cdk/portal';\nimport {CdkStepperModule} from '@angular/cdk/stepper';\nimport {NgModule} from '@angular/core';\nimport {ErrorStateMatcher, MatCommonModule, MatRippleModule} from '../core';\nimport {MatIconModule} from '../icon';\nimport {MatStepHeader} from './step-header';\nimport {MatStepLabel} from './step-label';\nimport {MatStep, MatStepper} from './stepper';\nimport {MatStepperNext, MatStepperPrevious} from './stepper-button';\nimport {MatStepperIcon} from './stepper-icon';\nimport {MAT_STEPPER_INTL_PROVIDER} from './stepper-intl';\nimport {MatStepContent} from './step-content';\n\n@NgModule({\n  imports: [\n    MatCommonModule,\n    PortalModule,\n    CdkStepperModule,\n    MatIconModule,\n    MatRippleModule,\n    MatStep,\n    MatStepLabel,\n    MatStepper,\n    MatStepperNext,\n    MatStepperPrevious,\n    MatStepHeader,\n    MatStepperIcon,\n    MatStepContent,\n  ],\n  exports: [\n    MatCommonModule,\n    MatStep,\n    MatStepLabel,\n    MatStepper,\n    MatStepperNext,\n    MatStepperPrevious,\n    MatStepHeader,\n    MatStepperIcon,\n    MatStepContent,\n  ],\n  providers: [MAT_STEPPER_INTL_PROVIDER, ErrorStateMatcher],\n})\nexport class MatStepperModule {}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/**\n * Animations used by the Material steppers.\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nexport const matStepperAnimations: {\n  readonly horizontalStepTransition: any;\n  readonly verticalStepTransition: any;\n} = {\n  // Represents:\n  // trigger('horizontalStepTransition', [\n  //   state('previous', style({transform: 'translate3d(-100%, 0, 0)', visibility: 'hidden'})),\n  //   // Transition to `inherit`, rather than `visible`,\n  //   // because visibility on a child element the one from the parent,\n  //   // making this element focusable inside of a `hidden` element.\n  //   state('current', style({transform: 'none', visibility: 'inherit'})),\n  //   state('next', style({transform: 'translate3d(100%, 0, 0)', visibility: 'hidden'})),\n  //   transition(\n  //     '* => *',\n  //     group([\n  //       animate('{{animationDuration}} cubic-bezier(0.35, 0, 0.25, 1)'),\n  //       query('@*', animateChild(), {optional: true}),\n  //     ]),\n  //     {\n  //       params: {animationDuration: '500ms'},\n  //     },\n  //   ),\n  // ])\n\n  /** Animation that transitions the step along the X axis in a horizontal stepper. */\n  horizontalStepTransition: {\n    type: 7,\n    name: 'horizontalStepTransition',\n    definitions: [\n      {\n        type: 0,\n        name: 'previous',\n        styles: {\n          type: 6,\n          styles: {transform: 'translate3d(-100%, 0, 0)', visibility: 'hidden'},\n          offset: null,\n        },\n      },\n      {\n        type: 0,\n        name: 'current',\n        styles: {\n          type: 6,\n          styles: {transform: 'none', visibility: 'inherit'},\n          offset: null,\n        },\n      },\n      {\n        type: 0,\n        name: 'next',\n        styles: {\n          type: 6,\n          styles: {transform: 'translate3d(100%, 0, 0)', visibility: 'hidden'},\n          offset: null,\n        },\n      },\n      {\n        type: 1,\n        expr: '* => *',\n        animation: {\n          type: 3,\n          steps: [\n            {\n              type: 4,\n              styles: null,\n              timings: '{{animationDuration}} cubic-bezier(0.35, 0, 0.25, 1)',\n            },\n            {\n              type: 11,\n              selector: '@*',\n              animation: {type: 9, options: null},\n              options: {optional: true},\n            },\n          ],\n          options: null,\n        },\n        options: {params: {animationDuration: '500ms'}},\n      },\n    ],\n    options: {},\n  },\n\n  // Represents:\n  // trigger('verticalStepTransition', [\n  //   state('previous', style({height: '0px', visibility: 'hidden'})),\n  //   state('next', style({height: '0px', visibility: 'hidden'})),\n  //   // Transition to `inherit`, rather than `visible`,\n  //   // because visibility on a child element the one from the parent,\n  //   // making this element focusable inside of a `hidden` element.\n  //   state('current', style({height: '*', visibility: 'inherit'})),\n  //   transition(\n  //     '* <=> current',\n  //     group([\n  //       animate('{{animationDuration}} cubic-bezier(0.4, 0.0, 0.2, 1)'),\n  //       query('@*', animateChild(), {optional: true}),\n  //     ]),\n  //     {\n  //       params: {animationDuration: '225ms'},\n  //     },\n  //   ),\n  // ])\n\n  /** Animation that transitions the step along the Y axis in a vertical stepper. */\n  verticalStepTransition: {\n    type: 7,\n    name: 'verticalStepTransition',\n    definitions: [\n      {\n        type: 0,\n        name: 'previous',\n        styles: {type: 6, styles: {'height': '0px', visibility: 'hidden'}, offset: null},\n      },\n      {\n        type: 0,\n        name: 'next',\n        styles: {type: 6, styles: {'height': '0px', visibility: 'hidden'}, offset: null},\n      },\n      {\n        type: 0,\n        name: 'current',\n        styles: {type: 6, styles: {'height': '*', visibility: 'inherit'}, offset: null},\n      },\n      {\n        type: 1,\n        expr: '* <=> current',\n        animation: {\n          type: 3,\n          steps: [\n            {\n              type: 4,\n              styles: null,\n              timings: '{{animationDuration}} cubic-bezier(0.4, 0.0, 0.2, 1)',\n            },\n            {\n              type: 11,\n              selector: '@*',\n              animation: {type: 9, options: null},\n              options: {optional: true},\n            },\n          ],\n          options: null,\n        },\n        options: {params: {animationDuration: '225ms'}},\n      },\n    ],\n    options: {},\n  },\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAcM,MAAO,YAAa,SAAQ,YAAY,CAAA;uGAAjC,YAAY,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAZ,YAAY,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,gBAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAAZ,YAAY,EAAA,UAAA,EAAA,CAAA;kBAHxB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,gBAAgB;AAC3B,iBAAA;;;ACFD;MAEa,cAAc,CAAA;AACzB;;;AAGG;AACM,IAAA,OAAO,GAAkB,IAAI,OAAO,EAAQ;;IAGrD,aAAa,GAAW,UAAU;;IAGlC,cAAc,GAAW,WAAW;;IAGpC,aAAa,GAAW,UAAU;uGAdvB,cAAc,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;AAAd,IAAA,OAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,cAAc,cADF,MAAM,EAAA,CAAA;;2FAClB,cAAc,EAAA,UAAA,EAAA,CAAA;kBAD1B,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC;;AAkBhC;;;;AAIG;AACG,SAAU,iCAAiC,CAAC,UAA0B,EAAA;AAC1E,IAAA,OAAO,UAAU,IAAI,IAAI,cAAc,EAAE;AAC3C;AAEA;;;;AAIG;AACU,MAAA,yBAAyB,GAAG;AACvC,IAAA,OAAO,EAAE,cAAc;AACvB,IAAA,IAAI,EAAE,CAAC,CAAC,IAAI,QAAQ,EAAE,EAAE,IAAI,QAAQ,EAAE,EAAE,cAAc,CAAC,CAAC;AACxD,IAAA,UAAU,EAAE,iCAAiC;;;ACJzC,MAAO,aAAc,SAAQ,aAAa,CAAA;AAC9C,IAAA,KAAK,GAAG,MAAM,CAAC,cAAc,CAAC;AACtB,IAAA,aAAa,GAAG,MAAM,CAAC,YAAY,CAAC;AAEpC,IAAA,iBAAiB;;AAGhB,IAAA,KAAK;;AAGL,IAAA,KAAK;;AAGL,IAAA,YAAY;;AAGZ,IAAA,aAAa;;AAGb,IAAA,KAAK;;AAGL,IAAA,QAAQ;;AAGR,IAAA,MAAM;;AAGN,IAAA,QAAQ;;AAGR,IAAA,aAAa;AAEtB;;;;;;AAMG;AACM,IAAA,KAAK;AAId,IAAA,WAAA,GAAA;AACE,QAAA,KAAK,EAAE;AAEP,QAAA,MAAM,WAAW,GAAG,MAAM,CAAC,sBAAsB,CAAC;AAClD,QAAA,WAAW,CAAC,IAAI,CAAC,uBAAuB,CAAC;AACzC,QAAA,WAAW,CAAC,IAAI,CAAC,qBAAqB,CAAC;AACvC,QAAA,MAAM,iBAAiB,GAAG,MAAM,CAAC,iBAAiB,CAAC;AACnD,QAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM,iBAAiB,CAAC,YAAY,EAAE,CAAC;;IAG/F,eAAe,GAAA;QACb,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC;;IAGpD,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,iBAAiB,CAAC,WAAW,EAAE;QACpC,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,IAAI,CAAC,WAAW,CAAC;;;IAI5C,KAAK,CAAC,MAAoB,EAAE,OAAsB,EAAA;QACzD,IAAI,MAAM,EAAE;AACV,YAAA,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,EAAE,MAAM,EAAE,OAAO,CAAC;;aACzD;YACL,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,KAAK,CAAC,OAAO,CAAC;;;;IAKjD,YAAY,GAAA;AACV,QAAA,OAAO,IAAI,CAAC,KAAK,YAAY,YAAY,GAAG,IAAI,GAAG,IAAI,CAAC,KAAK;;;IAI/D,cAAc,GAAA;AACZ,QAAA,OAAO,IAAI,CAAC,KAAK,YAAY,YAAY,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI;;;IAI/D,eAAe,GAAA;AACb,QAAA,OAAO,IAAI,CAAC,WAAW,CAAC,aAAa;;;IAIvC,eAAe,GAAA;QACb,OAAO;YACL,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,QAAQ,EAAE,IAAI,CAAC,QAAQ;SACxB;;AAGH,IAAA,uBAAuB,CAAC,KAAgB,EAAA;AACtC,QAAA,IAAI,KAAK,IAAI,QAAQ,EAAE;AACrB,YAAA,OAAO,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC,EAAE;;AAE5B,QAAA,IAAI,KAAK,IAAI,MAAM,EAAE;AACnB,YAAA,OAAO,QAAQ;;AAEjB,QAAA,IAAI,KAAK,IAAI,OAAO,EAAE;AACpB,YAAA,OAAO,SAAS;;AAElB,QAAA,OAAO,KAAK;;uGA1GH,aAAa,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAb,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,aAAa,odC3C1B,62DAoDA,EAAA,MAAA,EAAA,CAAA,+lIAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EDXY,SAAS,EAAE,QAAA,EAAA,2BAAA,EAAA,MAAA,EAAA,CAAA,gBAAA,EAAA,oBAAA,EAAA,mBAAA,EAAA,iBAAA,EAAA,oBAAA,EAAA,mBAAA,EAAA,kBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,WAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,gBAAgB,oJAAE,OAAO,EAAA,QAAA,EAAA,UAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,SAAA,EAAA,SAAA,EAAA,UAAA,CAAA,EAAA,QAAA,EAAA,CAAA,SAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAEnC,aAAa,EAAA,UAAA,EAAA,CAAA;kBAbzB,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,iBAAiB,EAGrB,IAAA,EAAA;AACJ,wBAAA,OAAO,EAAE,iBAAiB;AAC1B,wBAAA,SAAS,EAAE,+BAA+B;AAC1C,wBAAA,MAAM,EAAE,KAAK;AACd,qBAAA,EAAA,aAAA,EACc,iBAAiB,CAAC,IAAI,EAAA,eAAA,EACpB,uBAAuB,CAAC,MAAM,EACtC,OAAA,EAAA,CAAC,SAAS,EAAE,gBAAgB,EAAE,OAAO,CAAC,EAAA,QAAA,EAAA,62DAAA,EAAA,MAAA,EAAA,CAAA,+lIAAA,CAAA,EAAA;wDAStC,KAAK,EAAA,CAAA;sBAAb;gBAGQ,KAAK,EAAA,CAAA;sBAAb;gBAGQ,YAAY,EAAA,CAAA;sBAApB;gBAGQ,aAAa,EAAA,CAAA;sBAArB;gBAGQ,KAAK,EAAA,CAAA;sBAAb;gBAGQ,QAAQ,EAAA,CAAA;sBAAhB;gBAGQ,MAAM,EAAA,CAAA;sBAAd;gBAGQ,QAAQ,EAAA,CAAA;sBAAhB;gBAGQ,aAAa,EAAA,CAAA;sBAArB;gBASQ,KAAK,EAAA,CAAA;sBAAb;;;AE9DH;;AAEG;MAIU,cAAc,CAAA;AACzB,IAAA,WAAW,GAAG,MAAM,CAAqC,WAAW,CAAC;;AAG5C,IAAA,IAAI;AAG7B,IAAA,WAAA,GAAA;uGAPW,cAAc,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAd,cAAc,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,6BAAA,EAAA,MAAA,EAAA,EAAA,IAAA,EAAA,CAAA,gBAAA,EAAA,MAAA,CAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAAd,cAAc,EAAA,UAAA,EAAA,CAAA;kBAH1B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,6BAA6B;AACxC,iBAAA;wDAK0B,IAAI,EAAA,CAAA;sBAA5B,KAAK;uBAAC,gBAAgB;;;ACrBzB;;AAEG;MAIU,cAAc,CAAA;AACzB,IAAA,SAAS,GAAG,MAAM,CAAmB,WAAW,CAAC;AAGjD,IAAA,WAAA,GAAA;uGAJW,cAAc,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAd,cAAc,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,6BAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAAd,cAAc,EAAA,UAAA,EAAA,CAAA;kBAH1B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,6BAA6B;AACxC,iBAAA;;;AC6CK,MAAO,OAAQ,SAAQ,OAAO,CAAA;IAC1B,kBAAkB,GAAG,MAAM,CAAC,iBAAiB,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;AAChE,IAAA,iBAAiB,GAAG,MAAM,CAAC,gBAAgB,CAAC;AAC5C,IAAA,WAAW,GAAG,YAAY,CAAC,KAAK;;;IAIH,SAAS,GAAiB,SAAU;AAEzE;;;;;;AAMG;AACM,IAAA,KAAK;;AAGiC,IAAA,YAAY;;AAG3D,IAAA,OAAO;IAEP,kBAAkB,GAAA;QAChB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC;AACpC,aAAA,IAAI,CACH,SAAS,CAAC,MAAK;AACb,YAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,IAAI,CACvC,GAAG,CAAC,KAAK,IAAI,KAAK,CAAC,YAAY,KAAK,IAAI,CAAC,EACzC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,KAAK,IAAI,CAAC,CAC3C;AACH,SAAC,CAAC;aAEH,SAAS,CAAC,UAAU,IAAG;YACtB,IAAI,UAAU,IAAI,IAAI,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;AACpD,gBAAA,IAAI,CAAC,OAAO,GAAG,IAAI,cAAc,CAAC,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,IAAI,CAAC,iBAAkB,CAAC;;AAE3F,SAAC,CAAC;;IAGN,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE;;;IAIhC,YAAY,CAAC,OAA+B,EAAE,IAAwC,EAAA;AACpF,QAAA,MAAM,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,OAAO,EAAE,IAAI,CAAC;;;;AAK9E,QAAA,MAAM,gBAAgB,GAAG,CAAC,EAAE,OAAO,IAAI,OAAO,CAAC,OAAO,IAAI,IAAI,CAAC,UAAU,CAAC;QAE1E,OAAO,kBAAkB,IAAI,gBAAgB;;uGAtDpC,OAAO,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAP,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,OAAO,EAZP,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,UAAA,EAAA,MAAA,EAAA,EAAA,KAAA,EAAA,OAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,EAAA,EAAA,SAAA,EAAA;AACT,YAAA,EAAC,OAAO,EAAE,iBAAiB,EAAE,WAAW,EAAE,OAAO,EAAC;AAClD,YAAA,EAAC,OAAO,EAAE,OAAO,EAAE,WAAW,EAAE,OAAO,EAAC;AACzC,SAAA,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,WAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAgBa,YAAY,EAYZ,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,cAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,cAAc,EC/E9B,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,SAAA,CAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,2HAIA,4CDmDY,eAAe,EAAA,QAAA,EAAA,mBAAA,EAAA,MAAA,EAAA,CAAA,iBAAA,CAAA,EAAA,OAAA,EAAA,CAAA,UAAA,CAAA,EAAA,QAAA,EAAA,CAAA,iBAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAKd,OAAO,EAAA,UAAA,EAAA,CAAA;kBAfnB,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,UAAU,EAET,SAAA,EAAA;AACT,wBAAA,EAAC,OAAO,EAAE,iBAAiB,EAAE,WAAW,SAAS,EAAC;AAClD,wBAAA,EAAC,OAAO,EAAE,OAAO,EAAE,WAAW,SAAS,EAAC;AACzC,qBAAA,EAAA,aAAA,EACc,iBAAiB,CAAC,IAAI,EAAA,QAAA,EAC3B,SAAS,EAAA,eAAA,EACF,uBAAuB,CAAC,MAAM,EAAA,OAAA,EACtC,CAAC,eAAe,CAAC,EACpB,IAAA,EAAA;wBACJ,QAAQ,EAAE,EAAE;AACb,qBAAA,EAAA,QAAA,EAAA,2HAAA,EAAA;8BASoC,SAAS,EAAA,CAAA;sBAA7C,YAAY;uBAAC,YAAY;gBASjB,KAAK,EAAA,CAAA;sBAAb;gBAG8C,YAAY,EAAA,CAAA;sBAA1D,YAAY;AAAC,gBAAA,IAAA,EAAA,CAAA,cAAc,EAAE,EAAC,MAAM,EAAE,KAAK,EAAC;;AA8DzC,MAAO,UAAW,SAAQ,UAAU,CAAA;AAChC,IAAA,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;AACxB,IAAA,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC;IAC7B,iBAAiB,GAAG,MAAM,CAAC,qBAAqB,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;AACnE,IAAA,kBAAkB;AAChB,IAAA,YAAY,GAAG,MAAM,CAAC,KAAK,CAAC;;IAGA,WAAW,GAA6B,SAAU;;AAGrD,IAAA,mBAAmB;;IAGE,MAAM,GAAuB,SAAU;;AAG7E,IAAA,KAAK,GAAuB,IAAI,SAAS,EAAW;;AAGhB,IAAA,MAAM;;AAGzC,IAAA,aAAa,GAAuB,IAAI,YAAY,EAAQ;;AAGtE,IAAA,aAAa;AAEtB;;;;;;AAMG;AACM,IAAA,KAAK;AAEd;;;AAGG;IAEH,aAAa,GAAqB,KAAK;AAEvC;;;AAGG;IAEH,cAAc,GAAqB,KAAK;;IAGxC,cAAc,GAAuD,EAAE;;AAGvE,IAAA,IACI,iBAAiB,GAAA;QACnB,OAAO,IAAI,CAAC,kBAAkB;;IAEhC,IAAI,iBAAiB,CAAC,KAAa,EAAA;AACjC,QAAA,IAAI,CAAC,kBAAkB,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,KAAK,GAAG,IAAI,GAAG,KAAK;;IAE9D,kBAAkB,GAAG,EAAE;;IAGrB,SAAS,GAAY,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,SAAS;AAI1D,IAAA,WAAA,GAAA;AACE,QAAA,KAAK,EAAE;AAEP,QAAA,MAAM,UAAU,GAAG,MAAM,CAA0B,UAAU,CAAC;QAC9D,MAAM,QAAQ,GAAG,UAAU,CAAC,aAAa,CAAC,QAAQ,CAAC,WAAW,EAAE;AAChE,QAAA,IAAI,CAAC,WAAW,GAAG,QAAQ,KAAK,sBAAsB,GAAG,UAAU,GAAG,YAAY;;IAG3E,kBAAkB,GAAA;QACzB,KAAK,CAAC,kBAAkB,EAAE;QAC1B,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,EAAC,IAAI,EAAE,WAAW,EAAC,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,WAAW,CAAC,CAAC;;QAGvF,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,aAAa,EAAE,CAAC;;AAGzF,QAAA,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,MAAK;AACvE,YAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,qBAAqB,EAAE;YAC7C,IAAI,QAAQ,KAAK,KAAK,IAAI,QAAQ,KAAK,IAAI,EAAE;gBAC3C,IAAI,CAAC,gBAAgB,EAAE;;iBAClB;AACL,gBAAA,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC;;AAE/B,SAAC,CAAC;AAEF,QAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;AAClC,YAAA,IAAI,IAAI,CAAC,iBAAiB,KAAK,gBAAgB,EAAE;gBAC/C,UAAU,CAAC,MAAK;;oBAEd,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,SAAS,CAAC,GAAG,CAAC,gCAAgC,CAAC;;oBAG9E,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAC7C,IAAI,CAAC,WAAW,CAAC,aAAa,EAC9B,eAAe,EACf,IAAI,CAAC,oBAAoB,CAC1B;iBACF,EAAE,GAAG,CAAC;;AAEX,SAAC,CAAC;;IAGK,eAAe,GAAA;QACtB,KAAK,CAAC,eAAe,EAAE;;;;;;;;AASvB,QAAA,IAAI,OAAO,cAAc,KAAK,UAAU,EAAE;YACxC,IAAI,iBAAiB,GAAG,KAAK;YAC7B,IAAI,CAAC,mBAAmB,CAAC;AACtB,iBAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC;AAChD,iBAAA,SAAS,CAAC,MACT,cAAc,CAAC,MAAK;;;gBAGlB,IAAI,CAAC,iBAAiB,EAAE;oBACtB,iBAAiB,GAAG,IAAI;AACxB,oBAAA,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE;;gBAG3B,IAAI,CAAC,aAAa,EAAE;aACrB,CAAC,CACH;;;IAIE,WAAW,GAAA;QAClB,KAAK,CAAC,WAAW,EAAE;AACnB,QAAA,IAAI,CAAC,kBAAkB,IAAI;;IAG7B,gBAAgB,CAAC,KAAa,EAAE,IAAa,EAAA;AAC3C,QAAA,OAAO,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,aAAa,KAAK,KAAK,IAAI,CAAC,IAAI,CAAC,MAAM;;IAGvE,qBAAqB,GAAA;AACnB,QAAA,IAAI,IAAI,CAAC,iBAAiB,KAAK,gBAAgB,EAAE;AAC/C,YAAA,OAAO,KAAK;;AAGd,QAAA,IAAI,IAAI,CAAC,iBAAiB,EAAE;YAC1B,OAAO,IAAI,CAAC,iBAAiB;;AAG/B,QAAA,OAAO,IAAI,CAAC,WAAW,KAAK,YAAY,GAAG,OAAO,GAAG,OAAO;;AAGtD,IAAA,oBAAoB,GAAG,CAAC,KAAsB,KAAI;AACxD,QAAA,MAAM,MAAM,GAAG,KAAK,CAAC,MAA4B;QAEjD,IAAI,CAAC,MAAM,EAAE;YACX;;;;;;AAOF,QAAA,MAAM,yBAAyB,GAC7B,IAAI,CAAC,WAAW,KAAK,YAAY;YACjC,KAAK,CAAC,YAAY,KAAK,WAAW;AAClC,YAAA,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,wCAAwC,CAAC;AACrE,QAAA,MAAM,uBAAuB,GAC3B,IAAI,CAAC,WAAW,KAAK,UAAU;YAC/B,KAAK,CAAC,YAAY,KAAK,oBAAoB;AAC3C,YAAA,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,uCAAuC,CAAC;;;AAIpE,QAAA,MAAM,UAAU,GACd,CAAC,yBAAyB,IAAI,uBAAuB;AACrD,YAAA,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,GAAG,IAAI,GAAG,CAAC,aAAa,KAAK,MAAM,CAAC;QAEpE,IAAI,UAAU,EAAE;YACd,IAAI,CAAC,gBAAgB,EAAE;;AAE3B,KAAC;IAEO,gBAAgB,GAAA;AACtB,QAAA,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,KAAK,CAAC;AAC5B,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE;;uGAlMhB,UAAU,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAV,UAAU,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,yEAAA,EAAA,MAAA,EAAA,EAAA,aAAA,EAAA,eAAA,EAAA,KAAA,EAAA,OAAA,EAAA,aAAA,EAAA,eAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,EAAA,OAAA,EAAA,EAAA,aAAA,EAAA,eAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,MAAA,EAAA,SAAA,EAAA,EAAA,UAAA,EAAA,EAAA,8BAAA,EAAA,gCAAA,EAAA,4BAAA,EAAA,8BAAA,EAAA,sCAAA,EAAA,4DAAA,EAAA,yCAAA,EAAA,+DAAA,EAAA,0CAAA,EAAA,+BAAA,EAAA,6BAAA,EAAA,gBAAA,EAAA,wCAAA,EAAA,yBAAA,EAAA,uBAAA,EAAA,aAAA,EAAA,EAAA,EAAA,SAAA,EALV,CAAC,EAAC,OAAO,EAAE,UAAU,EAAE,WAAW,EAAE,UAAU,EAAC,CAAC,EAmB1C,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,QAAA,EAAA,SAAA,EAAA,OAAO,EAMP,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,QAAA,EAAA,SAAA,EAAA,cAAc,EAZjB,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,aAAA,EAAA,SAAA,EAAA,aAAa,6OErJ7B,wjHA8FA,EAAA,MAAA,EAAA,CAAA,s3LAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EF6CY,gBAAgB,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAE,aAAa,EAAA,QAAA,EAAA,iBAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,OAAA,EAAA,cAAA,EAAA,eAAA,EAAA,OAAA,EAAA,UAAA,EAAA,QAAA,EAAA,UAAA,EAAA,eAAA,EAAA,OAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAE9B,UAAU,EAAA,UAAA,EAAA,CAAA;kBAvBtB,SAAS;+BACE,yEAAyE,EAAA,QAAA,EACzE,sDAAsD,EAG1D,IAAA,EAAA;AACJ,wBAAA,gCAAgC,EAAE,8BAA8B;AAChE,wBAAA,8BAA8B,EAAE,4BAA4B;AAC5D,wBAAA,wCAAwC,EACtC,wDAAwD;AAC1D,wBAAA,2CAA2C,EACzC,2DAA2D;AAC7D,wBAAA,4CAA4C,EAAE,6BAA6B;AAC3E,wBAAA,+BAA+B,EAAE,gBAAgB;AACjD,wBAAA,0CAA0C,EAAE,yBAAyB;AACrE,wBAAA,yBAAyB,EAAE,aAAa;AACxC,wBAAA,MAAM,EAAE,SAAS;qBAClB,EACU,SAAA,EAAA,CAAC,EAAC,OAAO,EAAE,UAAU,EAAE,WAAW,EAAY,UAAA,EAAC,CAAC,EAAA,aAAA,EAC5C,iBAAiB,CAAC,IAAI,EACpB,eAAA,EAAA,uBAAuB,CAAC,MAAM,WACtC,CAAC,gBAAgB,EAAE,aAAa,CAAC,EAAA,QAAA,EAAA,wjHAAA,EAAA,MAAA,EAAA,CAAA,s3LAAA,CAAA,EAAA;wDAUJ,WAAW,EAAA,CAAA;sBAAhD,YAAY;uBAAC,aAAa;gBAGQ,mBAAmB,EAAA,CAAA;sBAArD,YAAY;uBAAC,mBAAmB;gBAGuB,MAAM,EAAA,CAAA;sBAA7D,eAAe;AAAC,gBAAA,IAAA,EAAA,CAAA,OAAO,EAAE,EAAC,WAAW,EAAE,IAAI,EAAC;gBAMS,MAAM,EAAA,CAAA;sBAA3D,eAAe;AAAC,gBAAA,IAAA,EAAA,CAAA,cAAc,EAAE,EAAC,WAAW,EAAE,IAAI,EAAC;gBAGjC,aAAa,EAAA,CAAA;sBAA/B;gBAGQ,aAAa,EAAA,CAAA;sBAArB;gBASQ,KAAK,EAAA,CAAA;sBAAb;gBAOD,aAAa,EAAA,CAAA;sBADZ;gBAQD,cAAc,EAAA,CAAA;sBADb;gBAQG,iBAAiB,EAAA,CAAA;sBADpB;;;AGzLH;AAQM,MAAO,cAAe,SAAQ,cAAc,CAAA;uGAArC,cAAc,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAd,cAAc,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,wBAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,MAAA,EAAA,MAAA,EAAA,EAAA,cAAA,EAAA,kBAAA,EAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAAd,cAAc,EAAA,UAAA,EAAA,CAAA;kBAP1B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,wBAAwB;AAClC,oBAAA,IAAI,EAAE;AACJ,wBAAA,OAAO,EAAE,kBAAkB;AAC3B,wBAAA,QAAQ,EAAE,MAAM;AACjB,qBAAA;AACF,iBAAA;;AAGD;AAQM,MAAO,kBAAmB,SAAQ,kBAAkB,CAAA;uGAA7C,kBAAkB,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAlB,kBAAkB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,4BAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,MAAA,EAAA,MAAA,EAAA,EAAA,cAAA,EAAA,sBAAA,EAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAAlB,kBAAkB,EAAA,UAAA,EAAA,CAAA;kBAP9B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,4BAA4B;AACtC,oBAAA,IAAI,EAAE;AACJ,wBAAA,OAAO,EAAE,sBAAsB;AAC/B,wBAAA,QAAQ,EAAE,MAAM;AACjB,qBAAA;AACF,iBAAA;;;MCsBY,gBAAgB,CAAA;uGAAhB,gBAAgB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;AAAhB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,gBAAgB,YA3BzB,eAAe;YACf,YAAY;YACZ,gBAAgB;YAChB,aAAa;YACb,eAAe;YACf,OAAO;YACP,YAAY;YACZ,UAAU;YACV,cAAc;YACd,kBAAkB;YAClB,aAAa;YACb,cAAc;AACd,YAAA,cAAc,aAGd,eAAe;YACf,OAAO;YACP,YAAY;YACZ,UAAU;YACV,cAAc;YACd,kBAAkB;YAClB,aAAa;YACb,cAAc;YACd,cAAc,CAAA,EAAA,CAAA;AAIL,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,gBAAgB,aAFhB,CAAC,yBAAyB,EAAE,iBAAiB,CAAC,YAzBvD,eAAe;YACf,YAAY;YACZ,gBAAgB;YAChB,aAAa;YACb,eAAe;YAGf,UAAU;AAGV,YAAA,aAAa,EAKb,eAAe,CAAA,EAAA,CAAA;;2FAYN,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBA7B5B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,OAAO,EAAE;wBACP,eAAe;wBACf,YAAY;wBACZ,gBAAgB;wBAChB,aAAa;wBACb,eAAe;wBACf,OAAO;wBACP,YAAY;wBACZ,UAAU;wBACV,cAAc;wBACd,kBAAkB;wBAClB,aAAa;wBACb,cAAc;wBACd,cAAc;AACf,qBAAA;AACD,oBAAA,OAAO,EAAE;wBACP,eAAe;wBACf,OAAO;wBACP,YAAY;wBACZ,UAAU;wBACV,cAAc;wBACd,kBAAkB;wBAClB,aAAa;wBACb,cAAc;wBACd,cAAc;AACf,qBAAA;AACD,oBAAA,SAAS,EAAE,CAAC,yBAAyB,EAAE,iBAAiB,CAAC;AAC1D,iBAAA;;;ACzCD;;;;;AAKG;AACU,MAAA,oBAAoB,GAG7B;;;;;;;;;;;;;;;;;;;;;AAsBF,IAAA,wBAAwB,EAAE;AACxB,QAAA,IAAI,EAAE,CAAC;AACP,QAAA,IAAI,EAAE,0BAA0B;AAChC,QAAA,WAAW,EAAE;AACX,YAAA;AACE,gBAAA,IAAI,EAAE,CAAC;AACP,gBAAA,IAAI,EAAE,UAAU;AAChB,gBAAA,MAAM,EAAE;AACN,oBAAA,IAAI,EAAE,CAAC;oBACP,MAAM,EAAE,EAAC,SAAS,EAAE,0BAA0B,EAAE,UAAU,EAAE,QAAQ,EAAC;AACrE,oBAAA,MAAM,EAAE,IAAI;AACb,iBAAA;AACF,aAAA;AACD,YAAA;AACE,gBAAA,IAAI,EAAE,CAAC;AACP,gBAAA,IAAI,EAAE,SAAS;AACf,gBAAA,MAAM,EAAE;AACN,oBAAA,IAAI,EAAE,CAAC;oBACP,MAAM,EAAE,EAAC,SAAS,EAAE,MAAM,EAAE,UAAU,EAAE,SAAS,EAAC;AAClD,oBAAA,MAAM,EAAE,IAAI;AACb,iBAAA;AACF,aAAA;AACD,YAAA;AACE,gBAAA,IAAI,EAAE,CAAC;AACP,gBAAA,IAAI,EAAE,MAAM;AACZ,gBAAA,MAAM,EAAE;AACN,oBAAA,IAAI,EAAE,CAAC;oBACP,MAAM,EAAE,EAAC,SAAS,EAAE,yBAAyB,EAAE,UAAU,EAAE,QAAQ,EAAC;AACpE,oBAAA,MAAM,EAAE,IAAI;AACb,iBAAA;AACF,aAAA;AACD,YAAA;AACE,gBAAA,IAAI,EAAE,CAAC;AACP,gBAAA,IAAI,EAAE,QAAQ;AACd,gBAAA,SAAS,EAAE;AACT,oBAAA,IAAI,EAAE,CAAC;AACP,oBAAA,KAAK,EAAE;AACL,wBAAA;AACE,4BAAA,IAAI,EAAE,CAAC;AACP,4BAAA,MAAM,EAAE,IAAI;AACZ,4BAAA,OAAO,EAAE,sDAAsD;AAChE,yBAAA;AACD,wBAAA;AACE,4BAAA,IAAI,EAAE,EAAE;AACR,4BAAA,QAAQ,EAAE,IAAI;4BACd,SAAS,EAAE,EAAC,IAAI,EAAE,CAAC,EAAE,OAAO,EAAE,IAAI,EAAC;AACnC,4BAAA,OAAO,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC;AAC1B,yBAAA;AACF,qBAAA;AACD,oBAAA,OAAO,EAAE,IAAI;AACd,iBAAA;gBACD,OAAO,EAAE,EAAC,MAAM,EAAE,EAAC,iBAAiB,EAAE,OAAO,EAAC,EAAC;AAChD,aAAA;AACF,SAAA;AACD,QAAA,OAAO,EAAE,EAAE;AACZ,KAAA;;;;;;;;;;;;;;;;;;;;;AAuBD,IAAA,sBAAsB,EAAE;AACtB,QAAA,IAAI,EAAE,CAAC;AACP,QAAA,IAAI,EAAE,wBAAwB;AAC9B,QAAA,WAAW,EAAE;AACX,YAAA;AACE,gBAAA,IAAI,EAAE,CAAC;AACP,gBAAA,IAAI,EAAE,UAAU;gBAChB,MAAM,EAAE,EAAC,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,EAAC,QAAQ,EAAE,KAAK,EAAE,UAAU,EAAE,QAAQ,EAAC,EAAE,MAAM,EAAE,IAAI,EAAC;AACjF,aAAA;AACD,YAAA;AACE,gBAAA,IAAI,EAAE,CAAC;AACP,gBAAA,IAAI,EAAE,MAAM;gBACZ,MAAM,EAAE,EAAC,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,EAAC,QAAQ,EAAE,KAAK,EAAE,UAAU,EAAE,QAAQ,EAAC,EAAE,MAAM,EAAE,IAAI,EAAC;AACjF,aAAA;AACD,YAAA;AACE,gBAAA,IAAI,EAAE,CAAC;AACP,gBAAA,IAAI,EAAE,SAAS;gBACf,MAAM,EAAE,EAAC,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,EAAC,QAAQ,EAAE,GAAG,EAAE,UAAU,EAAE,SAAS,EAAC,EAAE,MAAM,EAAE,IAAI,EAAC;AAChF,aAAA;AACD,YAAA;AACE,gBAAA,IAAI,EAAE,CAAC;AACP,gBAAA,IAAI,EAAE,eAAe;AACrB,gBAAA,SAAS,EAAE;AACT,oBAAA,IAAI,EAAE,CAAC;AACP,oBAAA,KAAK,EAAE;AACL,wBAAA;AACE,4BAAA,IAAI,EAAE,CAAC;AACP,4BAAA,MAAM,EAAE,IAAI;AACZ,4BAAA,OAAO,EAAE,sDAAsD;AAChE,yBAAA;AACD,wBAAA;AACE,4BAAA,IAAI,EAAE,EAAE;AACR,4BAAA,QAAQ,EAAE,IAAI;4BACd,SAAS,EAAE,EAAC,IAAI,EAAE,CAAC,EAAE,OAAO,EAAE,IAAI,EAAC;AACnC,4BAAA,OAAO,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC;AAC1B,yBAAA;AACF,qBAAA;AACD,oBAAA,OAAO,EAAE,IAAI;AACd,iBAAA;gBACD,OAAO,EAAE,EAAC,MAAM,EAAE,EAAC,iBAAiB,EAAE,OAAO,EAAC,EAAC;AAChD,aAAA;AACF,SAAA;AACD,QAAA,OAAO,EAAE,EAAE;AACZ,KAAA;;;;;"}