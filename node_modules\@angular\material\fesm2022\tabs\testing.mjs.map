{"version": 3, "file": "testing.mjs", "sources": ["../../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/tabs/testing/tab-harness.ts", "../../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/tabs/testing/tab-group-harness.ts", "../../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/tabs/testing/tab-link-harness.ts", "../../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/tabs/testing/tab-nav-panel-harness.ts", "../../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/tabs/testing/tab-nav-bar-harness.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  ComponentHarnessConstructor,\n  ContentContainerComponentHarness,\n  HarnessLoader,\n  HarnessPredicate,\n} from '@angular/cdk/testing';\nimport {TabHarnessFilters} from './tab-harness-filters';\n\n/** Harness for interacting with an Angular Material tab in tests. */\nexport class MatTabHarness extends ContentContainerComponentHarness<string> {\n  /** The selector for the host element of a `MatTab` instance. */\n  static hostSelector = '.mat-mdc-tab';\n\n  /**\n   * Gets a `HarnessPredicate` that can be used to search for a tab with specific attributes.\n   * @param options Options for filtering which tab instances are considered a match.\n   * @return a `HarnessPredicate` configured with the given options.\n   */\n  static with<T extends MatTabHarness>(\n    this: ComponentHarnessConstructor<T>,\n    options: TabHarnessFilters = {},\n  ): HarnessPredicate<T> {\n    return new HarnessPredicate(this, options)\n      .addOption('label', options.label, (harness, label) =>\n        HarnessPredicate.stringMatches(harness.getLabel(), label),\n      )\n      .addOption(\n        'selected',\n        options.selected,\n        async (harness, selected) => (await harness.isSelected()) == selected,\n      );\n  }\n\n  /** Gets the label of the tab. */\n  async getLabel(): Promise<string> {\n    return (await this.host()).text();\n  }\n\n  /** Gets the aria-label of the tab. */\n  async getAriaLabel(): Promise<string | null> {\n    return (await this.host()).getAttribute('aria-label');\n  }\n\n  /** Gets the value of the \"aria-labelledby\" attribute. */\n  async getAriaLabelledby(): Promise<string | null> {\n    return (await this.host()).getAttribute('aria-labelledby');\n  }\n\n  /** Whether the tab is selected. */\n  async isSelected(): Promise<boolean> {\n    const hostEl = await this.host();\n    return (await hostEl.getAttribute('aria-selected')) === 'true';\n  }\n\n  /** Whether the tab is disabled. */\n  async isDisabled(): Promise<boolean> {\n    const hostEl = await this.host();\n    return (await hostEl.getAttribute('aria-disabled')) === 'true';\n  }\n\n  /** Selects the given tab by clicking on the label. Tab cannot be selected if disabled. */\n  async select(): Promise<void> {\n    await (await this.host()).click('center');\n  }\n\n  /** Gets the text content of the tab. */\n  async getTextContent(): Promise<string> {\n    const contentId = await this._getContentId();\n    const contentEl = await this.documentRootLocatorFactory().locatorFor(`#${contentId}`)();\n    return contentEl.text();\n  }\n\n  protected override async getRootHarnessLoader(): Promise<HarnessLoader> {\n    const contentId = await this._getContentId();\n    return this.documentRootLocatorFactory().harnessLoaderFor(`#${contentId}`);\n  }\n\n  /** Gets the element id for the content of the current tab. */\n  private async _getContentId(): Promise<string> {\n    const hostEl = await this.host();\n    // Tabs never have an empty \"aria-controls\" attribute.\n    return (await hostEl.getAttribute('aria-controls'))!;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  ComponentHarness,\n  ComponentHarnessConstructor,\n  HarnessPredicate,\n  parallel,\n} from '@angular/cdk/testing';\nimport {TabGroupHarnessFilters, TabHarnessFilters} from './tab-harness-filters';\nimport {MatTabHarness} from './tab-harness';\n\n/** <PERSON><PERSON><PERSON> for interacting with a mat-tab-group in tests. */\nexport class MatTabGroupHarness extends ComponentHarness {\n  /** The selector for the host element of a `MatTabGroup` instance. */\n  static hostSelector = '.mat-mdc-tab-group';\n\n  /**\n   * Gets a `HarnessPredicate` that can be used to search for a tab group with specific attributes.\n   * @param options Options for filtering which tab group instances are considered a match.\n   * @return a `HarnessPredicate` configured with the given options.\n   */\n  static with<T extends MatTabGroupHarness>(\n    this: ComponentHarnessConstructor<T>,\n    options: TabGroupHarnessFilters = {},\n  ): HarnessPredicate<T> {\n    return new HarnessPredicate(this, options).addOption(\n      'selectedTabLabel',\n      options.selectedTabLabel,\n      async (harness, label) => {\n        const selectedTab = await harness.getSelectedTab();\n        return HarnessPredicate.stringMatches(await selectedTab.getLabel(), label);\n      },\n    );\n  }\n\n  /**\n   * Gets the list of tabs in the tab group.\n   * @param filter Optionally filters which tabs are included.\n   */\n  async getTabs(filter: TabHarnessFilters = {}): Promise<MatTabHarness[]> {\n    return this.locatorForAll(MatTabHarness.with(filter))();\n  }\n\n  /** Gets the selected tab of the tab group. */\n  async getSelectedTab(): Promise<MatTabHarness> {\n    const tabs = await this.getTabs();\n    const isSelected = await parallel(() => tabs.map(t => t.isSelected()));\n    for (let i = 0; i < tabs.length; i++) {\n      if (isSelected[i]) {\n        return tabs[i];\n      }\n    }\n    throw new Error('No selected tab could be found.');\n  }\n\n  /**\n   * Selects a tab in this tab group.\n   * @param filter An optional filter to apply to the child tabs. The first tab matching the filter\n   *     will be selected.\n   */\n  async selectTab(filter: TabHarnessFilters = {}): Promise<void> {\n    const tabs = await this.getTabs(filter);\n    if (!tabs.length) {\n      throw Error(`Cannot find mat-tab matching filter ${JSON.stringify(filter)}`);\n    }\n    await tabs[0].select();\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  ComponentHarness,\n  ComponentHarnessConstructor,\n  HarnessPredicate,\n} from '@angular/cdk/testing';\nimport {TabLinkHarnessFilters} from './tab-harness-filters';\n\n/** Harness for interacting with a Angular Material tab link in tests. */\nexport class MatTabLinkHarness extends ComponentHarness {\n  /** The selector for the host element of a `MatTabLink` instance. */\n  static hostSelector = '.mat-mdc-tab-link';\n\n  /**\n   * Gets a `HarnessPredicate` that can be used to search for a tab link with specific attributes.\n   * @param options Options for filtering which tab link instances are considered a match.\n   * @return a `HarnessPredicate` configured with the given options.\n   */\n  static with<T extends MatTabLinkHarness>(\n    this: ComponentHarnessConstructor<T>,\n    options: TabLinkHarnessFilters = {},\n  ): HarnessPredicate<T> {\n    return new HarnessPredicate(this, options).addOption('label', options.label, (harness, label) =>\n      HarnessPredicate.stringMatches(harness.getLabel(), label),\n    );\n  }\n\n  /** Gets the label of the link. */\n  async getLabel(): Promise<string> {\n    return (await this.host()).text();\n  }\n\n  /** Whether the link is active. */\n  async isActive(): Promise<boolean> {\n    const host = await this.host();\n    return host.hasClass('mdc-tab--active');\n  }\n\n  /** Whether the link is disabled. */\n  async isDisabled(): Promise<boolean> {\n    const host = await this.host();\n    return host.hasClass('mat-mdc-tab-disabled');\n  }\n\n  /** Clicks on the link. */\n  async click(): Promise<void> {\n    await (await this.host()).click();\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  ComponentHarnessConstructor,\n  ContentContainerComponentHarness,\n  HarnessPredicate,\n} from '@angular/cdk/testing';\nimport {TabNavPanelHarnessFilters} from './tab-harness-filters';\n\n/** <PERSON><PERSON>ss for interacting with a standard mat-tab-nav-panel in tests. */\nexport class MatTabNavPanelHarness extends ContentContainerComponentHarness {\n  /** The selector for the host element of a `MatTabNavPanel` instance. */\n  static hostSelector = '.mat-mdc-tab-nav-panel';\n\n  /**\n   * Gets a `HarnessPredicate` that can be used to search for a tab nav panel with specific\n   * attributes.\n   * @param options Options for filtering which tab nav panel instances are considered a match.\n   * @return a `HarnessPredicate` configured with the given options.\n   */\n  static with<T extends MatTabNavPanelHarness>(\n    this: ComponentHarnessConstructor<T>,\n    options: TabNavPanelHarnessFilters = {},\n  ): HarnessPredicate<T> {\n    return new HarnessPredicate(this, options);\n  }\n\n  /** Gets the tab panel text content. */\n  async getTextContent(): Promise<string> {\n    return (await this.host()).text();\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  ComponentHarness,\n  ComponentHarnessConstructor,\n  HarnessPredicate,\n  parallel,\n} from '@angular/cdk/testing';\nimport {\n  TabNavBarHarnessFilters,\n  TabNavPanelHarnessFilters,\n  TabLinkHarnessFilters,\n} from './tab-harness-filters';\nimport {MatTabLinkHarness} from './tab-link-harness';\nimport {MatTabNavPanelHarness} from './tab-nav-panel-harness';\n\n/** Harness for interacting with a mat-tab-nav-bar in tests. */\nexport class MatTabNavBarHarness extends ComponentHarness {\n  /** The selector for the host element of a `MatTabNavBar` instance. */\n  static hostSelector = '.mat-mdc-tab-nav-bar';\n\n  /**\n   * Gets a `HarnessPredicate` that can be used to search for a tab nav bar with specific\n   * attributes.\n   * @param options Options for filtering which tab nav bar instances are considered a match.\n   * @return a `HarnessPredicate` configured with the given options.\n   */\n  static with<T extends MatTabNavBarHarness>(\n    this: ComponentHarnessConstructor<T>,\n    options: TabNavBarHarnessFilters = {},\n  ): HarnessPredicate<T> {\n    return new HarnessPredicate(this, options);\n  }\n\n  /**\n   * Gets the list of links in the nav bar.\n   * @param filter Optionally filters which links are included.\n   */\n  async getLinks(filter: TabLinkHarnessFilters = {}): Promise<MatTabLinkHarness[]> {\n    return this.locatorForAll(MatTabLinkHarness.with(filter))();\n  }\n\n  /** Gets the active link in the nav bar. */\n  async getActiveLink(): Promise<MatTabLinkHarness> {\n    const links = await this.getLinks();\n    const isActive = await parallel(() => links.map(t => t.isActive()));\n    for (let i = 0; i < links.length; i++) {\n      if (isActive[i]) {\n        return links[i];\n      }\n    }\n    throw new Error('No active link could be found.');\n  }\n\n  /**\n   * Clicks a link inside the nav bar.\n   * @param filter An optional filter to apply to the child link. The first link matching the filter\n   *     will be clicked.\n   */\n  async clickLink(filter: TabLinkHarnessFilters = {}): Promise<void> {\n    const tabs = await this.getLinks(filter);\n    if (!tabs.length) {\n      throw Error(`Cannot find mat-tab-link matching filter ${JSON.stringify(filter)}`);\n    }\n    await tabs[0].click();\n  }\n\n  /** Gets the panel associated with the nav bar. */\n  async getPanel(): Promise<MatTabNavPanelHarness> {\n    const link = await this.getActiveLink();\n    const host = await link.host();\n    const panelId = await host.getAttribute('aria-controls');\n    if (!panelId) {\n      throw Error('No panel is controlled by the nav bar.');\n    }\n\n    const filter: TabNavPanelHarnessFilters = {selector: `#${panelId}`};\n    return await this.documentRootLocatorFactory().locatorFor(MatTabNavPanelHarness.with(filter))();\n  }\n}\n"], "names": [], "mappings": ";;AAgBA;AACM,MAAO,aAAc,SAAQ,gCAAwC,CAAA;;AAEzE,IAAA,OAAO,YAAY,GAAG,cAAc;AAEpC;;;;AAIG;AACH,IAAA,OAAO,IAAI,CAET,OAAA,GAA6B,EAAE,EAAA;AAE/B,QAAA,OAAO,IAAI,gBAAgB,CAAC,IAAI,EAAE,OAAO;aACtC,SAAS,CAAC,OAAO,EAAE,OAAO,CAAC,KAAK,EAAE,CAAC,OAAO,EAAE,KAAK,KAChD,gBAAgB,CAAC,aAAa,CAAC,OAAO,CAAC,QAAQ,EAAE,EAAE,KAAK,CAAC;aAE1D,SAAS,CACR,UAAU,EACV,OAAO,CAAC,QAAQ,EAChB,OAAO,OAAO,EAAE,QAAQ,KAAK,CAAC,MAAM,OAAO,CAAC,UAAU,EAAE,KAAK,QAAQ,CACtE;;;AAIL,IAAA,MAAM,QAAQ,GAAA;QACZ,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,IAAI,EAAE;;;AAInC,IAAA,MAAM,YAAY,GAAA;AAChB,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,YAAY,CAAC,YAAY,CAAC;;;AAIvD,IAAA,MAAM,iBAAiB,GAAA;AACrB,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,YAAY,CAAC,iBAAiB,CAAC;;;AAI5D,IAAA,MAAM,UAAU,GAAA;AACd,QAAA,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE;QAChC,OAAO,CAAC,MAAM,MAAM,CAAC,YAAY,CAAC,eAAe,CAAC,MAAM,MAAM;;;AAIhE,IAAA,MAAM,UAAU,GAAA;AACd,QAAA,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE;QAChC,OAAO,CAAC,MAAM,MAAM,CAAC,YAAY,CAAC,eAAe,CAAC,MAAM,MAAM;;;AAIhE,IAAA,MAAM,MAAM,GAAA;AACV,QAAA,MAAM,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,KAAK,CAAC,QAAQ,CAAC;;;AAI3C,IAAA,MAAM,cAAc,GAAA;AAClB,QAAA,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,aAAa,EAAE;AAC5C,QAAA,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,0BAA0B,EAAE,CAAC,UAAU,CAAC,CAAI,CAAA,EAAA,SAAS,CAAE,CAAA,CAAC,EAAE;AACvF,QAAA,OAAO,SAAS,CAAC,IAAI,EAAE;;AAGN,IAAA,MAAM,oBAAoB,GAAA;AAC3C,QAAA,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,aAAa,EAAE;QAC5C,OAAO,IAAI,CAAC,0BAA0B,EAAE,CAAC,gBAAgB,CAAC,CAAI,CAAA,EAAA,SAAS,CAAE,CAAA,CAAC;;;AAIpE,IAAA,MAAM,aAAa,GAAA;AACzB,QAAA,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE;;QAEhC,QAAQ,MAAM,MAAM,CAAC,YAAY,CAAC,eAAe,CAAC;;;;ACxEtD;AACM,MAAO,kBAAmB,SAAQ,gBAAgB,CAAA;;AAEtD,IAAA,OAAO,YAAY,GAAG,oBAAoB;AAE1C;;;;AAIG;AACH,IAAA,OAAO,IAAI,CAET,OAAA,GAAkC,EAAE,EAAA;QAEpC,OAAO,IAAI,gBAAgB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,SAAS,CAClD,kBAAkB,EAClB,OAAO,CAAC,gBAAgB,EACxB,OAAO,OAAO,EAAE,KAAK,KAAI;AACvB,YAAA,MAAM,WAAW,GAAG,MAAM,OAAO,CAAC,cAAc,EAAE;AAClD,YAAA,OAAO,gBAAgB,CAAC,aAAa,CAAC,MAAM,WAAW,CAAC,QAAQ,EAAE,EAAE,KAAK,CAAC;AAC5E,SAAC,CACF;;AAGH;;;AAGG;AACH,IAAA,MAAM,OAAO,CAAC,MAAA,GAA4B,EAAE,EAAA;AAC1C,QAAA,OAAO,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE;;;AAIzD,IAAA,MAAM,cAAc,GAAA;AAClB,QAAA,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,OAAO,EAAE;QACjC,MAAM,UAAU,GAAG,MAAM,QAAQ,CAAC,MAAM,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC;AACtE,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACpC,YAAA,IAAI,UAAU,CAAC,CAAC,CAAC,EAAE;AACjB,gBAAA,OAAO,IAAI,CAAC,CAAC,CAAC;;;AAGlB,QAAA,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC;;AAGpD;;;;AAIG;AACH,IAAA,MAAM,SAAS,CAAC,MAAA,GAA4B,EAAE,EAAA;QAC5C,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;AACvC,QAAA,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YAChB,MAAM,KAAK,CAAC,CAAA,oCAAA,EAAuC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAE,CAAA,CAAC;;AAE9E,QAAA,MAAM,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE;;;;ACxD1B;AACM,MAAO,iBAAkB,SAAQ,gBAAgB,CAAA;;AAErD,IAAA,OAAO,YAAY,GAAG,mBAAmB;AAEzC;;;;AAIG;AACH,IAAA,OAAO,IAAI,CAET,OAAA,GAAiC,EAAE,EAAA;AAEnC,QAAA,OAAO,IAAI,gBAAgB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,SAAS,CAAC,OAAO,EAAE,OAAO,CAAC,KAAK,EAAE,CAAC,OAAO,EAAE,KAAK,KAC1F,gBAAgB,CAAC,aAAa,CAAC,OAAO,CAAC,QAAQ,EAAE,EAAE,KAAK,CAAC,CAC1D;;;AAIH,IAAA,MAAM,QAAQ,GAAA;QACZ,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,IAAI,EAAE;;;AAInC,IAAA,MAAM,QAAQ,GAAA;AACZ,QAAA,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE;AAC9B,QAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC;;;AAIzC,IAAA,MAAM,UAAU,GAAA;AACd,QAAA,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE;AAC9B,QAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,sBAAsB,CAAC;;;AAI9C,IAAA,MAAM,KAAK,GAAA;QACT,MAAM,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,KAAK,EAAE;;;;ACtCrC;AACM,MAAO,qBAAsB,SAAQ,gCAAgC,CAAA;;AAEzE,IAAA,OAAO,YAAY,GAAG,wBAAwB;AAE9C;;;;;AAKG;AACH,IAAA,OAAO,IAAI,CAET,OAAA,GAAqC,EAAE,EAAA;AAEvC,QAAA,OAAO,IAAI,gBAAgB,CAAC,IAAI,EAAE,OAAO,CAAC;;;AAI5C,IAAA,MAAM,cAAc,GAAA;QAClB,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,IAAI,EAAE;;;;ACbrC;AACM,MAAO,mBAAoB,SAAQ,gBAAgB,CAAA;;AAEvD,IAAA,OAAO,YAAY,GAAG,sBAAsB;AAE5C;;;;;AAKG;AACH,IAAA,OAAO,IAAI,CAET,OAAA,GAAmC,EAAE,EAAA;AAErC,QAAA,OAAO,IAAI,gBAAgB,CAAC,IAAI,EAAE,OAAO,CAAC;;AAG5C;;;AAGG;AACH,IAAA,MAAM,QAAQ,CAAC,MAAA,GAAgC,EAAE,EAAA;AAC/C,QAAA,OAAO,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE;;;AAI7D,IAAA,MAAM,aAAa,GAAA;AACjB,QAAA,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,QAAQ,EAAE;QACnC,MAAM,QAAQ,GAAG,MAAM,QAAQ,CAAC,MAAM,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC;AACnE,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACrC,YAAA,IAAI,QAAQ,CAAC,CAAC,CAAC,EAAE;AACf,gBAAA,OAAO,KAAK,CAAC,CAAC,CAAC;;;AAGnB,QAAA,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC;;AAGnD;;;;AAIG;AACH,IAAA,MAAM,SAAS,CAAC,MAAA,GAAgC,EAAE,EAAA;QAChD,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;AACxC,QAAA,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YAChB,MAAM,KAAK,CAAC,CAAA,yCAAA,EAA4C,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAE,CAAA,CAAC;;AAEnF,QAAA,MAAM,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE;;;AAIvB,IAAA,MAAM,QAAQ,GAAA;AACZ,QAAA,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,aAAa,EAAE;AACvC,QAAA,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE;QAC9B,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC;QACxD,IAAI,CAAC,OAAO,EAAE;AACZ,YAAA,MAAM,KAAK,CAAC,wCAAwC,CAAC;;QAGvD,MAAM,MAAM,GAA8B,EAAC,QAAQ,EAAE,CAAI,CAAA,EAAA,OAAO,CAAE,CAAA,EAAC;AACnE,QAAA,OAAO,MAAM,IAAI,CAAC,0BAA0B,EAAE,CAAC,UAAU,CAAC,qBAAqB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE;;;;;;"}