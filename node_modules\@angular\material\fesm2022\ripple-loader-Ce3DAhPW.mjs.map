{"version": 3, "file": "ripple-loader-Ce3DAhPW.mjs", "sources": ["../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/core/private/ripple-loader.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {DOCUMENT} from '@angular/common';\nimport {\n  ANIMATION_MODULE_TYPE,\n  Injectable,\n  Injector,\n  NgZone,\n  OnDestroy,\n  RendererFactory2,\n  inject,\n} from '@angular/core';\nimport {\n  MAT_RIPPLE_GLOBAL_OPTIONS,\n  R<PERSON>ple<PERSON><PERSON>er,\n  RippleTarget,\n  defaultRippleAnimationConfig,\n} from '../ripple';\nimport {Platform, _bindEventWithOptions, _getEventTarget} from '@angular/cdk/platform';\nimport {_CdkPrivateStyleLoader} from '@angular/cdk/private';\n\n/** The options for the MatRippleLoader's event listeners. */\nconst eventListenerOptions = {capture: true};\n\n/**\n * The events that should trigger the initialization of the ripple.\n * Note that we use `mousedown`, rather than `click`, for mouse devices because\n * we can't rely on `mouseenter` in the shadow DOM and `click` happens too late.\n */\nconst rippleInteractionEvents = ['focus', 'mousedown', 'mouseenter', 'touchstart'];\n\n/** The attribute attached to a component whose ripple has not yet been initialized. */\nconst matRippleUninitialized = 'mat-ripple-loader-uninitialized';\n\n/** Additional classes that should be added to the ripple when it is rendered. */\nconst matRippleClassName = 'mat-ripple-loader-class-name';\n\n/** Whether the ripple should be centered. */\nconst matRippleCentered = 'mat-ripple-loader-centered';\n\n/** Whether the ripple should be disabled. */\nconst matRippleDisabled = 'mat-ripple-loader-disabled';\n\n/**\n * Handles attaching ripples on demand.\n *\n * This service allows us to avoid eagerly creating & attaching MatRipples.\n * It works by creating & attaching a ripple only when a component is first interacted with.\n *\n * @docs-private\n */\n@Injectable({providedIn: 'root'})\nexport class MatRippleLoader implements OnDestroy {\n  private _document = inject(DOCUMENT);\n  private _animationMode = inject(ANIMATION_MODULE_TYPE, {optional: true});\n  private _globalRippleOptions = inject(MAT_RIPPLE_GLOBAL_OPTIONS, {optional: true});\n  private _platform = inject(Platform);\n  private _ngZone = inject(NgZone);\n  private _injector = inject(Injector);\n  private _eventCleanups: (() => void)[];\n  private _hosts = new Map<\n    HTMLElement,\n    {renderer: RippleRenderer; target: RippleTarget; hasSetUpEvents: boolean}\n  >();\n\n  constructor() {\n    const renderer = inject(RendererFactory2).createRenderer(null, null);\n\n    this._eventCleanups = this._ngZone.runOutsideAngular(() => {\n      return rippleInteractionEvents.map(name =>\n        _bindEventWithOptions(\n          renderer,\n          this._document,\n          name,\n          this._onInteraction,\n          eventListenerOptions,\n        ),\n      );\n    });\n  }\n\n  ngOnDestroy(): void {\n    const hosts = this._hosts.keys();\n\n    for (const host of hosts) {\n      this.destroyRipple(host);\n    }\n\n    this._eventCleanups.forEach(cleanup => cleanup());\n  }\n\n  /**\n   * Configures the ripple that will be rendered by the ripple loader.\n   *\n   * Stores the given information about how the ripple should be configured on the host\n   * element so that it can later be retrived & used when the ripple is actually created.\n   */\n  configureRipple(\n    host: HTMLElement,\n    config: {\n      className?: string;\n      centered?: boolean;\n      disabled?: boolean;\n    },\n  ): void {\n    // Indicates that the ripple has not yet been rendered for this component.\n    host.setAttribute(matRippleUninitialized, this._globalRippleOptions?.namespace ?? '');\n\n    // Store the additional class name(s) that should be added to the ripple element.\n    if (config.className || !host.hasAttribute(matRippleClassName)) {\n      host.setAttribute(matRippleClassName, config.className || '');\n    }\n\n    // Store whether the ripple should be centered.\n    if (config.centered) {\n      host.setAttribute(matRippleCentered, '');\n    }\n\n    if (config.disabled) {\n      host.setAttribute(matRippleDisabled, '');\n    }\n  }\n\n  /** Sets the disabled state on the ripple instance corresponding to the given host element. */\n  setDisabled(host: HTMLElement, disabled: boolean): void {\n    const ripple = this._hosts.get(host);\n\n    // If the ripple has already been instantiated, just disable it.\n    if (ripple) {\n      ripple.target.rippleDisabled = disabled;\n\n      if (!disabled && !ripple.hasSetUpEvents) {\n        ripple.hasSetUpEvents = true;\n        ripple.renderer.setupTriggerEvents(host);\n      }\n    } else if (disabled) {\n      // Otherwise, set an attribute so we know what the\n      // disabled state should be when the ripple is initialized.\n      host.setAttribute(matRippleDisabled, '');\n    } else {\n      host.removeAttribute(matRippleDisabled);\n    }\n  }\n\n  /**\n   * Handles creating and attaching component internals\n   * when a component is initially interacted with.\n   */\n  private _onInteraction = (event: Event) => {\n    const eventTarget = _getEventTarget(event);\n\n    if (eventTarget instanceof HTMLElement) {\n      // TODO(wagnermaciel): Consider batching these events to improve runtime performance.\n      const element = eventTarget.closest(\n        `[${matRippleUninitialized}=\"${this._globalRippleOptions?.namespace ?? ''}\"]`,\n      );\n\n      if (element) {\n        this._createRipple(element as HTMLElement);\n      }\n    }\n  };\n\n  /** Creates a MatRipple and appends it to the given element. */\n  private _createRipple(host: HTMLElement): void {\n    if (!this._document || this._hosts.has(host)) {\n      return;\n    }\n\n    // Create the ripple element.\n    host.querySelector('.mat-ripple')?.remove();\n    const rippleEl = this._document.createElement('span');\n    rippleEl.classList.add('mat-ripple', host.getAttribute(matRippleClassName)!);\n    host.append(rippleEl);\n\n    const isNoopAnimations = this._animationMode === 'NoopAnimations';\n    const globalOptions = this._globalRippleOptions;\n    const enterDuration = isNoopAnimations\n      ? 0\n      : globalOptions?.animation?.enterDuration ?? defaultRippleAnimationConfig.enterDuration;\n    const exitDuration = isNoopAnimations\n      ? 0\n      : globalOptions?.animation?.exitDuration ?? defaultRippleAnimationConfig.exitDuration;\n    const target: RippleTarget = {\n      rippleDisabled:\n        isNoopAnimations || globalOptions?.disabled || host.hasAttribute(matRippleDisabled),\n      rippleConfig: {\n        centered: host.hasAttribute(matRippleCentered),\n        terminateOnPointerUp: globalOptions?.terminateOnPointerUp,\n        animation: {\n          enterDuration,\n          exitDuration,\n        },\n      },\n    };\n\n    const renderer = new RippleRenderer(\n      target,\n      this._ngZone,\n      rippleEl,\n      this._platform,\n      this._injector,\n    );\n    const hasSetUpEvents = !target.rippleDisabled;\n\n    if (hasSetUpEvents) {\n      renderer.setupTriggerEvents(host);\n    }\n\n    this._hosts.set(host, {\n      target,\n      renderer,\n      hasSetUpEvents,\n    });\n\n    host.removeAttribute(matRippleUninitialized);\n  }\n\n  destroyRipple(host: HTMLElement): void {\n    const ripple = this._hosts.get(host);\n\n    if (ripple) {\n      ripple.renderer._removeTriggerEvents();\n      this._hosts.delete(host);\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;;;AA2BA;AACA,MAAM,oBAAoB,GAAG,EAAC,OAAO,EAAE,IAAI,EAAC;AAE5C;;;;AAIG;AACH,MAAM,uBAAuB,GAAG,CAAC,OAAO,EAAE,WAAW,EAAE,YAAY,EAAE,YAAY,CAAC;AAElF;AACA,MAAM,sBAAsB,GAAG,iCAAiC;AAEhE;AACA,MAAM,kBAAkB,GAAG,8BAA8B;AAEzD;AACA,MAAM,iBAAiB,GAAG,4BAA4B;AAEtD;AACA,MAAM,iBAAiB,GAAG,4BAA4B;AAEtD;;;;;;;AAOG;MAEU,eAAe,CAAA;AAClB,IAAA,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC;IAC5B,cAAc,GAAG,MAAM,CAAC,qBAAqB,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;IAChE,oBAAoB,GAAG,MAAM,CAAC,yBAAyB,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;AAC1E,IAAA,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC;AAC5B,IAAA,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;AACxB,IAAA,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC;AAC5B,IAAA,cAAc;AACd,IAAA,MAAM,GAAG,IAAI,GAAG,EAGrB;AAEH,IAAA,WAAA,GAAA;AACE,QAAA,MAAM,QAAQ,GAAG,MAAM,CAAC,gBAAgB,CAAC,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC;QAEpE,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;YACxD,OAAO,uBAAuB,CAAC,GAAG,CAAC,IAAI,IACrC,qBAAqB,CACnB,QAAQ,EACR,IAAI,CAAC,SAAS,EACd,IAAI,EACJ,IAAI,CAAC,cAAc,EACnB,oBAAoB,CACrB,CACF;AACH,SAAC,CAAC;;IAGJ,WAAW,GAAA;QACT,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE;AAEhC,QAAA,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;AACxB,YAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC;;AAG1B,QAAA,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,OAAO,IAAI,OAAO,EAAE,CAAC;;AAGnD;;;;;AAKG;IACH,eAAe,CACb,IAAiB,EACjB,MAIC,EAAA;;AAGD,QAAA,IAAI,CAAC,YAAY,CAAC,sBAAsB,EAAE,IAAI,CAAC,oBAAoB,EAAE,SAAS,IAAI,EAAE,CAAC;;AAGrF,QAAA,IAAI,MAAM,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC,EAAE;YAC9D,IAAI,CAAC,YAAY,CAAC,kBAAkB,EAAE,MAAM,CAAC,SAAS,IAAI,EAAE,CAAC;;;AAI/D,QAAA,IAAI,MAAM,CAAC,QAAQ,EAAE;AACnB,YAAA,IAAI,CAAC,YAAY,CAAC,iBAAiB,EAAE,EAAE,CAAC;;AAG1C,QAAA,IAAI,MAAM,CAAC,QAAQ,EAAE;AACnB,YAAA,IAAI,CAAC,YAAY,CAAC,iBAAiB,EAAE,EAAE,CAAC;;;;IAK5C,WAAW,CAAC,IAAiB,EAAE,QAAiB,EAAA;QAC9C,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC;;QAGpC,IAAI,MAAM,EAAE;AACV,YAAA,MAAM,CAAC,MAAM,CAAC,cAAc,GAAG,QAAQ;YAEvC,IAAI,CAAC,QAAQ,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE;AACvC,gBAAA,MAAM,CAAC,cAAc,GAAG,IAAI;AAC5B,gBAAA,MAAM,CAAC,QAAQ,CAAC,kBAAkB,CAAC,IAAI,CAAC;;;aAErC,IAAI,QAAQ,EAAE;;;AAGnB,YAAA,IAAI,CAAC,YAAY,CAAC,iBAAiB,EAAE,EAAE,CAAC;;aACnC;AACL,YAAA,IAAI,CAAC,eAAe,CAAC,iBAAiB,CAAC;;;AAI3C;;;AAGG;AACK,IAAA,cAAc,GAAG,CAAC,KAAY,KAAI;AACxC,QAAA,MAAM,WAAW,GAAG,eAAe,CAAC,KAAK,CAAC;AAE1C,QAAA,IAAI,WAAW,YAAY,WAAW,EAAE;;AAEtC,YAAA,MAAM,OAAO,GAAG,WAAW,CAAC,OAAO,CACjC,IAAI,sBAAsB,CAAA,EAAA,EAAK,IAAI,CAAC,oBAAoB,EAAE,SAAS,IAAI,EAAE,CAAA,EAAA,CAAI,CAC9E;YAED,IAAI,OAAO,EAAE;AACX,gBAAA,IAAI,CAAC,aAAa,CAAC,OAAsB,CAAC;;;AAGhD,KAAC;;AAGO,IAAA,aAAa,CAAC,IAAiB,EAAA;AACrC,QAAA,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;YAC5C;;;QAIF,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,EAAE,MAAM,EAAE;QAC3C,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,MAAM,CAAC;AACrD,QAAA,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,YAAY,EAAE,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAE,CAAC;AAC5E,QAAA,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC;AAErB,QAAA,MAAM,gBAAgB,GAAG,IAAI,CAAC,cAAc,KAAK,gBAAgB;AACjE,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,oBAAoB;QAC/C,MAAM,aAAa,GAAG;AACpB,cAAE;cACA,aAAa,EAAE,SAAS,EAAE,aAAa,IAAI,4BAA4B,CAAC,aAAa;QACzF,MAAM,YAAY,GAAG;AACnB,cAAE;cACA,aAAa,EAAE,SAAS,EAAE,YAAY,IAAI,4BAA4B,CAAC,YAAY;AACvF,QAAA,MAAM,MAAM,GAAiB;AAC3B,YAAA,cAAc,EACZ,gBAAgB,IAAI,aAAa,EAAE,QAAQ,IAAI,IAAI,CAAC,YAAY,CAAC,iBAAiB,CAAC;AACrF,YAAA,YAAY,EAAE;AACZ,gBAAA,QAAQ,EAAE,IAAI,CAAC,YAAY,CAAC,iBAAiB,CAAC;gBAC9C,oBAAoB,EAAE,aAAa,EAAE,oBAAoB;AACzD,gBAAA,SAAS,EAAE;oBACT,aAAa;oBACb,YAAY;AACb,iBAAA;AACF,aAAA;SACF;QAED,MAAM,QAAQ,GAAG,IAAI,cAAc,CACjC,MAAM,EACN,IAAI,CAAC,OAAO,EACZ,QAAQ,EACR,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,SAAS,CACf;AACD,QAAA,MAAM,cAAc,GAAG,CAAC,MAAM,CAAC,cAAc;QAE7C,IAAI,cAAc,EAAE;AAClB,YAAA,QAAQ,CAAC,kBAAkB,CAAC,IAAI,CAAC;;AAGnC,QAAA,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE;YACpB,MAAM;YACN,QAAQ;YACR,cAAc;AACf,SAAA,CAAC;AAEF,QAAA,IAAI,CAAC,eAAe,CAAC,sBAAsB,CAAC;;AAG9C,IAAA,aAAa,CAAC,IAAiB,EAAA;QAC7B,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC;QAEpC,IAAI,MAAM,EAAE;AACV,YAAA,MAAM,CAAC,QAAQ,CAAC,oBAAoB,EAAE;AACtC,YAAA,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC;;;uGA3KjB,eAAe,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;AAAf,IAAA,OAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,eAAe,cADH,MAAM,EAAA,CAAA;;2FAClB,eAAe,EAAA,UAAA,EAAA,CAAA;kBAD3B,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC;;;;;"}