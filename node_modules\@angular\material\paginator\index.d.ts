import * as i0 from '@angular/core';
import { MatButtonModule } from '../button/index.js';
import { M as MatSelectModule } from '../module.d-CyLvt0Fz.js';
import { M as MatTooltipModule } from '../module.d-C9bwr5Wr.js';
import { M as MatPaginator } from '../paginator.d-CexYxFq4.js';
export { c as MAT_PAGINATOR_DEFAULT_OPTIONS, f as MAT_PAGINATOR_INTL_PROVIDER, e as MAT_PAGINATOR_INTL_PROVIDER_FACTORY, b as MatPaginatorDefaultOptions, d as MatPaginatorIntl, a as MatPaginatorSelectConfig, P as PageEvent } from '../paginator.d-CexYxFq4.js';
import '@angular/cdk/a11y';
import '../palette.d-BSSFKjO6.js';
import '../ripple-loader.d-C3HznB6v.js';
import '../common-module.d-C8xzHJDr.js';
import '@angular/cdk/bidi';
import '../index.d-DG9eDM2-.js';
import '../ripple.d-BxTUZJt7.js';
import '@angular/cdk/platform';
import '@angular/cdk/overlay';
import '../index.d-CwEYxGJi.js';
import '../pseudo-checkbox-module.d-DL5oxSJM.js';
import '../option.d-BVGX3edu.js';
import 'rxjs';
import '@angular/cdk/collections';
import '@angular/cdk/scrolling';
import '@angular/forms';
import '../error-options.d-CGdTZUYk.js';
import '../form-field.d-CMA_QQ0R.js';
import '@angular/cdk/coercion';
import '../form-field-control.d-QxD-9xJ3.js';
import '../module.d-1ZCYe5BH.js';
import '@angular/cdk/observers';

declare class MatPaginatorModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<MatPaginatorModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<MatPaginatorModule, never, [typeof MatButtonModule, typeof MatSelectModule, typeof MatTooltipModule, typeof MatPaginator], [typeof MatPaginator]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<MatPaginatorModule>;
}

export { MatPaginator, MatPaginatorModule };
