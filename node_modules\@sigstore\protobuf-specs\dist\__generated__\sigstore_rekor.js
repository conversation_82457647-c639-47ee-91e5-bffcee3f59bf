"use strict";
// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.0
//   protoc               v6.30.2
// source: sigstore_rekor.proto
Object.defineProperty(exports, "__esModule", { value: true });
exports.TransparencyLogEntry = exports.InclusionPromise = exports.InclusionProof = exports.Checkpoint = exports.KindVersion = void 0;
/* eslint-disable */
const sigstore_common_1 = require("./sigstore_common");
exports.KindVersion = {
    fromJSON(object) {
        return {
            kind: isSet(object.kind) ? globalThis.String(object.kind) : "",
            version: isSet(object.version) ? globalThis.String(object.version) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.kind !== "") {
            obj.kind = message.kind;
        }
        if (message.version !== "") {
            obj.version = message.version;
        }
        return obj;
    },
};
exports.Checkpoint = {
    fromJSON(object) {
        return { envelope: isSet(object.envelope) ? globalThis.String(object.envelope) : "" };
    },
    toJSON(message) {
        const obj = {};
        if (message.envelope !== "") {
            obj.envelope = message.envelope;
        }
        return obj;
    },
};
exports.InclusionProof = {
    fromJSON(object) {
        return {
            logIndex: isSet(object.logIndex) ? globalThis.String(object.logIndex) : "0",
            rootHash: isSet(object.rootHash) ? Buffer.from(bytesFromBase64(object.rootHash)) : Buffer.alloc(0),
            treeSize: isSet(object.treeSize) ? globalThis.String(object.treeSize) : "0",
            hashes: globalThis.Array.isArray(object?.hashes)
                ? object.hashes.map((e) => Buffer.from(bytesFromBase64(e)))
                : [],
            checkpoint: isSet(object.checkpoint) ? exports.Checkpoint.fromJSON(object.checkpoint) : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.logIndex !== "0") {
            obj.logIndex = message.logIndex;
        }
        if (message.rootHash.length !== 0) {
            obj.rootHash = base64FromBytes(message.rootHash);
        }
        if (message.treeSize !== "0") {
            obj.treeSize = message.treeSize;
        }
        if (message.hashes?.length) {
            obj.hashes = message.hashes.map((e) => base64FromBytes(e));
        }
        if (message.checkpoint !== undefined) {
            obj.checkpoint = exports.Checkpoint.toJSON(message.checkpoint);
        }
        return obj;
    },
};
exports.InclusionPromise = {
    fromJSON(object) {
        return {
            signedEntryTimestamp: isSet(object.signedEntryTimestamp)
                ? Buffer.from(bytesFromBase64(object.signedEntryTimestamp))
                : Buffer.alloc(0),
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.signedEntryTimestamp.length !== 0) {
            obj.signedEntryTimestamp = base64FromBytes(message.signedEntryTimestamp);
        }
        return obj;
    },
};
exports.TransparencyLogEntry = {
    fromJSON(object) {
        return {
            logIndex: isSet(object.logIndex) ? globalThis.String(object.logIndex) : "0",
            logId: isSet(object.logId) ? sigstore_common_1.LogId.fromJSON(object.logId) : undefined,
            kindVersion: isSet(object.kindVersion) ? exports.KindVersion.fromJSON(object.kindVersion) : undefined,
            integratedTime: isSet(object.integratedTime) ? globalThis.String(object.integratedTime) : "0",
            inclusionPromise: isSet(object.inclusionPromise) ? exports.InclusionPromise.fromJSON(object.inclusionPromise) : undefined,
            inclusionProof: isSet(object.inclusionProof) ? exports.InclusionProof.fromJSON(object.inclusionProof) : undefined,
            canonicalizedBody: isSet(object.canonicalizedBody)
                ? Buffer.from(bytesFromBase64(object.canonicalizedBody))
                : Buffer.alloc(0),
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.logIndex !== "0") {
            obj.logIndex = message.logIndex;
        }
        if (message.logId !== undefined) {
            obj.logId = sigstore_common_1.LogId.toJSON(message.logId);
        }
        if (message.kindVersion !== undefined) {
            obj.kindVersion = exports.KindVersion.toJSON(message.kindVersion);
        }
        if (message.integratedTime !== "0") {
            obj.integratedTime = message.integratedTime;
        }
        if (message.inclusionPromise !== undefined) {
            obj.inclusionPromise = exports.InclusionPromise.toJSON(message.inclusionPromise);
        }
        if (message.inclusionProof !== undefined) {
            obj.inclusionProof = exports.InclusionProof.toJSON(message.inclusionProof);
        }
        if (message.canonicalizedBody.length !== 0) {
            obj.canonicalizedBody = base64FromBytes(message.canonicalizedBody);
        }
        return obj;
    },
};
function bytesFromBase64(b64) {
    return Uint8Array.from(globalThis.Buffer.from(b64, "base64"));
}
function base64FromBytes(arr) {
    return globalThis.Buffer.from(arr).toString("base64");
}
function isSet(value) {
    return value !== null && value !== undefined;
}
