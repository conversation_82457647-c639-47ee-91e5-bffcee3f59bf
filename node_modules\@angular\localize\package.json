{"name": "@angular/localize", "version": "19.2.13", "description": "Angular - library for localizing messages", "bin": {"localize-translate": "./tools/bundles/src/translate/cli.js", "localize-extract": "./tools/bundles/src/extract/cli.js", "localize-migrate": "./tools/bundles/src/migrate/cli.js"}, "exports": {"./tools": {"types": "./tools/index.d.ts", "default": "./tools/bundles/index.js"}, "./package.json": {"default": "./package.json"}, ".": {"types": "./index.d.ts", "default": "./fesm2022/localize.mjs"}, "./init": {"types": "./init/index.d.ts", "default": "./fesm2022/init.mjs"}}, "author": "angular", "license": "MIT", "engines": {"node": "^18.19.1 || ^20.11.1 || >=22.0.0"}, "repository": {"type": "git", "url": "https://github.com/angular/angular.git"}, "schematics": "./schematics/collection.json", "ng-update": {"packageGroup": ["@angular/core", "@angular/bazel", "@angular/common", "@angular/compiler", "@angular/compiler-cli", "@angular/animations", "@angular/elements", "@angular/platform-browser", "@angular/platform-browser-dynamic", "@angular/forms", "@angular/platform-server", "@angular/upgrade", "@angular/router", "@angular/language-service", "@angular/localize", "@angular/service-worker"]}, "ng-add": {"save": "devDependencies"}, "sideEffects": ["./fesm2022/init.mjs"], "dependencies": {"@babel/core": "7.26.9", "@types/babel__core": "7.20.5", "fast-glob": "3.3.3", "yargs": "^17.2.1"}, "peerDependencies": {"@angular/compiler": "19.2.13", "@angular/compiler-cli": "19.2.13"}, "module": "./fesm2022/localize.mjs", "typings": "./index.d.ts", "type": "module"}