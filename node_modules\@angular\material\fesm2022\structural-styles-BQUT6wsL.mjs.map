{"version": 3, "file": "structural-styles-BQUT6wsL.mjs", "sources": ["../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/core/focus-indicators/structural-styles.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {ChangeDetectionStrategy, Component, ViewEncapsulation} from '@angular/core';\n\n/**\n * Component used to load structural styles for focus indicators.\n * @docs-private\n */\n@Component({\n  selector: 'structural-styles',\n  styleUrl: 'structural-styles.css',\n  encapsulation: ViewEncapsulation.None,\n  template: '',\n  changeDetection: ChangeDetectionStrategy.OnPush,\n})\nexport class _StructuralStylesLoader {}\n"], "names": [], "mappings": ";;;AAUA;;;AAGG;MAQU,uBAAuB,CAAA;uGAAvB,uBAAuB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAvB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,uBAAuB,6EAHxB,EAAE,EAAA,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,gkBAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAGD,uBAAuB,EAAA,UAAA,EAAA,CAAA;kBAPnC,SAAS;+BACE,mBAAmB,EAAA,aAAA,EAEd,iBAAiB,CAAC,IAAI,YAC3B,EAAE,EAAA,eAAA,EACK,uBAAuB,CAAC,MAAM,EAAA,MAAA,EAAA,CAAA,gkBAAA,CAAA,EAAA;;;;;"}