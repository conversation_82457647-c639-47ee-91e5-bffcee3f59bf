{"version": 3, "file": "testing.mjs", "sources": ["../../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/toolbar/testing/toolbar-harness.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {ContentContainerComponentHarness, HarnessPredicate, parallel} from '@angular/cdk/testing';\nimport {ToolbarHarnessFilters} from './toolbar-harness-filters';\n\n/** Selectors for different sections of the mat-toolbar that contain user content. */\nexport enum MatToolbarSection {\n  ROW = '.mat-toolbar-row',\n}\n\n/** Harness for interacting with a standard mat-toolbar in tests. */\nexport class MatToolbarHarness extends ContentContainerComponentHarness<MatToolbarSection> {\n  static hostSelector = '.mat-toolbar';\n\n  private _getRows = this.locatorForAll(MatToolbarSection.ROW);\n\n  /**\n   * Gets a `HarnessPredicate` that can be used to search for a `MatToolbarHarness` that meets\n   * certain criteria.\n   * @param options Options for filtering which card instances are considered a match.\n   * @return a `HarnessPredicate` configured with the given options.\n   */\n  static with(options: ToolbarHarnessFilters = {}): HarnessPredicate<MatToolbarHarness> {\n    return new HarnessPredicate(MatToolbarHarness, options).addOption(\n      'text',\n      options.text,\n      (harness, text) => HarnessPredicate.stringMatches(harness._getText(), text),\n    );\n  }\n\n  /** Whether the toolbar has multiple rows. */\n  async hasMultipleRows(): Promise<boolean> {\n    return (await this.host()).hasClass('mat-toolbar-multiple-rows');\n  }\n\n  /** Gets all of the toolbar's content as text. */\n  private async _getText(): Promise<string> {\n    return (await this.host()).text();\n  }\n\n  /** Gets the text of each row in the toolbar. */\n  async getRowsAsText(): Promise<string[]> {\n    const rows = await this._getRows();\n    return parallel(() => (rows.length ? rows.map(r => r.text()) : [this._getText()]));\n  }\n}\n"], "names": [], "mappings": ";;AAWA;IACY;AAAZ,CAAA,UAAY,iBAAiB,EAAA;AAC3B,IAAA,iBAAA,CAAA,KAAA,CAAA,GAAA,kBAAwB;AAC1B,CAAC,EAFW,iBAAiB,KAAjB,iBAAiB,GAE5B,EAAA,CAAA,CAAA;AAED;AACM,MAAO,iBAAkB,SAAQ,gCAAmD,CAAA;AACxF,IAAA,OAAO,YAAY,GAAG,cAAc;IAE5B,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,GAAG,CAAC;AAE5D;;;;;AAKG;AACH,IAAA,OAAO,IAAI,CAAC,OAAA,GAAiC,EAAE,EAAA;AAC7C,QAAA,OAAO,IAAI,gBAAgB,CAAC,iBAAiB,EAAE,OAAO,CAAC,CAAC,SAAS,CAC/D,MAAM,EACN,OAAO,CAAC,IAAI,EACZ,CAAC,OAAO,EAAE,IAAI,KAAK,gBAAgB,CAAC,aAAa,CAAC,OAAO,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,CAC5E;;;AAIH,IAAA,MAAM,eAAe,GAAA;AACnB,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,QAAQ,CAAC,2BAA2B,CAAC;;;AAI1D,IAAA,MAAM,QAAQ,GAAA;QACpB,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,IAAI,EAAE;;;AAInC,IAAA,MAAM,aAAa,GAAA;AACjB,QAAA,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,QAAQ,EAAE;AAClC,QAAA,OAAO,QAAQ,CAAC,OAAO,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;;;;;;"}