{"version": 3, "sources": ["../src/ngtsc/perf/src/api.ts", "../src/ngtsc/perf/src/noop.ts", "../src/ngtsc/perf/src/clock.ts", "../src/ngtsc/perf/src/recorder.ts"], "mappings": ";;;;;;AAWA,IAAY;CAAZ,SAAYA,YAAS;AAInB,EAAAA,WAAAA,WAAA,iBAAA,KAAA;AAOA,EAAAA,WAAAA,WAAA,WAAA,KAAA;AAQA,EAAAA,WAAAA,WAAA,6BAAA,KAAA;AAOA,EAAAA,WAAAA,WAAA,oBAAA,KAAA;AAOA,EAAAA,WAAAA,WAAA,oBAAA,KAAA;AAKA,EAAAA,WAAAA,WAAA,2BAAA,KAAA;AAKA,EAAAA,WAAAA,WAAA,cAAA,KAAA;AAMA,EAAAA,WAAAA,WAAA,aAAA,KAAA;AAKA,EAAAA,WAAAA,WAAA,oBAAA,KAAA;AAKA,EAAAA,WAAAA,WAAA,mBAAA,KAAA;AAKA,EAAAA,WAAAA,WAAA,sBAAA,MAAA;AAMA,EAAAA,WAAAA,WAAA,oBAAA,MAAA;AAQA,EAAAA,WAAAA,WAAA,aAAA,MAAA;AAKA,EAAAA,WAAAA,WAAA,uBAAA,MAAA;AAKA,EAAAA,WAAAA,WAAA,oBAAA,MAAA;AAKA,EAAAA,WAAAA,WAAA,eAAA,MAAA;AAMA,EAAAA,WAAAA,WAAA,4BAAA,MAAA;AAKA,EAAAA,WAAAA,WAAA,iBAAA,MAAA;AAMA,EAAAA,WAAAA,WAAA,kBAAA,MAAA;AAMA,EAAAA,WAAAA,WAAA,mBAAA,MAAA;AAMA,EAAAA,WAAAA,WAAA,WAAA,MAAA;AAKA,EAAAA,WAAAA,WAAA,mBAAA,MAAA;AAMA,EAAAA,WAAAA,WAAA,0BAAA,MAAA;AAKA,EAAAA,WAAAA,WAAA,qBAAA,MAAA;AAKA,EAAAA,WAAAA,WAAA,oBAAA,MAAA;AAKA,EAAAA,WAAAA,WAAA,UAAA,MAAA;AAKA,EAAAA,WAAAA,WAAA,iBAAA,MAAA;AAKA,EAAAA,WAAAA,WAAA,oBAAA,MAAA;AAKA,EAAAA,WAAAA,WAAA,qCAAA,MAAA;AAKA,EAAAA,WAAAA,WAAA,wBAAA,MAAA;AACF,GAzKY,cAAA,YAAS,CAAA,EAAA;AA8KrB,IAAY;CAAZ,SAAYC,YAAS;AAInB,EAAAA,WAAAA,WAAA,kBAAA,KAAA;AAKA,EAAAA,WAAAA,WAAA,iBAAA,KAAA;AAKA,EAAAA,WAAAA,WAAA,sBAAA,KAAA;AAKA,EAAAA,WAAAA,WAAA,sBAAA,KAAA;AAKA,EAAAA,WAAAA,WAAA,uBAAA,KAAA;AAKA,EAAAA,WAAAA,WAAA,qBAAA,KAAA;AAKA,EAAAA,WAAAA,WAAA,iBAAA,KAAA;AAOA,EAAAA,WAAAA,WAAA,kBAAA,KAAA;AAMA,EAAAA,WAAAA,WAAA,wBAAA,KAAA;AAKA,EAAAA,WAAAA,WAAA,8BAAA,KAAA;AAMA,EAAAA,WAAAA,WAAA,6BAAA,MAAA;AAMA,EAAAA,WAAAA,WAAA,6BAAA,MAAA;AAKA,EAAAA,WAAAA,WAAA,iBAAA,MAAA;AAMA,EAAAA,WAAAA,WAAA,6BAAA,MAAA;AAMA,EAAAA,WAAAA,WAAA,wBAAA,MAAA;AAMA,EAAAA,WAAAA,WAAA,4BAAA,MAAA;AAKA,EAAAA,WAAAA,WAAA,wBAAA,MAAA;AAKA,EAAAA,WAAAA,WAAA,oBAAA,MAAA;AAKA,EAAAA,WAAAA,WAAA,UAAA,MAAA;AACF,GAvGY,cAAA,YAAS,CAAA,EAAA;AA6GrB,IAAY;CAAZ,SAAYC,iBAAc;AAKxB,EAAAA,gBAAAA,gBAAA,aAAA,KAAA;AAKA,EAAAA,gBAAAA,gBAAA,6BAAA,KAAA;AASA,EAAAA,gBAAAA,gBAAA,iBAAA,KAAA;AAKA,EAAAA,gBAAAA,gBAAA,cAAA,KAAA;AAKA,EAAAA,gBAAAA,gBAAA,aAAA,KAAA;AAKA,EAAAA,gBAAAA,gBAAA,mBAAA,KAAA;AAKA,EAAAA,gBAAAA,gBAAA,sBAAA,KAAA;AAQA,EAAAA,gBAAAA,gBAAA,aAAA,KAAA;AAKA,EAAAA,gBAAAA,gBAAA,UAAA,KAAA;AAKA,EAAAA,gBAAAA,gBAAA,UAAA,KAAA;AACF,GA1DY,mBAAA,iBAAc,CAAA,EAAA;;;AC7R1B,IAAM,mBAAN,MAAsB;EACpB,aAAU;EAAU;EAEpB,SAAM;EAAU;EAEhB,QAAK;AACH,WAAO,UAAU;EACnB;EAEA,QAAW,OAAkB,IAAW;AACtC,WAAO,GAAE;EACX;EAEA,QAAK;EAAU;;AAGV,IAAM,qBAAmC,IAAI,iBAAgB;;;ACZ9D,SAAU,OAAI;AAClB,SAAO,QAAQ,OAAM;AACvB;AAEM,SAAU,kBAAkBC,OAAY;AAC5C,QAAM,QAAQ,QAAQ,OAAOA,KAAI;AACjC,SAAO,MAAM,KAAK,MAAU,KAAK,MAAM,MAAM,KAAK,GAAI;AACxD;;;ACIM,IAAO,qBAAP,MAAyB;EAeD;EAdpB;EACA;EACA;EAEA,eAAe,UAAU;EACzB;EAKR,OAAO,cAAW;AAChB,WAAO,IAAI,mBAAmB,KAAI,CAAE;EACtC;EAEA,YAA4B,UAAgB;AAAhB,SAAA,WAAA;AAC1B,SAAK,sBAAsB,KAAK;AAChC,SAAK,WAAW,MAAM,UAAU,IAAI,EAAE,KAAK,CAAC;AAC5C,SAAK,YAAY,MAAM,UAAU,IAAI,EAAE,KAAK,CAAC;AAC7C,SAAK,QAAQ,MAAM,eAAe,IAAI,EAAE,KAAK,CAAC;AAG9C,SAAK,OAAO,eAAe,OAAO;EACpC;EAEA,QAAK;AACH,SAAK,WAAW,MAAM,UAAU,IAAI,EAAE,KAAK,CAAC;AAC5C,SAAK,YAAY,MAAM,UAAU,IAAI,EAAE,KAAK,CAAC;AAC7C,SAAK,QAAQ,MAAM,eAAe,IAAI,EAAE,KAAK,CAAC;AAC9C,SAAK,WAAW,KAAI;AACpB,SAAK,eAAe,UAAU;AAC9B,SAAK,sBAAsB,KAAK;EAClC;EAEA,OAAO,OAAqB;AAC1B,SAAK,MAAM,SAAS,QAAQ,YAAW,EAAG;EAC5C;EAEA,MAAM,OAAgB;AACpB,UAAM,WAAW,KAAK;AACtB,SAAK,UAAU,KAAK,iBAAiB,kBAAkB,KAAK,mBAAmB;AAC/E,SAAK,eAAe;AACpB,SAAK,sBAAsB,KAAI;AAC/B,WAAO;EACT;EAEA,QAAW,OAAkB,IAAW;AACtC,UAAM,gBAAgB,KAAK,MAAM,KAAK;AACtC,QAAI;AACF,aAAO,GAAE;IACX;AACE,WAAK,MAAM,aAAa;IAC1B;EACF;EAEA,WAAW,SAAoB,cAAsB,GAAC;AACpD,SAAK,SAAS,YAAY;EAC5B;EAKA,WAAQ;AAEN,SAAK,MAAM,UAAU,WAAW;AAEhC,UAAM,UAAuB;MAC3B,QAAQ,CAAA;MACR,QAAQ,CAAA;MACR,QAAQ,CAAA;;AAGV,aAAS,IAAI,GAAG,IAAI,KAAK,UAAU,QAAQ,KAAK;AAC9C,UAAI,KAAK,UAAU,KAAK,GAAG;AACzB,gBAAQ,OAAO,UAAU,MAAM,KAAK,UAAU;MAChD;IACF;AAEA,aAAS,IAAI,GAAG,IAAI,KAAK,UAAU,QAAQ,KAAK;AAC9C,UAAI,KAAK,SAAS,KAAK,GAAG;AACxB,gBAAQ,OAAO,UAAU,MAAM,KAAK,SAAS;MAC/C;IACF;AAEA,aAAS,IAAI,GAAG,IAAI,KAAK,MAAM,QAAQ,KAAK;AAC1C,UAAI,KAAK,MAAM,KAAK,GAAG;AACrB,gBAAQ,OAAO,eAAe,MAAM,KAAK,MAAM;MACjD;IACF;AAEA,WAAO;EACT;;AAUI,IAAO,yBAAP,MAA6B;EACd;EAAnB,YAAmB,QAAoB;AAApB,SAAA,SAAA;EAAuB;EAE1C,WAAW,SAAoB,aAAoB;AACjD,SAAK,OAAO,WAAW,SAAS,WAAW;EAC7C;EAEA,MAAM,OAAgB;AACpB,WAAO,KAAK,OAAO,MAAM,KAAK;EAChC;EAEA,QAAW,OAAkB,IAAW;AAGtC,UAAM,gBAAgB,KAAK,OAAO,MAAM,KAAK;AAC7C,QAAI;AACF,aAAO,GAAE;IACX;AACE,WAAK,OAAO,MAAM,aAAa;IACjC;EACF;EAEA,OAAO,OAAqB;AAC1B,SAAK,OAAO,OAAO,KAAK;EAC1B;EAEA,QAAK;AACH,SAAK,OAAO,MAAK;EACnB;;", "names": ["PerfPhase", "PerfEvent", "PerfCheckpoint", "mark"]}