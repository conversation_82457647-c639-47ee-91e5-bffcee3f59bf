
      import {createRequire as __cjsCompatRequire} from 'module';
      const require = __cjsCompatRequire(import.meta.url);
    
import {
  ArbTranslationSerializer,
  LegacyMessageIdMigrationSerializer,
  MessageExtractor,
  SimpleJsonTranslationSerializer,
  Xliff1TranslationSerializer,
  Xliff2TranslationSerializer,
  XmbTranslationSerializer,
  checkDuplicateMessages
} from "./chunk-AU6TZOIN.js";
import {
  ArbTranslationParser,
  SimpleJsonTranslationParser,
  Xliff1TranslationParser,
  Xliff2TranslationParser,
  XtbTranslationParser,
  makeEs2015TranslatePlugin,
  makeEs5TranslatePlugin,
  makeLocalePlugin
} from "./chunk-7JRN4H23.js";
import {
  Diagnostics,
  buildLocalizeReplacement,
  isGlobalIdentifier,
  translate,
  unwrapExpressionsFromTemplateLiteral,
  unwrapMessagePartsFromLocalizeCall,
  unwrapMessagePartsFromTemplateLiteral,
  unwrapSubstitutionsFromLocalizeCall
} from "./chunk-AIKR22BS.js";

// bazel-out/darwin_arm64-fastbuild/bin/packages/localize/tools/index.js
import { NodeJSFileSystem, setFileSystem } from "@angular/compiler-cli/private/localize";
setFileSystem(new NodeJSFileSystem());
export {
  ArbTranslationParser,
  ArbTranslationSerializer,
  Diagnostics,
  LegacyMessageIdMigrationSerializer,
  MessageExtractor,
  SimpleJsonTranslationParser,
  SimpleJsonTranslationSerializer,
  Xliff1TranslationParser,
  Xliff1TranslationSerializer,
  Xliff2TranslationParser,
  Xliff2TranslationSerializer,
  XmbTranslationSerializer,
  XtbTranslationParser,
  buildLocalizeReplacement,
  checkDuplicateMessages,
  isGlobalIdentifier,
  makeEs2015TranslatePlugin,
  makeEs5TranslatePlugin,
  makeLocalePlugin,
  translate,
  unwrapExpressionsFromTemplateLiteral,
  unwrapMessagePartsFromLocalizeCall,
  unwrapMessagePartsFromTemplateLiteral,
  unwrapSubstitutionsFromLocalizeCall
};
/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
//# sourceMappingURL=index.js.map
