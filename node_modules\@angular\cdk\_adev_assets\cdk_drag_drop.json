{"repo": "angular/components", "moduleLabel": "@angular/cdk/drag-drop", "moduleName": "@angular/cdk/drag-drop", "normalizedModuleName": "angular_cdk_drag-drop", "entries": [{"name": "moveItemInArray", "signatures": [{"name": "moveItemInArray", "entryType": "function", "description": "Moves an item one index in an array to another.", "generics": [{"name": "T", "default": "any"}], "isNewType": false, "jsdocTags": [{"name": "param", "comment": "Array in which to move the item."}, {"name": "param", "comment": "Starting index of the item."}, {"name": "param", "comment": "Index to which the item should be moved."}], "params": [{"name": "array", "description": "Array in which to move the item.", "type": "T[]", "isOptional": false, "isRestParam": false}, {"name": "fromIndex", "description": "Starting index of the item.", "type": "number", "isOptional": false, "isRestParam": false}, {"name": "toIndex", "description": "Index to which the item should be moved.", "type": "number", "isOptional": false, "isRestParam": false}], "rawComment": "/**\n * Moves an item one index in an array to another.\n * @param array Array in which to move the item.\n * @param fromIndex Starting index of the item.\n * @param toIndex Index to which the item should be moved.\n */", "returnType": "void"}], "implementation": {"params": [{"name": "array", "description": "Array in which to move the item.", "type": "T[]", "isOptional": false, "isRestParam": false}, {"name": "fromIndex", "description": "Starting index of the item.", "type": "number", "isOptional": false, "isRestParam": false}, {"name": "toIndex", "description": "Index to which the item should be moved.", "type": "number", "isOptional": false, "isRestParam": false}], "isNewType": false, "returnType": "void", "generics": [{"name": "T", "default": "any"}], "name": "moveItemInArray", "description": "Moves an item one index in an array to another.", "entryType": "function", "jsdocTags": [{"name": "param", "comment": "Array in which to move the item."}, {"name": "param", "comment": "Starting index of the item."}, {"name": "param", "comment": "Index to which the item should be moved."}], "rawComment": "/**\n * Moves an item one index in an array to another.\n * @param array Array in which to move the item.\n * @param fromIndex Starting index of the item.\n * @param toIndex Index to which the item should be moved.\n */"}, "entryType": "function", "description": "Moves an item one index in an array to another.", "jsdocTags": [{"name": "param", "comment": "Array in which to move the item."}, {"name": "param", "comment": "Starting index of the item."}, {"name": "param", "comment": "Index to which the item should be moved."}], "rawComment": "/**\n * Moves an item one index in an array to another.\n * @param array Array in which to move the item.\n * @param fromIndex Starting index of the item.\n * @param toIndex Index to which the item should be moved.\n */", "source": {"filePath": "/src/cdk/drag-drop/drag-utils.ts", "startLine": 15, "endLine": 31}}, {"name": "CdkDragStart", "isAbstract": false, "entryType": "interface", "members": [{"name": "source", "type": "CdkDrag<T>", "memberType": "property", "memberTags": [], "description": "Draggable that emitted the event.", "jsdocTags": []}, {"name": "event", "type": "MouseEvent | TouchEvent", "memberType": "property", "memberTags": [], "description": "Native event that started the drag sequence.", "jsdocTags": []}], "generics": [{"name": "T", "default": "any"}], "description": "Event emitted when the user starts dragging a draggable.", "jsdocTags": [], "rawComment": "/** Event emitted when the user starts dragging a draggable. */", "implements": [], "source": {"filePath": "/src/cdk/drag-drop/drag-events.ts", "startLine": 13, "endLine": 18}}, {"name": "DragStartDelay", "type": "number | {touch: number; mouse: number}", "entryType": "type_alias", "generics": [], "rawComment": "/** Possible values that can be used to configure the drag start delay. */", "description": "Possible values that can be used to configure the drag start delay.", "jsdocTags": [], "source": {"filePath": "src/cdk/drag-drop/directives/config.ts", "startLine": 13, "endLine": 13}}, {"name": "Drag<PERSON><PERSON><PERSON>", "type": "'x' | 'y'", "entryType": "type_alias", "generics": [], "rawComment": "/** Possible axis along which dragging can be locked. */", "description": "Possible axis along which dragging can be locked.", "jsdocTags": [], "source": {"filePath": "src/cdk/drag-drop/directives/config.ts", "startLine": 16, "endLine": 16}}, {"name": "CDK_DRAG_PARENT", "type": "any", "entryType": "constant", "rawComment": "/**\n * Injection token that can be used for a `CdkDrag` to provide itself as a parent to the\n * drag-specific child directive (`CdkDragHandle`, `CdkDragPreview` etc.). Used primarily\n * to avoid circular imports.\n * @docs-private\n */", "description": "Injection token that can be used for a `CdkDrag` to provide itself as a parent to the\ndrag-specific child directive (`CdkDragHandle`, `CdkDragPreview` etc.). Used primarily\nto avoid circular imports.", "jsdocTags": [{"name": "docs-private", "comment": ""}], "source": {"filePath": "/src/cdk/drag-drop/drag-parent.ts", "startLine": 18, "endLine": 18}}, {"name": "DragConstrainPosition", "type": "(point: Point, dragRef: DragRef) => Point", "entryType": "type_alias", "generics": [], "rawComment": "/** Function that can be used to constrain the position of a dragged element. */", "description": "Function that can be used to constrain the position of a dragged element.", "jsdocTags": [], "source": {"filePath": "src/cdk/drag-drop/directives/config.ts", "startLine": 19, "endLine": 19}}, {"name": "CDK_DROP_LIST_GROUP", "type": "any", "entryType": "constant", "rawComment": "/**\n * Injection token that can be used to reference instances of `CdkDropListGroup`. It serves as\n * alternative token to the actual `CdkDropListGroup` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */", "description": "Injection token that can be used to reference instances of `CdkDropListGroup`. It serves as\nalternative token to the actual `CdkDropListGroup` class which could cause unnecessary\nretention of the class and its directive metadata.", "jsdocTags": [], "source": {"filePath": "/src/cdk/drag-drop/directives/drop-list-group.ts", "startLine": 16, "endLine": 18}}, {"name": "CdkDragRelease", "isAbstract": false, "entryType": "interface", "members": [{"name": "source", "type": "CdkDrag<T>", "memberType": "property", "memberTags": [], "description": "Draggable that emitted the event.", "jsdocTags": []}, {"name": "event", "type": "MouseEvent | TouchEvent", "memberType": "property", "memberTags": [], "description": "Native event that caused the release event.", "jsdocTags": []}], "generics": [{"name": "T", "default": "any"}], "description": "Event emitted when the user releases an item, before any animations have started.", "jsdocTags": [], "rawComment": "/** Event emitted when the user releases an item, before any animations have started. */", "implements": [], "source": {"filePath": "/src/cdk/drag-drop/drag-events.ts", "startLine": 21, "endLine": 26}}, {"name": "CDK_DRAG_PLACEHOLDER", "type": "any", "entryType": "constant", "rawComment": "/**\n * Injection token that can be used to reference instances of `CdkDragPlaceholder`. It serves as\n * alternative token to the actual `CdkDragPlaceholder` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */", "description": "Injection token that can be used to reference instances of `CdkDragPlaceholder`. It serves as\nalternative token to the actual `CdkDragPlaceholder` class which could cause unnecessary\nretention of the class and its directive metadata.", "jsdocTags": [], "source": {"filePath": "/src/cdk/drag-drop/directives/drag-placeholder.ts", "startLine": 17, "endLine": 17}}, {"name": "CDK_DRAG_PREVIEW", "type": "any", "entryType": "constant", "rawComment": "/**\n * Injection token that can be used to reference instances of `CdkDragPreview`. It serves as\n * alternative token to the actual `CdkDragPreview` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */", "description": "Injection token that can be used to reference instances of `CdkDragPreview`. It serves as\nalternative token to the actual `CdkDragPreview` class which could cause unnecessary\nretention of the class and its directive metadata.", "jsdocTags": [], "source": {"filePath": "/src/cdk/drag-drop/directives/drag-preview.ts", "startLine": 25, "endLine": 25}}, {"name": "CdkDropListGroup", "isAbstract": false, "entryType": "undecorated_class", "members": [{"name": "disabled", "type": "boolean", "memberType": "property", "memberTags": [], "description": "Whether starting a dragging sequence from inside this group is disabled.", "jsdocTags": []}, {"name": "ngOnDestroy", "signatures": [{"name": "ngOnDestroy", "entryType": "function", "description": "", "generics": [], "isNewType": false, "jsdocTags": [], "params": [], "rawComment": "", "returnType": "void"}], "implementation": {"params": [], "isNewType": false, "returnType": "void", "generics": [], "name": "ngOnDestroy", "description": "", "entryType": "function", "jsdocTags": [], "rawComment": ""}, "entryType": "function", "description": "", "jsdocTags": [], "rawComment": "", "memberType": "method", "memberTags": []}], "generics": [{"name": "T"}], "description": "Declaratively connects sibling `cdkDropList` instances together. All of the `cdkDropList`\nelements that are placed inside a `cdkDropListGroup` will be connected to each other\nautomatically. Can be used as an alternative to the `cdkDropListConnectedTo` input\nfrom `cdkDropList`.", "jsdocTags": [], "rawComment": "/**\n * Declaratively connects sibling `cdkDropList` instances together. All of the `cdkDropList`\n * elements that are placed inside a `cdkDropListGroup` will be connected to each other\n * automatically. Can be used as an alternative to the `cdkDropListConnectedTo` input\n * from `cdkDropList`.\n */", "implements": ["OnDestroy"], "source": {"filePath": "/src/cdk/drag-drop/directives/drop-list-group.ts", "startLine": 26, "endLine": 42}}, {"name": "DragDrop", "isAbstract": false, "entryType": "undecorated_class", "members": [{"name": "constructor", "signatures": [{"name": "constructor", "params": [{"name": "args", "description": "", "type": "unknown[]", "isOptional": false, "isRestParam": true}], "returnType": "DragDrop", "description": "", "entryType": "function", "jsdocTags": [], "rawComment": "", "generics": [], "isNewType": false}], "implementation": {"params": [], "isNewType": false, "returnType": "DragDrop", "generics": [], "name": "constructor", "description": "", "entryType": "function", "jsdocTags": [], "rawComment": ""}, "entryType": "function", "description": "", "jsdocTags": [], "rawComment": "", "memberType": "method", "memberTags": []}, {"name": "createDrag", "signatures": [{"name": "createDrag", "entryType": "function", "description": "Turns an element into a draggable item.", "generics": [{"name": "T", "default": "any"}], "isNewType": false, "jsdocTags": [{"name": "param", "comment": "Element to which to attach the dragging functionality."}, {"name": "param", "comment": "Object used to configure the dragging behavior."}], "params": [{"name": "element", "description": "Element to which to attach the dragging functionality.", "type": "any", "isOptional": false, "isRestParam": false}, {"name": "config", "description": "Object used to configure the dragging behavior.", "type": "DragRefConfig", "isOptional": true, "isRestParam": false}], "rawComment": "/**\n   * Turns an element into a draggable item.\n   * @param element Element to which to attach the dragging functionality.\n   * @param config Object used to configure the dragging behavior.\n   */", "returnType": "DragRef<T>"}], "implementation": {"params": [{"name": "element", "description": "Element to which to attach the dragging functionality.", "type": "any", "isOptional": false, "isRestParam": false}, {"name": "config", "description": "Object used to configure the dragging behavior.", "type": "DragRefConfig", "isOptional": true, "isRestParam": false}], "isNewType": false, "returnType": "DragRef<T>", "generics": [{"name": "T", "default": "any"}], "name": "createDrag", "description": "Turns an element into a draggable item.", "entryType": "function", "jsdocTags": [{"name": "param", "comment": "Element to which to attach the dragging functionality."}, {"name": "param", "comment": "Object used to configure the dragging behavior."}], "rawComment": "/**\n   * Turns an element into a draggable item.\n   * @param element Element to which to attach the dragging functionality.\n   * @param config Object used to configure the dragging behavior.\n   */"}, "entryType": "function", "description": "Turns an element into a draggable item.", "jsdocTags": [{"name": "param", "comment": "Element to which to attach the dragging functionality."}, {"name": "param", "comment": "Object used to configure the dragging behavior."}], "rawComment": "/**\n   * Turns an element into a draggable item.\n   * @param element Element to which to attach the dragging functionality.\n   * @param config Object used to configure the dragging behavior.\n   */", "memberType": "method", "memberTags": []}, {"name": "createDropList", "signatures": [{"name": "createDropList", "entryType": "function", "description": "Turns an element into a drop list.", "generics": [{"name": "T", "default": "any"}], "isNewType": false, "jsdocTags": [{"name": "param", "comment": "Element to which to attach the drop list functionality."}], "params": [{"name": "element", "description": "Element to which to attach the drop list functionality.", "type": "any", "isOptional": false, "isRestParam": false}], "rawComment": "/**\n   * Turns an element into a drop list.\n   * @param element Element to which to attach the drop list functionality.\n   */", "returnType": "DropListRef<T>"}], "implementation": {"params": [{"name": "element", "description": "Element to which to attach the drop list functionality.", "type": "any", "isOptional": false, "isRestParam": false}], "isNewType": false, "returnType": "DropListRef<T>", "generics": [{"name": "T", "default": "any"}], "name": "createDropList", "description": "Turns an element into a drop list.", "entryType": "function", "jsdocTags": [{"name": "param", "comment": "Element to which to attach the drop list functionality."}], "rawComment": "/**\n   * Turns an element into a drop list.\n   * @param element Element to which to attach the drop list functionality.\n   */"}, "entryType": "function", "description": "Turns an element into a drop list.", "jsdocTags": [{"name": "param", "comment": "Element to which to attach the drop list functionality."}], "rawComment": "/**\n   * Turns an element into a drop list.\n   * @param element Element to which to attach the drop list functionality.\n   */", "memberType": "method", "memberTags": []}], "generics": [], "description": "Service that allows for drag-and-drop functionality to be attached to DOM elements.", "jsdocTags": [], "rawComment": "/**\n * Service that allows for drag-and-drop functionality to be attached to DOM elements.\n */", "implements": [], "source": {"filePath": "/src/cdk/drag-drop/drag-drop.ts", "startLine": 25, "endLine": 69}}, {"name": "CdkDragPlaceholder", "isAbstract": false, "entryType": "directive", "members": [{"name": "constructor", "signatures": [{"name": "constructor", "params": [{"name": "args", "description": "", "type": "unknown[]", "isOptional": false, "isRestParam": true}], "returnType": "CdkDragPlaceholder", "description": "", "entryType": "function", "jsdocTags": [], "rawComment": "", "generics": [], "isNewType": false}], "implementation": {"params": [], "isNewType": false, "returnType": "CdkDragPlaceholder<T>", "generics": [], "name": "constructor", "description": "", "entryType": "function", "jsdocTags": [], "rawComment": ""}, "entryType": "function", "description": "", "jsdocTags": [], "rawComment": "", "memberType": "method", "memberTags": []}, {"name": "templateRef", "type": "any", "memberType": "property", "memberTags": [], "description": "", "jsdocTags": []}, {"name": "data", "type": "T", "memberType": "property", "memberTags": ["input"], "description": "Context data to be added to the placeholder template instance.", "jsdocTags": [], "inputAlias": "data", "isRequiredInput": false}, {"name": "ngOnDestroy", "signatures": [{"name": "ngOnDestroy", "entryType": "function", "description": "", "generics": [], "isNewType": false, "jsdocTags": [], "params": [], "rawComment": "", "returnType": "void"}], "implementation": {"params": [], "isNewType": false, "returnType": "void", "generics": [], "name": "ngOnDestroy", "description": "", "entryType": "function", "jsdocTags": [], "rawComment": ""}, "entryType": "function", "description": "", "jsdocTags": [], "rawComment": "", "memberType": "method", "memberTags": []}], "generics": [{"name": "T", "default": "any"}], "description": "Element that will be used as a template for the placeholder of a CdkDrag when\nit is being dragged. The placeholder is displayed in place of the element being dragged.", "jsdocTags": [], "rawComment": "/**\n * Element that will be used as a template for the placeholder of a CdkDrag when\n * it is being dragged. The placeholder is displayed in place of the element being dragged.\n */", "implements": ["OnDestroy"], "isStandalone": true, "selector": "ng-template[cdkDragPlaceholder]", "exportAs": [], "source": {"filePath": "/src/cdk/drag-drop/directives/drag-placeholder.ts", "startLine": 23, "endLine": 44}}, {"name": "DropListOrientation", "type": "'horizontal' | 'vertical' | 'mixed'", "entryType": "type_alias", "generics": [], "rawComment": "/** Possible orientations for a drop list. */", "description": "Possible orientations for a drop list.", "jsdocTags": [], "source": {"filePath": "src/cdk/drag-drop/directives/config.ts", "startLine": 22, "endLine": 22}}, {"name": "CdkDragPreview", "isAbstract": false, "entryType": "undecorated_class", "members": [{"name": "constructor", "signatures": [{"name": "constructor", "params": [{"name": "args", "description": "", "type": "unknown[]", "isOptional": false, "isRestParam": true}], "returnType": "CdkDragPreview", "description": "", "entryType": "function", "jsdocTags": [], "rawComment": "", "generics": [], "isNewType": false}], "implementation": {"params": [], "isNewType": false, "returnType": "CdkDragPreview<T>", "generics": [], "name": "constructor", "description": "", "entryType": "function", "jsdocTags": [], "rawComment": ""}, "entryType": "function", "description": "", "jsdocTags": [], "rawComment": "", "memberType": "method", "memberTags": []}, {"name": "templateRef", "type": "any", "memberType": "property", "memberTags": [], "description": "", "jsdocTags": []}, {"name": "data", "type": "T", "memberType": "property", "memberTags": [], "description": "Context data to be added to the preview template instance.", "jsdocTags": []}, {"name": "matchSize", "type": "boolean", "memberType": "property", "memberTags": [], "description": "Whether the preview should preserve the same size as the item that is being dragged.", "jsdocTags": []}, {"name": "ngOnDestroy", "signatures": [{"name": "ngOnDestroy", "entryType": "function", "description": "", "generics": [], "isNewType": false, "jsdocTags": [], "params": [], "rawComment": "", "returnType": "void"}], "implementation": {"params": [], "isNewType": false, "returnType": "void", "generics": [], "name": "ngOnDestroy", "description": "", "entryType": "function", "jsdocTags": [], "rawComment": ""}, "entryType": "function", "description": "", "jsdocTags": [], "rawComment": "", "memberType": "method", "memberTags": []}], "generics": [{"name": "T", "default": "any"}], "description": "Element that will be used as a template for the preview\nof a CdkDrag when it is being dragged.", "jsdocTags": [], "rawComment": "/**\n * Element that will be used as a template for the preview\n * of a CdkDrag when it is being dragged.\n */", "implements": ["OnDestroy"], "source": {"filePath": "/src/cdk/drag-drop/directives/drag-preview.ts", "startLine": 31, "endLine": 55}}, {"name": "DragDropModule", "isAbstract": false, "entryType": "undecorated_class", "members": [], "generics": [], "description": "", "jsdocTags": [], "rawComment": "", "implements": [], "source": {"filePath": "src/cdk/drag-drop/drag-drop-module.ts", "startLine": 28, "endLine": 33}}, {"name": "CDK_DRAG_HANDLE", "type": "any", "entryType": "constant", "rawComment": "/**\n * Injection token that can be used to reference instances of `CdkDragHandle`. It serves as\n * alternative token to the actual `CdkDragHandle` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */", "description": "Injection token that can be used to reference instances of `CdkDragHandle`. It serves as\nalternative token to the actual `CdkDragHandle` class which could cause unnecessary\nretention of the class and its directive metadata.", "jsdocTags": [], "source": {"filePath": "/src/cdk/drag-drop/directives/drag-handle.ts", "startLine": 30, "endLine": 30}}, {"name": "transferArrayItem", "signatures": [{"name": "transferArrayItem", "entryType": "function", "description": "Moves an item from one array to another.", "generics": [{"name": "T", "default": "any"}], "isNewType": false, "jsdocTags": [{"name": "param", "comment": "Array from which to transfer the item."}, {"name": "param", "comment": "Array into which to put the item."}, {"name": "param", "comment": "Index of the item in its current array."}, {"name": "param", "comment": "Index at which to insert the item."}], "params": [{"name": "currentArray", "description": "Array from which to transfer the item.", "type": "T[]", "isOptional": false, "isRestParam": false}, {"name": "targetArray", "description": "Array into which to put the item.", "type": "T[]", "isOptional": false, "isRestParam": false}, {"name": "currentIndex", "description": "Index of the item in its current array.", "type": "number", "isOptional": false, "isRestParam": false}, {"name": "targetIndex", "description": "Index at which to insert the item.", "type": "number", "isOptional": false, "isRestParam": false}], "rawComment": "/**\n * Moves an item from one array to another.\n * @param currentArray Array from which to transfer the item.\n * @param targetArray Array into which to put the item.\n * @param currentIndex Index of the item in its current array.\n * @param targetIndex Index at which to insert the item.\n */", "returnType": "void"}], "implementation": {"params": [{"name": "currentArray", "description": "Array from which to transfer the item.", "type": "T[]", "isOptional": false, "isRestParam": false}, {"name": "targetArray", "description": "Array into which to put the item.", "type": "T[]", "isOptional": false, "isRestParam": false}, {"name": "currentIndex", "description": "Index of the item in its current array.", "type": "number", "isOptional": false, "isRestParam": false}, {"name": "targetIndex", "description": "Index at which to insert the item.", "type": "number", "isOptional": false, "isRestParam": false}], "isNewType": false, "returnType": "void", "generics": [{"name": "T", "default": "any"}], "name": "transferArrayItem", "description": "Moves an item from one array to another.", "entryType": "function", "jsdocTags": [{"name": "param", "comment": "Array from which to transfer the item."}, {"name": "param", "comment": "Array into which to put the item."}, {"name": "param", "comment": "Index of the item in its current array."}, {"name": "param", "comment": "Index at which to insert the item."}], "rawComment": "/**\n * Moves an item from one array to another.\n * @param currentArray Array from which to transfer the item.\n * @param targetArray Array into which to put the item.\n * @param currentIndex Index of the item in its current array.\n * @param targetIndex Index at which to insert the item.\n */"}, "entryType": "function", "description": "Moves an item from one array to another.", "jsdocTags": [{"name": "param", "comment": "Array from which to transfer the item."}, {"name": "param", "comment": "Array into which to put the item."}, {"name": "param", "comment": "Index of the item in its current array."}, {"name": "param", "comment": "Index at which to insert the item."}], "rawComment": "/**\n * Moves an item from one array to another.\n * @param currentArray Array from which to transfer the item.\n * @param targetArray Array into which to put the item.\n * @param currentIndex Index of the item in its current array.\n * @param targetIndex Index at which to insert the item.\n */", "source": {"filePath": "/src/cdk/drag-drop/drag-utils.ts", "startLine": 40, "endLine": 52}}, {"name": "CdkDragEnd", "isAbstract": false, "entryType": "interface", "members": [{"name": "source", "type": "CdkDrag<T>", "memberType": "property", "memberTags": [], "description": "Draggable that emitted the event.", "jsdocTags": []}, {"name": "distance", "type": "{ x: number; y: number; }", "memberType": "property", "memberTags": [], "description": "Distance in pixels that the user has dragged since the drag sequence started.", "jsdocTags": []}, {"name": "dropPoint", "type": "{ x: number; y: number; }", "memberType": "property", "memberTags": [], "description": "Position where the pointer was when the item was dropped", "jsdocTags": []}, {"name": "event", "type": "MouseEvent | TouchEvent", "memberType": "property", "memberTags": [], "description": "Native event that caused the dragging to stop.", "jsdocTags": []}], "generics": [{"name": "T", "default": "any"}], "description": "Event emitted when the user stops dragging a draggable.", "jsdocTags": [], "rawComment": "/** Event emitted when the user stops dragging a draggable. */", "implements": [], "source": {"filePath": "/src/cdk/drag-drop/drag-events.ts", "startLine": 29, "endLine": 38}}, {"name": "CdkDragHandle", "isAbstract": false, "entryType": "undecorated_class", "members": [{"name": "constructor", "signatures": [{"name": "constructor", "params": [{"name": "args", "description": "", "type": "unknown[]", "isOptional": false, "isRestParam": true}], "returnType": "CdkDragHandle", "description": "", "entryType": "function", "jsdocTags": [], "rawComment": "", "generics": [], "isNewType": false}], "implementation": {"params": [], "isNewType": false, "returnType": "CdkDragHandle", "generics": [], "name": "constructor", "description": "", "entryType": "function", "jsdocTags": [], "rawComment": ""}, "entryType": "function", "description": "", "jsdocTags": [], "rawComment": "", "memberType": "method", "memberTags": []}, {"name": "element", "type": "any", "memberType": "property", "memberTags": [], "description": "", "jsdocTags": []}, {"name": "disabled", "type": "boolean", "memberType": "getter", "memberTags": [], "description": "Whether starting to drag through this handle is disabled.", "jsdocTags": []}, {"name": "disabled", "type": "boolean", "memberType": "setter", "memberTags": [], "description": "", "jsdocTags": []}, {"name": "ngAfterViewInit", "signatures": [{"name": "ngAfterViewInit", "entryType": "function", "description": "", "generics": [], "isNewType": false, "jsdocTags": [], "params": [], "rawComment": "", "returnType": "void"}], "implementation": {"params": [], "isNewType": false, "returnType": "void", "generics": [], "name": "ngAfterViewInit", "description": "", "entryType": "function", "jsdocTags": [], "rawComment": ""}, "entryType": "function", "description": "", "jsdocTags": [], "rawComment": "", "memberType": "method", "memberTags": []}, {"name": "ngOnDestroy", "signatures": [{"name": "ngOnDestroy", "entryType": "function", "description": "", "generics": [], "isNewType": false, "jsdocTags": [], "params": [], "rawComment": "", "returnType": "void"}], "implementation": {"params": [], "isNewType": false, "returnType": "void", "generics": [], "name": "ngOnDestroy", "description": "", "entryType": "function", "jsdocTags": [], "rawComment": ""}, "entryType": "function", "description": "", "jsdocTags": [], "rawComment": "", "memberType": "method", "memberTags": []}], "generics": [], "description": "Handle that can be used to drag a CdkDrag instance.", "jsdocTags": [], "rawComment": "/** Handle that can be used to drag a CdkDrag instance. */", "implements": ["AfterViewInit", "OnDestroy"], "source": {"filePath": "/src/cdk/drag-drop/directives/drag-handle.ts", "startLine": 33, "endLine": 89}}, {"name": "CDK_DRAG_CONFIG", "type": "any", "entryType": "constant", "rawComment": "/**\n * Injection token that can be used to configure the\n * behavior of the drag&drop-related components.\n */", "description": "Injection token that can be used to configure the\nbehavior of the drag&drop-related components.", "jsdocTags": [], "source": {"filePath": "src/cdk/drag-drop/directives/config.ts", "startLine": 28, "endLine": 28}}, {"name": "DragDropConfig", "isAbstract": false, "entryType": "interface", "members": [{"name": "lockAxis", "type": "DragAxis | undefined", "memberType": "property", "memberTags": ["optional"], "description": "", "jsdocTags": []}, {"name": "dragStartDelay", "type": "DragStartDelay | undefined", "memberType": "property", "memberTags": ["optional"], "description": "", "jsdocTags": []}, {"name": "constrainPosition", "type": "DragConstrainPosition | undefined", "memberType": "property", "memberTags": ["optional"], "description": "", "jsdocTags": []}, {"name": "previewClass", "type": "string | string[] | undefined", "memberType": "property", "memberTags": ["optional"], "description": "", "jsdocTags": []}, {"name": "boundaryElement", "type": "string | undefined", "memberType": "property", "memberTags": ["optional"], "description": "", "jsdocTags": []}, {"name": "rootElementSelector", "type": "string | undefined", "memberType": "property", "memberTags": ["optional"], "description": "", "jsdocTags": []}, {"name": "draggingDisabled", "type": "boolean | undefined", "memberType": "property", "memberTags": ["optional"], "description": "", "jsdocTags": []}, {"name": "sortingDisabled", "type": "boolean | undefined", "memberType": "property", "memberTags": ["optional"], "description": "", "jsdocTags": []}, {"name": "listAutoScrollDisabled", "type": "boolean | undefined", "memberType": "property", "memberTags": ["optional"], "description": "", "jsdocTags": []}, {"name": "listOrientation", "type": "DropListOrientation | undefined", "memberType": "property", "memberTags": ["optional"], "description": "", "jsdocTags": []}, {"name": "zIndex", "type": "number | undefined", "memberType": "property", "memberTags": ["optional"], "description": "", "jsdocTags": []}, {"name": "previewContainer", "type": "\"global\" | \"parent\" | undefined", "memberType": "property", "memberTags": ["optional"], "description": "", "jsdocTags": []}, {"name": "dragStartThreshold", "type": "number", "memberType": "property", "memberTags": ["override"], "description": "Minimum amount of pixels that the user should\ndrag, before the CDK initiates a drag sequence.", "jsdocTags": []}, {"name": "pointerDirectionChangeThreshold", "type": "number", "memberType": "property", "memberTags": ["override"], "description": "Amount the pixels the user should drag before the CDK\nconsiders them to have changed the drag direction.", "jsdocTags": []}, {"name": "parentDragRef", "type": "DragRef<any> | undefined", "memberType": "property", "memberTags": ["optional", "override"], "description": "Ref that the current drag item is nested in.", "jsdocTags": []}], "generics": [], "description": "Object that can be used to configure the drag\nitems and drop lists within a module or a component.", "jsdocTags": [], "rawComment": "/**\n * Object that can be used to configure the drag\n * items and drop lists within a module or a component.\n */", "extends": "Partial<DragRefConfig>", "implements": [], "source": {"filePath": "src/cdk/drag-drop/directives/config.ts", "startLine": 34, "endLine": 47}}, {"name": "CdkDropList", "isAbstract": false, "entryType": "undecorated_class", "members": [{"name": "constructor", "signatures": [{"name": "constructor", "params": [{"name": "args", "description": "", "type": "unknown[]", "isOptional": false, "isRestParam": true}], "returnType": "CdkDropList", "description": "", "entryType": "function", "jsdocTags": [], "rawComment": "", "generics": [], "isNewType": false}], "implementation": {"params": [], "isNewType": false, "returnType": "CdkDropList<T>", "generics": [], "name": "constructor", "description": "", "entryType": "function", "jsdocTags": [], "rawComment": ""}, "entryType": "function", "description": "", "jsdocTags": [], "rawComment": "", "memberType": "method", "memberTags": []}, {"name": "element", "type": "any", "memberType": "property", "memberTags": [], "description": "", "jsdocTags": []}, {"name": "connectedTo", "type": "string | CdkDropList<any> | (string | CdkDropList<any>)[]", "memberType": "property", "memberTags": [], "description": "Other draggable containers that this container is connected to and into which the\ncontainer's items can be transferred. Can either be references to other drop containers,\nor their unique IDs.", "jsdocTags": []}, {"name": "data", "type": "T", "memberType": "property", "memberTags": [], "description": "Arbitrary data to attach to this container.", "jsdocTags": []}, {"name": "orientation", "type": "DropListOrientation", "memberType": "property", "memberTags": [], "description": "Direction in which the list is oriented.", "jsdocTags": []}, {"name": "id", "type": "string", "memberType": "property", "memberTags": [], "description": "Unique ID for the drop zone. Can be used as a reference\nin the `connectedTo` of another `CdkDropList`.", "jsdocTags": []}, {"name": "lockAxis", "type": "Drag<PERSON><PERSON><PERSON>", "memberType": "property", "memberTags": [], "description": "Locks the position of the draggable elements inside the container along the specified axis.", "jsdocTags": []}, {"name": "disabled", "type": "boolean", "memberType": "getter", "memberTags": [], "description": "Whether starting a dragging sequence from this container is disabled.", "jsdocTags": []}, {"name": "disabled", "type": "boolean", "memberType": "setter", "memberTags": [], "description": "", "jsdocTags": []}, {"name": "sortingDisabled", "type": "boolean", "memberType": "property", "memberTags": [], "description": "Whether sorting within this drop list is disabled.", "jsdocTags": []}, {"name": "enterPredicate", "type": "(drag: Cdk<PERSON><PERSON><any>, drop: CdkDropList<any>) => boolean", "memberType": "property", "memberTags": [], "description": "Function that is used to determine whether an item\nis allowed to be moved into a drop container.", "jsdocTags": []}, {"name": "sortPredicate", "type": "(index: number, drag: CdkDrag<any>, drop: CdkDropList<any>) => boolean", "memberType": "property", "memberTags": [], "description": "Functions that is used to determine whether an item can be sorted into a particular index.", "jsdocTags": []}, {"name": "autoScrollDisabled", "type": "boolean", "memberType": "property", "memberTags": [], "description": "Whether to auto-scroll the view when the user moves their pointer close to the edges.", "jsdocTags": []}, {"name": "autoScrollStep", "type": "NumberInput", "memberType": "property", "memberTags": [], "description": "Number of pixels to scroll for each frame when auto-scrolling an element.", "jsdocTags": []}, {"name": "elementContainerSelector", "type": "string | null", "memberType": "property", "memberTags": [], "description": "Selector that will be used to resolve an alternate element container for the drop list.\nPassing an alternate container is useful for the cases where one might not have control\nover the parent node of the draggable items within the list (e.g. due to content projection).\nThis allows for usages like:\n\n```\n<div cdkDropList cdkDropListElementContainer=\".inner\">\n  <div class=\"inner\">\n    <div cdkDrag></div>\n  </div>\n</div>\n```", "jsdocTags": []}, {"name": "dropped", "type": "EventEmitter<CdkDragDrop<T, any, any>>", "memberType": "property", "memberTags": ["readonly"], "description": "Emits when the user drops an item inside the container.", "jsdocTags": []}, {"name": "entered", "type": "EventEmitter<CdkDragEnter<T, T>>", "memberType": "property", "memberTags": ["readonly"], "description": "Emits when the user has moved a new drag item into this container.", "jsdocTags": []}, {"name": "exited", "type": "EventEmitter<CdkDragExit<T, T>>", "memberType": "property", "memberTags": ["readonly"], "description": "Emits when the user removes an item from the container\nby dragging it into another container.", "jsdocTags": []}, {"name": "sorted", "type": "EventEmitter<CdkDragSortEvent<T, T>>", "memberType": "property", "memberTags": ["readonly"], "description": "Emits as the user is swapping items while actively dragging.", "jsdocTags": []}, {"name": "addItem", "signatures": [{"name": "addItem", "entryType": "function", "description": "Registers an items with the drop list.", "generics": [], "isNewType": false, "jsdocTags": [], "params": [{"name": "item", "description": "", "type": "CdkDrag<any>", "isOptional": false, "isRestParam": false}], "rawComment": "/** Registers an items with the drop list. */", "returnType": "void"}], "implementation": {"params": [{"name": "item", "description": "", "type": "CdkDrag<any>", "isOptional": false, "isRestParam": false}], "isNewType": false, "returnType": "void", "generics": [], "name": "addItem", "description": "Registers an items with the drop list.", "entryType": "function", "jsdocTags": [], "rawComment": "/** Registers an items with the drop list. */"}, "entryType": "function", "description": "Registers an items with the drop list.", "jsdocTags": [], "rawComment": "/** Registers an items with the drop list. */", "memberType": "method", "memberTags": []}, {"name": "removeItem", "signatures": [{"name": "removeItem", "entryType": "function", "description": "Removes an item from the drop list.", "generics": [], "isNewType": false, "jsdocTags": [], "params": [{"name": "item", "description": "", "type": "CdkDrag<any>", "isOptional": false, "isRestParam": false}], "rawComment": "/** Removes an item from the drop list. */", "returnType": "void"}], "implementation": {"params": [{"name": "item", "description": "", "type": "CdkDrag<any>", "isOptional": false, "isRestParam": false}], "isNewType": false, "returnType": "void", "generics": [], "name": "removeItem", "description": "Removes an item from the drop list.", "entryType": "function", "jsdocTags": [], "rawComment": "/** Removes an item from the drop list. */"}, "entryType": "function", "description": "Removes an item from the drop list.", "jsdocTags": [], "rawComment": "/** Removes an item from the drop list. */", "memberType": "method", "memberTags": []}, {"name": "getSortedItems", "signatures": [{"name": "getSortedItems", "entryType": "function", "description": "Gets the registered items in the list, sorted by their position in the DOM.", "generics": [], "isNewType": false, "jsdocTags": [], "params": [], "rawComment": "/** Gets the registered items in the list, sorted by their position in the DOM. */", "returnType": "CdkDrag<any>[]"}], "implementation": {"params": [], "isNewType": false, "returnType": "CdkDrag<any>[]", "generics": [], "name": "getSortedItems", "description": "Gets the registered items in the list, sorted by their position in the DOM.", "entryType": "function", "jsdocTags": [], "rawComment": "/** Gets the registered items in the list, sorted by their position in the DOM. */"}, "entryType": "function", "description": "Gets the registered items in the list, sorted by their position in the DOM.", "jsdocTags": [], "rawComment": "/** Gets the registered items in the list, sorted by their position in the DOM. */", "memberType": "method", "memberTags": []}, {"name": "ngOnDestroy", "signatures": [{"name": "ngOnDestroy", "entryType": "function", "description": "", "generics": [], "isNewType": false, "jsdocTags": [], "params": [], "rawComment": "", "returnType": "void"}], "implementation": {"params": [], "isNewType": false, "returnType": "void", "generics": [], "name": "ngOnDestroy", "description": "", "entryType": "function", "jsdocTags": [], "rawComment": ""}, "entryType": "function", "description": "", "jsdocTags": [], "rawComment": "", "memberType": "method", "memberTags": []}], "generics": [{"name": "T", "default": "any"}], "description": "Container that wraps a set of draggable items.", "jsdocTags": [], "rawComment": "/** Container that wraps a set of draggable items. */", "implements": ["OnDestroy"], "source": {"filePath": "/src/cdk/drag-drop/directives/drop-list.ts", "startLine": 36, "endLine": 423}}, {"name": "DragRefConfig", "isAbstract": false, "entryType": "interface", "members": [{"name": "dragStartThreshold", "type": "number", "memberType": "property", "memberTags": [], "description": "Minimum amount of pixels that the user should\ndrag, before the CDK initiates a drag sequence.", "jsdocTags": []}, {"name": "pointerDirectionChangeThreshold", "type": "number", "memberType": "property", "memberTags": [], "description": "Amount the pixels the user should drag before the CDK\nconsiders them to have changed the drag direction.", "jsdocTags": []}, {"name": "zIndex", "type": "number | undefined", "memberType": "property", "memberTags": ["optional"], "description": "`z-index` for the absolutely-positioned elements that are created by the drag item.", "jsdocTags": []}, {"name": "parentDragRef", "type": "DragRef<any> | undefined", "memberType": "property", "memberTags": ["optional"], "description": "Ref that the current drag item is nested in.", "jsdocTags": []}], "generics": [], "description": "Object that can be used to configure the behavior of DragRef.", "jsdocTags": [], "rawComment": "/** Object that can be used to configure the behavior of DragRef. */", "implements": [], "source": {"filePath": "/src/cdk/drag-drop/drag-ref.ts", "startLine": 40, "endLine": 58}}, {"name": "DragDropRegistry", "isAbstract": false, "entryType": "undecorated_class", "members": [{"name": "constructor", "signatures": [{"name": "constructor", "params": [{"name": "args", "description": "", "type": "unknown[]", "isOptional": false, "isRestParam": true}], "returnType": "DragDropRegistry", "description": "", "entryType": "function", "jsdocTags": [], "rawComment": "", "generics": [], "isNewType": false}], "implementation": {"params": [], "isNewType": false, "returnType": "DragDropRegistry<_, __>", "generics": [], "name": "constructor", "description": "", "entryType": "function", "jsdocTags": [], "rawComment": ""}, "entryType": "function", "description": "", "jsdocTags": [], "rawComment": "", "memberType": "method", "memberTags": []}, {"name": "pointer<PERSON><PERSON>", "type": "Subject<MouseEvent | TouchEvent>", "memberType": "property", "memberTags": ["readonly"], "description": "Emits the `touchmove` or `mousemove` events that are dispatched\nwhile the user is dragging a drag item instance.", "jsdocTags": []}, {"name": "pointerUp", "type": "Subject<MouseEvent | TouchEvent>", "memberType": "property", "memberTags": ["readonly"], "description": "Emits the `touchend` or `mouseup` events that are dispatched\nwhile the user is dragging a drag item instance.", "jsdocTags": []}, {"name": "scroll", "type": "Subject<Event>", "memberType": "property", "memberTags": ["readonly"], "description": "Emits when the viewport has been scrolled while the user is dragging an item.", "jsdocTags": [{"name": "deprecated", "comment": "To be turned into a private member. Use the `scrolled` method instead."}, {"name": "breaking-change", "comment": "13.0.0"}]}, {"name": "registerDropContainer", "signatures": [{"name": "registerDropContainer", "entryType": "function", "description": "Adds a drop container to the registry.", "generics": [], "isNewType": false, "jsdocTags": [], "params": [{"name": "drop", "description": "", "type": "DropListRef<any>", "isOptional": false, "isRestParam": false}], "rawComment": "/** Adds a drop container to the registry. */", "returnType": "void"}], "implementation": {"params": [{"name": "drop", "description": "", "type": "DropListRef<any>", "isOptional": false, "isRestParam": false}], "isNewType": false, "returnType": "void", "generics": [], "name": "registerDropContainer", "description": "Adds a drop container to the registry.", "entryType": "function", "jsdocTags": [], "rawComment": "/** Adds a drop container to the registry. */"}, "entryType": "function", "description": "Adds a drop container to the registry.", "jsdocTags": [], "rawComment": "/** Adds a drop container to the registry. */", "memberType": "method", "memberTags": []}, {"name": "registerDragItem", "signatures": [{"name": "registerDragItem", "entryType": "function", "description": "Adds a drag item instance to the registry.", "generics": [], "isNewType": false, "jsdocTags": [], "params": [{"name": "drag", "description": "", "type": "DragRef<any>", "isOptional": false, "isRestParam": false}], "rawComment": "/** Adds a drag item instance to the registry. */", "returnType": "void"}], "implementation": {"params": [{"name": "drag", "description": "", "type": "DragRef<any>", "isOptional": false, "isRestParam": false}], "isNewType": false, "returnType": "void", "generics": [], "name": "registerDragItem", "description": "Adds a drag item instance to the registry.", "entryType": "function", "jsdocTags": [], "rawComment": "/** Adds a drag item instance to the registry. */"}, "entryType": "function", "description": "Adds a drag item instance to the registry.", "jsdocTags": [], "rawComment": "/** Adds a drag item instance to the registry. */", "memberType": "method", "memberTags": []}, {"name": "removeDropContainer", "signatures": [{"name": "removeDropContainer", "entryType": "function", "description": "Removes a drop container from the registry.", "generics": [], "isNewType": false, "jsdocTags": [], "params": [{"name": "drop", "description": "", "type": "DropListRef<any>", "isOptional": false, "isRestParam": false}], "rawComment": "/** Removes a drop container from the registry. */", "returnType": "void"}], "implementation": {"params": [{"name": "drop", "description": "", "type": "DropListRef<any>", "isOptional": false, "isRestParam": false}], "isNewType": false, "returnType": "void", "generics": [], "name": "removeDropContainer", "description": "Removes a drop container from the registry.", "entryType": "function", "jsdocTags": [], "rawComment": "/** Removes a drop container from the registry. */"}, "entryType": "function", "description": "Removes a drop container from the registry.", "jsdocTags": [], "rawComment": "/** Removes a drop container from the registry. */", "memberType": "method", "memberTags": []}, {"name": "removeDragItem", "signatures": [{"name": "removeDragItem", "entryType": "function", "description": "Removes a drag item instance from the registry.", "generics": [], "isNewType": false, "jsdocTags": [], "params": [{"name": "drag", "description": "", "type": "DragRef<any>", "isOptional": false, "isRestParam": false}], "rawComment": "/** Removes a drag item instance from the registry. */", "returnType": "void"}], "implementation": {"params": [{"name": "drag", "description": "", "type": "DragRef<any>", "isOptional": false, "isRestParam": false}], "isNewType": false, "returnType": "void", "generics": [], "name": "removeDragItem", "description": "Removes a drag item instance from the registry.", "entryType": "function", "jsdocTags": [], "rawComment": "/** Removes a drag item instance from the registry. */"}, "entryType": "function", "description": "Removes a drag item instance from the registry.", "jsdocTags": [], "rawComment": "/** Removes a drag item instance from the registry. */", "memberType": "method", "memberTags": []}, {"name": "startDragging", "signatures": [{"name": "startDragging", "entryType": "function", "description": "Starts the dragging sequence for a drag instance.", "generics": [], "isNewType": false, "jsdocTags": [{"name": "param", "comment": "Drag instance which is being dragged."}, {"name": "param", "comment": "Event that initiated the dragging."}], "params": [{"name": "drag", "description": "Drag instance which is being dragged.", "type": "DragRef<any>", "isOptional": false, "isRestParam": false}, {"name": "event", "description": "Event that initiated the dragging.", "type": "MouseEvent | TouchEvent", "isOptional": false, "isRestParam": false}], "rawComment": "/**\n   * Starts the dragging sequence for a drag instance.\n   * @param drag Drag instance which is being dragged.\n   * @param event Event that initiated the dragging.\n   */", "returnType": "void"}], "implementation": {"params": [{"name": "drag", "description": "Drag instance which is being dragged.", "type": "DragRef<any>", "isOptional": false, "isRestParam": false}, {"name": "event", "description": "Event that initiated the dragging.", "type": "MouseEvent | TouchEvent", "isOptional": false, "isRestParam": false}], "isNewType": false, "returnType": "void", "generics": [], "name": "startDragging", "description": "Starts the dragging sequence for a drag instance.", "entryType": "function", "jsdocTags": [{"name": "param", "comment": "Drag instance which is being dragged."}, {"name": "param", "comment": "Event that initiated the dragging."}], "rawComment": "/**\n   * Starts the dragging sequence for a drag instance.\n   * @param drag Drag instance which is being dragged.\n   * @param event Event that initiated the dragging.\n   */"}, "entryType": "function", "description": "Starts the dragging sequence for a drag instance.", "jsdocTags": [{"name": "param", "comment": "Drag instance which is being dragged."}, {"name": "param", "comment": "Event that initiated the dragging."}], "rawComment": "/**\n   * Starts the dragging sequence for a drag instance.\n   * @param drag Drag instance which is being dragged.\n   * @param event Event that initiated the dragging.\n   */", "memberType": "method", "memberTags": []}, {"name": "stopDragging", "signatures": [{"name": "stopDragging", "entryType": "function", "description": "Stops dragging a drag item instance.", "generics": [], "isNewType": false, "jsdocTags": [], "params": [{"name": "drag", "description": "", "type": "DragRef<any>", "isOptional": false, "isRestParam": false}], "rawComment": "/** Stops dragging a drag item instance. */", "returnType": "void"}], "implementation": {"params": [{"name": "drag", "description": "", "type": "DragRef<any>", "isOptional": false, "isRestParam": false}], "isNewType": false, "returnType": "void", "generics": [], "name": "stopDragging", "description": "Stops dragging a drag item instance.", "entryType": "function", "jsdocTags": [], "rawComment": "/** Stops dragging a drag item instance. */"}, "entryType": "function", "description": "Stops dragging a drag item instance.", "jsdocTags": [], "rawComment": "/** Stops dragging a drag item instance. */", "memberType": "method", "memberTags": []}, {"name": "isDragging", "signatures": [{"name": "isDragging", "entryType": "function", "description": "Gets whether a drag item instance is currently being dragged.", "generics": [], "isNewType": false, "jsdocTags": [], "params": [{"name": "drag", "description": "", "type": "DragRef<any>", "isOptional": false, "isRestParam": false}], "rawComment": "/** Gets whether a drag item instance is currently being dragged. */", "returnType": "boolean"}], "implementation": {"params": [{"name": "drag", "description": "", "type": "DragRef<any>", "isOptional": false, "isRestParam": false}], "isNewType": false, "returnType": "boolean", "generics": [], "name": "isDragging", "description": "Gets whether a drag item instance is currently being dragged.", "entryType": "function", "jsdocTags": [], "rawComment": "/** Gets whether a drag item instance is currently being dragged. */"}, "entryType": "function", "description": "Gets whether a drag item instance is currently being dragged.", "jsdocTags": [], "rawComment": "/** Gets whether a drag item instance is currently being dragged. */", "memberType": "method", "memberTags": []}, {"name": "scrolled", "signatures": [{"name": "scrolled", "entryType": "function", "description": "Gets a stream that will emit when any element on the page is scrolled while an item is being\ndragged.", "generics": [], "isNewType": false, "jsdocTags": [{"name": "param", "comment": "Optional shadow root that the current dragging sequence started from.\nTop-level listeners won't pick up events coming from the shadow DOM so this parameter can\nbe used to include an additional top-level listener at the shadow root level."}], "params": [{"name": "shadowRoot", "description": "Optional shadow root that the current dragging sequence started from.\nTop-level listeners won't pick up events coming from the shadow DOM so this parameter can\nbe used to include an additional top-level listener at the shadow root level.", "type": "DocumentOrShadowRoot | null | undefined", "isOptional": true, "isRestParam": false}], "rawComment": "/**\n   * Gets a stream that will emit when any element on the page is scrolled while an item is being\n   * dragged.\n   * @param shadowRoot Optional shadow root that the current dragging sequence started from.\n   *   Top-level listeners won't pick up events coming from the shadow DOM so this parameter can\n   *   be used to include an additional top-level listener at the shadow root level.\n   */", "returnType": "Observable<Event>"}], "implementation": {"params": [{"name": "shadowRoot", "description": "Optional shadow root that the current dragging sequence started from.\nTop-level listeners won't pick up events coming from the shadow DOM so this parameter can\nbe used to include an additional top-level listener at the shadow root level.", "type": "DocumentOrShadowRoot | null | undefined", "isOptional": true, "isRestParam": false}], "isNewType": false, "returnType": "Observable<Event>", "generics": [], "name": "scrolled", "description": "Gets a stream that will emit when any element on the page is scrolled while an item is being\ndragged.", "entryType": "function", "jsdocTags": [{"name": "param", "comment": "Optional shadow root that the current dragging sequence started from.\nTop-level listeners won't pick up events coming from the shadow DOM so this parameter can\nbe used to include an additional top-level listener at the shadow root level."}], "rawComment": "/**\n   * Gets a stream that will emit when any element on the page is scrolled while an item is being\n   * dragged.\n   * @param shadowRoot Optional shadow root that the current dragging sequence started from.\n   *   Top-level listeners won't pick up events coming from the shadow DOM so this parameter can\n   *   be used to include an additional top-level listener at the shadow root level.\n   */"}, "entryType": "function", "description": "Gets a stream that will emit when any element on the page is scrolled while an item is being\ndragged.", "jsdocTags": [{"name": "param", "comment": "Optional shadow root that the current dragging sequence started from.\nTop-level listeners won't pick up events coming from the shadow DOM so this parameter can\nbe used to include an additional top-level listener at the shadow root level."}], "rawComment": "/**\n   * Gets a stream that will emit when any element on the page is scrolled while an item is being\n   * dragged.\n   * @param shadowRoot Optional shadow root that the current dragging sequence started from.\n   *   Top-level listeners won't pick up events coming from the shadow DOM so this parameter can\n   *   be used to include an additional top-level listener at the shadow root level.\n   */", "memberType": "method", "memberTags": []}, {"name": "registerDirectiveNode", "signatures": [{"name": "registerDirectiveNode", "entryType": "function", "description": "Tracks the DOM node which has a draggable directive.", "generics": [], "isNewType": false, "jsdocTags": [{"name": "param", "comment": "Node to track."}, {"name": "param", "comment": "Drag directive set on the node."}], "params": [{"name": "node", "description": "Node to track.", "type": "Node", "isOptional": false, "isRestParam": false}, {"name": "dragRef", "description": "Drag directive set on the node.", "type": "CdkDrag<any>", "isOptional": false, "isRestParam": false}], "rawComment": "/**\n   * Tracks the DOM node which has a draggable directive.\n   * @param node Node to track.\n   * @param dragRef Drag directive set on the node.\n   */", "returnType": "void"}], "implementation": {"params": [{"name": "node", "description": "Node to track.", "type": "Node", "isOptional": false, "isRestParam": false}, {"name": "dragRef", "description": "Drag directive set on the node.", "type": "CdkDrag<any>", "isOptional": false, "isRestParam": false}], "isNewType": false, "returnType": "void", "generics": [], "name": "registerDirectiveNode", "description": "Tracks the DOM node which has a draggable directive.", "entryType": "function", "jsdocTags": [{"name": "param", "comment": "Node to track."}, {"name": "param", "comment": "Drag directive set on the node."}], "rawComment": "/**\n   * Tracks the DOM node which has a draggable directive.\n   * @param node Node to track.\n   * @param dragRef Drag directive set on the node.\n   */"}, "entryType": "function", "description": "Tracks the DOM node which has a draggable directive.", "jsdocTags": [{"name": "param", "comment": "Node to track."}, {"name": "param", "comment": "Drag directive set on the node."}], "rawComment": "/**\n   * Tracks the DOM node which has a draggable directive.\n   * @param node Node to track.\n   * @param dragRef Drag directive set on the node.\n   */", "memberType": "method", "memberTags": []}, {"name": "removeDirectiveNode", "signatures": [{"name": "removeDirectiveNode", "entryType": "function", "description": "Stops tracking a draggable directive node.", "generics": [], "isNewType": false, "jsdocTags": [{"name": "param", "comment": "Node to stop tracking."}], "params": [{"name": "node", "description": "Node to stop tracking.", "type": "Node", "isOptional": false, "isRestParam": false}], "rawComment": "/**\n   * Stops tracking a draggable directive node.\n   * @param node Node to stop tracking.\n   */", "returnType": "void"}], "implementation": {"params": [{"name": "node", "description": "Node to stop tracking.", "type": "Node", "isOptional": false, "isRestParam": false}], "isNewType": false, "returnType": "void", "generics": [], "name": "removeDirectiveNode", "description": "Stops tracking a draggable directive node.", "entryType": "function", "jsdocTags": [{"name": "param", "comment": "Node to stop tracking."}], "rawComment": "/**\n   * Stops tracking a draggable directive node.\n   * @param node Node to stop tracking.\n   */"}, "entryType": "function", "description": "Stops tracking a draggable directive node.", "jsdocTags": [{"name": "param", "comment": "Node to stop tracking."}], "rawComment": "/**\n   * Stops tracking a draggable directive node.\n   * @param node Node to stop tracking.\n   */", "memberType": "method", "memberTags": []}, {"name": "getDragDirectiveForNode", "signatures": [{"name": "getDragDirectiveForNode", "entryType": "function", "description": "Gets the drag directive corresponding to a specific DOM node, if any.", "generics": [], "isNewType": false, "jsdocTags": [{"name": "param", "comment": "Node for which to do the lookup."}], "params": [{"name": "node", "description": "Node for which to do the lookup.", "type": "Node", "isOptional": false, "isRestParam": false}], "rawComment": "/**\n   * Gets the drag directive corresponding to a specific DOM node, if any.\n   * @param node Node for which to do the lookup.\n   */", "returnType": "CdkDrag<any> | null"}], "implementation": {"params": [{"name": "node", "description": "Node for which to do the lookup.", "type": "Node", "isOptional": false, "isRestParam": false}], "isNewType": false, "returnType": "CdkDrag<any> | null", "generics": [], "name": "getDragDirectiveForNode", "description": "Gets the drag directive corresponding to a specific DOM node, if any.", "entryType": "function", "jsdocTags": [{"name": "param", "comment": "Node for which to do the lookup."}], "rawComment": "/**\n   * Gets the drag directive corresponding to a specific DOM node, if any.\n   * @param node Node for which to do the lookup.\n   */"}, "entryType": "function", "description": "Gets the drag directive corresponding to a specific DOM node, if any.", "jsdocTags": [{"name": "param", "comment": "Node for which to do the lookup."}], "rawComment": "/**\n   * Gets the drag directive corresponding to a specific DOM node, if any.\n   * @param node Node for which to do the lookup.\n   */", "memberType": "method", "memberTags": []}, {"name": "ngOnDestroy", "signatures": [{"name": "ngOnDestroy", "entryType": "function", "description": "", "generics": [], "isNewType": false, "jsdocTags": [], "params": [], "rawComment": "", "returnType": "void"}], "implementation": {"params": [], "isNewType": false, "returnType": "void", "generics": [], "name": "ngOnDestroy", "description": "", "entryType": "function", "jsdocTags": [], "rawComment": ""}, "entryType": "function", "description": "", "jsdocTags": [], "rawComment": "", "memberType": "method", "memberTags": []}], "generics": [{"name": "_", "default": "unknown"}, {"name": "__", "default": "unknown"}], "description": "Service that keeps track of all the drag item and drop container\ninstances, and manages global event listeners on the `document`.", "jsdocTags": [{"name": "docs-private", "comment": ""}], "rawComment": "/**\n * Service that keeps track of all the drag item and drop container\n * instances, and manages global event listeners on the `document`.\n * @docs-private\n */", "implements": ["OnDestroy"], "source": {"filePath": "/src/cdk/drag-drop/drag-drop-registry.ts", "startLine": 59, "endLine": 344}}, {"name": "CdkDragEnter", "isAbstract": false, "entryType": "interface", "members": [{"name": "container", "type": "CdkDropList<T>", "memberType": "property", "memberTags": [], "description": "Container into which the user has moved the item.", "jsdocTags": []}, {"name": "item", "type": "CdkDrag<I>", "memberType": "property", "memberTags": [], "description": "Item that was moved into the container.", "jsdocTags": []}, {"name": "currentIndex", "type": "number", "memberType": "property", "memberTags": [], "description": "Index at which the item has entered the container.", "jsdocTags": []}], "generics": [{"name": "T", "default": "any"}, {"name": "I", "default": "T"}], "description": "Event emitted when the user moves an item into a new drop container.", "jsdocTags": [], "rawComment": "/** Event emitted when the user moves an item into a new drop container. */", "implements": [], "source": {"filePath": "/src/cdk/drag-drop/drag-events.ts", "startLine": 41, "endLine": 48}}, {"name": "copyArrayItem", "signatures": [{"name": "copyArrayItem", "entryType": "function", "description": "Copies an item from one array to another, leaving it in its\noriginal position in current array.", "generics": [{"name": "T", "default": "any"}], "isNewType": false, "jsdocTags": [{"name": "param", "comment": "Array from which to copy the item."}, {"name": "param", "comment": "Array into which is copy the item."}, {"name": "param", "comment": "Index of the item in its current array."}, {"name": "param", "comment": "Index at which to insert the item."}], "params": [{"name": "currentArray", "description": "Array from which to copy the item.", "type": "T[]", "isOptional": false, "isRestParam": false}, {"name": "targetArray", "description": "Array into which is copy the item.", "type": "T[]", "isOptional": false, "isRestParam": false}, {"name": "currentIndex", "description": "Index of the item in its current array.", "type": "number", "isOptional": false, "isRestParam": false}, {"name": "targetIndex", "description": "Index at which to insert the item.", "type": "number", "isOptional": false, "isRestParam": false}], "rawComment": "/**\n * Copies an item from one array to another, leaving it in its\n * original position in current array.\n * @param currentArray Array from which to copy the item.\n * @param targetArray Array into which is copy the item.\n * @param currentIndex Index of the item in its current array.\n * @param targetIndex Index at which to insert the item.\n *\n */", "returnType": "void"}], "implementation": {"params": [{"name": "currentArray", "description": "Array from which to copy the item.", "type": "T[]", "isOptional": false, "isRestParam": false}, {"name": "targetArray", "description": "Array into which is copy the item.", "type": "T[]", "isOptional": false, "isRestParam": false}, {"name": "currentIndex", "description": "Index of the item in its current array.", "type": "number", "isOptional": false, "isRestParam": false}, {"name": "targetIndex", "description": "Index at which to insert the item.", "type": "number", "isOptional": false, "isRestParam": false}], "isNewType": false, "returnType": "void", "generics": [{"name": "T", "default": "any"}], "name": "copyArrayItem", "description": "Copies an item from one array to another, leaving it in its\noriginal position in current array.", "entryType": "function", "jsdocTags": [{"name": "param", "comment": "Array from which to copy the item."}, {"name": "param", "comment": "Array into which is copy the item."}, {"name": "param", "comment": "Index of the item in its current array."}, {"name": "param", "comment": "Index at which to insert the item."}], "rawComment": "/**\n * Copies an item from one array to another, leaving it in its\n * original position in current array.\n * @param currentArray Array from which to copy the item.\n * @param targetArray Array into which is copy the item.\n * @param currentIndex Index of the item in its current array.\n * @param targetIndex Index at which to insert the item.\n *\n */"}, "entryType": "function", "description": "Copies an item from one array to another, leaving it in its\noriginal position in current array.", "jsdocTags": [{"name": "param", "comment": "Array from which to copy the item."}, {"name": "param", "comment": "Array into which is copy the item."}, {"name": "param", "comment": "Index of the item in its current array."}, {"name": "param", "comment": "Index at which to insert the item."}], "rawComment": "/**\n * Copies an item from one array to another, leaving it in its\n * original position in current array.\n * @param currentArray Array from which to copy the item.\n * @param targetArray Array into which is copy the item.\n * @param currentIndex Index of the item in its current array.\n * @param targetIndex Index at which to insert the item.\n *\n */", "source": {"filePath": "/src/cdk/drag-drop/drag-utils.ts", "startLine": 63, "endLine": 74}}, {"name": "CdkDragExit", "isAbstract": false, "entryType": "interface", "members": [{"name": "container", "type": "CdkDropList<T>", "memberType": "property", "memberTags": [], "description": "Container from which the user has a removed an item.", "jsdocTags": []}, {"name": "item", "type": "CdkDrag<I>", "memberType": "property", "memberTags": [], "description": "Item that was removed from the container.", "jsdocTags": []}], "generics": [{"name": "T", "default": "any"}, {"name": "I", "default": "T"}], "description": "Event emitted when the user removes an item from a\ndrop container by moving it into another one.", "jsdocTags": [], "rawComment": "/**\n * Event emitted when the user removes an item from a\n * drop container by moving it into another one.\n */", "implements": [], "source": {"filePath": "/src/cdk/drag-drop/drag-events.ts", "startLine": 54, "endLine": 59}}, {"name": "DropListRef", "isAbstract": false, "entryType": "undecorated_class", "members": [{"name": "constructor", "signatures": [], "implementation": {"params": [{"name": "element", "description": "", "type": "any", "isOptional": false, "isRestParam": false}, {"name": "_dragDropRegistry", "description": "", "type": "DragDropRegistry<unknown, unknown>", "isOptional": false, "isRestParam": false}, {"name": "_document", "description": "", "type": "any", "isOptional": false, "isRestParam": false}, {"name": "_ngZone", "description": "", "type": "NgZone", "isOptional": false, "isRestParam": false}, {"name": "_viewportRuler", "description": "", "type": "ViewportRuler", "isOptional": false, "isRestParam": false}], "isNewType": false, "returnType": "DropListRef<T>", "generics": [], "name": "constructor", "description": "", "entryType": "function", "jsdocTags": [], "rawComment": ""}, "entryType": "function", "description": "", "jsdocTags": [], "rawComment": "", "memberType": "method", "memberTags": []}, {"name": "element", "type": "any", "memberType": "property", "memberTags": [], "description": "Element that the drop list is attached to.", "jsdocTags": []}, {"name": "disabled", "type": "boolean", "memberType": "property", "memberTags": [], "description": "Whether starting a dragging sequence from this container is disabled.", "jsdocTags": []}, {"name": "sortingDisabled", "type": "boolean", "memberType": "property", "memberTags": [], "description": "Whether sorting items within the list is disabled.", "jsdocTags": []}, {"name": "lockAxis", "type": "\"x\" | \"y\"", "memberType": "property", "memberTags": [], "description": "Locks the position of the draggable elements inside the container along the specified axis.", "jsdocTags": []}, {"name": "autoScrollDisabled", "type": "boolean", "memberType": "property", "memberTags": [], "description": "Whether auto-scrolling the view when the user\nmoves their pointer close to the edges is disabled.", "jsdocTags": []}, {"name": "autoScrollStep", "type": "number", "memberType": "property", "memberTags": [], "description": "Number of pixels to scroll for each frame when auto-scrolling an element.", "jsdocTags": []}, {"name": "enterPredicate", "type": "(drag: DragRef<any>, drop: DropListRef<any>) => boolean", "memberType": "property", "memberTags": [], "description": "Function that is used to determine whether an item\nis allowed to be moved into a drop container.", "jsdocTags": []}, {"name": "sortPredicate", "type": "(index: number, drag: DragRef<any>, drop: DropListRef<any>) => boolean", "memberType": "property", "memberTags": [], "description": "Function that is used to determine whether an item can be sorted into a particular index.", "jsdocTags": []}, {"name": "beforeStarted", "type": "any", "memberType": "property", "memberTags": ["readonly"], "description": "Emits right before dragging has started.", "jsdocTags": []}, {"name": "entered", "type": "any", "memberType": "property", "memberTags": ["readonly"], "description": "Emits when the user has moved a new drag item into this container.", "jsdocTags": []}, {"name": "exited", "type": "any", "memberType": "property", "memberTags": ["readonly"], "description": "Emits when the user removes an item from the container\nby dragging it into another container.", "jsdocTags": []}, {"name": "dropped", "type": "any", "memberType": "property", "memberTags": ["readonly"], "description": "Emits when the user drops an item inside the container.", "jsdocTags": []}, {"name": "sorted", "type": "any", "memberType": "property", "memberTags": ["readonly"], "description": "Emits as the user is swapping items while actively dragging.", "jsdocTags": []}, {"name": "receivingStarted", "type": "any", "memberType": "property", "memberTags": ["readonly"], "description": "Emits when a dragging sequence is started in a list connected to the current one.", "jsdocTags": []}, {"name": "receivingStopped", "type": "any", "memberType": "property", "memberTags": ["readonly"], "description": "Emits when a dragging sequence is stopped from a list connected to the current one.", "jsdocTags": []}, {"name": "data", "type": "T", "memberType": "property", "memberTags": [], "description": "Arbitrary data that can be attached to the drop list.", "jsdocTags": []}, {"name": "dispose", "signatures": [{"name": "dispose", "entryType": "function", "description": "Removes the drop list functionality from the DOM element.", "generics": [], "isNewType": false, "jsdocTags": [], "params": [], "rawComment": "/** Removes the drop list functionality from the DOM element. */", "returnType": "void"}], "implementation": {"params": [], "isNewType": false, "returnType": "void", "generics": [], "name": "dispose", "description": "Removes the drop list functionality from the DOM element.", "entryType": "function", "jsdocTags": [], "rawComment": "/** Removes the drop list functionality from the DOM element. */"}, "entryType": "function", "description": "Removes the drop list functionality from the DOM element.", "jsdocTags": [], "rawComment": "/** Removes the drop list functionality from the DOM element. */", "memberType": "method", "memberTags": []}, {"name": "isDragging", "signatures": [{"name": "isDragging", "entryType": "function", "description": "Whether an item from this list is currently being dragged.", "generics": [], "isNewType": false, "jsdocTags": [], "params": [], "rawComment": "/** Whether an item from this list is currently being dragged. */", "returnType": "boolean"}], "implementation": {"params": [], "isNewType": false, "returnType": "boolean", "generics": [], "name": "isDragging", "description": "Whether an item from this list is currently being dragged.", "entryType": "function", "jsdocTags": [], "rawComment": "/** Whether an item from this list is currently being dragged. */"}, "entryType": "function", "description": "Whether an item from this list is currently being dragged.", "jsdocTags": [], "rawComment": "/** Whether an item from this list is currently being dragged. */", "memberType": "method", "memberTags": []}, {"name": "start", "signatures": [{"name": "start", "entryType": "function", "description": "Starts dragging an item.", "generics": [], "isNewType": false, "jsdocTags": [], "params": [], "rawComment": "/** Starts dragging an item. */", "returnType": "void"}], "implementation": {"params": [], "isNewType": false, "returnType": "void", "generics": [], "name": "start", "description": "Starts dragging an item.", "entryType": "function", "jsdocTags": [], "rawComment": "/** Starts dragging an item. */"}, "entryType": "function", "description": "Starts dragging an item.", "jsdocTags": [], "rawComment": "/** Starts dragging an item. */", "memberType": "method", "memberTags": []}, {"name": "enter", "signatures": [{"name": "enter", "entryType": "function", "description": "Attempts to move an item into the container.", "generics": [], "isNewType": false, "jsdocTags": [{"name": "param", "comment": "Item that was moved into the container."}, {"name": "param", "comment": "Position of the item along the X axis."}, {"name": "param", "comment": "Position of the item along the Y axis."}, {"name": "param", "comment": "Index at which the item entered. If omitted, the container will try to figure it\nout automatically."}], "params": [{"name": "item", "description": "Item that was moved into the container.", "type": "DragRef<any>", "isOptional": false, "isRestParam": false}, {"name": "pointerX", "description": "Position of the item along the X axis.", "type": "number", "isOptional": false, "isRestParam": false}, {"name": "pointerY", "description": "Position of the item along the Y axis.", "type": "number", "isOptional": false, "isRestParam": false}, {"name": "index", "description": "Index at which the item entered. If omitted, the container will try to figure it\nout automatically.", "type": "number | undefined", "isOptional": true, "isRestParam": false}], "rawComment": "/**\n   * Attempts to move an item into the container.\n   * @param item Item that was moved into the container.\n   * @param pointerX Position of the item along the X axis.\n   * @param pointerY Position of the item along the Y axis.\n   * @param index Index at which the item entered. If omitted, the container will try to figure it\n   *   out automatically.\n   */", "returnType": "void"}], "implementation": {"params": [{"name": "item", "description": "Item that was moved into the container.", "type": "DragRef<any>", "isOptional": false, "isRestParam": false}, {"name": "pointerX", "description": "Position of the item along the X axis.", "type": "number", "isOptional": false, "isRestParam": false}, {"name": "pointerY", "description": "Position of the item along the Y axis.", "type": "number", "isOptional": false, "isRestParam": false}, {"name": "index", "description": "Index at which the item entered. If omitted, the container will try to figure it\nout automatically.", "type": "number | undefined", "isOptional": true, "isRestParam": false}], "isNewType": false, "returnType": "void", "generics": [], "name": "enter", "description": "Attempts to move an item into the container.", "entryType": "function", "jsdocTags": [{"name": "param", "comment": "Item that was moved into the container."}, {"name": "param", "comment": "Position of the item along the X axis."}, {"name": "param", "comment": "Position of the item along the Y axis."}, {"name": "param", "comment": "Index at which the item entered. If omitted, the container will try to figure it\nout automatically."}], "rawComment": "/**\n   * Attempts to move an item into the container.\n   * @param item Item that was moved into the container.\n   * @param pointerX Position of the item along the X axis.\n   * @param pointerY Position of the item along the Y axis.\n   * @param index Index at which the item entered. If omitted, the container will try to figure it\n   *   out automatically.\n   */"}, "entryType": "function", "description": "Attempts to move an item into the container.", "jsdocTags": [{"name": "param", "comment": "Item that was moved into the container."}, {"name": "param", "comment": "Position of the item along the X axis."}, {"name": "param", "comment": "Position of the item along the Y axis."}, {"name": "param", "comment": "Index at which the item entered. If omitted, the container will try to figure it\nout automatically."}], "rawComment": "/**\n   * Attempts to move an item into the container.\n   * @param item Item that was moved into the container.\n   * @param pointerX Position of the item along the X axis.\n   * @param pointerY Position of the item along the Y axis.\n   * @param index Index at which the item entered. If omitted, the container will try to figure it\n   *   out automatically.\n   */", "memberType": "method", "memberTags": []}, {"name": "exit", "signatures": [{"name": "exit", "entryType": "function", "description": "Removes an item from the container after it was dragged into another container by the user.", "generics": [], "isNewType": false, "jsdocTags": [{"name": "param", "comment": "Item that was dragged out."}], "params": [{"name": "item", "description": "Item that was dragged out.", "type": "DragRef<any>", "isOptional": false, "isRestParam": false}], "rawComment": "/**\n   * Removes an item from the container after it was dragged into another container by the user.\n   * @param item Item that was dragged out.\n   */", "returnType": "void"}], "implementation": {"params": [{"name": "item", "description": "Item that was dragged out.", "type": "DragRef<any>", "isOptional": false, "isRestParam": false}], "isNewType": false, "returnType": "void", "generics": [], "name": "exit", "description": "Removes an item from the container after it was dragged into another container by the user.", "entryType": "function", "jsdocTags": [{"name": "param", "comment": "Item that was dragged out."}], "rawComment": "/**\n   * Removes an item from the container after it was dragged into another container by the user.\n   * @param item Item that was dragged out.\n   */"}, "entryType": "function", "description": "Removes an item from the container after it was dragged into another container by the user.", "jsdocTags": [{"name": "param", "comment": "Item that was dragged out."}], "rawComment": "/**\n   * Removes an item from the container after it was dragged into another container by the user.\n   * @param item Item that was dragged out.\n   */", "memberType": "method", "memberTags": []}, {"name": "drop", "signatures": [{"name": "drop", "entryType": "function", "description": "Drops an item into this container.", "generics": [], "isNewType": false, "jsdocTags": [{"name": "param", "comment": "Item being dropped into the container."}, {"name": "param", "comment": "Index at which the item should be inserted."}, {"name": "param", "comment": "Index of the item when dragging started."}, {"name": "param", "comment": "Container from which the item got dragged in."}, {"name": "param", "comment": "Whether the user's pointer was over the\ncontainer when the item was dropped."}, {"name": "param", "comment": "Distance the user has dragged since the start of the dragging sequence."}, {"name": "param", "comment": "Event that triggered the dropping sequence."}, {"name": "breaking-change", "comment": "15.0.0 `previousIndex` and `event` parameters to become required."}], "params": [{"name": "item", "description": "Item being dropped into the container.", "type": "DragRef<any>", "isOptional": false, "isRestParam": false}, {"name": "currentIndex", "description": "Index at which the item should be inserted.", "type": "number", "isOptional": false, "isRestParam": false}, {"name": "previousIndex", "description": "Index of the item when dragging started.", "type": "number", "isOptional": false, "isRestParam": false}, {"name": "previousContainer", "description": "Container from which the item got dragged in.", "type": "DropListRef<any>", "isOptional": false, "isRestParam": false}, {"name": "isPointerOverContainer", "description": "Whether the user's pointer was over the\ncontainer when the item was dropped.", "type": "boolean", "isOptional": false, "isRestParam": false}, {"name": "distance", "description": "Distance the user has dragged since the start of the dragging sequence.", "type": "Point", "isOptional": false, "isRestParam": false}, {"name": "dropPoint", "description": "", "type": "Point", "isOptional": false, "isRestParam": false}, {"name": "event", "description": "Event that triggered the dropping sequence.", "type": "MouseEvent | TouchEvent", "isOptional": true, "isRestParam": false}], "rawComment": "/**\n   * Drops an item into this container.\n   * @param item Item being dropped into the container.\n   * @param currentIndex Index at which the item should be inserted.\n   * @param previousIndex Index of the item when dragging started.\n   * @param previousContainer Container from which the item got dragged in.\n   * @param isPointerOverContainer Whether the user's pointer was over the\n   *    container when the item was dropped.\n   * @param distance Distance the user has dragged since the start of the dragging sequence.\n   * @param event Event that triggered the dropping sequence.\n   *\n   * @breaking-change 15.0.0 `previousIndex` and `event` parameters to become required.\n   */", "returnType": "void"}], "implementation": {"params": [{"name": "item", "description": "Item being dropped into the container.", "type": "DragRef<any>", "isOptional": false, "isRestParam": false}, {"name": "currentIndex", "description": "Index at which the item should be inserted.", "type": "number", "isOptional": false, "isRestParam": false}, {"name": "previousIndex", "description": "Index of the item when dragging started.", "type": "number", "isOptional": false, "isRestParam": false}, {"name": "previousContainer", "description": "Container from which the item got dragged in.", "type": "DropListRef<any>", "isOptional": false, "isRestParam": false}, {"name": "isPointerOverContainer", "description": "Whether the user's pointer was over the\ncontainer when the item was dropped.", "type": "boolean", "isOptional": false, "isRestParam": false}, {"name": "distance", "description": "Distance the user has dragged since the start of the dragging sequence.", "type": "Point", "isOptional": false, "isRestParam": false}, {"name": "dropPoint", "description": "", "type": "Point", "isOptional": false, "isRestParam": false}, {"name": "event", "description": "Event that triggered the dropping sequence.", "type": "MouseEvent | TouchEvent", "isOptional": true, "isRestParam": false}], "isNewType": false, "returnType": "void", "generics": [], "name": "drop", "description": "Drops an item into this container.", "entryType": "function", "jsdocTags": [{"name": "param", "comment": "Item being dropped into the container."}, {"name": "param", "comment": "Index at which the item should be inserted."}, {"name": "param", "comment": "Index of the item when dragging started."}, {"name": "param", "comment": "Container from which the item got dragged in."}, {"name": "param", "comment": "Whether the user's pointer was over the\ncontainer when the item was dropped."}, {"name": "param", "comment": "Distance the user has dragged since the start of the dragging sequence."}, {"name": "param", "comment": "Event that triggered the dropping sequence."}, {"name": "breaking-change", "comment": "15.0.0 `previousIndex` and `event` parameters to become required."}], "rawComment": "/**\n   * Drops an item into this container.\n   * @param item Item being dropped into the container.\n   * @param currentIndex Index at which the item should be inserted.\n   * @param previousIndex Index of the item when dragging started.\n   * @param previousContainer Container from which the item got dragged in.\n   * @param isPointerOverContainer Whether the user's pointer was over the\n   *    container when the item was dropped.\n   * @param distance Distance the user has dragged since the start of the dragging sequence.\n   * @param event Event that triggered the dropping sequence.\n   *\n   * @breaking-change 15.0.0 `previousIndex` and `event` parameters to become required.\n   */"}, "entryType": "function", "description": "Drops an item into this container.", "jsdocTags": [{"name": "param", "comment": "Item being dropped into the container."}, {"name": "param", "comment": "Index at which the item should be inserted."}, {"name": "param", "comment": "Index of the item when dragging started."}, {"name": "param", "comment": "Container from which the item got dragged in."}, {"name": "param", "comment": "Whether the user's pointer was over the\ncontainer when the item was dropped."}, {"name": "param", "comment": "Distance the user has dragged since the start of the dragging sequence."}, {"name": "param", "comment": "Event that triggered the dropping sequence."}, {"name": "breaking-change", "comment": "15.0.0 `previousIndex` and `event` parameters to become required."}], "rawComment": "/**\n   * Drops an item into this container.\n   * @param item Item being dropped into the container.\n   * @param currentIndex Index at which the item should be inserted.\n   * @param previousIndex Index of the item when dragging started.\n   * @param previousContainer Container from which the item got dragged in.\n   * @param isPointerOverContainer Whether the user's pointer was over the\n   *    container when the item was dropped.\n   * @param distance Distance the user has dragged since the start of the dragging sequence.\n   * @param event Event that triggered the dropping sequence.\n   *\n   * @breaking-change 15.0.0 `previousIndex` and `event` parameters to become required.\n   */", "memberType": "method", "memberTags": []}, {"name": "withItems", "signatures": [{"name": "withItems", "entryType": "function", "description": "Sets the draggable items that are a part of this list.", "generics": [], "isNewType": false, "jsdocTags": [{"name": "param", "comment": "Items that are a part of this list."}], "params": [{"name": "items", "description": "Items that are a part of this list.", "type": "DragRef<any>[]", "isOptional": false, "isRestParam": false}], "rawComment": "/**\n   * Sets the draggable items that are a part of this list.\n   * @param items Items that are a part of this list.\n   */", "returnType": "this"}], "implementation": {"params": [{"name": "items", "description": "Items that are a part of this list.", "type": "DragRef<any>[]", "isOptional": false, "isRestParam": false}], "isNewType": false, "returnType": "this", "generics": [], "name": "withItems", "description": "Sets the draggable items that are a part of this list.", "entryType": "function", "jsdocTags": [{"name": "param", "comment": "Items that are a part of this list."}], "rawComment": "/**\n   * Sets the draggable items that are a part of this list.\n   * @param items Items that are a part of this list.\n   */"}, "entryType": "function", "description": "Sets the draggable items that are a part of this list.", "jsdocTags": [{"name": "param", "comment": "Items that are a part of this list."}], "rawComment": "/**\n   * Sets the draggable items that are a part of this list.\n   * @param items Items that are a part of this list.\n   */", "memberType": "method", "memberTags": []}, {"name": "withDirection", "signatures": [{"name": "withDirection", "entryType": "function", "description": "Sets the layout direction of the drop list.", "generics": [], "isNewType": false, "jsdocTags": [], "params": [{"name": "direction", "description": "", "type": "Direction", "isOptional": false, "isRestParam": false}], "rawComment": "/** Sets the layout direction of the drop list. */", "returnType": "this"}], "implementation": {"params": [{"name": "direction", "description": "", "type": "Direction", "isOptional": false, "isRestParam": false}], "isNewType": false, "returnType": "this", "generics": [], "name": "withDirection", "description": "Sets the layout direction of the drop list.", "entryType": "function", "jsdocTags": [], "rawComment": "/** Sets the layout direction of the drop list. */"}, "entryType": "function", "description": "Sets the layout direction of the drop list.", "jsdocTags": [], "rawComment": "/** Sets the layout direction of the drop list. */", "memberType": "method", "memberTags": []}, {"name": "connectedTo", "signatures": [{"name": "connectedTo", "entryType": "function", "description": "Sets the containers that are connected to this one. When two or more containers are\nconnected, the user will be allowed to transfer items between them.", "generics": [], "isNewType": false, "jsdocTags": [{"name": "param", "comment": "Other containers that the current containers should be connected to."}], "params": [{"name": "connectedTo", "description": "Other containers that the current containers should be connected to.", "type": "DropListRef<any>[]", "isOptional": false, "isRestParam": false}], "rawComment": "/**\n   * Sets the containers that are connected to this one. When two or more containers are\n   * connected, the user will be allowed to transfer items between them.\n   * @param connectedTo Other containers that the current containers should be connected to.\n   */", "returnType": "this"}], "implementation": {"params": [{"name": "connectedTo", "description": "Other containers that the current containers should be connected to.", "type": "DropListRef<any>[]", "isOptional": false, "isRestParam": false}], "isNewType": false, "returnType": "this", "generics": [], "name": "connectedTo", "description": "Sets the containers that are connected to this one. When two or more containers are\nconnected, the user will be allowed to transfer items between them.", "entryType": "function", "jsdocTags": [{"name": "param", "comment": "Other containers that the current containers should be connected to."}], "rawComment": "/**\n   * Sets the containers that are connected to this one. When two or more containers are\n   * connected, the user will be allowed to transfer items between them.\n   * @param connectedTo Other containers that the current containers should be connected to.\n   */"}, "entryType": "function", "description": "Sets the containers that are connected to this one. When two or more containers are\nconnected, the user will be allowed to transfer items between them.", "jsdocTags": [{"name": "param", "comment": "Other containers that the current containers should be connected to."}], "rawComment": "/**\n   * Sets the containers that are connected to this one. When two or more containers are\n   * connected, the user will be allowed to transfer items between them.\n   * @param connectedTo Other containers that the current containers should be connected to.\n   */", "memberType": "method", "memberTags": []}, {"name": "withOrientation", "signatures": [{"name": "withOrientation", "entryType": "function", "description": "Sets the orientation of the container.", "generics": [], "isNewType": false, "jsdocTags": [{"name": "param", "comment": "New orientation for the container."}], "params": [{"name": "orientation", "description": "New orientation for the container.", "type": "DropListOrientation", "isOptional": false, "isRestParam": false}], "rawComment": "/**\n   * Sets the orientation of the container.\n   * @param orientation New orientation for the container.\n   */", "returnType": "this"}], "implementation": {"params": [{"name": "orientation", "description": "New orientation for the container.", "type": "DropListOrientation", "isOptional": false, "isRestParam": false}], "isNewType": false, "returnType": "this", "generics": [], "name": "withOrientation", "description": "Sets the orientation of the container.", "entryType": "function", "jsdocTags": [{"name": "param", "comment": "New orientation for the container."}], "rawComment": "/**\n   * Sets the orientation of the container.\n   * @param orientation New orientation for the container.\n   */"}, "entryType": "function", "description": "Sets the orientation of the container.", "jsdocTags": [{"name": "param", "comment": "New orientation for the container."}], "rawComment": "/**\n   * Sets the orientation of the container.\n   * @param orientation New orientation for the container.\n   */", "memberType": "method", "memberTags": []}, {"name": "withScrollableParents", "signatures": [{"name": "withScrollableParents", "entryType": "function", "description": "Sets which parent elements are can be scrolled while the user is dragging.", "generics": [], "isNewType": false, "jsdocTags": [{"name": "param", "comment": "Elements that can be scrolled."}], "params": [{"name": "elements", "description": "Elements that can be scrolled.", "type": "HTMLElement[]", "isOptional": false, "isRestParam": false}], "rawComment": "/**\n   * Sets which parent elements are can be scrolled while the user is dragging.\n   * @param elements Elements that can be scrolled.\n   */", "returnType": "this"}], "implementation": {"params": [{"name": "elements", "description": "Elements that can be scrolled.", "type": "HTMLElement[]", "isOptional": false, "isRestParam": false}], "isNewType": false, "returnType": "this", "generics": [], "name": "withScrollableParents", "description": "Sets which parent elements are can be scrolled while the user is dragging.", "entryType": "function", "jsdocTags": [{"name": "param", "comment": "Elements that can be scrolled."}], "rawComment": "/**\n   * Sets which parent elements are can be scrolled while the user is dragging.\n   * @param elements Elements that can be scrolled.\n   */"}, "entryType": "function", "description": "Sets which parent elements are can be scrolled while the user is dragging.", "jsdocTags": [{"name": "param", "comment": "Elements that can be scrolled."}], "rawComment": "/**\n   * Sets which parent elements are can be scrolled while the user is dragging.\n   * @param elements Elements that can be scrolled.\n   */", "memberType": "method", "memberTags": []}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "signatures": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entryType": "function", "description": "Configures the drop list so that a different element is used as the container for the\ndragged items. This is useful for the cases when one might not have control over the\nfull DOM that sets up the dragging.\nNote that the alternate container needs to be a descendant of the drop list.", "generics": [], "isNewType": false, "jsdocTags": [{"name": "param", "comment": "New element container to be assigned."}], "params": [{"name": "container", "description": "New element container to be assigned.", "type": "HTMLElement", "isOptional": false, "isRestParam": false}], "rawComment": "/**\n   * Configures the drop list so that a different element is used as the container for the\n   * dragged items. This is useful for the cases when one might not have control over the\n   * full DOM that sets up the dragging.\n   * Note that the alternate container needs to be a descendant of the drop list.\n   * @param container New element container to be assigned.\n   */", "returnType": "this"}], "implementation": {"params": [{"name": "container", "description": "New element container to be assigned.", "type": "HTMLElement", "isOptional": false, "isRestParam": false}], "isNewType": false, "returnType": "this", "generics": [], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Configures the drop list so that a different element is used as the container for the\ndragged items. This is useful for the cases when one might not have control over the\nfull DOM that sets up the dragging.\nNote that the alternate container needs to be a descendant of the drop list.", "entryType": "function", "jsdocTags": [{"name": "param", "comment": "New element container to be assigned."}], "rawComment": "/**\n   * Configures the drop list so that a different element is used as the container for the\n   * dragged items. This is useful for the cases when one might not have control over the\n   * full DOM that sets up the dragging.\n   * Note that the alternate container needs to be a descendant of the drop list.\n   * @param container New element container to be assigned.\n   */"}, "entryType": "function", "description": "Configures the drop list so that a different element is used as the container for the\ndragged items. This is useful for the cases when one might not have control over the\nfull DOM that sets up the dragging.\nNote that the alternate container needs to be a descendant of the drop list.", "jsdocTags": [{"name": "param", "comment": "New element container to be assigned."}], "rawComment": "/**\n   * Configures the drop list so that a different element is used as the container for the\n   * dragged items. This is useful for the cases when one might not have control over the\n   * full DOM that sets up the dragging.\n   * Note that the alternate container needs to be a descendant of the drop list.\n   * @param container New element container to be assigned.\n   */", "memberType": "method", "memberTags": []}, {"name": "getScrollableParents", "signatures": [{"name": "getScrollableParents", "entryType": "function", "description": "Gets the scrollable parents that are registered with this drop container.", "generics": [], "isNewType": false, "jsdocTags": [], "params": [], "rawComment": "/** Gets the scrollable parents that are registered with this drop container. */", "returnType": "readonly <PERSON>[]"}], "implementation": {"params": [], "isNewType": false, "returnType": "readonly <PERSON>[]", "generics": [], "name": "getScrollableParents", "description": "Gets the scrollable parents that are registered with this drop container.", "entryType": "function", "jsdocTags": [], "rawComment": "/** Gets the scrollable parents that are registered with this drop container. */"}, "entryType": "function", "description": "Gets the scrollable parents that are registered with this drop container.", "jsdocTags": [], "rawComment": "/** Gets the scrollable parents that are registered with this drop container. */", "memberType": "method", "memberTags": []}, {"name": "getItemIndex", "signatures": [{"name": "getItemIndex", "entryType": "function", "description": "Figures out the index of an item in the container.", "generics": [], "isNewType": false, "jsdocTags": [{"name": "param", "comment": "Item whose index should be determined."}], "params": [{"name": "item", "description": "Item whose index should be determined.", "type": "DragRef<any>", "isOptional": false, "isRestParam": false}], "rawComment": "/**\n   * Figures out the index of an item in the container.\n   * @param item Item whose index should be determined.\n   */", "returnType": "number"}], "implementation": {"params": [{"name": "item", "description": "Item whose index should be determined.", "type": "DragRef<any>", "isOptional": false, "isRestParam": false}], "isNewType": false, "returnType": "number", "generics": [], "name": "getItemIndex", "description": "Figures out the index of an item in the container.", "entryType": "function", "jsdocTags": [{"name": "param", "comment": "Item whose index should be determined."}], "rawComment": "/**\n   * Figures out the index of an item in the container.\n   * @param item Item whose index should be determined.\n   */"}, "entryType": "function", "description": "Figures out the index of an item in the container.", "jsdocTags": [{"name": "param", "comment": "Item whose index should be determined."}], "rawComment": "/**\n   * Figures out the index of an item in the container.\n   * @param item Item whose index should be determined.\n   */", "memberType": "method", "memberTags": []}, {"name": "isReceiving", "signatures": [{"name": "isReceiving", "entryType": "function", "description": "Whether the list is able to receive the item that\nis currently being dragged inside a connected drop list.", "generics": [], "isNewType": false, "jsdocTags": [], "params": [], "rawComment": "/**\n   * Whether the list is able to receive the item that\n   * is currently being dragged inside a connected drop list.\n   */", "returnType": "boolean"}], "implementation": {"params": [], "isNewType": false, "returnType": "boolean", "generics": [], "name": "isReceiving", "description": "Whether the list is able to receive the item that\nis currently being dragged inside a connected drop list.", "entryType": "function", "jsdocTags": [], "rawComment": "/**\n   * Whether the list is able to receive the item that\n   * is currently being dragged inside a connected drop list.\n   */"}, "entryType": "function", "description": "Whether the list is able to receive the item that\nis currently being dragged inside a connected drop list.", "jsdocTags": [], "rawComment": "/**\n   * Whether the list is able to receive the item that\n   * is currently being dragged inside a connected drop list.\n   */", "memberType": "method", "memberTags": []}], "generics": [{"name": "T", "default": "any"}], "description": "Reference to a drop list. Used to manipulate or dispose of the container.", "jsdocTags": [], "rawComment": "/**\n * Reference to a drop list. Used to manipulate or dispose of the container.\n */", "implements": [], "source": {"filePath": "/src/cdk/drag-drop/drop-list-ref.ts", "startLine": 55, "endLine": 772}}, {"name": "CDK_DROP_LIST", "type": "any", "entryType": "constant", "rawComment": "/**\n * Injection token that can be used to reference instances of `CdkDropList`. It serves as\n * alternative token to the actual `CdkDropList` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */", "description": "Injection token that can be used to reference instances of `CdkDropList`. It serves as\nalternative token to the actual `CdkDropList` class which could cause unnecessary\nretention of the class and its directive metadata.", "jsdocTags": [], "source": {"filePath": "/src/cdk/drag-drop/directives/drag.ts", "startLine": 58, "endLine": 58}}, {"name": "CdkDrag", "isAbstract": false, "entryType": "undecorated_class", "members": [{"name": "constructor", "signatures": [{"name": "constructor", "params": [{"name": "args", "description": "", "type": "unknown[]", "isOptional": false, "isRestParam": true}], "returnType": "CdkDrag", "description": "", "entryType": "function", "jsdocTags": [], "rawComment": "", "generics": [], "isNewType": false}], "implementation": {"params": [], "isNewType": false, "returnType": "CdkDrag<T>", "generics": [], "name": "constructor", "description": "", "entryType": "function", "jsdocTags": [], "rawComment": ""}, "entryType": "function", "description": "", "jsdocTags": [], "rawComment": "", "memberType": "method", "memberTags": []}, {"name": "element", "type": "any", "memberType": "property", "memberTags": [], "description": "", "jsdocTags": []}, {"name": "dropContainer", "type": "any", "memberType": "property", "memberTags": [], "description": "", "jsdocTags": []}, {"name": "data", "type": "T", "memberType": "property", "memberTags": [], "description": "Arbitrary data to attach to this drag instance.", "jsdocTags": []}, {"name": "lockAxis", "type": "Drag<PERSON><PERSON><PERSON>", "memberType": "property", "memberTags": [], "description": "Locks the position of the dragged element along the specified axis.", "jsdocTags": []}, {"name": "rootElementSelector", "type": "string", "memberType": "property", "memberTags": [], "description": "Selector that will be used to determine the root draggable element, starting from\nthe `cdkDrag` element and going up the DOM. Passing an alternate root element is useful\nwhen trying to enable dragging on an element that you might not have access to.", "jsdocTags": []}, {"name": "boundaryElement", "type": "any", "memberType": "property", "memberTags": [], "description": "Node or selector that will be used to determine the element to which the draggable's\nposition will be constrained. If a string is passed in, it'll be used as a selector that\nwill be matched starting from the element's parent and going up the DOM until a match\nhas been found.", "jsdocTags": []}, {"name": "dragStartDelay", "type": "DragStartDelay", "memberType": "property", "memberTags": [], "description": "Amount of milliseconds to wait after the user has put their\npointer down before starting to drag the element.", "jsdocTags": []}, {"name": "freeDragPosition", "type": "Point", "memberType": "property", "memberTags": [], "description": "Sets the position of a `CdkDrag` that is outside of a drop container.\nCan be used to restore the element's position for a returning user.", "jsdocTags": []}, {"name": "disabled", "type": "boolean", "memberType": "getter", "memberTags": [], "description": "Whether starting to drag this element is disabled.", "jsdocTags": []}, {"name": "disabled", "type": "boolean", "memberType": "setter", "memberTags": [], "description": "", "jsdocTags": []}, {"name": "constrainPosition", "type": "((userPointerPosition: Point, dragRef: DragRef<any>, dimensions: DOMRect, pickupPositionInElement: Point) => Point) | undefined", "memberType": "property", "memberTags": ["optional"], "description": "Function that can be used to customize the logic of how the position of the drag item\nis limited while it's being dragged. Gets called with a point containing the current position\nof the user's pointer on the page, a reference to the item being dragged and its dimensions.\nShould return a point describing where the item should be rendered.", "jsdocTags": []}, {"name": "previewClass", "type": "string | string[]", "memberType": "property", "memberTags": [], "description": "Class to be added to the preview element.", "jsdocTags": []}, {"name": "previewContainer", "type": "any", "memberType": "property", "memberTags": [], "description": "Configures the place into which the preview of the item will be inserted. Can be configured\nglobally through `CDK_DROP_LIST`. Possible values:\n- `global` - Preview will be inserted at the bottom of the `<body>`. The advantage is that\nyou don't have to worry about `overflow: hidden` or `z-index`, but the item won't retain\nits inherited styles.\n- `parent` - Preview will be inserted into the parent of the drag item. The advantage is that\ninherited styles will be preserved, but it may be clipped by `overflow: hidden` or not be\nvisible due to `z-index`. Furthermore, the preview is going to have an effect over selectors\nlike `:nth-child` and some flexbox configurations.\n- `ElementRef<HTMLElement> | HTMLElement` - Preview will be inserted into a specific element.\nSame advantages and disadvantages as `parent`.", "jsdocTags": []}, {"name": "scale", "type": "number", "memberType": "property", "memberTags": [], "description": "If the parent of the dragged element has a `scale` transform, it can throw off the\npositioning when the user starts dragging. Use this input to notify the CDK of the scale.", "jsdocTags": []}, {"name": "started", "type": "EventEmitter<CdkDragStart<any>>", "memberType": "property", "memberTags": ["readonly"], "description": "Emits when the user starts dragging the item.", "jsdocTags": []}, {"name": "released", "type": "EventEmitter<CdkDragRelease<any>>", "memberType": "property", "memberTags": ["readonly"], "description": "Emits when the user has released a drag item, before any animations have started.", "jsdocTags": []}, {"name": "ended", "type": "EventEmitter<CdkDragEnd<any>>", "memberType": "property", "memberTags": ["readonly"], "description": "Emits when the user stops dragging an item in the container.", "jsdocTags": []}, {"name": "entered", "type": "EventEmitter<CdkDragEnter<any, any>>", "memberType": "property", "memberTags": ["readonly"], "description": "Emits when the user has moved the item into a new container.", "jsdocTags": []}, {"name": "exited", "type": "EventEmitter<CdkDragExit<any, any>>", "memberType": "property", "memberTags": ["readonly"], "description": "Emits when the user removes the item its container by dragging it into another container.", "jsdocTags": []}, {"name": "dropped", "type": "EventEmitter<CdkDragDrop<any, any, any>>", "memberType": "property", "memberTags": ["readonly"], "description": "Emits when the user drops the item inside a container.", "jsdocTags": []}, {"name": "moved", "type": "Observable<CdkDragMove<T>>", "memberType": "property", "memberTags": ["readonly"], "description": "Emits as the user is dragging the item. Use with caution,\nbecause this event will fire for every pixel that the user has dragged.", "jsdocTags": []}, {"name": "getPlaceholderElement", "signatures": [{"name": "getPlaceholderElement", "entryType": "function", "description": "Returns the element that is being used as a placeholder\nwhile the current element is being dragged.", "generics": [], "isNewType": false, "jsdocTags": [], "params": [], "rawComment": "/**\n   * Returns the element that is being used as a placeholder\n   * while the current element is being dragged.\n   */", "returnType": "HTMLElement"}], "implementation": {"params": [], "isNewType": false, "returnType": "HTMLElement", "generics": [], "name": "getPlaceholderElement", "description": "Returns the element that is being used as a placeholder\nwhile the current element is being dragged.", "entryType": "function", "jsdocTags": [], "rawComment": "/**\n   * Returns the element that is being used as a placeholder\n   * while the current element is being dragged.\n   */"}, "entryType": "function", "description": "Returns the element that is being used as a placeholder\nwhile the current element is being dragged.", "jsdocTags": [], "rawComment": "/**\n   * Returns the element that is being used as a placeholder\n   * while the current element is being dragged.\n   */", "memberType": "method", "memberTags": []}, {"name": "getRootElement", "signatures": [{"name": "getRootElement", "entryType": "function", "description": "Returns the root draggable element.", "generics": [], "isNewType": false, "jsdocTags": [], "params": [], "rawComment": "/** Returns the root draggable element. */", "returnType": "HTMLElement"}], "implementation": {"params": [], "isNewType": false, "returnType": "HTMLElement", "generics": [], "name": "getRootElement", "description": "Returns the root draggable element.", "entryType": "function", "jsdocTags": [], "rawComment": "/** Returns the root draggable element. */"}, "entryType": "function", "description": "Returns the root draggable element.", "jsdocTags": [], "rawComment": "/** Returns the root draggable element. */", "memberType": "method", "memberTags": []}, {"name": "reset", "signatures": [{"name": "reset", "entryType": "function", "description": "Resets a standalone drag item to its initial position.", "generics": [], "isNewType": false, "jsdocTags": [], "params": [], "rawComment": "/** Resets a standalone drag item to its initial position. */", "returnType": "void"}], "implementation": {"params": [], "isNewType": false, "returnType": "void", "generics": [], "name": "reset", "description": "Resets a standalone drag item to its initial position.", "entryType": "function", "jsdocTags": [], "rawComment": "/** Resets a standalone drag item to its initial position. */"}, "entryType": "function", "description": "Resets a standalone drag item to its initial position.", "jsdocTags": [], "rawComment": "/** Resets a standalone drag item to its initial position. */", "memberType": "method", "memberTags": []}, {"name": "getFreeDragPosition", "signatures": [{"name": "getFreeDragPosition", "entryType": "function", "description": "Gets the pixel coordinates of the draggable outside of a drop container.", "generics": [], "isNewType": false, "jsdocTags": [], "params": [], "rawComment": "/**\n   * Gets the pixel coordinates of the draggable outside of a drop container.\n   */", "returnType": "Readonly<Point>"}], "implementation": {"params": [], "isNewType": false, "returnType": "Readonly<Point>", "generics": [], "name": "getFreeDragPosition", "description": "Gets the pixel coordinates of the draggable outside of a drop container.", "entryType": "function", "jsdocTags": [], "rawComment": "/**\n   * Gets the pixel coordinates of the draggable outside of a drop container.\n   */"}, "entryType": "function", "description": "Gets the pixel coordinates of the draggable outside of a drop container.", "jsdocTags": [], "rawComment": "/**\n   * Gets the pixel coordinates of the draggable outside of a drop container.\n   */", "memberType": "method", "memberTags": []}, {"name": "setFreeDragPosition", "signatures": [{"name": "setFreeDragPosition", "entryType": "function", "description": "Sets the current position in pixels the draggable outside of a drop container.", "generics": [], "isNewType": false, "jsdocTags": [{"name": "param", "comment": "New position to be set."}], "params": [{"name": "value", "description": "New position to be set.", "type": "Point", "isOptional": false, "isRestParam": false}], "rawComment": "/**\n   * Sets the current position in pixels the draggable outside of a drop container.\n   * @param value New position to be set.\n   */", "returnType": "void"}], "implementation": {"params": [{"name": "value", "description": "New position to be set.", "type": "Point", "isOptional": false, "isRestParam": false}], "isNewType": false, "returnType": "void", "generics": [], "name": "setFreeDragPosition", "description": "Sets the current position in pixels the draggable outside of a drop container.", "entryType": "function", "jsdocTags": [{"name": "param", "comment": "New position to be set."}], "rawComment": "/**\n   * Sets the current position in pixels the draggable outside of a drop container.\n   * @param value New position to be set.\n   */"}, "entryType": "function", "description": "Sets the current position in pixels the draggable outside of a drop container.", "jsdocTags": [{"name": "param", "comment": "New position to be set."}], "rawComment": "/**\n   * Sets the current position in pixels the draggable outside of a drop container.\n   * @param value New position to be set.\n   */", "memberType": "method", "memberTags": []}, {"name": "ngAfterViewInit", "signatures": [{"name": "ngAfterViewInit", "entryType": "function", "description": "", "generics": [], "isNewType": false, "jsdocTags": [], "params": [], "rawComment": "", "returnType": "void"}], "implementation": {"params": [], "isNewType": false, "returnType": "void", "generics": [], "name": "ngAfterViewInit", "description": "", "entryType": "function", "jsdocTags": [], "rawComment": ""}, "entryType": "function", "description": "", "jsdocTags": [], "rawComment": "", "memberType": "method", "memberTags": []}, {"name": "ngOnChanges", "signatures": [{"name": "ngOnChanges", "entryType": "function", "description": "", "generics": [], "isNewType": false, "jsdocTags": [], "params": [{"name": "changes", "description": "", "type": "SimpleChanges", "isOptional": false, "isRestParam": false}], "rawComment": "", "returnType": "void"}], "implementation": {"params": [{"name": "changes", "description": "", "type": "SimpleChanges", "isOptional": false, "isRestParam": false}], "isNewType": false, "returnType": "void", "generics": [], "name": "ngOnChanges", "description": "", "entryType": "function", "jsdocTags": [], "rawComment": ""}, "entryType": "function", "description": "", "jsdocTags": [], "rawComment": "", "memberType": "method", "memberTags": []}, {"name": "ngOnDestroy", "signatures": [{"name": "ngOnDestroy", "entryType": "function", "description": "", "generics": [], "isNewType": false, "jsdocTags": [], "params": [], "rawComment": "", "returnType": "void"}], "implementation": {"params": [], "isNewType": false, "returnType": "void", "generics": [], "name": "ngOnDestroy", "description": "", "entryType": "function", "jsdocTags": [], "rawComment": ""}, "entryType": "function", "description": "", "jsdocTags": [], "rawComment": "", "memberType": "method", "memberTags": []}], "generics": [{"name": "T", "default": "any"}], "description": "Element that can be moved inside a CdkDropList container.", "jsdocTags": [], "rawComment": "/** Element that can be moved inside a CdkDropList container. */", "implements": ["AfterViewInit", "OnChanges", "OnDestroy"], "source": {"filePath": "/src/cdk/drag-drop/directives/drag.ts", "startLine": 61, "endLine": 623}}, {"name": "CdkDragDrop", "isAbstract": false, "entryType": "interface", "members": [{"name": "previousIndex", "type": "number", "memberType": "property", "memberTags": [], "description": "Index of the item when it was picked up.", "jsdocTags": []}, {"name": "currentIndex", "type": "number", "memberType": "property", "memberTags": [], "description": "Current index of the item.", "jsdocTags": []}, {"name": "item", "type": "CdkDrag<I>", "memberType": "property", "memberTags": [], "description": "Item that is being dropped.", "jsdocTags": []}, {"name": "container", "type": "CdkDropList<T>", "memberType": "property", "memberTags": [], "description": "Container in which the item was dropped.", "jsdocTags": []}, {"name": "previousContainer", "type": "CdkDropList<O>", "memberType": "property", "memberTags": [], "description": "Container from which the item was picked up. Can be the same as the `container`.", "jsdocTags": []}, {"name": "isPointerOverContainer", "type": "boolean", "memberType": "property", "memberTags": [], "description": "Whether the user's pointer was over the container when the item was dropped.", "jsdocTags": []}, {"name": "distance", "type": "{ x: number; y: number; }", "memberType": "property", "memberTags": [], "description": "Distance in pixels that the user has dragged since the drag sequence started.", "jsdocTags": []}, {"name": "dropPoint", "type": "{ x: number; y: number; }", "memberType": "property", "memberTags": [], "description": "Position where the pointer was when the item was dropped", "jsdocTags": []}, {"name": "event", "type": "MouseEvent | TouchEvent", "memberType": "property", "memberTags": [], "description": "Native event that caused the drop event.", "jsdocTags": []}], "generics": [{"name": "T"}, {"name": "O", "default": "T"}, {"name": "I", "default": "any"}], "description": "Event emitted when the user drops a draggable item inside a drop container.", "jsdocTags": [], "rawComment": "/** Event emitted when the user drops a draggable item inside a drop container. */", "implements": [], "source": {"filePath": "/src/cdk/drag-drop/drag-events.ts", "startLine": 62, "endLine": 81}}, {"name": "Point", "isAbstract": false, "entryType": "interface", "members": [{"name": "x", "type": "number", "memberType": "property", "memberTags": [], "description": "", "jsdocTags": []}, {"name": "y", "type": "number", "memberType": "property", "memberTags": [], "description": "", "jsdocTags": []}], "generics": [], "description": "Point on the page or within an element.", "jsdocTags": [], "rawComment": "/** Point on the page or within an element. */", "implements": [], "source": {"filePath": "/src/cdk/drag-drop/drag-ref.ts", "startLine": 91, "endLine": 94}}, {"name": "CdkDragMove", "isAbstract": false, "entryType": "interface", "members": [{"name": "source", "type": "CdkDrag<T>", "memberType": "property", "memberTags": [], "description": "Item that is being dragged.", "jsdocTags": []}, {"name": "pointerPosition", "type": "{ x: number; y: number; }", "memberType": "property", "memberTags": [], "description": "Position of the user's pointer on the page.", "jsdocTags": []}, {"name": "event", "type": "MouseEvent | TouchEvent", "memberType": "property", "memberTags": [], "description": "Native event that is causing the dragging.", "jsdocTags": []}, {"name": "distance", "type": "{ x: number; y: number; }", "memberType": "property", "memberTags": [], "description": "Distance in pixels that the user has dragged since the drag sequence started.", "jsdocTags": []}, {"name": "delta", "type": "{ x: 0 | 1 | -1; y: 0 | 1 | -1; }", "memberType": "property", "memberTags": [], "description": "Indicates the direction in which the user is dragging the element along each axis.\n`1` means that the position is increasing (e.g. the user is moving to the right or downwards),\nwhereas `-1` means that it's decreasing (they're moving to the left or upwards). `0` means\nthat the position hasn't changed.", "jsdocTags": []}], "generics": [{"name": "T", "default": "any"}], "description": "Event emitted as the user is dragging a draggable item.", "jsdocTags": [], "rawComment": "/** Event emitted as the user is dragging a draggable item. */", "implements": [], "source": {"filePath": "/src/cdk/drag-drop/drag-events.ts", "startLine": 84, "endLine": 100}}, {"name": "PreviewContainer", "type": "'global' | 'parent' | ElementRef<HTMLElement> | HTMLElement", "entryType": "type_alias", "generics": [], "rawComment": "/**\n * Possible places into which the preview of a drag item can be inserted.\n * - `global` - Preview will be inserted at the bottom of the `<body>`. The advantage is that\n * you don't have to worry about `overflow: hidden` or `z-index`, but the item won't retain\n * its inherited styles.\n * - `parent` - Preview will be inserted into the parent of the drag item. The advantage is that\n * inherited styles will be preserved, but it may be clipped by `overflow: hidden` or not be\n * visible due to `z-index`. Furthermore, the preview is going to have an effect over selectors\n * like `:nth-child` and some flexbox configurations.\n * - `ElementRef<HTMLElement> | HTMLElement` - Preview will be inserted into a specific element.\n * Same advantages and disadvantages as `parent`.\n */", "description": "Possible places into which the preview of a drag item can be inserted.\n- `global` - Preview will be inserted at the bottom of the `<body>`. The advantage is that\nyou don't have to worry about `overflow: hidden` or `z-index`, but the item won't retain\nits inherited styles.\n- `parent` - Preview will be inserted into the parent of the drag item. The advantage is that\ninherited styles will be preserved, but it may be clipped by `overflow: hidden` or not be\nvisible due to `z-index`. Furthermore, the preview is going to have an effect over selectors\nlike `:nth-child` and some flexbox configurations.\n- `ElementRef<HTMLElement> | HTMLElement` - Preview will be inserted into a specific element.\nSame advantages and disadvantages as `parent`.", "jsdocTags": [], "source": {"filePath": "/src/cdk/drag-drop/drag-ref.ts", "startLine": 114, "endLine": 114}}, {"name": "CdkDragSortEvent", "isAbstract": false, "entryType": "interface", "members": [{"name": "previousIndex", "type": "number", "memberType": "property", "memberTags": [], "description": "Index from which the item was sorted previously.", "jsdocTags": []}, {"name": "currentIndex", "type": "number", "memberType": "property", "memberTags": [], "description": "Index that the item is currently in.", "jsdocTags": []}, {"name": "container", "type": "CdkDropList<T>", "memberType": "property", "memberTags": [], "description": "Container that the item belongs to.", "jsdocTags": []}, {"name": "item", "type": "CdkDrag<I>", "memberType": "property", "memberTags": [], "description": "Item that is being sorted.", "jsdocTags": []}], "generics": [{"name": "T", "default": "any"}, {"name": "I", "default": "T"}], "description": "Event emitted when the user swaps the position of two drag items.", "jsdocTags": [], "rawComment": "/** Event emitted when the user swaps the position of two drag items. */", "implements": [], "source": {"filePath": "/src/cdk/drag-drop/drag-events.ts", "startLine": 103, "endLine": 112}}, {"name": "DragRef", "isAbstract": false, "entryType": "undecorated_class", "members": [{"name": "constructor", "signatures": [], "implementation": {"params": [{"name": "element", "description": "", "type": "any", "isOptional": false, "isRestParam": false}, {"name": "_config", "description": "", "type": "DragRefConfig", "isOptional": false, "isRestParam": false}, {"name": "_document", "description": "", "type": "Document", "isOptional": false, "isRestParam": false}, {"name": "_ngZone", "description": "", "type": "NgZone", "isOptional": false, "isRestParam": false}, {"name": "_viewportRuler", "description": "", "type": "ViewportRuler", "isOptional": false, "isRestParam": false}, {"name": "_dragDropRegistry", "description": "", "type": "DragDropRegistry<unknown, unknown>", "isOptional": false, "isRestParam": false}, {"name": "_renderer", "description": "", "type": "Renderer2", "isOptional": false, "isRestParam": false}], "isNewType": false, "returnType": "DragRef<T>", "generics": [], "name": "constructor", "description": "", "entryType": "function", "jsdocTags": [], "rawComment": ""}, "entryType": "function", "description": "", "jsdocTags": [], "rawComment": "", "memberType": "method", "memberTags": []}, {"name": "lockAxis", "type": "\"x\" | \"y\"", "memberType": "property", "memberTags": [], "description": "Axis along which dragging is locked.", "jsdocTags": []}, {"name": "dragStartDelay", "type": "number | { touch: number; mouse: number; }", "memberType": "property", "memberTags": [], "description": "Amount of milliseconds to wait after the user has put their\npointer down before starting to drag the element.", "jsdocTags": []}, {"name": "previewClass", "type": "string | string[] | undefined", "memberType": "property", "memberTags": [], "description": "Class to be added to the preview element.", "jsdocTags": []}, {"name": "scale", "type": "number", "memberType": "property", "memberTags": [], "description": "If the parent of the dragged element has a `scale` transform, it can throw off the\npositioning when the user starts dragging. Use this input to notify the CDK of the scale.", "jsdocTags": []}, {"name": "disabled", "type": "boolean", "memberType": "getter", "memberTags": [], "description": "Whether starting to drag this element is disabled.", "jsdocTags": []}, {"name": "disabled", "type": "boolean", "memberType": "setter", "memberTags": [], "description": "", "jsdocTags": []}, {"name": "beforeStarted", "type": "any", "memberType": "property", "memberTags": ["readonly"], "description": "Emits as the drag sequence is being prepared.", "jsdocTags": []}, {"name": "started", "type": "any", "memberType": "property", "memberTags": ["readonly"], "description": "Emits when the user starts dragging the item.", "jsdocTags": []}, {"name": "released", "type": "any", "memberType": "property", "memberTags": ["readonly"], "description": "Emits when the user has released a drag item, before any animations have started.", "jsdocTags": []}, {"name": "ended", "type": "any", "memberType": "property", "memberTags": ["readonly"], "description": "Emits when the user stops dragging an item in the container.", "jsdocTags": []}, {"name": "entered", "type": "any", "memberType": "property", "memberTags": ["readonly"], "description": "Emits when the user has moved the item into a new container.", "jsdocTags": []}, {"name": "exited", "type": "any", "memberType": "property", "memberTags": ["readonly"], "description": "Emits when the user removes the item its container by dragging it into another container.", "jsdocTags": []}, {"name": "dropped", "type": "any", "memberType": "property", "memberTags": ["readonly"], "description": "Emits when the user drops the item inside a container.", "jsdocTags": []}, {"name": "moved", "type": "Observable<{ source: DragRef<any>; pointerPosition: { x: number; y: number; }; event: MouseEvent | TouchEvent; distance: Point; delta: { x: 0 | 1 | -1; y: 0 | 1 | -1; }; }>", "memberType": "property", "memberTags": ["readonly"], "description": "Emits as the user is dragging the item. Use with caution,\nbecause this event will fire for every pixel that the user has dragged.", "jsdocTags": []}, {"name": "data", "type": "T", "memberType": "property", "memberTags": [], "description": "Arbitrary data that can be attached to the drag item.", "jsdocTags": []}, {"name": "constrainPosition", "type": "((userPointerPosition: Point, dragRef: DragRef<any>, dimensions: DOMRect, pickupPositionInElement: Point) => Point) | undefined", "memberType": "property", "memberTags": ["optional"], "description": "Function that can be used to customize the logic of how the position of the drag item\nis limited while it's being dragged. Gets called with a point containing the current position\nof the user's pointer on the page, a reference to the item being dragged and its dimensions.\nShould return a point describing where the item should be rendered.", "jsdocTags": []}, {"name": "getPlaceholderElement", "signatures": [{"name": "getPlaceholderElement", "entryType": "function", "description": "Returns the element that is being used as a placeholder\nwhile the current element is being dragged.", "generics": [], "isNewType": false, "jsdocTags": [], "params": [], "rawComment": "/**\n   * Returns the element that is being used as a placeholder\n   * while the current element is being dragged.\n   */", "returnType": "HTMLElement"}], "implementation": {"params": [], "isNewType": false, "returnType": "HTMLElement", "generics": [], "name": "getPlaceholderElement", "description": "Returns the element that is being used as a placeholder\nwhile the current element is being dragged.", "entryType": "function", "jsdocTags": [], "rawComment": "/**\n   * Returns the element that is being used as a placeholder\n   * while the current element is being dragged.\n   */"}, "entryType": "function", "description": "Returns the element that is being used as a placeholder\nwhile the current element is being dragged.", "jsdocTags": [], "rawComment": "/**\n   * Returns the element that is being used as a placeholder\n   * while the current element is being dragged.\n   */", "memberType": "method", "memberTags": []}, {"name": "getRootElement", "signatures": [{"name": "getRootElement", "entryType": "function", "description": "Returns the root draggable element.", "generics": [], "isNewType": false, "jsdocTags": [], "params": [], "rawComment": "/** Returns the root draggable element. */", "returnType": "HTMLElement"}], "implementation": {"params": [], "isNewType": false, "returnType": "HTMLElement", "generics": [], "name": "getRootElement", "description": "Returns the root draggable element.", "entryType": "function", "jsdocTags": [], "rawComment": "/** Returns the root draggable element. */"}, "entryType": "function", "description": "Returns the root draggable element.", "jsdocTags": [], "rawComment": "/** Returns the root draggable element. */", "memberType": "method", "memberTags": []}, {"name": "getVisibleElement", "signatures": [{"name": "getVisibleElement", "entryType": "function", "description": "Gets the currently-visible element that represents the drag item.\nWhile dragging this is the placeholder, otherwise it's the root element.", "generics": [], "isNewType": false, "jsdocTags": [], "params": [], "rawComment": "/**\n   * Gets the currently-visible element that represents the drag item.\n   * While dragging this is the placeholder, otherwise it's the root element.\n   */", "returnType": "HTMLElement"}], "implementation": {"params": [], "isNewType": false, "returnType": "HTMLElement", "generics": [], "name": "getVisibleElement", "description": "Gets the currently-visible element that represents the drag item.\nWhile dragging this is the placeholder, otherwise it's the root element.", "entryType": "function", "jsdocTags": [], "rawComment": "/**\n   * Gets the currently-visible element that represents the drag item.\n   * While dragging this is the placeholder, otherwise it's the root element.\n   */"}, "entryType": "function", "description": "Gets the currently-visible element that represents the drag item.\nWhile dragging this is the placeholder, otherwise it's the root element.", "jsdocTags": [], "rawComment": "/**\n   * Gets the currently-visible element that represents the drag item.\n   * While dragging this is the placeholder, otherwise it's the root element.\n   */", "memberType": "method", "memberTags": []}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "signatures": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "entryType": "function", "description": "Registers the handles that can be used to drag the element.", "generics": [], "isNewType": false, "jsdocTags": [], "params": [{"name": "handles", "description": "", "type": "any[]", "isOptional": false, "isRestParam": false}], "rawComment": "/** Registers the handles that can be used to drag the element. */", "returnType": "this"}], "implementation": {"params": [{"name": "handles", "description": "", "type": "any[]", "isOptional": false, "isRestParam": false}], "isNewType": false, "returnType": "this", "generics": [], "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Registers the handles that can be used to drag the element.", "entryType": "function", "jsdocTags": [], "rawComment": "/** Registers the handles that can be used to drag the element. */"}, "entryType": "function", "description": "Registers the handles that can be used to drag the element.", "jsdocTags": [], "rawComment": "/** Registers the handles that can be used to drag the element. */", "memberType": "method", "memberTags": []}, {"name": "withPreviewTemplate", "signatures": [{"name": "withPreviewTemplate", "entryType": "function", "description": "Registers the template that should be used for the drag preview.", "generics": [], "isNewType": false, "jsdocTags": [{"name": "param", "comment": "Template that from which to stamp out the preview."}], "params": [{"name": "template", "description": "Template that from which to stamp out the preview.", "type": "DragPreviewTemplate<any> | null", "isOptional": false, "isRestParam": false}], "rawComment": "/**\n   * Registers the template that should be used for the drag preview.\n   * @param template Template that from which to stamp out the preview.\n   */", "returnType": "this"}], "implementation": {"params": [{"name": "template", "description": "Template that from which to stamp out the preview.", "type": "DragPreviewTemplate<any> | null", "isOptional": false, "isRestParam": false}], "isNewType": false, "returnType": "this", "generics": [], "name": "withPreviewTemplate", "description": "Registers the template that should be used for the drag preview.", "entryType": "function", "jsdocTags": [{"name": "param", "comment": "Template that from which to stamp out the preview."}], "rawComment": "/**\n   * Registers the template that should be used for the drag preview.\n   * @param template Template that from which to stamp out the preview.\n   */"}, "entryType": "function", "description": "Registers the template that should be used for the drag preview.", "jsdocTags": [{"name": "param", "comment": "Template that from which to stamp out the preview."}], "rawComment": "/**\n   * Registers the template that should be used for the drag preview.\n   * @param template Template that from which to stamp out the preview.\n   */", "memberType": "method", "memberTags": []}, {"name": "withPlaceholderTemplate", "signatures": [{"name": "withPlaceholderTemplate", "entryType": "function", "description": "Registers the template that should be used for the drag placeholder.", "generics": [], "isNewType": false, "jsdocTags": [{"name": "param", "comment": "Template that from which to stamp out the placeholder."}], "params": [{"name": "template", "description": "Template that from which to stamp out the placeholder.", "type": "DragHelperTemplate<any> | null", "isOptional": false, "isRestParam": false}], "rawComment": "/**\n   * Registers the template that should be used for the drag placeholder.\n   * @param template Template that from which to stamp out the placeholder.\n   */", "returnType": "this"}], "implementation": {"params": [{"name": "template", "description": "Template that from which to stamp out the placeholder.", "type": "DragHelperTemplate<any> | null", "isOptional": false, "isRestParam": false}], "isNewType": false, "returnType": "this", "generics": [], "name": "withPlaceholderTemplate", "description": "Registers the template that should be used for the drag placeholder.", "entryType": "function", "jsdocTags": [{"name": "param", "comment": "Template that from which to stamp out the placeholder."}], "rawComment": "/**\n   * Registers the template that should be used for the drag placeholder.\n   * @param template Template that from which to stamp out the placeholder.\n   */"}, "entryType": "function", "description": "Registers the template that should be used for the drag placeholder.", "jsdocTags": [{"name": "param", "comment": "Template that from which to stamp out the placeholder."}], "rawComment": "/**\n   * Registers the template that should be used for the drag placeholder.\n   * @param template Template that from which to stamp out the placeholder.\n   */", "memberType": "method", "memberTags": []}, {"name": "withRootElement", "signatures": [{"name": "withRootElement", "entryType": "function", "description": "Sets an alternate drag root element. The root element is the element that will be moved as\nthe user is dragging. Passing an alternate root element is useful when trying to enable\ndragging on an element that you might not have access to.", "generics": [], "isNewType": false, "jsdocTags": [], "params": [{"name": "rootElement", "description": "", "type": "any", "isOptional": false, "isRestParam": false}], "rawComment": "/**\n   * Sets an alternate drag root element. The root element is the element that will be moved as\n   * the user is dragging. Passing an alternate root element is useful when trying to enable\n   * dragging on an element that you might not have access to.\n   */", "returnType": "this"}], "implementation": {"params": [{"name": "rootElement", "description": "", "type": "any", "isOptional": false, "isRestParam": false}], "isNewType": false, "returnType": "this", "generics": [], "name": "withRootElement", "description": "Sets an alternate drag root element. The root element is the element that will be moved as\nthe user is dragging. Passing an alternate root element is useful when trying to enable\ndragging on an element that you might not have access to.", "entryType": "function", "jsdocTags": [], "rawComment": "/**\n   * Sets an alternate drag root element. The root element is the element that will be moved as\n   * the user is dragging. Passing an alternate root element is useful when trying to enable\n   * dragging on an element that you might not have access to.\n   */"}, "entryType": "function", "description": "Sets an alternate drag root element. The root element is the element that will be moved as\nthe user is dragging. Passing an alternate root element is useful when trying to enable\ndragging on an element that you might not have access to.", "jsdocTags": [], "rawComment": "/**\n   * Sets an alternate drag root element. The root element is the element that will be moved as\n   * the user is dragging. Passing an alternate root element is useful when trying to enable\n   * dragging on an element that you might not have access to.\n   */", "memberType": "method", "memberTags": []}, {"name": "withBoundaryElement", "signatures": [{"name": "withBoundaryElement", "entryType": "function", "description": "Element to which the draggable's position will be constrained.", "generics": [], "isNewType": false, "jsdocTags": [], "params": [{"name": "boundaryElement", "description": "", "type": "any", "isOptional": false, "isRestParam": false}], "rawComment": "/**\n   * Element to which the draggable's position will be constrained.\n   */", "returnType": "this"}], "implementation": {"params": [{"name": "boundaryElement", "description": "", "type": "any", "isOptional": false, "isRestParam": false}], "isNewType": false, "returnType": "this", "generics": [], "name": "withBoundaryElement", "description": "Element to which the draggable's position will be constrained.", "entryType": "function", "jsdocTags": [], "rawComment": "/**\n   * Element to which the draggable's position will be constrained.\n   */"}, "entryType": "function", "description": "Element to which the draggable's position will be constrained.", "jsdocTags": [], "rawComment": "/**\n   * Element to which the draggable's position will be constrained.\n   */", "memberType": "method", "memberTags": []}, {"name": "with<PERSON><PERSON>nt", "signatures": [{"name": "with<PERSON><PERSON>nt", "entryType": "function", "description": "Sets the parent ref that the ref is nested in.", "generics": [], "isNewType": false, "jsdocTags": [], "params": [{"name": "parent", "description": "", "type": "DragRef<unknown> | null", "isOptional": false, "isRestParam": false}], "rawComment": "/** Sets the parent ref that the ref is nested in.  */", "returnType": "this"}], "implementation": {"params": [{"name": "parent", "description": "", "type": "DragRef<unknown> | null", "isOptional": false, "isRestParam": false}], "isNewType": false, "returnType": "this", "generics": [], "name": "with<PERSON><PERSON>nt", "description": "Sets the parent ref that the ref is nested in.", "entryType": "function", "jsdocTags": [], "rawComment": "/** Sets the parent ref that the ref is nested in.  */"}, "entryType": "function", "description": "Sets the parent ref that the ref is nested in.", "jsdocTags": [], "rawComment": "/** Sets the parent ref that the ref is nested in.  */", "memberType": "method", "memberTags": []}, {"name": "dispose", "signatures": [{"name": "dispose", "entryType": "function", "description": "Removes the dragging functionality from the DOM element.", "generics": [], "isNewType": false, "jsdocTags": [], "params": [], "rawComment": "/** Removes the dragging functionality from the DOM element. */", "returnType": "void"}], "implementation": {"params": [], "isNewType": false, "returnType": "void", "generics": [], "name": "dispose", "description": "Removes the dragging functionality from the DOM element.", "entryType": "function", "jsdocTags": [], "rawComment": "/** Removes the dragging functionality from the DOM element. */"}, "entryType": "function", "description": "Removes the dragging functionality from the DOM element.", "jsdocTags": [], "rawComment": "/** Removes the dragging functionality from the DOM element. */", "memberType": "method", "memberTags": []}, {"name": "isDragging", "signatures": [{"name": "isDragging", "entryType": "function", "description": "Checks whether the element is currently being dragged.", "generics": [], "isNewType": false, "jsdocTags": [], "params": [], "rawComment": "/** Checks whether the element is currently being dragged. */", "returnType": "boolean"}], "implementation": {"params": [], "isNewType": false, "returnType": "boolean", "generics": [], "name": "isDragging", "description": "Checks whether the element is currently being dragged.", "entryType": "function", "jsdocTags": [], "rawComment": "/** Checks whether the element is currently being dragged. */"}, "entryType": "function", "description": "Checks whether the element is currently being dragged.", "jsdocTags": [], "rawComment": "/** Checks whether the element is currently being dragged. */", "memberType": "method", "memberTags": []}, {"name": "reset", "signatures": [{"name": "reset", "entryType": "function", "description": "Resets a standalone drag item to its initial position.", "generics": [], "isNewType": false, "jsdocTags": [], "params": [], "rawComment": "/** Resets a standalone drag item to its initial position. */", "returnType": "void"}], "implementation": {"params": [], "isNewType": false, "returnType": "void", "generics": [], "name": "reset", "description": "Resets a standalone drag item to its initial position.", "entryType": "function", "jsdocTags": [], "rawComment": "/** Resets a standalone drag item to its initial position. */"}, "entryType": "function", "description": "Resets a standalone drag item to its initial position.", "jsdocTags": [], "rawComment": "/** Resets a standalone drag item to its initial position. */", "memberType": "method", "memberTags": []}, {"name": "disable<PERSON><PERSON><PERSON>", "signatures": [{"name": "disable<PERSON><PERSON><PERSON>", "entryType": "function", "description": "Sets a handle as disabled. While a handle is disabled, it'll capture and interrupt dragging.", "generics": [], "isNewType": false, "jsdocTags": [{"name": "param", "comment": "Handle element that should be disabled."}], "params": [{"name": "handle", "description": "Handle element that should be disabled.", "type": "HTMLElement", "isOptional": false, "isRestParam": false}], "rawComment": "/**\n   * Sets a handle as disabled. While a handle is disabled, it'll capture and interrupt dragging.\n   * @param handle Handle element that should be disabled.\n   */", "returnType": "void"}], "implementation": {"params": [{"name": "handle", "description": "Handle element that should be disabled.", "type": "HTMLElement", "isOptional": false, "isRestParam": false}], "isNewType": false, "returnType": "void", "generics": [], "name": "disable<PERSON><PERSON><PERSON>", "description": "Sets a handle as disabled. While a handle is disabled, it'll capture and interrupt dragging.", "entryType": "function", "jsdocTags": [{"name": "param", "comment": "Handle element that should be disabled."}], "rawComment": "/**\n   * Sets a handle as disabled. While a handle is disabled, it'll capture and interrupt dragging.\n   * @param handle Handle element that should be disabled.\n   */"}, "entryType": "function", "description": "Sets a handle as disabled. While a handle is disabled, it'll capture and interrupt dragging.", "jsdocTags": [{"name": "param", "comment": "Handle element that should be disabled."}], "rawComment": "/**\n   * Sets a handle as disabled. While a handle is disabled, it'll capture and interrupt dragging.\n   * @param handle Handle element that should be disabled.\n   */", "memberType": "method", "memberTags": []}, {"name": "enableHandle", "signatures": [{"name": "enableHandle", "entryType": "function", "description": "Enables a handle, if it has been disabled.", "generics": [], "isNewType": false, "jsdocTags": [{"name": "param", "comment": "Handle element to be enabled."}], "params": [{"name": "handle", "description": "Handle element to be enabled.", "type": "HTMLElement", "isOptional": false, "isRestParam": false}], "rawComment": "/**\n   * Enables a handle, if it has been disabled.\n   * @param handle Handle element to be enabled.\n   */", "returnType": "void"}], "implementation": {"params": [{"name": "handle", "description": "Handle element to be enabled.", "type": "HTMLElement", "isOptional": false, "isRestParam": false}], "isNewType": false, "returnType": "void", "generics": [], "name": "enableHandle", "description": "Enables a handle, if it has been disabled.", "entryType": "function", "jsdocTags": [{"name": "param", "comment": "Handle element to be enabled."}], "rawComment": "/**\n   * Enables a handle, if it has been disabled.\n   * @param handle Handle element to be enabled.\n   */"}, "entryType": "function", "description": "Enables a handle, if it has been disabled.", "jsdocTags": [{"name": "param", "comment": "Handle element to be enabled."}], "rawComment": "/**\n   * Enables a handle, if it has been disabled.\n   * @param handle Handle element to be enabled.\n   */", "memberType": "method", "memberTags": []}, {"name": "withDirection", "signatures": [{"name": "withDirection", "entryType": "function", "description": "Sets the layout direction of the draggable item.", "generics": [], "isNewType": false, "jsdocTags": [], "params": [{"name": "direction", "description": "", "type": "Direction", "isOptional": false, "isRestParam": false}], "rawComment": "/** Sets the layout direction of the draggable item. */", "returnType": "this"}], "implementation": {"params": [{"name": "direction", "description": "", "type": "Direction", "isOptional": false, "isRestParam": false}], "isNewType": false, "returnType": "this", "generics": [], "name": "withDirection", "description": "Sets the layout direction of the draggable item.", "entryType": "function", "jsdocTags": [], "rawComment": "/** Sets the layout direction of the draggable item. */"}, "entryType": "function", "description": "Sets the layout direction of the draggable item.", "jsdocTags": [], "rawComment": "/** Sets the layout direction of the draggable item. */", "memberType": "method", "memberTags": []}, {"name": "getFreeDragPosition", "signatures": [{"name": "getFreeDragPosition", "entryType": "function", "description": "Gets the current position in pixels the draggable outside of a drop container.", "generics": [], "isNewType": false, "jsdocTags": [], "params": [], "rawComment": "/**\n   * Gets the current position in pixels the draggable outside of a drop container.\n   */", "returnType": "Readonly<Point>"}], "implementation": {"params": [], "isNewType": false, "returnType": "Readonly<Point>", "generics": [], "name": "getFreeDragPosition", "description": "Gets the current position in pixels the draggable outside of a drop container.", "entryType": "function", "jsdocTags": [], "rawComment": "/**\n   * Gets the current position in pixels the draggable outside of a drop container.\n   */"}, "entryType": "function", "description": "Gets the current position in pixels the draggable outside of a drop container.", "jsdocTags": [], "rawComment": "/**\n   * Gets the current position in pixels the draggable outside of a drop container.\n   */", "memberType": "method", "memberTags": []}, {"name": "setFreeDragPosition", "signatures": [{"name": "setFreeDragPosition", "entryType": "function", "description": "Sets the current position in pixels the draggable outside of a drop container.", "generics": [], "isNewType": false, "jsdocTags": [{"name": "param", "comment": "New position to be set."}], "params": [{"name": "value", "description": "New position to be set.", "type": "Point", "isOptional": false, "isRestParam": false}], "rawComment": "/**\n   * Sets the current position in pixels the draggable outside of a drop container.\n   * @param value New position to be set.\n   */", "returnType": "this"}], "implementation": {"params": [{"name": "value", "description": "New position to be set.", "type": "Point", "isOptional": false, "isRestParam": false}], "isNewType": false, "returnType": "this", "generics": [], "name": "setFreeDragPosition", "description": "Sets the current position in pixels the draggable outside of a drop container.", "entryType": "function", "jsdocTags": [{"name": "param", "comment": "New position to be set."}], "rawComment": "/**\n   * Sets the current position in pixels the draggable outside of a drop container.\n   * @param value New position to be set.\n   */"}, "entryType": "function", "description": "Sets the current position in pixels the draggable outside of a drop container.", "jsdocTags": [{"name": "param", "comment": "New position to be set."}], "rawComment": "/**\n   * Sets the current position in pixels the draggable outside of a drop container.\n   * @param value New position to be set.\n   */", "memberType": "method", "memberTags": []}, {"name": "withPreviewContainer", "signatures": [{"name": "withPreviewContainer", "entryType": "function", "description": "Sets the container into which to insert the preview element.", "generics": [], "isNewType": false, "jsdocTags": [{"name": "param", "comment": "Container into which to insert the preview."}], "params": [{"name": "value", "description": "Container into which to insert the preview.", "type": "any", "isOptional": false, "isRestParam": false}], "rawComment": "/**\n   * Sets the container into which to insert the preview element.\n   * @param value Container into which to insert the preview.\n   */", "returnType": "this"}], "implementation": {"params": [{"name": "value", "description": "Container into which to insert the preview.", "type": "any", "isOptional": false, "isRestParam": false}], "isNewType": false, "returnType": "this", "generics": [], "name": "withPreviewContainer", "description": "Sets the container into which to insert the preview element.", "entryType": "function", "jsdocTags": [{"name": "param", "comment": "Container into which to insert the preview."}], "rawComment": "/**\n   * Sets the container into which to insert the preview element.\n   * @param value Container into which to insert the preview.\n   */"}, "entryType": "function", "description": "Sets the container into which to insert the preview element.", "jsdocTags": [{"name": "param", "comment": "Container into which to insert the preview."}], "rawComment": "/**\n   * Sets the container into which to insert the preview element.\n   * @param value Container into which to insert the preview.\n   */", "memberType": "method", "memberTags": []}], "generics": [{"name": "T", "default": "any"}], "description": "Reference to a draggable item. Used to manipulate or dispose of the item.", "jsdocTags": [], "rawComment": "/**\n * Reference to a draggable item. Used to manipulate or dispose of the item.\n */", "implements": [], "source": {"filePath": "/src/cdk/drag-drop/drag-ref.ts", "startLine": 119, "endLine": 1559}}], "symbols": [["InjectionToken", "@angular/core"], ["Directive", "@angular/core"], ["OnDestroy", "@angular/core"], ["Input", "@angular/core"], ["booleanAttribute", "@angular/core"], ["TemplateRef", "@angular/core"], ["inject", "@angular/core"], ["Injectable", "@angular/core"], ["NgZone", "@angular/core"], ["ElementRef", "@angular/core"], ["RendererFactory2", "@angular/core"], ["DOCUMENT", "@angular/common"], ["NgModule", "@angular/core"], ["AfterViewInit", "@angular/core"], ["EventEmitter", "@angular/core"], ["Output", "@angular/core"], ["ChangeDetectorRef", "@angular/core"], ["EmbeddedViewRef", "@angular/core"], ["Renderer2", "@angular/core"], ["ViewContainerRef", "@angular/core"], ["signal", "@angular/core"], ["ChangeDetectionStrategy", "@angular/core"], ["Component", "@angular/core"], ["ViewEncapsulation", "@angular/core"], ["WritableSignal", "@angular/core"], ["OnChanges", "@angular/core"], ["SimpleChanges", "@angular/core"], ["afterNextRender", "@angular/core"], ["Injector", "@angular/core"], ["numberAttribute", "@angular/core"], ["moveItemInArray", "@angular/cdk/drag-drop"], ["CdkDragStart", "@angular/cdk/drag-drop"], ["DragStartDelay", "@angular/cdk/drag-drop"], ["Drag<PERSON><PERSON><PERSON>", "@angular/cdk/drag-drop"], ["CDK_DRAG_PARENT", "@angular/cdk/drag-drop"], ["DragConstrainPosition", "@angular/cdk/drag-drop"], ["CDK_DROP_LIST_GROUP", "@angular/cdk/drag-drop"], ["CdkDragRelease", "@angular/cdk/drag-drop"], ["CDK_DRAG_PLACEHOLDER", "@angular/cdk/drag-drop"], ["CDK_DRAG_PREVIEW", "@angular/cdk/drag-drop"], ["CdkDropListGroup", "@angular/cdk/drag-drop"], ["DragDrop", "@angular/cdk/drag-drop"], ["CdkDragPlaceholder", "@angular/cdk/drag-drop"], ["DropListOrientation", "@angular/cdk/drag-drop"], ["CdkDragPreview", "@angular/cdk/drag-drop"], ["DragDropModule", "@angular/cdk/drag-drop"], ["CDK_DRAG_HANDLE", "@angular/cdk/drag-drop"], ["transferArrayItem", "@angular/cdk/drag-drop"], ["CdkDragEnd", "@angular/cdk/drag-drop"], ["CdkDragHandle", "@angular/cdk/drag-drop"], ["CDK_DRAG_CONFIG", "@angular/cdk/drag-drop"], ["DragDropConfig", "@angular/cdk/drag-drop"], ["CdkDropList", "@angular/cdk/drag-drop"], ["DragRefConfig", "@angular/cdk/drag-drop"], ["DragDropRegistry", "@angular/cdk/drag-drop"], ["CdkDragEnter", "@angular/cdk/drag-drop"], ["copyArrayItem", "@angular/cdk/drag-drop"], ["CdkDragExit", "@angular/cdk/drag-drop"], ["DropListRef", "@angular/cdk/drag-drop"], ["CDK_DROP_LIST", "@angular/cdk/drag-drop"], ["CdkDrag", "@angular/cdk/drag-drop"], ["CdkDragDrop", "@angular/cdk/drag-drop"], ["Point", "@angular/cdk/drag-drop"], ["CdkDragMove", "@angular/cdk/drag-drop"], ["PreviewContainer", "@angular/cdk/drag-drop"], ["CdkDragSortEvent", "@angular/cdk/drag-drop"], ["DragRef", "@angular/cdk/drag-drop"], ["moveItemInArray", "@angular/cdk/drag-drop"], ["CdkDragStart", "@angular/cdk/drag-drop"], ["CdkDragStart.source", "@angular/cdk/drag-drop"], ["CdkDragStart.event", "@angular/cdk/drag-drop"], ["DragStartDelay", "@angular/cdk/drag-drop"], ["Drag<PERSON><PERSON><PERSON>", "@angular/cdk/drag-drop"], ["CDK_DRAG_PARENT", "@angular/cdk/drag-drop"], ["DragConstrainPosition", "@angular/cdk/drag-drop"], ["CDK_DROP_LIST_GROUP", "@angular/cdk/drag-drop"], ["CdkDragRelease", "@angular/cdk/drag-drop"], ["CdkDragRelease.source", "@angular/cdk/drag-drop"], ["CdkDragRelease.event", "@angular/cdk/drag-drop"], ["CDK_DRAG_PLACEHOLDER", "@angular/cdk/drag-drop"], ["CDK_DRAG_PREVIEW", "@angular/cdk/drag-drop"], ["CdkDropListGroup", "@angular/cdk/drag-drop"], ["CdkDropListGroup.disabled", "@angular/cdk/drag-drop"], ["CdkDropListGroup.ngOnDestroy", "@angular/cdk/drag-drop"], ["DragDrop", "@angular/cdk/drag-drop"], ["DragDrop.constructor", "@angular/cdk/drag-drop"], ["DragDrop.createDrag", "@angular/cdk/drag-drop"], ["DragDrop.createDropList", "@angular/cdk/drag-drop"], ["CdkDragPlaceholder", "@angular/cdk/drag-drop"], ["CdkDragPlaceholder.constructor", "@angular/cdk/drag-drop"], ["CdkDragPlaceholder.templateRef", "@angular/cdk/drag-drop"], ["CdkDragPlaceholder.data", "@angular/cdk/drag-drop"], ["CdkDragPlaceholder.ngOnDestroy", "@angular/cdk/drag-drop"], ["DropListOrientation", "@angular/cdk/drag-drop"], ["CdkDragPreview", "@angular/cdk/drag-drop"], ["CdkDragPreview.constructor", "@angular/cdk/drag-drop"], ["CdkDragPreview.templateRef", "@angular/cdk/drag-drop"], ["CdkDragPreview.data", "@angular/cdk/drag-drop"], ["CdkDragPreview.matchSize", "@angular/cdk/drag-drop"], ["CdkDragPreview.ngOnDestroy", "@angular/cdk/drag-drop"], ["DragDropModule", "@angular/cdk/drag-drop"], ["CDK_DRAG_HANDLE", "@angular/cdk/drag-drop"], ["transferArrayItem", "@angular/cdk/drag-drop"], ["CdkDragEnd", "@angular/cdk/drag-drop"], ["CdkDragEnd.source", "@angular/cdk/drag-drop"], ["CdkDragEnd.distance", "@angular/cdk/drag-drop"], ["CdkDragEnd.dropPoint", "@angular/cdk/drag-drop"], ["CdkDragEnd.event", "@angular/cdk/drag-drop"], ["CdkDragHandle", "@angular/cdk/drag-drop"], ["CdkDragHandle.constructor", "@angular/cdk/drag-drop"], ["CdkDragHandle.element", "@angular/cdk/drag-drop"], ["CdkDragHandle.disabled", "@angular/cdk/drag-drop"], ["CdkDragHandle.disabled", "@angular/cdk/drag-drop"], ["CdkDragHandle.ngAfterViewInit", "@angular/cdk/drag-drop"], ["CdkDragHandle.ngOnDestroy", "@angular/cdk/drag-drop"], ["CDK_DRAG_CONFIG", "@angular/cdk/drag-drop"], ["DragDropConfig", "@angular/cdk/drag-drop"], ["DragDropConfig.lockAxis", "@angular/cdk/drag-drop"], ["DragDropConfig.dragStartDelay", "@angular/cdk/drag-drop"], ["DragDropConfig.constrainPosition", "@angular/cdk/drag-drop"], ["DragDropConfig.previewClass", "@angular/cdk/drag-drop"], ["DragDropConfig.boundaryElement", "@angular/cdk/drag-drop"], ["DragDropConfig.rootElementSelector", "@angular/cdk/drag-drop"], ["DragDropConfig.draggingDisabled", "@angular/cdk/drag-drop"], ["DragDropConfig.sortingDisabled", "@angular/cdk/drag-drop"], ["DragDropConfig.listAutoScrollDisabled", "@angular/cdk/drag-drop"], ["DragDropConfig.listOrientation", "@angular/cdk/drag-drop"], ["DragDropConfig.zIndex", "@angular/cdk/drag-drop"], ["DragDropConfig.previewContainer", "@angular/cdk/drag-drop"], ["DragDropConfig.dragStartThreshold", "@angular/cdk/drag-drop"], ["DragDropConfig.pointerDirectionChangeThreshold", "@angular/cdk/drag-drop"], ["DragDropConfig.parentDragRef", "@angular/cdk/drag-drop"], ["CdkDropList", "@angular/cdk/drag-drop"], ["CdkDropList.constructor", "@angular/cdk/drag-drop"], ["CdkDropList.element", "@angular/cdk/drag-drop"], ["CdkDropList.connectedTo", "@angular/cdk/drag-drop"], ["CdkDropList.data", "@angular/cdk/drag-drop"], ["CdkDropList.orientation", "@angular/cdk/drag-drop"], ["CdkDropList.id", "@angular/cdk/drag-drop"], ["CdkDropList.lockAxis", "@angular/cdk/drag-drop"], ["CdkDropList.disabled", "@angular/cdk/drag-drop"], ["CdkDropList.disabled", "@angular/cdk/drag-drop"], ["CdkDropList.sortingDisabled", "@angular/cdk/drag-drop"], ["CdkDropList.enterPredicate", "@angular/cdk/drag-drop"], ["CdkDropList.sortPredicate", "@angular/cdk/drag-drop"], ["CdkDropList.autoScrollDisabled", "@angular/cdk/drag-drop"], ["CdkDropList.autoScrollStep", "@angular/cdk/drag-drop"], ["CdkDropList.elementContainerSelector", "@angular/cdk/drag-drop"], ["CdkDropList.dropped", "@angular/cdk/drag-drop"], ["CdkDropList.entered", "@angular/cdk/drag-drop"], ["CdkDropList.exited", "@angular/cdk/drag-drop"], ["CdkDropList.sorted", "@angular/cdk/drag-drop"], ["CdkDropList.addItem", "@angular/cdk/drag-drop"], ["CdkDropList.removeItem", "@angular/cdk/drag-drop"], ["CdkDropList.getSortedItems", "@angular/cdk/drag-drop"], ["CdkDropList.ngOnDestroy", "@angular/cdk/drag-drop"], ["DragRefConfig", "@angular/cdk/drag-drop"], ["DragRefConfig.dragStartThreshold", "@angular/cdk/drag-drop"], ["DragRefConfig.pointerDirectionChangeThreshold", "@angular/cdk/drag-drop"], ["DragRefConfig.zIndex", "@angular/cdk/drag-drop"], ["DragRefConfig.parentDragRef", "@angular/cdk/drag-drop"], ["DragDropRegistry", "@angular/cdk/drag-drop"], ["DragDropRegistry.constructor", "@angular/cdk/drag-drop"], ["DragDropRegistry.pointerMove", "@angular/cdk/drag-drop"], ["DragDropRegistry.pointerUp", "@angular/cdk/drag-drop"], ["DragDropRegistry.scroll", "@angular/cdk/drag-drop"], ["DragDropRegistry.registerDropContainer", "@angular/cdk/drag-drop"], ["DragDropRegistry.registerDragItem", "@angular/cdk/drag-drop"], ["DragDropRegistry.removeDropContainer", "@angular/cdk/drag-drop"], ["DragDropRegistry.removeDragItem", "@angular/cdk/drag-drop"], ["DragDropRegistry.startDragging", "@angular/cdk/drag-drop"], ["DragDropRegistry.stopDragging", "@angular/cdk/drag-drop"], ["DragDropRegistry.isDragging", "@angular/cdk/drag-drop"], ["DragDropRegistry.scrolled", "@angular/cdk/drag-drop"], ["DragDropRegistry.registerDirectiveNode", "@angular/cdk/drag-drop"], ["DragDropRegistry.removeDirectiveNode", "@angular/cdk/drag-drop"], ["DragDropRegistry.getDragDirectiveForNode", "@angular/cdk/drag-drop"], ["DragDropRegistry.ngOnDestroy", "@angular/cdk/drag-drop"], ["CdkDragEnter", "@angular/cdk/drag-drop"], ["CdkDragEnter.container", "@angular/cdk/drag-drop"], ["CdkDragEnter.item", "@angular/cdk/drag-drop"], ["CdkDragEnter.currentIndex", "@angular/cdk/drag-drop"], ["copyArrayItem", "@angular/cdk/drag-drop"], ["CdkDragExit", "@angular/cdk/drag-drop"], ["CdkDragExit.container", "@angular/cdk/drag-drop"], ["CdkDragExit.item", "@angular/cdk/drag-drop"], ["DropListRef", "@angular/cdk/drag-drop"], ["DropListRef.constructor", "@angular/cdk/drag-drop"], ["DropListRef.element", "@angular/cdk/drag-drop"], ["DropListRef.disabled", "@angular/cdk/drag-drop"], ["DropListRef.sortingDisabled", "@angular/cdk/drag-drop"], ["DropListRef.lockAxis", "@angular/cdk/drag-drop"], ["DropListRef.autoScrollDisabled", "@angular/cdk/drag-drop"], ["DropListRef.autoScrollStep", "@angular/cdk/drag-drop"], ["DropListRef.enterPredicate", "@angular/cdk/drag-drop"], ["DropListRef.sortPredicate", "@angular/cdk/drag-drop"], ["DropListRef.beforeStarted", "@angular/cdk/drag-drop"], ["DropListRef.entered", "@angular/cdk/drag-drop"], ["DropListRef.exited", "@angular/cdk/drag-drop"], ["DropListRef.dropped", "@angular/cdk/drag-drop"], ["DropListRef.sorted", "@angular/cdk/drag-drop"], ["DropListRef.receivingStarted", "@angular/cdk/drag-drop"], ["DropListRef.receivingStopped", "@angular/cdk/drag-drop"], ["DropListRef.data", "@angular/cdk/drag-drop"], ["DropListRef.dispose", "@angular/cdk/drag-drop"], ["DropListRef.isDragging", "@angular/cdk/drag-drop"], ["DropListRef.start", "@angular/cdk/drag-drop"], ["DropListRef.enter", "@angular/cdk/drag-drop"], ["DropListRef.exit", "@angular/cdk/drag-drop"], ["DropListRef.drop", "@angular/cdk/drag-drop"], ["DropListRef.withItems", "@angular/cdk/drag-drop"], ["DropListRef.withDirection", "@angular/cdk/drag-drop"], ["DropListRef.connectedTo", "@angular/cdk/drag-drop"], ["DropListRef.withOrientation", "@angular/cdk/drag-drop"], ["DropListRef.withScrollableParents", "@angular/cdk/drag-drop"], ["DropListRef.withElementContainer", "@angular/cdk/drag-drop"], ["DropListRef.getScrollableParents", "@angular/cdk/drag-drop"], ["DropListRef.getItemIndex", "@angular/cdk/drag-drop"], ["DropListRef.isReceiving", "@angular/cdk/drag-drop"], ["CDK_DROP_LIST", "@angular/cdk/drag-drop"], ["CdkDrag", "@angular/cdk/drag-drop"], ["CdkDrag.constructor", "@angular/cdk/drag-drop"], ["CdkDrag.element", "@angular/cdk/drag-drop"], ["CdkDrag.dropContainer", "@angular/cdk/drag-drop"], ["CdkDrag.data", "@angular/cdk/drag-drop"], ["CdkDrag.lockAxis", "@angular/cdk/drag-drop"], ["CdkDrag.rootElementSelector", "@angular/cdk/drag-drop"], ["CdkDrag.boundaryElement", "@angular/cdk/drag-drop"], ["CdkDrag.dragStartDelay", "@angular/cdk/drag-drop"], ["CdkDrag.freeDragPosition", "@angular/cdk/drag-drop"], ["CdkDrag.disabled", "@angular/cdk/drag-drop"], ["CdkDrag.disabled", "@angular/cdk/drag-drop"], ["CdkDrag.constrainPosition", "@angular/cdk/drag-drop"], ["CdkDrag.previewClass", "@angular/cdk/drag-drop"], ["CdkDrag.previewContainer", "@angular/cdk/drag-drop"], ["CdkDrag.scale", "@angular/cdk/drag-drop"], ["CdkDrag.started", "@angular/cdk/drag-drop"], ["CdkDrag.released", "@angular/cdk/drag-drop"], ["CdkDrag.ended", "@angular/cdk/drag-drop"], ["CdkDrag.entered", "@angular/cdk/drag-drop"], ["CdkDrag.exited", "@angular/cdk/drag-drop"], ["CdkDrag.dropped", "@angular/cdk/drag-drop"], ["CdkDrag.moved", "@angular/cdk/drag-drop"], ["CdkDrag.getPlaceholderElement", "@angular/cdk/drag-drop"], ["CdkDrag.getRootElement", "@angular/cdk/drag-drop"], ["CdkDrag.reset", "@angular/cdk/drag-drop"], ["CdkDrag.getFreeDragPosition", "@angular/cdk/drag-drop"], ["CdkDrag.setFreeDragPosition", "@angular/cdk/drag-drop"], ["CdkDrag.ngAfterViewInit", "@angular/cdk/drag-drop"], ["CdkDrag.ngOnChanges", "@angular/cdk/drag-drop"], ["CdkDrag.ngOnDestroy", "@angular/cdk/drag-drop"], ["CdkDragDrop", "@angular/cdk/drag-drop"], ["CdkDragDrop.previousIndex", "@angular/cdk/drag-drop"], ["CdkDragDrop.currentIndex", "@angular/cdk/drag-drop"], ["CdkDragDrop.item", "@angular/cdk/drag-drop"], ["CdkDragDrop.container", "@angular/cdk/drag-drop"], ["CdkDragDrop.previousContainer", "@angular/cdk/drag-drop"], ["CdkDragDrop.isPointerOverContainer", "@angular/cdk/drag-drop"], ["CdkDragDrop.distance", "@angular/cdk/drag-drop"], ["CdkDragDrop.dropPoint", "@angular/cdk/drag-drop"], ["CdkDragDrop.event", "@angular/cdk/drag-drop"], ["Point", "@angular/cdk/drag-drop"], ["Point.x", "@angular/cdk/drag-drop"], ["Point.y", "@angular/cdk/drag-drop"], ["CdkDragMove", "@angular/cdk/drag-drop"], ["CdkDragMove.source", "@angular/cdk/drag-drop"], ["CdkDragMove.pointerPosition", "@angular/cdk/drag-drop"], ["CdkDragMove.event", "@angular/cdk/drag-drop"], ["CdkDragMove.distance", "@angular/cdk/drag-drop"], ["CdkDragMove.delta", "@angular/cdk/drag-drop"], ["PreviewContainer", "@angular/cdk/drag-drop"], ["CdkDragSortEvent", "@angular/cdk/drag-drop"], ["CdkDragSortEvent.previousIndex", "@angular/cdk/drag-drop"], ["CdkDragSortEvent.currentIndex", "@angular/cdk/drag-drop"], ["CdkDragSortEvent.container", "@angular/cdk/drag-drop"], ["CdkDragSortEvent.item", "@angular/cdk/drag-drop"], ["DragRef", "@angular/cdk/drag-drop"], ["DragRef.constructor", "@angular/cdk/drag-drop"], ["DragRef.lockAxis", "@angular/cdk/drag-drop"], ["DragRef.dragStartDelay", "@angular/cdk/drag-drop"], ["DragRef.previewClass", "@angular/cdk/drag-drop"], ["DragRef.scale", "@angular/cdk/drag-drop"], ["DragRef.disabled", "@angular/cdk/drag-drop"], ["DragRef.disabled", "@angular/cdk/drag-drop"], ["DragRef.beforeStarted", "@angular/cdk/drag-drop"], ["DragRef.started", "@angular/cdk/drag-drop"], ["DragRef.released", "@angular/cdk/drag-drop"], ["DragRef.ended", "@angular/cdk/drag-drop"], ["DragRef.entered", "@angular/cdk/drag-drop"], ["DragRef.exited", "@angular/cdk/drag-drop"], ["DragRef.dropped", "@angular/cdk/drag-drop"], ["DragRef.moved", "@angular/cdk/drag-drop"], ["DragRef.data", "@angular/cdk/drag-drop"], ["DragRef.constrainPosition", "@angular/cdk/drag-drop"], ["DragRef.getPlaceholderElement", "@angular/cdk/drag-drop"], ["DragRef.getRootElement", "@angular/cdk/drag-drop"], ["DragRef.getVisibleElement", "@angular/cdk/drag-drop"], ["DragRef.with<PERSON><PERSON><PERSON>", "@angular/cdk/drag-drop"], ["DragRef.withPreviewTemplate", "@angular/cdk/drag-drop"], ["DragRef.withPlaceholderTemplate", "@angular/cdk/drag-drop"], ["DragRef.withRootElement", "@angular/cdk/drag-drop"], ["DragRef.withBoundaryElement", "@angular/cdk/drag-drop"], ["DragRef.with<PERSON><PERSON>nt", "@angular/cdk/drag-drop"], ["DragRef.dispose", "@angular/cdk/drag-drop"], ["DragRef.isDragging", "@angular/cdk/drag-drop"], ["DragRef.reset", "@angular/cdk/drag-drop"], ["DragRef.disableHandle", "@angular/cdk/drag-drop"], ["DragRef.enableHandle", "@angular/cdk/drag-drop"], ["DragRef.withDirection", "@angular/cdk/drag-drop"], ["DragRef.getFreeDragPosition", "@angular/cdk/drag-drop"], ["DragRef.setFreeDragPosition", "@angular/cdk/drag-drop"], ["DragRef.withPreviewContainer", "@angular/cdk/drag-drop"]]}