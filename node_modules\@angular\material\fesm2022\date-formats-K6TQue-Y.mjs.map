{"version": 3, "file": "date-formats-K6TQue-Y.mjs", "sources": ["../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/core/datetime/date-adapter.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/core/datetime/date-formats.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {inject, InjectionToken, LOCALE_ID} from '@angular/core';\nimport {Observable, Subject} from 'rxjs';\n\n/** InjectionToken for datepicker that can be used to override default locale code. */\nexport const MAT_DATE_LOCALE = new InjectionToken<{}>('MAT_DATE_LOCALE', {\n  providedIn: 'root',\n  factory: MAT_DATE_LOCALE_FACTORY,\n});\n\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nexport function MAT_DATE_LOCALE_FACTORY(): {} {\n  return inject(LOCALE_ID);\n}\n\nconst NOT_IMPLEMENTED = 'Method not implemented';\n\n/** Adapts type `D` to be usable as a date by cdk-based components that work with dates. */\nexport abstract class DateAdapter<D, L = any> {\n  /** The locale to use for all dates. */\n  protected locale: L;\n  protected readonly _localeChanges = new Subject<void>();\n\n  /** A stream that emits when the locale changes. */\n  readonly localeChanges: Observable<void> = this._localeChanges;\n\n  /**\n   * Gets the year component of the given date.\n   * @param date The date to extract the year from.\n   * @returns The year component.\n   */\n  abstract getYear(date: D): number;\n\n  /**\n   * Gets the month component of the given date.\n   * @param date The date to extract the month from.\n   * @returns The month component (0-indexed, 0 = January).\n   */\n  abstract getMonth(date: D): number;\n\n  /**\n   * Gets the date of the month component of the given date.\n   * @param date The date to extract the date of the month from.\n   * @returns The month component (1-indexed, 1 = first of month).\n   */\n  abstract getDate(date: D): number;\n\n  /**\n   * Gets the day of the week component of the given date.\n   * @param date The date to extract the day of the week from.\n   * @returns The month component (0-indexed, 0 = Sunday).\n   */\n  abstract getDayOfWeek(date: D): number;\n\n  /**\n   * Gets a list of names for the months.\n   * @param style The naming style (e.g. long = 'January', short = 'Jan', narrow = 'J').\n   * @returns An ordered list of all month names, starting with January.\n   */\n  abstract getMonthNames(style: 'long' | 'short' | 'narrow'): string[];\n\n  /**\n   * Gets a list of names for the dates of the month.\n   * @returns An ordered list of all date of the month names, starting with '1'.\n   */\n  abstract getDateNames(): string[];\n\n  /**\n   * Gets a list of names for the days of the week.\n   * @param style The naming style (e.g. long = 'Sunday', short = 'Sun', narrow = 'S').\n   * @returns An ordered list of all weekday names, starting with Sunday.\n   */\n  abstract getDayOfWeekNames(style: 'long' | 'short' | 'narrow'): string[];\n\n  /**\n   * Gets the name for the year of the given date.\n   * @param date The date to get the year name for.\n   * @returns The name of the given year (e.g. '2017').\n   */\n  abstract getYearName(date: D): string;\n\n  /**\n   * Gets the first day of the week.\n   * @returns The first day of the week (0-indexed, 0 = Sunday).\n   */\n  abstract getFirstDayOfWeek(): number;\n\n  /**\n   * Gets the number of days in the month of the given date.\n   * @param date The date whose month should be checked.\n   * @returns The number of days in the month of the given date.\n   */\n  abstract getNumDaysInMonth(date: D): number;\n\n  /**\n   * Clones the given date.\n   * @param date The date to clone\n   * @returns A new date equal to the given date.\n   */\n  abstract clone(date: D): D;\n\n  /**\n   * Creates a date with the given year, month, and date. Does not allow over/under-flow of the\n   * month and date.\n   * @param year The full year of the date. (e.g. 89 means the year 89, not the year 1989).\n   * @param month The month of the date (0-indexed, 0 = January). Must be an integer 0 - 11.\n   * @param date The date of month of the date. Must be an integer 1 - length of the given month.\n   * @returns The new date, or null if invalid.\n   */\n  abstract createDate(year: number, month: number, date: number): D;\n\n  /**\n   * Gets today's date.\n   * @returns Today's date.\n   */\n  abstract today(): D;\n\n  /**\n   * Parses a date from a user-provided value.\n   * @param value The value to parse.\n   * @param parseFormat The expected format of the value being parsed\n   *     (type is implementation-dependent).\n   * @returns The parsed date.\n   */\n  abstract parse(value: any, parseFormat: any): D | null;\n\n  /**\n   * Formats a date as a string according to the given format.\n   * @param date The value to format.\n   * @param displayFormat The format to use to display the date as a string.\n   * @returns The formatted date string.\n   */\n  abstract format(date: D, displayFormat: any): string;\n\n  /**\n   * Adds the given number of years to the date. Years are counted as if flipping 12 pages on the\n   * calendar for each year and then finding the closest date in the new month. For example when\n   * adding 1 year to Feb 29, 2016, the resulting date will be Feb 28, 2017.\n   * @param date The date to add years to.\n   * @param years The number of years to add (may be negative).\n   * @returns A new date equal to the given one with the specified number of years added.\n   */\n  abstract addCalendarYears(date: D, years: number): D;\n\n  /**\n   * Adds the given number of months to the date. Months are counted as if flipping a page on the\n   * calendar for each month and then finding the closest date in the new month. For example when\n   * adding 1 month to Jan 31, 2017, the resulting date will be Feb 28, 2017.\n   * @param date The date to add months to.\n   * @param months The number of months to add (may be negative).\n   * @returns A new date equal to the given one with the specified number of months added.\n   */\n  abstract addCalendarMonths(date: D, months: number): D;\n\n  /**\n   * Adds the given number of days to the date. Days are counted as if moving one cell on the\n   * calendar for each day.\n   * @param date The date to add days to.\n   * @param days The number of days to add (may be negative).\n   * @returns A new date equal to the given one with the specified number of days added.\n   */\n  abstract addCalendarDays(date: D, days: number): D;\n\n  /**\n   * Gets the RFC 3339 compatible string (https://tools.ietf.org/html/rfc3339) for the given date.\n   * This method is used to generate date strings that are compatible with native HTML attributes\n   * such as the `min` or `max` attribute of an `<input>`.\n   * @param date The date to get the ISO date string for.\n   * @returns The ISO date string date string.\n   */\n  abstract toIso8601(date: D): string;\n\n  /**\n   * Checks whether the given object is considered a date instance by this DateAdapter.\n   * @param obj The object to check\n   * @returns Whether the object is a date instance.\n   */\n  abstract isDateInstance(obj: any): boolean;\n\n  /**\n   * Checks whether the given date is valid.\n   * @param date The date to check.\n   * @returns Whether the date is valid.\n   */\n  abstract isValid(date: D): boolean;\n\n  /**\n   * Gets date instance that is not valid.\n   * @returns An invalid date.\n   */\n  abstract invalid(): D;\n\n  /**\n   * Sets the time of one date to the time of another.\n   * @param target Date whose time will be set.\n   * @param hours New hours to set on the date object.\n   * @param minutes New minutes to set on the date object.\n   * @param seconds New seconds to set on the date object.\n   */\n  setTime(target: D, hours: number, minutes: number, seconds: number): D {\n    throw new Error(NOT_IMPLEMENTED);\n  }\n\n  /**\n   * Gets the hours component of the given date.\n   * @param date The date to extract the hours from.\n   */\n  getHours(date: D): number {\n    throw new Error(NOT_IMPLEMENTED);\n  }\n\n  /**\n   * Gets the minutes component of the given date.\n   * @param date The date to extract the minutes from.\n   */\n  getMinutes(date: D): number {\n    throw new Error(NOT_IMPLEMENTED);\n  }\n\n  /**\n   * Gets the seconds component of the given date.\n   * @param date The date to extract the seconds from.\n   */\n  getSeconds(date: D): number {\n    throw new Error(NOT_IMPLEMENTED);\n  }\n\n  /**\n   * Parses a date with a specific time from a user-provided value.\n   * @param value The value to parse.\n   * @param parseFormat The expected format of the value being parsed\n   *     (type is implementation-dependent).\n   */\n  parseTime(value: any, parseFormat: any): D | null {\n    throw new Error(NOT_IMPLEMENTED);\n  }\n\n  /**\n   * Adds an amount of seconds to the specified date.\n   * @param date Date to which to add the seconds.\n   * @param amount Amount of seconds to add to the date.\n   */\n  addSeconds(date: D, amount: number): D {\n    throw new Error(NOT_IMPLEMENTED);\n  }\n\n  /**\n   * Given a potential date object, returns that same date object if it is\n   * a valid date, or `null` if it's not a valid date.\n   * @param obj The object to check.\n   * @returns A date or `null`.\n   */\n  getValidDateOrNull(obj: unknown): D | null {\n    return this.isDateInstance(obj) && this.isValid(obj as D) ? (obj as D) : null;\n  }\n\n  /**\n   * Attempts to deserialize a value to a valid date object. This is different from parsing in that\n   * deserialize should only accept non-ambiguous, locale-independent formats (e.g. a ISO 8601\n   * string). The default implementation does not allow any deserialization, it simply checks that\n   * the given value is already a valid date object or null. The `<mat-datepicker>` will call this\n   * method on all of its `@Input()` properties that accept dates. It is therefore possible to\n   * support passing values from your backend directly to these properties by overriding this method\n   * to also deserialize the format used by your backend.\n   * @param value The value to be deserialized into a date object.\n   * @returns The deserialized date object, either a valid date, null if the value can be\n   *     deserialized into a null date (e.g. the empty string), or an invalid date.\n   */\n  deserialize(value: any): D | null {\n    if (value == null || (this.isDateInstance(value) && this.isValid(value))) {\n      return value;\n    }\n    return this.invalid();\n  }\n\n  /**\n   * Sets the locale used for all dates.\n   * @param locale The new locale.\n   */\n  setLocale(locale: L) {\n    this.locale = locale;\n    this._localeChanges.next();\n  }\n\n  /**\n   * Compares two dates.\n   * @param first The first date to compare.\n   * @param second The second date to compare.\n   * @returns 0 if the dates are equal, a number less than 0 if the first date is earlier,\n   *     a number greater than 0 if the first date is later.\n   */\n  compareDate(first: D, second: D): number {\n    return (\n      this.getYear(first) - this.getYear(second) ||\n      this.getMonth(first) - this.getMonth(second) ||\n      this.getDate(first) - this.getDate(second)\n    );\n  }\n\n  /**\n   * Compares the time values of two dates.\n   * @param first First date to compare.\n   * @param second Second date to compare.\n   * @returns 0 if the times are equal, a number less than 0 if the first time is earlier,\n   *     a number greater than 0 if the first time is later.\n   */\n  compareTime(first: D, second: D): number {\n    return (\n      this.getHours(first) - this.getHours(second) ||\n      this.getMinutes(first) - this.getMinutes(second) ||\n      this.getSeconds(first) - this.getSeconds(second)\n    );\n  }\n\n  /**\n   * Checks if two dates are equal.\n   * @param first The first date to check.\n   * @param second The second date to check.\n   * @returns Whether the two dates are equal.\n   *     Null dates are considered equal to other null dates.\n   */\n  sameDate(first: D | null, second: D | null): boolean {\n    if (first && second) {\n      let firstValid = this.isValid(first);\n      let secondValid = this.isValid(second);\n      if (firstValid && secondValid) {\n        return !this.compareDate(first, second);\n      }\n      return firstValid == secondValid;\n    }\n    return first == second;\n  }\n\n  /**\n   * Checks if the times of two dates are equal.\n   * @param first The first date to check.\n   * @param second The second date to check.\n   * @returns Whether the times of the two dates are equal.\n   *     Null dates are considered equal to other null dates.\n   */\n  sameTime(first: D | null, second: D | null): boolean {\n    if (first && second) {\n      const firstValid = this.isValid(first);\n      const secondValid = this.isValid(second);\n      if (firstValid && secondValid) {\n        return !this.compareTime(first, second);\n      }\n      return firstValid == secondValid;\n    }\n    return first == second;\n  }\n\n  /**\n   * Clamp the given date between min and max dates.\n   * @param date The date to clamp.\n   * @param min The minimum value to allow. If null or omitted no min is enforced.\n   * @param max The maximum value to allow. If null or omitted no max is enforced.\n   * @returns `min` if `date` is less than `min`, `max` if date is greater than `max`,\n   *     otherwise `date`.\n   */\n  clampDate(date: D, min?: D | null, max?: D | null): D {\n    if (min && this.compareDate(date, min) < 0) {\n      return min;\n    }\n    if (max && this.compareDate(date, max) > 0) {\n      return max;\n    }\n    return date;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {InjectionToken} from '@angular/core';\n\nexport type MatDateFormats = {\n  parse: {\n    dateInput: any;\n    timeInput?: any;\n  };\n  display: {\n    dateInput: any;\n    monthLabel?: any;\n    monthYearLabel: any;\n    dateA11yLabel: any;\n    monthYearA11yLabel: any;\n    timeInput?: any;\n    timeOptionLabel?: any;\n  };\n};\n\nexport const MAT_DATE_FORMATS = new InjectionToken<MatDateFormats>('mat-date-formats');\n"], "names": [], "mappings": ";;;AAWA;MACa,eAAe,GAAG,IAAI,cAAc,CAAK,iBAAiB,EAAE;AACvE,IAAA,UAAU,EAAE,MAAM;AAClB,IAAA,OAAO,EAAE,uBAAuB;AACjC,CAAA;AAED;;;;AAIG;SACa,uBAAuB,GAAA;AACrC,IAAA,OAAO,MAAM,CAAC,SAAS,CAAC;AAC1B;AAEA,MAAM,eAAe,GAAG,wBAAwB;AAEhD;MACsB,WAAW,CAAA;;AAErB,IAAA,MAAM;AACG,IAAA,cAAc,GAAG,IAAI,OAAO,EAAQ;;AAG9C,IAAA,aAAa,GAAqB,IAAI,CAAC,cAAc;AAwK9D;;;;;;AAMG;AACH,IAAA,OAAO,CAAC,MAAS,EAAE,KAAa,EAAE,OAAe,EAAE,OAAe,EAAA;AAChE,QAAA,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC;;AAGlC;;;AAGG;AACH,IAAA,QAAQ,CAAC,IAAO,EAAA;AACd,QAAA,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC;;AAGlC;;;AAGG;AACH,IAAA,UAAU,CAAC,IAAO,EAAA;AAChB,QAAA,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC;;AAGlC;;;AAGG;AACH,IAAA,UAAU,CAAC,IAAO,EAAA;AAChB,QAAA,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC;;AAGlC;;;;;AAKG;IACH,SAAS,CAAC,KAAU,EAAE,WAAgB,EAAA;AACpC,QAAA,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC;;AAGlC;;;;AAIG;IACH,UAAU,CAAC,IAAO,EAAE,MAAc,EAAA;AAChC,QAAA,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC;;AAGlC;;;;;AAKG;AACH,IAAA,kBAAkB,CAAC,GAAY,EAAA;QAC7B,OAAO,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,GAAQ,CAAC,GAAI,GAAS,GAAG,IAAI;;AAG/E;;;;;;;;;;;AAWG;AACH,IAAA,WAAW,CAAC,KAAU,EAAA;QACpB,IAAI,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE;AACxE,YAAA,OAAO,KAAK;;AAEd,QAAA,OAAO,IAAI,CAAC,OAAO,EAAE;;AAGvB;;;AAGG;AACH,IAAA,SAAS,CAAC,MAAS,EAAA;AACjB,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM;AACpB,QAAA,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE;;AAG5B;;;;;;AAMG;IACH,WAAW,CAAC,KAAQ,EAAE,MAAS,EAAA;AAC7B,QAAA,QACE,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;YAC1C,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;AAC5C,YAAA,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;;AAI9C;;;;;;AAMG;IACH,WAAW,CAAC,KAAQ,EAAE,MAAS,EAAA;AAC7B,QAAA,QACE,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;YAC5C,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;AAChD,YAAA,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;;AAIpD;;;;;;AAMG;IACH,QAAQ,CAAC,KAAe,EAAE,MAAgB,EAAA;AACxC,QAAA,IAAI,KAAK,IAAI,MAAM,EAAE;YACnB,IAAI,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;YACpC,IAAI,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;AACtC,YAAA,IAAI,UAAU,IAAI,WAAW,EAAE;gBAC7B,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,MAAM,CAAC;;YAEzC,OAAO,UAAU,IAAI,WAAW;;QAElC,OAAO,KAAK,IAAI,MAAM;;AAGxB;;;;;;AAMG;IACH,QAAQ,CAAC,KAAe,EAAE,MAAgB,EAAA;AACxC,QAAA,IAAI,KAAK,IAAI,MAAM,EAAE;YACnB,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;YACtC,MAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;AACxC,YAAA,IAAI,UAAU,IAAI,WAAW,EAAE;gBAC7B,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,MAAM,CAAC;;YAEzC,OAAO,UAAU,IAAI,WAAW;;QAElC,OAAO,KAAK,IAAI,MAAM;;AAGxB;;;;;;;AAOG;AACH,IAAA,SAAS,CAAC,IAAO,EAAE,GAAc,EAAE,GAAc,EAAA;AAC/C,QAAA,IAAI,GAAG,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,GAAG,CAAC,GAAG,CAAC,EAAE;AAC1C,YAAA,OAAO,GAAG;;AAEZ,QAAA,IAAI,GAAG,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,GAAG,CAAC,GAAG,CAAC,EAAE;AAC1C,YAAA,OAAO,GAAG;;AAEZ,QAAA,OAAO,IAAI;;AAEd;;MClWY,gBAAgB,GAAG,IAAI,cAAc,CAAiB,kBAAkB;;;;"}