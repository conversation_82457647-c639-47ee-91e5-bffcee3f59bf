{"version": 3, "file": "input-value-accessor-D1GvPuqO.mjs", "sources": ["../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/input/input-value-accessor.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {InjectionToken, WritableSignal} from '@angular/core';\n\n/**\n * This token is used to inject the object whose value should be set into `MatInput`. If none is\n * provided, the native `HTMLInputElement` is used. Directives like `MatDatepickerInput` can provide\n * themselves for this token, in order to make `MatInput` delegate the getting and setting of the\n * value to them.\n */\nexport const MAT_INPUT_VALUE_ACCESSOR = new InjectionToken<{value: any | WritableSignal<any>}>(\n  'MAT_INPUT_VALUE_ACCESSOR',\n);\n"], "names": [], "mappings": ";;AAUA;;;;;AAKG;MACU,wBAAwB,GAAG,IAAI,cAAc,CACxD,0BAA0B;;;;"}