
      import {createRequire as __cjsCompatRequire} from 'module';
      const require = __cjsCompatRequire(import.meta.url);
    
import {
  DynamicValue,
  PartialEvaluator,
  PotentialImportKind,
  PotentialImportMode,
  StaticInterpreter,
  createForwardRefResolver
} from "../chunk-BSAOKZSO.js";
import {
  ImportManager,
  Reference,
  TypeScriptReflectionHost,
  reflectObjectLiteral
} from "../chunk-6JLQ22O6.js";
import "../chunk-WUCT7QTW.js";
import "../chunk-ERYCP7NI.js";
import "../chunk-KPQ72R34.js";
export {
  DynamicValue,
  ImportManager,
  PartialEvaluator,
  PotentialImportKind,
  PotentialImportMode,
  Reference,
  StaticInterpreter,
  TypeScriptReflectionHost,
  createForwardRefResolver,
  reflectObjectLiteral
};
/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
//# sourceMappingURL=migrations.js.map
