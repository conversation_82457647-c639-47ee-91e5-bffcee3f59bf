{"version": 3, "file": "localize-CajB9YLv.mjs", "sources": ["../../../../../darwin_arm64-fastbuild-ST-2d99d9656325/bin/packages/localize/src/utils/src/constants.ts", "../../../../../../packages/compiler/src/i18n/digest.ts", "../../../../../darwin_arm64-fastbuild-ST-2d99d9656325/bin/packages/localize/src/utils/src/messages.ts", "../../../../../darwin_arm64-fastbuild-ST-2d99d9656325/bin/packages/localize/src/localize/src/localize.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/**\n * The character used to mark the start and end of a \"block\" in a `$localize` tagged string.\n * A block can indicate metadata about the message or specify a name of a placeholder for a\n * substitution expressions.\n *\n * For example:\n *\n * ```ts\n * $localize`Hello, ${title}:title:!`;\n * $localize`:meaning|description@@id:source message text`;\n * ```\n */\nexport const BLOCK_MARKER = ':';\n\n/**\n * The marker used to separate a message's \"meaning\" from its \"description\" in a metadata block.\n *\n * For example:\n *\n * ```ts\n * $localize `:correct|Indicates that the user got the answer correct: Right!`;\n * $localize `:movement|Button label for moving to the right: Right!`;\n * ```\n */\nexport const MEANING_SEPARATOR = '|';\n\n/**\n * The marker used to separate a message's custom \"id\" from its \"description\" in a metadata block.\n *\n * For example:\n *\n * ```ts\n * $localize `:A welcome message on the home page@@myApp-homepage-welcome: Welcome!`;\n * ```\n */\nexport const ID_SEPARATOR = '@@';\n\n/**\n * The marker used to separate legacy message ids from the rest of a metadata block.\n *\n * For example:\n *\n * ```ts\n * $localize `:@@custom-id␟2df64767cd895a8fabe3e18b94b5b6b6f9e2e3f0: Welcome!`;\n * ```\n *\n * Note that this character is the \"symbol for the unit separator\" (␟) not the \"unit separator\n * character\" itself, since that has no visual representation. See https://graphemica.com/%E2%90%9F.\n *\n * Here is some background for the original \"unit separator character\":\n * https://stackoverflow.com/questions/8695118/whats-the-file-group-record-unit-separator-control-characters-and-its-usage\n */\nexport const LEGACY_ID_INDICATOR = '\\u241F';\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Byte} from '../util';\n\nimport * as i18n from './i18n_ast';\n\n/**\n * A lazily created TextEncoder instance for converting strings into UTF-8 bytes\n */\nlet textEncoder: TextEncoder | undefined;\n\n/**\n * Return the message id or compute it using the XLIFF1 digest.\n */\nexport function digest(message: i18n.Message): string {\n  return message.id || computeDigest(message);\n}\n\n/**\n * Compute the message id using the XLIFF1 digest.\n */\nexport function computeDigest(message: i18n.Message): string {\n  return sha1(serializeNodes(message.nodes).join('') + `[${message.meaning}]`);\n}\n\n/**\n * Return the message id or compute it using the XLIFF2/XMB/$localize digest.\n */\nexport function decimalDigest(message: i18n.Message): string {\n  return message.id || computeDecimalDigest(message);\n}\n\n/**\n * Compute the message id using the XLIFF2/XMB/$localize digest.\n */\nexport function computeDecimalDigest(message: i18n.Message): string {\n  const visitor = new _SerializerIgnoreIcuExpVisitor();\n  const parts = message.nodes.map((a) => a.visit(visitor, null));\n  return computeMsgId(parts.join(''), message.meaning);\n}\n\n/**\n * Serialize the i18n ast to something xml-like in order to generate an UID.\n *\n * The visitor is also used in the i18n parser tests\n *\n * @internal\n */\nclass _SerializerVisitor implements i18n.Visitor {\n  visitText(text: i18n.Text, context: any): any {\n    return text.value;\n  }\n\n  visitContainer(container: i18n.Container, context: any): any {\n    return `[${container.children.map((child) => child.visit(this)).join(', ')}]`;\n  }\n\n  visitIcu(icu: i18n.Icu, context: any): any {\n    const strCases = Object.keys(icu.cases).map(\n      (k: string) => `${k} {${icu.cases[k].visit(this)}}`,\n    );\n    return `{${icu.expression}, ${icu.type}, ${strCases.join(', ')}}`;\n  }\n\n  visitTagPlaceholder(ph: i18n.TagPlaceholder, context: any): any {\n    return ph.isVoid\n      ? `<ph tag name=\"${ph.startName}\"/>`\n      : `<ph tag name=\"${ph.startName}\">${ph.children\n          .map((child) => child.visit(this))\n          .join(', ')}</ph name=\"${ph.closeName}\">`;\n  }\n\n  visitPlaceholder(ph: i18n.Placeholder, context: any): any {\n    return ph.value ? `<ph name=\"${ph.name}\">${ph.value}</ph>` : `<ph name=\"${ph.name}\"/>`;\n  }\n\n  visitIcuPlaceholder(ph: i18n.IcuPlaceholder, context?: any): any {\n    return `<ph icu name=\"${ph.name}\">${ph.value.visit(this)}</ph>`;\n  }\n\n  visitBlockPlaceholder(ph: i18n.BlockPlaceholder, context: any): any {\n    return `<ph block name=\"${ph.startName}\">${ph.children\n      .map((child) => child.visit(this))\n      .join(', ')}</ph name=\"${ph.closeName}\">`;\n  }\n}\n\nconst serializerVisitor = new _SerializerVisitor();\n\nexport function serializeNodes(nodes: i18n.Node[]): string[] {\n  return nodes.map((a) => a.visit(serializerVisitor, null));\n}\n\n/**\n * Serialize the i18n ast to something xml-like in order to generate an UID.\n *\n * Ignore the ICU expressions so that message IDs stays identical if only the expression changes.\n *\n * @internal\n */\nclass _SerializerIgnoreIcuExpVisitor extends _SerializerVisitor {\n  override visitIcu(icu: i18n.Icu): string {\n    let strCases = Object.keys(icu.cases).map((k: string) => `${k} {${icu.cases[k].visit(this)}}`);\n    // Do not take the expression into account\n    return `{${icu.type}, ${strCases.join(', ')}}`;\n  }\n}\n\n/**\n * Compute the SHA1 of the given string\n *\n * see https://csrc.nist.gov/publications/fips/fips180-4/fips-180-4.pdf\n *\n * WARNING: this function has not been designed not tested with security in mind.\n *          DO NOT USE IT IN A SECURITY SENSITIVE CONTEXT.\n */\nexport function sha1(str: string): string {\n  textEncoder ??= new TextEncoder();\n  const utf8 = [...textEncoder.encode(str)];\n  const words32 = bytesToWords32(utf8, Endian.Big);\n  const len = utf8.length * 8;\n\n  const w = new Uint32Array(80);\n  let a = 0x67452301,\n    b = 0xefcdab89,\n    c = 0x98badcfe,\n    d = 0x10325476,\n    e = 0xc3d2e1f0;\n\n  words32[len >> 5] |= 0x80 << (24 - (len % 32));\n  words32[(((len + 64) >> 9) << 4) + 15] = len;\n\n  for (let i = 0; i < words32.length; i += 16) {\n    const h0 = a,\n      h1 = b,\n      h2 = c,\n      h3 = d,\n      h4 = e;\n\n    for (let j = 0; j < 80; j++) {\n      if (j < 16) {\n        w[j] = words32[i + j];\n      } else {\n        w[j] = rol32(w[j - 3] ^ w[j - 8] ^ w[j - 14] ^ w[j - 16], 1);\n      }\n\n      const fkVal = fk(j, b, c, d);\n      const f = fkVal[0];\n      const k = fkVal[1];\n      const temp = [rol32(a, 5), f, e, k, w[j]].reduce(add32);\n      e = d;\n      d = c;\n      c = rol32(b, 30);\n      b = a;\n      a = temp;\n    }\n    a = add32(a, h0);\n    b = add32(b, h1);\n    c = add32(c, h2);\n    d = add32(d, h3);\n    e = add32(e, h4);\n  }\n\n  // Convert the output parts to a 160-bit hexadecimal string\n  return toHexU32(a) + toHexU32(b) + toHexU32(c) + toHexU32(d) + toHexU32(e);\n}\n\n/**\n * Convert and format a number as a string representing a 32-bit unsigned hexadecimal number.\n * @param value The value to format as a string.\n * @returns A hexadecimal string representing the value.\n */\nfunction toHexU32(value: number): string {\n  // unsigned right shift of zero ensures an unsigned 32-bit number\n  return (value >>> 0).toString(16).padStart(8, '0');\n}\n\nfunction fk(index: number, b: number, c: number, d: number): [number, number] {\n  if (index < 20) {\n    return [(b & c) | (~b & d), 0x5a827999];\n  }\n\n  if (index < 40) {\n    return [b ^ c ^ d, 0x6ed9eba1];\n  }\n\n  if (index < 60) {\n    return [(b & c) | (b & d) | (c & d), 0x8f1bbcdc];\n  }\n\n  return [b ^ c ^ d, 0xca62c1d6];\n}\n\n/**\n * Compute the fingerprint of the given string\n *\n * The output is 64 bit number encoded as a decimal string\n *\n * based on:\n * https://github.com/google/closure-compiler/blob/master/src/com/google/javascript/jscomp/GoogleJsMessageIdGenerator.java\n */\nexport function fingerprint(str: string): bigint {\n  textEncoder ??= new TextEncoder();\n  const utf8 = textEncoder.encode(str);\n  const view = new DataView(utf8.buffer, utf8.byteOffset, utf8.byteLength);\n\n  let hi = hash32(view, utf8.length, 0);\n  let lo = hash32(view, utf8.length, 102072);\n\n  if (hi == 0 && (lo == 0 || lo == 1)) {\n    hi = hi ^ 0x130f9bef;\n    lo = lo ^ -0x6b5f56d8;\n  }\n\n  return (BigInt.asUintN(32, BigInt(hi)) << BigInt(32)) | BigInt.asUintN(32, BigInt(lo));\n}\n\nexport function computeMsgId(msg: string, meaning: string = ''): string {\n  let msgFingerprint = fingerprint(msg);\n\n  if (meaning) {\n    // Rotate the 64-bit message fingerprint one bit to the left and then add the meaning\n    // fingerprint.\n    msgFingerprint =\n      BigInt.asUintN(64, msgFingerprint << BigInt(1)) |\n      ((msgFingerprint >> BigInt(63)) & BigInt(1));\n    msgFingerprint += fingerprint(meaning);\n  }\n\n  return BigInt.asUintN(63, msgFingerprint).toString();\n}\n\nfunction hash32(view: DataView, length: number, c: number): number {\n  let a = 0x9e3779b9,\n    b = 0x9e3779b9;\n  let index = 0;\n\n  const end = length - 12;\n  for (; index <= end; index += 12) {\n    a += view.getUint32(index, true);\n    b += view.getUint32(index + 4, true);\n    c += view.getUint32(index + 8, true);\n    const res = mix(a, b, c);\n    (a = res[0]), (b = res[1]), (c = res[2]);\n  }\n\n  const remainder = length - index;\n\n  // the first byte of c is reserved for the length\n  c += length;\n\n  if (remainder >= 4) {\n    a += view.getUint32(index, true);\n    index += 4;\n\n    if (remainder >= 8) {\n      b += view.getUint32(index, true);\n      index += 4;\n\n      // Partial 32-bit word for c\n      if (remainder >= 9) {\n        c += view.getUint8(index++) << 8;\n      }\n      if (remainder >= 10) {\n        c += view.getUint8(index++) << 16;\n      }\n      if (remainder === 11) {\n        c += view.getUint8(index++) << 24;\n      }\n    } else {\n      // Partial 32-bit word for b\n      if (remainder >= 5) {\n        b += view.getUint8(index++);\n      }\n      if (remainder >= 6) {\n        b += view.getUint8(index++) << 8;\n      }\n      if (remainder === 7) {\n        b += view.getUint8(index++) << 16;\n      }\n    }\n  } else {\n    // Partial 32-bit word for a\n    if (remainder >= 1) {\n      a += view.getUint8(index++);\n    }\n    if (remainder >= 2) {\n      a += view.getUint8(index++) << 8;\n    }\n    if (remainder === 3) {\n      a += view.getUint8(index++) << 16;\n    }\n  }\n\n  return mix(a, b, c)[2];\n}\n\nfunction mix(a: number, b: number, c: number): [number, number, number] {\n  a -= b;\n  a -= c;\n  a ^= c >>> 13;\n  b -= c;\n  b -= a;\n  b ^= a << 8;\n  c -= a;\n  c -= b;\n  c ^= b >>> 13;\n  a -= b;\n  a -= c;\n  a ^= c >>> 12;\n  b -= c;\n  b -= a;\n  b ^= a << 16;\n  c -= a;\n  c -= b;\n  c ^= b >>> 5;\n  a -= b;\n  a -= c;\n  a ^= c >>> 3;\n  b -= c;\n  b -= a;\n  b ^= a << 10;\n  c -= a;\n  c -= b;\n  c ^= b >>> 15;\n  return [a, b, c];\n}\n\n// Utils\n\nenum Endian {\n  Little,\n  Big,\n}\n\nfunction add32(a: number, b: number): number {\n  return add32to64(a, b)[1];\n}\n\nfunction add32to64(a: number, b: number): [number, number] {\n  const low = (a & 0xffff) + (b & 0xffff);\n  const high = (a >>> 16) + (b >>> 16) + (low >>> 16);\n  return [high >>> 16, (high << 16) | (low & 0xffff)];\n}\n\n// Rotate a 32b number left `count` position\nfunction rol32(a: number, count: number): number {\n  return (a << count) | (a >>> (32 - count));\n}\n\nfunction bytesToWords32(bytes: Byte[], endian: Endian): number[] {\n  const size = (bytes.length + 3) >>> 2;\n  const words32 = [];\n\n  for (let i = 0; i < size; i++) {\n    words32[i] = wordAt(bytes, i * 4, endian);\n  }\n\n  return words32;\n}\n\nfunction byteAt(bytes: Byte[], index: number): Byte {\n  return index >= bytes.length ? 0 : bytes[index];\n}\n\nfunction wordAt(bytes: Byte[], index: number, endian: Endian): number {\n  let word = 0;\n  if (endian === Endian.Big) {\n    for (let i = 0; i < 4; i++) {\n      word += byteAt(bytes, index + i) << (24 - 8 * i);\n    }\n  } else {\n    for (let i = 0; i < 4; i++) {\n      word += byteAt(bytes, index + i) << (8 * i);\n    }\n  }\n  return word;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n// This module specifier is intentionally a relative path to allow bundling the code directly\n// into the package.\n// @ng_package: ignore-cross-repo-import\nimport {computeMsgId} from '../../../../compiler/src/i18n/digest';\n\nimport {BLOCK_MARKER, ID_SEPARATOR, LEGACY_ID_INDICATOR, MEANING_SEPARATOR} from './constants';\n\n/**\n * Re-export this helper function so that users of `@angular/localize` don't need to actively import\n * from `@angular/compiler`.\n */\nexport {computeMsgId};\n\n/**\n * A string containing a translation source message.\n *\n * I.E. the message that indicates what will be translated from.\n *\n * Uses `{$placeholder-name}` to indicate a placeholder.\n */\nexport type SourceMessage = string;\n\n/**\n * A string containing a translation target message.\n *\n * I.E. the message that indicates what will be translated to.\n *\n * Uses `{$placeholder-name}` to indicate a placeholder.\n *\n * @publicApi\n */\nexport type TargetMessage = string;\n\n/**\n * A string that uniquely identifies a message, to be used for matching translations.\n *\n * @publicApi\n */\nexport type MessageId = string;\n\n/**\n * Declares a copy of the `AbsoluteFsPath` branded type in `@angular/compiler-cli` to avoid an\n * import into `@angular/compiler-cli`. The compiler-cli's declaration files are not necessarily\n * compatible with web environments that use `@angular/localize`, and would inadvertently include\n * `typescript` declaration files in any compilation unit that uses `@angular/localize` (which\n * increases parsing time and memory usage during builds) using a default import that only\n * type-checks when `allowSyntheticDefaultImports` is enabled.\n *\n * @see https://github.com/angular/angular/issues/45179\n */\ntype AbsoluteFsPathLocalizeCopy = string & {_brand: 'AbsoluteFsPath'};\n\n/**\n * The location of the message in the source file.\n *\n * The `line` and `column` values for the `start` and `end` properties are zero-based.\n */\nexport interface SourceLocation {\n  start: {line: number; column: number};\n  end: {line: number; column: number};\n  file: AbsoluteFsPathLocalizeCopy;\n  text?: string;\n}\n\n/**\n * Additional information that can be associated with a message.\n */\nexport interface MessageMetadata {\n  /**\n   * A human readable rendering of the message\n   */\n  text: string;\n  /**\n   * Legacy message ids, if provided.\n   *\n   * In legacy message formats the message id can only be computed directly from the original\n   * template source.\n   *\n   * Since this information is not available in `$localize` calls, the legacy message ids may be\n   * attached by the compiler to the `$localize` metablock so it can be used if needed at the point\n   * of translation if the translations are encoded using the legacy message id.\n   */\n  legacyIds?: string[];\n  /**\n   * The id of the `message` if a custom one was specified explicitly.\n   *\n   * This id overrides any computed or legacy ids.\n   */\n  customId?: string;\n  /**\n   * The meaning of the `message`, used to distinguish identical `messageString`s.\n   */\n  meaning?: string;\n  /**\n   * The description of the `message`, used to aid translation.\n   */\n  description?: string;\n  /**\n   * The location of the message in the source.\n   */\n  location?: SourceLocation;\n}\n\n/**\n * Information parsed from a `$localize` tagged string that is used to translate it.\n *\n * For example:\n *\n * ```ts\n * const name = 'Jo Bloggs';\n * $localize`Hello ${name}:title@@ID:!`;\n * ```\n *\n * May be parsed into:\n *\n * ```ts\n * {\n *   id: '6998194507597730591',\n *   substitutions: { title: 'Jo Bloggs' },\n *   messageString: 'Hello {$title}!',\n *   placeholderNames: ['title'],\n *   associatedMessageIds: { title: 'ID' },\n * }\n * ```\n */\nexport interface ParsedMessage extends MessageMetadata {\n  /**\n   * The key used to look up the appropriate translation target.\n   */\n  id: MessageId;\n  /**\n   * A mapping of placeholder names to substitution values.\n   */\n  substitutions: Record<string, any>;\n  /**\n   * An optional mapping of placeholder names to associated MessageIds.\n   * This can be used to match ICU placeholders to the message that contains the ICU.\n   */\n  associatedMessageIds?: Record<string, MessageId>;\n  /**\n   * An optional mapping of placeholder names to source locations\n   */\n  substitutionLocations?: Record<string, SourceLocation | undefined>;\n  /**\n   * The static parts of the message.\n   */\n  messageParts: string[];\n  /**\n   * An optional mapping of message parts to source locations\n   */\n  messagePartLocations?: (SourceLocation | undefined)[];\n  /**\n   * The names of the placeholders that will be replaced with substitutions.\n   */\n  placeholderNames: string[];\n}\n\n/**\n * Parse a `$localize` tagged string into a structure that can be used for translation or\n * extraction.\n *\n * See `ParsedMessage` for an example.\n */\nexport function parseMessage(\n  messageParts: TemplateStringsArray,\n  expressions?: readonly any[],\n  location?: SourceLocation,\n  messagePartLocations?: (SourceLocation | undefined)[],\n  expressionLocations: (SourceLocation | undefined)[] = [],\n): ParsedMessage {\n  const substitutions: {[placeholderName: string]: any} = {};\n  const substitutionLocations: {[placeholderName: string]: SourceLocation | undefined} = {};\n  const associatedMessageIds: {[placeholderName: string]: MessageId} = {};\n  const metadata = parseMetadata(messageParts[0], messageParts.raw[0]);\n  const cleanedMessageParts: string[] = [metadata.text];\n  const placeholderNames: string[] = [];\n  let messageString = metadata.text;\n  for (let i = 1; i < messageParts.length; i++) {\n    const {\n      messagePart,\n      placeholderName = computePlaceholderName(i),\n      associatedMessageId,\n    } = parsePlaceholder(messageParts[i], messageParts.raw[i]);\n    messageString += `{$${placeholderName}}${messagePart}`;\n    if (expressions !== undefined) {\n      substitutions[placeholderName] = expressions[i - 1];\n      substitutionLocations[placeholderName] = expressionLocations[i - 1];\n    }\n    placeholderNames.push(placeholderName);\n    if (associatedMessageId !== undefined) {\n      associatedMessageIds[placeholderName] = associatedMessageId;\n    }\n    cleanedMessageParts.push(messagePart);\n  }\n  const messageId = metadata.customId || computeMsgId(messageString, metadata.meaning || '');\n  const legacyIds = metadata.legacyIds ? metadata.legacyIds.filter((id) => id !== messageId) : [];\n  return {\n    id: messageId,\n    legacyIds,\n    substitutions,\n    substitutionLocations,\n    text: messageString,\n    customId: metadata.customId,\n    meaning: metadata.meaning || '',\n    description: metadata.description || '',\n    messageParts: cleanedMessageParts,\n    messagePartLocations,\n    placeholderNames,\n    associatedMessageIds,\n    location,\n  };\n}\n\n/**\n * Parse the given message part (`cooked` + `raw`) to extract the message metadata from the text.\n *\n * If the message part has a metadata block this function will extract the `meaning`,\n * `description`, `customId` and `legacyId` (if provided) from the block. These metadata properties\n * are serialized in the string delimited by `|`, `@@` and `␟` respectively.\n *\n * (Note that `␟` is the `LEGACY_ID_INDICATOR` - see `constants.ts`.)\n *\n * For example:\n *\n * ```ts\n * `:meaning|description@@custom-id:`\n * `:meaning|@@custom-id:`\n * `:meaning|description:`\n * `:description@@custom-id:`\n * `:meaning|:`\n * `:description:`\n * `:@@custom-id:`\n * `:meaning|description@@custom-id␟legacy-id-1␟legacy-id-2:`\n * ```\n *\n * @param cooked The cooked version of the message part to parse.\n * @param raw The raw version of the message part to parse.\n * @returns A object containing any metadata that was parsed from the message part.\n */\nexport function parseMetadata(cooked: string, raw: string): MessageMetadata {\n  const {text: messageString, block} = splitBlock(cooked, raw);\n  if (block === undefined) {\n    return {text: messageString};\n  } else {\n    const [meaningDescAndId, ...legacyIds] = block.split(LEGACY_ID_INDICATOR);\n    const [meaningAndDesc, customId] = meaningDescAndId.split(ID_SEPARATOR, 2);\n    let [meaning, description]: (string | undefined)[] = meaningAndDesc.split(MEANING_SEPARATOR, 2);\n    if (description === undefined) {\n      description = meaning;\n      meaning = undefined;\n    }\n    if (description === '') {\n      description = undefined;\n    }\n    return {text: messageString, meaning, description, customId, legacyIds};\n  }\n}\n\n/**\n * Parse the given message part (`cooked` + `raw`) to extract any placeholder metadata from the\n * text.\n *\n * If the message part has a metadata block this function will extract the `placeholderName` and\n * `associatedMessageId` (if provided) from the block.\n *\n * These metadata properties are serialized in the string delimited by `@@`.\n *\n * For example:\n *\n * ```ts\n * `:placeholder-name@@associated-id:`\n * ```\n *\n * @param cooked The cooked version of the message part to parse.\n * @param raw The raw version of the message part to parse.\n * @returns A object containing the metadata (`placeholderName` and `associatedMessageId`) of the\n *     preceding placeholder, along with the static text that follows.\n */\nexport function parsePlaceholder(\n  cooked: string,\n  raw: string,\n): {messagePart: string; placeholderName?: string; associatedMessageId?: string} {\n  const {text: messagePart, block} = splitBlock(cooked, raw);\n  if (block === undefined) {\n    return {messagePart};\n  } else {\n    const [placeholderName, associatedMessageId] = block.split(ID_SEPARATOR);\n    return {messagePart, placeholderName, associatedMessageId};\n  }\n}\n\n/**\n * Split a message part (`cooked` + `raw`) into an optional delimited \"block\" off the front and the\n * rest of the text of the message part.\n *\n * Blocks appear at the start of message parts. They are delimited by a colon `:` character at the\n * start and end of the block.\n *\n * If the block is in the first message part then it will be metadata about the whole message:\n * meaning, description, id.  Otherwise it will be metadata about the immediately preceding\n * substitution: placeholder name.\n *\n * Since blocks are optional, it is possible that the content of a message block actually starts\n * with a block marker. In this case the marker must be escaped `\\:`.\n *\n * @param cooked The cooked version of the message part to parse.\n * @param raw The raw version of the message part to parse.\n * @returns An object containing the `text` of the message part and the text of the `block`, if it\n * exists.\n * @throws an error if the `block` is unterminated\n */\nexport function splitBlock(cooked: string, raw: string): {text: string; block?: string} {\n  if (raw.charAt(0) !== BLOCK_MARKER) {\n    return {text: cooked};\n  } else {\n    const endOfBlock = findEndOfBlock(cooked, raw);\n    return {\n      block: cooked.substring(1, endOfBlock),\n      text: cooked.substring(endOfBlock + 1),\n    };\n  }\n}\n\nfunction computePlaceholderName(index: number) {\n  return index === 1 ? 'PH' : `PH_${index - 1}`;\n}\n\n/**\n * Find the end of a \"marked block\" indicated by the first non-escaped colon.\n *\n * @param cooked The cooked string (where escaped chars have been processed)\n * @param raw The raw string (where escape sequences are still in place)\n *\n * @returns the index of the end of block marker\n * @throws an error if the block is unterminated\n */\nexport function findEndOfBlock(cooked: string, raw: string): number {\n  for (let cookedIndex = 1, rawIndex = 1; cookedIndex < cooked.length; cookedIndex++, rawIndex++) {\n    if (raw[rawIndex] === '\\\\') {\n      rawIndex++;\n    } else if (cooked[cookedIndex] === BLOCK_MARKER) {\n      return cookedIndex;\n    }\n  }\n  throw new Error(`Unterminated $localize metadata block in \"${raw}\".`);\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {findEndOfBlock} from '../../utils';\n\n/** @docs-private */\nexport interface LocalizeFn {\n  (messageParts: TemplateStringsArray, ...expressions: readonly any[]): string;\n\n  /**\n   * A function that converts an input \"message with expressions\" into a translated \"message with\n   * expressions\".\n   *\n   * The conversion may be done in place, modifying the array passed to the function, so\n   * don't assume that this has no side-effects.\n   *\n   * The expressions must be passed in since it might be they need to be reordered for\n   * different translations.\n   */\n  translate?: TranslateFn;\n  /**\n   * The current locale of the translated messages.\n   *\n   * The compile-time translation inliner is able to replace the following code:\n   *\n   * ```ts\n   * typeof $localize !== \"undefined\" && $localize.locale\n   * ```\n   *\n   * with a string literal of the current locale. E.g.\n   *\n   * ```\n   * \"fr\"\n   * ```\n   */\n  locale?: string;\n}\n\n/** @docs-private */\nexport interface TranslateFn {\n  (\n    messageParts: TemplateStringsArray,\n    expressions: readonly any[],\n  ): [TemplateStringsArray, readonly any[]];\n}\n\n/**\n * Tag a template literal string for localization.\n *\n * For example:\n *\n * ```ts\n * $localize `some string to localize`\n * ```\n *\n * **Providing meaning, description and id**\n *\n * You can optionally specify one or more of `meaning`, `description` and `id` for a localized\n * string by pre-pending it with a colon delimited block of the form:\n *\n * ```ts\n * $localize`:meaning|description@@id:source message text`;\n *\n * $localize`:meaning|:source message text`;\n * $localize`:description:source message text`;\n * $localize`:@@id:source message text`;\n * ```\n *\n * This format is the same as that used for `i18n` markers in Angular templates. See the\n * [Angular i18n guide](guide/i18n/prepare#mark-text-in-component-template).\n *\n * **Naming placeholders**\n *\n * If the template literal string contains expressions, then the expressions will be automatically\n * associated with placeholder names for you.\n *\n * For example:\n *\n * ```ts\n * $localize `Hi ${name}! There are ${items.length} items.`;\n * ```\n *\n * will generate a message-source of `Hi {$PH}! There are {$PH_1} items`.\n *\n * The recommended practice is to name the placeholder associated with each expression though.\n *\n * Do this by providing the placeholder name wrapped in `:` characters directly after the\n * expression. These placeholder names are stripped out of the rendered localized string.\n *\n * For example, to name the `items.length` expression placeholder `itemCount` you write:\n *\n * ```ts\n * $localize `There are ${items.length}:itemCount: items`;\n * ```\n *\n * **Escaping colon markers**\n *\n * If you need to use a `:` character directly at the start of a tagged string that has no\n * metadata block, or directly after a substitution expression that has no name you must escape\n * the `:` by preceding it with a backslash:\n *\n * For example:\n *\n * ```ts\n * // message has a metadata block so no need to escape colon\n * $localize `:some description::this message starts with a colon (:)`;\n * // no metadata block so the colon must be escaped\n * $localize `\\:this message starts with a colon (:)`;\n * ```\n *\n * ```ts\n * // named substitution so no need to escape colon\n * $localize `${label}:label:: ${}`\n * // anonymous substitution so colon must be escaped\n * $localize `${label}\\: ${}`\n * ```\n *\n * **Processing localized strings:**\n *\n * There are three scenarios:\n *\n * * **compile-time inlining**: the `$localize` tag is transformed at compile time by a\n * transpiler, removing the tag and replacing the template literal string with a translated\n * literal string from a collection of translations provided to the transpilation tool.\n *\n * * **run-time evaluation**: the `$localize` tag is a run-time function that replaces and\n * reorders the parts (static strings and expressions) of the template literal string with strings\n * from a collection of translations loaded at run-time.\n *\n * * **pass-through evaluation**: the `$localize` tag is a run-time function that simply evaluates\n * the original template literal string without applying any translations to the parts. This\n * version is used during development or where there is no need to translate the localized\n * template literals.\n *\n * @param messageParts a collection of the static parts of the template string.\n * @param expressions a collection of the values of each placeholder in the template string.\n * @returns the translated string, with the `messageParts` and `expressions` interleaved together.\n *\n * @publicApi\n */\nexport const $localize: LocalizeFn = function (\n  messageParts: TemplateStringsArray,\n  ...expressions: readonly any[]\n) {\n  if ($localize.translate) {\n    // Don't use array expansion here to avoid the compiler adding `__read()` helper unnecessarily.\n    const translation = $localize.translate(messageParts, expressions);\n    messageParts = translation[0];\n    expressions = translation[1];\n  }\n  let message = stripBlock(messageParts[0], messageParts.raw[0]);\n  for (let i = 1; i < messageParts.length; i++) {\n    message += expressions[i - 1] + stripBlock(messageParts[i], messageParts.raw[i]);\n  }\n  return message;\n};\n\nconst BLOCK_MARKER = ':';\n\n/**\n * Strip a delimited \"block\" from the start of the `messagePart`, if it is found.\n *\n * If a marker character (:) actually appears in the content at the start of a tagged string or\n * after a substitution expression, where a block has not been provided the character must be\n * escaped with a backslash, `\\:`. This function checks for this by looking at the `raw`\n * messagePart, which should still contain the backslash.\n *\n * @param messagePart The cooked message part to process.\n * @param rawMessagePart The raw message part to check.\n * @returns the message part with the placeholder name stripped, if found.\n * @throws an error if the block is unterminated\n */\nfunction stripBlock(messagePart: string, rawMessagePart: string) {\n  return rawMessagePart.charAt(0) === BLOCK_MARKER\n    ? messagePart.substring(findEndOfBlock(messagePart, rawMessagePart) + 1)\n    : messagePart;\n}\n"], "names": ["BLOCK_MARKER"], "mappings": ";;;;;;AAQA;;;;;;;;;;;AAWG;AACI,MAAMA,cAAY,GAAG;AAE5B;;;;;;;;;AASG;AACI,MAAM,iBAAiB,GAAG,GAAG;AAEpC;;;;;;;;AAQG;AACI,MAAM,YAAY,GAAG,IAAI;AAEhC;;;;;;;;;;;;;;AAcG;AACI,MAAM,mBAAmB,GAAG,QAAQ;;AChD3C;;AAEG;AACH,IAAI,WAAoC;AAwLxC;;;;;;;AAOG;AACG,SAAU,WAAW,CAAC,GAAW,EAAA;AACrC,IAAA,WAAW,KAAK,IAAI,WAAW,EAAE;IACjC,MAAM,IAAI,GAAG,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC;AACpC,IAAA,MAAM,IAAI,GAAG,IAAI,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC;AAExE,IAAA,IAAI,EAAE,GAAG,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC;AACrC,IAAA,IAAI,EAAE,GAAG,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC;AAE1C,IAAA,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,EAAE;AACnC,QAAA,EAAE,GAAG,EAAE,GAAG,UAAU;AACpB,QAAA,EAAE,GAAG,EAAE,GAAG,WAAW;;AAGvB,IAAA,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,EAAE,MAAM,CAAC,EAAE,CAAC,CAAC,IAAI,MAAM,CAAC,EAAE,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,EAAE,EAAE,MAAM,CAAC,EAAE,CAAC,CAAC;AACxF;SAEgB,YAAY,CAAC,GAAW,EAAE,UAAkB,EAAE,EAAA;AAC5D,IAAA,IAAI,cAAc,GAAG,WAAW,CAAC,GAAG,CAAC;IAErC,IAAI,OAAO,EAAE;;;QAGX,cAAc;YACZ,MAAM,CAAC,OAAO,CAAC,EAAE,EAAE,cAAc,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC;AAC/C,iBAAC,CAAC,cAAc,IAAI,MAAM,CAAC,EAAE,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC;AAC9C,QAAA,cAAc,IAAI,WAAW,CAAC,OAAO,CAAC;;IAGxC,OAAO,MAAM,CAAC,OAAO,CAAC,EAAE,EAAE,cAAc,CAAC,CAAC,QAAQ,EAAE;AACtD;AAEA,SAAS,MAAM,CAAC,IAAc,EAAE,MAAc,EAAE,CAAS,EAAA;AACvD,IAAA,IAAI,CAAC,GAAG,UAAU,EAChB,CAAC,GAAG,UAAU;IAChB,IAAI,KAAK,GAAG,CAAC;AAEb,IAAA,MAAM,GAAG,GAAG,MAAM,GAAG,EAAE;IACvB,OAAO,KAAK,IAAI,GAAG,EAAE,KAAK,IAAI,EAAE,EAAE;QAChC,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,CAAC;QAChC,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,GAAG,CAAC,EAAE,IAAI,CAAC;QACpC,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,GAAG,CAAC,EAAE,IAAI,CAAC;QACpC,MAAM,GAAG,GAAG,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACxB,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;;AAG1C,IAAA,MAAM,SAAS,GAAG,MAAM,GAAG,KAAK;;IAGhC,CAAC,IAAI,MAAM;AAEX,IAAA,IAAI,SAAS,IAAI,CAAC,EAAE;QAClB,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,CAAC;QAChC,KAAK,IAAI,CAAC;AAEV,QAAA,IAAI,SAAS,IAAI,CAAC,EAAE;YAClB,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,CAAC;YAChC,KAAK,IAAI,CAAC;;AAGV,YAAA,IAAI,SAAS,IAAI,CAAC,EAAE;gBAClB,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC;;AAElC,YAAA,IAAI,SAAS,IAAI,EAAE,EAAE;gBACnB,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE;;AAEnC,YAAA,IAAI,SAAS,KAAK,EAAE,EAAE;gBACpB,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE;;;aAE9B;;AAEL,YAAA,IAAI,SAAS,IAAI,CAAC,EAAE;gBAClB,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;;AAE7B,YAAA,IAAI,SAAS,IAAI,CAAC,EAAE;gBAClB,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC;;AAElC,YAAA,IAAI,SAAS,KAAK,CAAC,EAAE;gBACnB,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE;;;;SAGhC;;AAEL,QAAA,IAAI,SAAS,IAAI,CAAC,EAAE;YAClB,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;;AAE7B,QAAA,IAAI,SAAS,IAAI,CAAC,EAAE;YAClB,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC;;AAElC,QAAA,IAAI,SAAS,KAAK,CAAC,EAAE;YACnB,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE;;;IAIrC,OAAO,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AACxB;AAEA,SAAS,GAAG,CAAC,CAAS,EAAE,CAAS,EAAE,CAAS,EAAA;IAC1C,CAAC,IAAI,CAAC;IACN,CAAC,IAAI,CAAC;AACN,IAAA,CAAC,IAAI,CAAC,KAAK,EAAE;IACb,CAAC,IAAI,CAAC;IACN,CAAC,IAAI,CAAC;AACN,IAAA,CAAC,IAAI,CAAC,IAAI,CAAC;IACX,CAAC,IAAI,CAAC;IACN,CAAC,IAAI,CAAC;AACN,IAAA,CAAC,IAAI,CAAC,KAAK,EAAE;IACb,CAAC,IAAI,CAAC;IACN,CAAC,IAAI,CAAC;AACN,IAAA,CAAC,IAAI,CAAC,KAAK,EAAE;IACb,CAAC,IAAI,CAAC;IACN,CAAC,IAAI,CAAC;AACN,IAAA,CAAC,IAAI,CAAC,IAAI,EAAE;IACZ,CAAC,IAAI,CAAC;IACN,CAAC,IAAI,CAAC;AACN,IAAA,CAAC,IAAI,CAAC,KAAK,CAAC;IACZ,CAAC,IAAI,CAAC;IACN,CAAC,IAAI,CAAC;AACN,IAAA,CAAC,IAAI,CAAC,KAAK,CAAC;IACZ,CAAC,IAAI,CAAC;IACN,CAAC,IAAI,CAAC;AACN,IAAA,CAAC,IAAI,CAAC,IAAI,EAAE;IACZ,CAAC,IAAI,CAAC;IACN,CAAC,IAAI,CAAC;AACN,IAAA,CAAC,IAAI,CAAC,KAAK,EAAE;AACb,IAAA,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AAClB;AAEA;AAEA,IAAK,MAGJ;AAHD,CAAA,UAAK,MAAM,EAAA;AACT,IAAA,MAAA,CAAA,MAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,GAAA,QAAM;AACN,IAAA,MAAA,CAAA,MAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA,GAAA,KAAG;AACL,CAAC,EAHI,MAAM,KAAN,MAAM,GAGV,EAAA,CAAA,CAAA;;AC5UD;AACA;AACA;AA2JA;;;;;AAKG;AACa,SAAA,YAAY,CAC1B,YAAkC,EAClC,WAA4B,EAC5B,QAAyB,EACzB,oBAAqD,EACrD,mBAAA,GAAsD,EAAE,EAAA;IAExD,MAAM,aAAa,GAAqC,EAAE;IAC1D,MAAM,qBAAqB,GAA4D,EAAE;IACzF,MAAM,oBAAoB,GAA2C,EAAE;AACvE,IAAA,MAAM,QAAQ,GAAG,aAAa,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AACpE,IAAA,MAAM,mBAAmB,GAAa,CAAC,QAAQ,CAAC,IAAI,CAAC;IACrD,MAAM,gBAAgB,GAAa,EAAE;AACrC,IAAA,IAAI,aAAa,GAAG,QAAQ,CAAC,IAAI;AACjC,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QAC5C,MAAM,EACJ,WAAW,EACX,eAAe,GAAG,sBAAsB,CAAC,CAAC,CAAC,EAC3C,mBAAmB,GACpB,GAAG,gBAAgB,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AAC1D,QAAA,aAAa,IAAI,CAAK,EAAA,EAAA,eAAe,CAAI,CAAA,EAAA,WAAW,EAAE;AACtD,QAAA,IAAI,WAAW,KAAK,SAAS,EAAE;YAC7B,aAAa,CAAC,eAAe,CAAC,GAAG,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC;YACnD,qBAAqB,CAAC,eAAe,CAAC,GAAG,mBAAmB,CAAC,CAAC,GAAG,CAAC,CAAC;;AAErE,QAAA,gBAAgB,CAAC,IAAI,CAAC,eAAe,CAAC;AACtC,QAAA,IAAI,mBAAmB,KAAK,SAAS,EAAE;AACrC,YAAA,oBAAoB,CAAC,eAAe,CAAC,GAAG,mBAAmB;;AAE7D,QAAA,mBAAmB,CAAC,IAAI,CAAC,WAAW,CAAC;;AAEvC,IAAA,MAAM,SAAS,GAAG,QAAQ,CAAC,QAAQ,IAAI,YAAY,CAAC,aAAa,EAAE,QAAQ,CAAC,OAAO,IAAI,EAAE,CAAC;IAC1F,MAAM,SAAS,GAAG,QAAQ,CAAC,SAAS,GAAG,QAAQ,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,EAAE,KAAK,EAAE,KAAK,SAAS,CAAC,GAAG,EAAE;IAC/F,OAAO;AACL,QAAA,EAAE,EAAE,SAAS;QACb,SAAS;QACT,aAAa;QACb,qBAAqB;AACrB,QAAA,IAAI,EAAE,aAAa;QACnB,QAAQ,EAAE,QAAQ,CAAC,QAAQ;AAC3B,QAAA,OAAO,EAAE,QAAQ,CAAC,OAAO,IAAI,EAAE;AAC/B,QAAA,WAAW,EAAE,QAAQ,CAAC,WAAW,IAAI,EAAE;AACvC,QAAA,YAAY,EAAE,mBAAmB;QACjC,oBAAoB;QACpB,gBAAgB;QAChB,oBAAoB;QACpB,QAAQ;KACT;AACH;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;AAyBG;AACa,SAAA,aAAa,CAAC,MAAc,EAAE,GAAW,EAAA;AACvD,IAAA,MAAM,EAAC,IAAI,EAAE,aAAa,EAAE,KAAK,EAAC,GAAG,UAAU,CAAC,MAAM,EAAE,GAAG,CAAC;AAC5D,IAAA,IAAI,KAAK,KAAK,SAAS,EAAE;AACvB,QAAA,OAAO,EAAC,IAAI,EAAE,aAAa,EAAC;;SACvB;AACL,QAAA,MAAM,CAAC,gBAAgB,EAAE,GAAG,SAAS,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,mBAAmB,CAAC;AACzE,QAAA,MAAM,CAAC,cAAc,EAAE,QAAQ,CAAC,GAAG,gBAAgB,CAAC,KAAK,CAAC,YAAY,EAAE,CAAC,CAAC;AAC1E,QAAA,IAAI,CAAC,OAAO,EAAE,WAAW,CAAC,GAA2B,cAAc,CAAC,KAAK,CAAC,iBAAiB,EAAE,CAAC,CAAC;AAC/F,QAAA,IAAI,WAAW,KAAK,SAAS,EAAE;YAC7B,WAAW,GAAG,OAAO;YACrB,OAAO,GAAG,SAAS;;AAErB,QAAA,IAAI,WAAW,KAAK,EAAE,EAAE;YACtB,WAAW,GAAG,SAAS;;AAEzB,QAAA,OAAO,EAAC,IAAI,EAAE,aAAa,EAAE,OAAO,EAAE,WAAW,EAAE,QAAQ,EAAE,SAAS,EAAC;;AAE3E;AAEA;;;;;;;;;;;;;;;;;;;AAmBG;AACa,SAAA,gBAAgB,CAC9B,MAAc,EACd,GAAW,EAAA;AAEX,IAAA,MAAM,EAAC,IAAI,EAAE,WAAW,EAAE,KAAK,EAAC,GAAG,UAAU,CAAC,MAAM,EAAE,GAAG,CAAC;AAC1D,IAAA,IAAI,KAAK,KAAK,SAAS,EAAE;QACvB,OAAO,EAAC,WAAW,EAAC;;SACf;AACL,QAAA,MAAM,CAAC,eAAe,EAAE,mBAAmB,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,YAAY,CAAC;AACxE,QAAA,OAAO,EAAC,WAAW,EAAE,eAAe,EAAE,mBAAmB,EAAC;;AAE9D;AAEA;;;;;;;;;;;;;;;;;;;AAmBG;AACa,SAAA,UAAU,CAAC,MAAc,EAAE,GAAW,EAAA;IACpD,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,KAAKA,cAAY,EAAE;AAClC,QAAA,OAAO,EAAC,IAAI,EAAE,MAAM,EAAC;;SAChB;QACL,MAAM,UAAU,GAAG,cAAc,CAAC,MAAM,EAAE,GAAG,CAAC;QAC9C,OAAO;YACL,KAAK,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE,UAAU,CAAC;YACtC,IAAI,EAAE,MAAM,CAAC,SAAS,CAAC,UAAU,GAAG,CAAC,CAAC;SACvC;;AAEL;AAEA,SAAS,sBAAsB,CAAC,KAAa,EAAA;AAC3C,IAAA,OAAO,KAAK,KAAK,CAAC,GAAG,IAAI,GAAG,CAAM,GAAA,EAAA,KAAK,GAAG,CAAC,EAAE;AAC/C;AAEA;;;;;;;;AAQG;AACa,SAAA,cAAc,CAAC,MAAc,EAAE,GAAW,EAAA;IACxD,KAAK,IAAI,WAAW,GAAG,CAAC,EAAE,QAAQ,GAAG,CAAC,EAAE,WAAW,GAAG,MAAM,CAAC,MAAM,EAAE,WAAW,EAAE,EAAE,QAAQ,EAAE,EAAE;AAC9F,QAAA,IAAI,GAAG,CAAC,QAAQ,CAAC,KAAK,IAAI,EAAE;AAC1B,YAAA,QAAQ,EAAE;;AACL,aAAA,IAAI,MAAM,CAAC,WAAW,CAAC,KAAKA,cAAY,EAAE;AAC/C,YAAA,OAAO,WAAW;;;AAGtB,IAAA,MAAM,IAAI,KAAK,CAAC,6CAA6C,GAAG,CAAA,EAAA,CAAI,CAAC;AACvE;;AC7SA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6FG;MACU,SAAS,GAAe,UACnC,YAAkC,EAClC,GAAG,WAA2B,EAAA;AAE9B,IAAA,IAAI,SAAS,CAAC,SAAS,EAAE;;QAEvB,MAAM,WAAW,GAAG,SAAS,CAAC,SAAS,CAAC,YAAY,EAAE,WAAW,CAAC;AAClE,QAAA,YAAY,GAAG,WAAW,CAAC,CAAC,CAAC;AAC7B,QAAA,WAAW,GAAG,WAAW,CAAC,CAAC,CAAC;;AAE9B,IAAA,IAAI,OAAO,GAAG,UAAU,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AAC9D,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QAC5C,OAAO,IAAI,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,UAAU,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;;AAElF,IAAA,OAAO,OAAO;AAChB;AAEA,MAAM,YAAY,GAAG,GAAG;AAExB;;;;;;;;;;;;AAYG;AACH,SAAS,UAAU,CAAC,WAAmB,EAAE,cAAsB,EAAA;AAC7D,IAAA,OAAO,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK;AAClC,UAAE,WAAW,CAAC,SAAS,CAAC,cAAc,CAAC,WAAW,EAAE,cAAc,CAAC,GAAG,CAAC;UACrE,WAAW;AACjB;;;;"}