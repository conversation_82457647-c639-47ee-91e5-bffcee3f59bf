{"version": 3, "file": "init.mjs", "sources": ["../../../../../darwin_arm64-fastbuild-ST-2d99d9656325/bin/packages/localize/init/index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\nimport {\n  ɵ$localize as $localize,\n  ɵLocalizeFn as LocalizeFn,\n  ɵTranslateFn as TranslateFn,\n} from '../index';\n\nexport {$localize, LocalizeFn, TranslateFn};\n\n// Attach $localize to the global context, as a side-effect of this module.\n(globalThis as any).$localize = $localize;\n"], "names": [], "mappings": ";;;;;;;;AAeA;AACC,UAAkB,CAAC,SAAS,GAAG,SAAS;;;;"}