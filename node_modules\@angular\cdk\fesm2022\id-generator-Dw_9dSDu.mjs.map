{"version": 3, "file": "id-generator-Dw_9dSDu.mjs", "sources": ["../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/a11y/id-generator.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {APP_ID, inject, Injectable} from '@angular/core';\n\n/**\n * Keeps track of the ID count per prefix. This helps us make the IDs a bit more deterministic\n * like they were before the service was introduced. Note that ideally we wouldn't have to do\n * this, but there are some internal tests that rely on the IDs.\n */\nconst counters: Record<string, number> = {};\n\n/** Service that generates unique IDs for DOM nodes. */\n@Injectable({providedIn: 'root'})\nexport class _IdGenerator {\n  private _appId = inject(APP_ID);\n\n  /**\n   * Generates a unique ID with a specific prefix.\n   * @param prefix Prefix to add to the ID.\n   */\n  getId(prefix: string): string {\n    // Omit the app ID if it's the default `ng`. Since the vast majority of pages have one\n    // Angular app on them, we can reduce the amount of breakages by not adding it.\n    if (this._appId !== 'ng') {\n      prefix += this._appId;\n    }\n\n    if (!counters.hasOwnProperty(prefix)) {\n      counters[prefix] = 0;\n    }\n\n    return `${prefix}${counters[prefix]++}`;\n  }\n}\n"], "names": [], "mappings": ";;;AAUA;;;;AAIG;AACH,MAAM,QAAQ,GAA2B,EAAE;AAE3C;MAEa,YAAY,CAAA;AACf,IAAA,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;AAE/B;;;AAGG;AACH,IAAA,KAAK,CAAC,MAAc,EAAA;;;AAGlB,QAAA,IAAI,IAAI,CAAC,MAAM,KAAK,IAAI,EAAE;AACxB,YAAA,MAAM,IAAI,IAAI,CAAC,MAAM;;QAGvB,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,MAAM,CAAC,EAAE;AACpC,YAAA,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC;;QAGtB,OAAO,CAAA,EAAG,MAAM,CAAG,EAAA,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAA,CAAE;;uGAlB9B,YAAY,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;AAAZ,IAAA,OAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,YAAY,cADA,MAAM,EAAA,CAAA;;2FAClB,YAAY,EAAA,UAAA,EAAA,CAAA;kBADxB,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC;;;;;"}