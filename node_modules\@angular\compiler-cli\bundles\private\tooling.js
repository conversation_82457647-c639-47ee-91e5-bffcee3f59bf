
      import {createRequire as __cjsCompatRequire} from 'module';
      const require = __cjsCompatRequire(import.meta.url);
    
import {
  GLOBAL_DEFS_FOR_TERSER,
  GLOBAL_DEFS_FOR_TERSER_WITH_AOT,
  constructorParametersDownlevelTransform
} from "../chunk-Y7CS6SFM.js";
import "../chunk-2PYABK44.js";
import "../chunk-BSAOKZSO.js";
import "../chunk-6JLQ22O6.js";
import "../chunk-WUCT7QTW.js";
import "../chunk-ERYCP7NI.js";
import "../chunk-KPQ72R34.js";
export {
  GLOBAL_DEFS_FOR_TERSER,
  GLOBAL_DEFS_FOR_TERSER_WITH_AOT,
  constructorParametersDownlevelTransform
};
//# sourceMappingURL=tooling.js.map
