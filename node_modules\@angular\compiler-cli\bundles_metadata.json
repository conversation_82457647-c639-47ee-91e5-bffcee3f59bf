{"inputs": {"bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/src/invalid_file_system.js": {"bytes": 2065, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/src/util.js": {"bytes": 792, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/src/helpers.js": {"bytes": 2872, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/src/invalid_file_system.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/src/util.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/src/compiler_host.js": {"bytes": 2195, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/src/helpers.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/src/logical.js": {"bytes": 3920, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/src/helpers.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/src/util.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/src/node_js_file_system.js": {"bytes": 3955, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/src/ts_read_directory.js": {"bytes": 2528, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.js": {"bytes": 833, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/src/compiler_host.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/src/helpers.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/src/logical.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/src/node_js_file_system.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/src/util.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/src/ts_read_directory.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/src/invalid_file_system.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/version.mjs": {"bytes": 1414, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/src/error_code.js": {"bytes": 22577, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/src/docs.js": {"bytes": 829, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/src/error_code.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/src/util.js": {"bytes": 951, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/src/error.js": {"bytes": 3183, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/src/error_code.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/src/util.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/src/error_details_base_url.js": {"bytes": 545, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/src/extended_template_diagnostic_name.js": {"bytes": 2027, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.js": {"bytes": 762, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/src/docs.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/src/error.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/src/error_code.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/src/error_details_base_url.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/src/extended_template_diagnostic_name.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/src/util.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/typescript.js": {"bytes": 5594, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/find_export.js": {"bytes": 1100, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/typescript.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/emitter.js": {"bytes": 17251, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/src/util.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/typescript.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/find_export.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/alias.js": {"bytes": 5991, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/emitter.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/path.js": {"bytes": 1665, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/src/util.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/core.js": {"bytes": 2869, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/path.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/patch_alias_reference_resolution.js": {"bytes": 7038, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/default.js": {"bytes": 4388, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/typescript.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/patch_alias_reference_resolution.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/src/host.js": {"bytes": 1715, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/src/type_to_value.js": {"bytes": 11838, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/src/util.js": {"bytes": 1377, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/src/host.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/src/typescript.js": {"bytes": 28993, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/src/host.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/src/type_to_value.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/src/util.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/deferred_symbol_tracker.js": {"bytes": 7915, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/src/typescript.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/imported_symbols_tracker.js": {"bytes": 5129, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/local_compilation_extra_imports_tracker.js": {"bytes": 4770, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/src/typescript.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/index.js": {"bytes": 657, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/src/host.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/src/type_to_value.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/src/typescript.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/src/util.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/references.js": {"bytes": 5974, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/typescript.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/resolver.js": {"bytes": 1172, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/typescript.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.js": {"bytes": 1192, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/alias.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/core.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/default.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/deferred_symbol_tracker.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/emitter.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/imported_symbols_tracker.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/local_compilation_extra_imports_tracker.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/patch_alias_reference_resolution.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/references.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/resolver.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/jit/src/downlevel_decorators_transform.js": {"bytes": 25723, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/api.js": {"bytes": 245, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/util.js": {"bytes": 14979, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/default.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/di.js": {"bytes": 8112, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/util.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/api.js": {"bytes": 922, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/property_mapping.js": {"bytes": 5670, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/util.js": {"bytes": 9450, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/typescript.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/src/typescript.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/dts.js": {"bytes": 13919, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/typescript.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/api.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/property_mapping.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/util.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/inheritance.js": {"bytes": 3015, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/property_mapping.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/registry.js": {"bytes": 2292, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/api.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/resource_registry.js": {"bytes": 2983, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/providers.js": {"bytes": 3437, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/host_directives_resolver.js": {"bytes": 3871, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/api.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/property_mapping.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/inheritance.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/util.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/index.js": {"bytes": 887, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/api.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/dts.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/inheritance.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/registry.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/resource_registry.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/util.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/property_mapping.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/providers.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/host_directives_resolver.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/src/dynamic.js": {"bytes": 4160, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/src/result.js": {"bytes": 1456, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/src/diagnostics.js": {"bytes": 6791, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/src/dynamic.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/src/result.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/src/builtin.js": {"bytes": 2011, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/src/dynamic.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/src/result.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/src/synthetic.js": {"bytes": 574, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/src/interpreter.js": {"bytes": 30318, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/typescript.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/src/builtin.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/src/dynamic.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/src/result.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/src/synthetic.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/src/interface.js": {"bytes": 991, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/src/interpreter.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/index.js": {"bytes": 568, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/src/diagnostics.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/src/dynamic.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/src/interface.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/src/interpreter.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/src/result.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/src/synthetic.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/src/api.js": {"bytes": 1827, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/src/alias.js": {"bytes": 1187, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/perf/src/api.js": {"bytes": 11777, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/perf/src/noop.js": {"bytes": 519, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/perf/src/api.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/perf/src/clock.js": {"bytes": 468, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/perf/src/recorder.js": {"bytes": 4241, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/perf/src/api.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/perf/src/clock.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/perf/index.js": {"bytes": 389, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/perf/src/api.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/perf/src/noop.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/perf/src/recorder.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/src/trait.js": {"bytes": 3601, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/src/compilation.js": {"bytes": 27123, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/perf/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/typescript.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/src/api.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/src/trait.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/context.js": {"bytes": 727, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/import_manager/check_unique_identifier_name.js": {"bytes": 1513, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/import_manager/import_typescript_transform.js": {"bytes": 4679, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/import_manager/reuse_generated_imports.js": {"bytes": 1869, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/import_manager/reuse_source_file_imports.js": {"bytes": 5131, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/import_manager/import_manager.js": {"bytes": 16945, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/import_manager/check_unique_identifier_name.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/import_manager/import_typescript_transform.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/import_manager/reuse_generated_imports.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/import_manager/reuse_source_file_imports.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/translator.js": {"bytes": 16465, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/type_emitter.js": {"bytes": 6701, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/ts_util.js": {"bytes": 818, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/type_translator.js": {"bytes": 10975, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/context.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/ts_util.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/type_emitter.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/typescript_ast_factory.js": {"bytes": 10868, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/ts_util.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/typescript_translator.js": {"bytes": 973, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/context.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/translator.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/typescript_ast_factory.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/index.js": {"bytes": 787, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/context.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/import_manager/import_manager.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/translator.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/type_emitter.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/type_translator.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/typescript_ast_factory.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/typescript_translator.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/src/declaration.js": {"bytes": 7552, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/index.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/visitor.js": {"bytes": 3731, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/src/transform.js": {"bytes": 17796, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/default.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/perf/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/visitor.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/index.js": {"bytes": 588, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/src/api.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/src/alias.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/src/compilation.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/src/declaration.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/src/trait.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/src/transform.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/diagnostics.js": {"bytes": 16540, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/typescript.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/util.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/index.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/evaluation.js": {"bytes": 3397, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/diagnostics.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/util.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/factory.js": {"bytes": 890, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/injectable_registry.js": {"bytes": 1357, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/di.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/metadata.js": {"bytes": 7889, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/util.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/debug_info.js": {"bytes": 1015, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/path.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/references_registry.js": {"bytes": 364, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/schema.js": {"bytes": 1907, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/diagnostics.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/input_transforms.js": {"bytes": 1069, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/jit_declaration_registry.js": {"bytes": 487, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/index.js": {"bytes": 687, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/api.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/di.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/diagnostics.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/evaluation.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/factory.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/injectable_registry.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/metadata.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/debug_info.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/references_registry.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/schema.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/util.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/input_transforms.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/jit_declaration_registry.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/semantic_graph/src/api.js": {"bytes": 1927, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/semantic_graph/src/graph.js": {"bytes": 10121, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/semantic_graph/src/api.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/semantic_graph/src/util.js": {"bytes": 2197, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/semantic_graph/src/type_parameters.js": {"bytes": 1655, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/semantic_graph/src/util.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/semantic_graph/index.js": {"bytes": 537, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/semantic_graph/src/api.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/semantic_graph/src/graph.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/semantic_graph/src/type_parameters.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/semantic_graph/src/util.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/src/api.js": {"bytes": 501, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/src/component_scope.js": {"bytes": 952, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/src/dependency.js": {"bytes": 5646, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/src/util.js": {"bytes": 3219, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/src/api.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/src/local.js": {"bytes": 30647, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/typescript.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/src/api.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/src/util.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/src/typecheck.js": {"bytes": 4629, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/src/api.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/index.js": {"bytes": 632, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/src/api.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/src/component_scope.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/src/dependency.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/src/local.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/src/typecheck.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/src/util.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/initializer_function_access.js": {"bytes": 1247, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/src/util.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/initializer_functions.js": {"bytes": 4581, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/input_output_parse_options.js": {"bytes": 1387, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/index.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/input_function.js": {"bytes": 2386, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/initializer_function_access.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/initializer_functions.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/input_output_parse_options.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/model_function.js": {"bytes": 2359, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/initializer_function_access.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/initializer_functions.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/input_output_parse_options.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/output_function.js": {"bytes": 2862, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/initializer_function_access.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/initializer_functions.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/input_output_parse_options.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/query_functions.js": {"bytes": 5651, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/initializer_function_access.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/initializer_functions.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/shared.js": {"bytes": 54626, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/input_function.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/model_function.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/output_function.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/query_functions.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/symbol.js": {"bytes": 5583, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/semantic_graph/index.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/handler.js": {"bytes": 13314, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/semantic_graph/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/perf/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/shared.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/symbol.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/index.js": {"bytes": 572, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/handler.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/symbol.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/shared.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/input_function.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/output_function.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/query_functions.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/model_function.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/initializer_functions.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/ng_module/src/module_with_providers.js": {"bytes": 5372, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/index.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/ng_module/src/handler.js": {"bytes": 41621, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/semantic_graph/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/perf/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/src/util.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/typescript.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/ng_module/src/module_with_providers.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/ng_module/index.js": {"bytes": 423, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/ng_module/src/handler.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/ng_module/src/module_with_providers.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/component/src/diagnostics.js": {"bytes": 1808, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/component/src/resources.js": {"bytes": 19813, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/index.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/component/src/symbol.js": {"bytes": 3968, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/semantic_graph/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/index.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/component/src/util.js": {"bytes": 6335, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/ng_module/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/index.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/program_driver/src/api.js": {"bytes": 876, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/shims/src/expando.js": {"bytes": 3171, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/shims/src/util.js": {"bytes": 848, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/shims/src/adapter.js": {"bytes": 8756, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/typescript.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/shims/src/expando.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/shims/src/util.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/shims/src/reference_tagger.js": {"bytes": 2476, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/typescript.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/shims/src/expando.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/shims/src/util.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/shims/index.js": {"bytes": 510, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/shims/src/adapter.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/shims/src/expando.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/shims/src/reference_tagger.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/program_driver/src/ts_create_program_driver.js": {"bytes": 9710, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/shims/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/typescript.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/program_driver/src/api.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/program_driver/index.js": {"bytes": 335, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/program_driver/src/api.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/program_driver/src/ts_create_program_driver.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/api.js": {"bytes": 245, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/checker.js": {"bytes": 1347, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/completion.js": {"bytes": 621, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/context.js": {"bytes": 249, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/scope.js": {"bytes": 1241, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/symbols.js": {"bytes": 1011, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/index.js": {"bytes": 395, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/api.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/checker.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/completion.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/context.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/scope.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/symbols.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/diagnostics/src/diagnostic.js": {"bytes": 6349, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/diagnostics/src/id.js": {"bytes": 649, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/diagnostics/index.js": {"bytes": 296, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/diagnostics/src/diagnostic.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/diagnostics/src/id.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/comments.js": {"bytes": 6457, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/completion.js": {"bytes": 8403, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/comments.js", "kind": "import-statement"}]}, "node_modules/magic-string/dist/magic-string.es.mjs": {"bytes": 38222, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/dom.js": {"bytes": 4054, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/diagnostics/index.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/reference_emit_environment.js": {"bytes": 3448, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/index.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/ts_util.js": {"bytes": 6206, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/comments.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/type_parameter_emitter.js": {"bytes": 4367, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/index.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/tcb_util.js": {"bytes": 7604, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/typescript.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/comments.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/type_parameter_emitter.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/type_constructor.js": {"bytes": 10639, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/tcb_util.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/ts_util.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/environment.js": {"bytes": 5239, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/reference_emit_environment.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/ts_util.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/type_constructor.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/type_parameter_emitter.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/oob.js": {"bytes": 15210, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/diagnostics/index.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/shim.js": {"bytes": 1671, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/diagnostics.js": {"bytes": 4278, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/diagnostics/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/tcb_util.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/expression.js": {"bytes": 21620, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/diagnostics.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/ts_util.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/type_check_block.js": {"bytes": 122305, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/comments.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/diagnostics.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/expression.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/ts_util.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/type_constructor.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/type_parameter_emitter.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/type_check_file.js": {"bytes": 4218, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/environment.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/tcb_util.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/type_check_block.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/context.js": {"bytes": 17989, "imports": [{"path": "node_modules/magic-string/dist/magic-string.es.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/perf/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/diagnostics/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/dom.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/environment.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/oob.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/reference_emit_environment.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/shim.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/tcb_util.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/type_check_block.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/type_check_file.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/type_constructor.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/line_mappings.js": {"bytes": 2147, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/source.js": {"bytes": 2681, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/diagnostics/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/line_mappings.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/template_symbol_builder.js": {"bytes": 31413, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/typescript.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/comments.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/ts_util.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/checker.js": {"bytes": 35951, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/perf/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/program_driver/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/shims/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/typescript.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/diagnostics/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/completion.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/context.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/diagnostics.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/shim.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/source.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/tcb_util.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/template_symbol_builder.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/index.js": {"bytes": 483, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/checker.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/context.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/shim.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/type_check_file.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/hmr/src/extract_dependencies.js": {"bytes": 14559, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/index.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/hmr/src/metadata.js": {"bytes": 1937, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/path.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/hmr/src/extract_dependencies.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/hmr/src/update_declaration.js": {"bytes": 2309, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/index.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/hmr/index.js": {"bytes": 310, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/hmr/src/metadata.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/hmr/src/update_declaration.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/component/src/handler.js": {"bytes": 84540, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/semantic_graph/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/perf/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/src/util.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/typescript.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/ng_module/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/component/src/diagnostics.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/component/src/resources.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/component/src/symbol.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/component/src/util.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/hmr/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/path.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/component/index.js": {"bytes": 295, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/component/src/handler.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/src/injectable.js": {"bytes": 13824, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/perf/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/index.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/src/pipe.js": {"bytes": 8648, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/semantic_graph/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/perf/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/index.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/index.js": {"bytes": 978, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/component/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/ng_module/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/src/injectable.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/src/pipe.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/jit/src/initializer_api_transforms/transform_api.js": {"bytes": 1568, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/jit/src/initializer_api_transforms/input_function.js": {"bytes": 2928, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/jit/src/initializer_api_transforms/transform_api.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/jit/src/initializer_api_transforms/model_function.js": {"bytes": 2755, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/jit/src/initializer_api_transforms/transform_api.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/jit/src/initializer_api_transforms/output_function.js": {"bytes": 1931, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/jit/src/initializer_api_transforms/transform_api.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/jit/src/initializer_api_transforms/query_functions.js": {"bytes": 3095, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/jit/src/initializer_api_transforms/transform_api.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/jit/src/initializer_api_transforms/transform.js": {"bytes": 4288, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/src/typescript.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/jit/src/initializer_api_transforms/input_function.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/jit/src/initializer_api_transforms/model_function.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/jit/src/initializer_api_transforms/output_function.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/jit/src/initializer_api_transforms/query_functions.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/jit/src/index.js": {"bytes": 2538, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/jit/src/downlevel_decorators_transform.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/jit/src/initializer_api_transforms/transform.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/jit/src/downlevel_decorators_transform.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/jit/src/initializer_api_transforms/transform.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/jit/index.js": {"bytes": 363, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/jit/src/index.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/transformers/api.mjs": {"bytes": 15078, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/transformers/compiler_host.mjs": {"bytes": 2422, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/transformers/i18n.mjs": {"bytes": 7576, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/version_helpers.mjs": {"bytes": 10492, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/typescript_support.mjs": {"bytes": 6635, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/version_helpers.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/cycles/src/analyzer.js": {"bytes": 4964, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/cycles/src/imports.js": {"bytes": 5593, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/perf/index.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/cycles/index.js": {"bytes": 336, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/cycles/src/analyzer.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/cycles/src/imports.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/entities.js": {"bytes": 2030, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/filters.js": {"bytes": 450, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/generics_extractor.js": {"bytes": 589, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/jsdoc_extractor.js": {"bytes": 3328, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/type_extractor.js": {"bytes": 510, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/function_extractor.js": {"bytes": 5832, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/entities.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/generics_extractor.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/jsdoc_extractor.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/type_extractor.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/internal.js": {"bytes": 1029, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/jsdoc_extractor.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/class_extractor.js": {"bytes": 19153, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/entities.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/filters.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/function_extractor.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/generics_extractor.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/internal.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/jsdoc_extractor.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/type_extractor.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/constant_extractor.js": {"bytes": 4327, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/entities.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/jsdoc_extractor.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/decorator_extractor.js": {"bytes": 5602, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/class_extractor.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/entities.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/jsdoc_extractor.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/enum_extractor.js": {"bytes": 1983, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/entities.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/jsdoc_extractor.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/type_extractor.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/initializer_api_function_extractor.js": {"bytes": 6885, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/entities.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/function_extractor.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/generics_extractor.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/jsdoc_extractor.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/type_alias_extractor.js": {"bytes": 911, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/entities.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/jsdoc_extractor.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/generics_extractor.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/import_extractor.js": {"bytes": 1486, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/extractor.js": {"bytes": 8127, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/class_extractor.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/constant_extractor.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/decorator_extractor.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/enum_extractor.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/filters.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/function_extractor.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/initializer_api_function_extractor.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/type_alias_extractor.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/import_extractor.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/index.js": {"bytes": 317, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/entities.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/extractor.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/entry_point/src/generator.js": {"bytes": 1275, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/path.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/entry_point/src/logic.js": {"bytes": 1528, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/typescript.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/entry_point/src/private_export_checker.js": {"bytes": 6051, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/entry_point/src/reference_graph.js": {"bytes": 2626, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/entry_point/index.js": {"bytes": 472, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/entry_point/src/generator.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/entry_point/src/logic.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/entry_point/src/private_export_checker.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/entry_point/src/reference_graph.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/src/dependency_tracking.js": {"bytes": 4249, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/src/state.js": {"bytes": 637, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/src/incremental.js": {"bytes": 14697, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/perf/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/program_driver/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/typescript.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/semantic_graph/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/src/dependency_tracking.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/src/state.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/src/noop.js": {"bytes": 399, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/src/strategy.js": {"bytes": 2524, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/index.js": {"bytes": 434, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/src/incremental.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/src/noop.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/src/state.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/src/strategy.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/indexer/src/api.js": {"bytes": 1156, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/indexer/src/context.js": {"bytes": 646, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/indexer/src/template.js": {"bytes": 16181, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/indexer/src/api.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/indexer/src/transform.js": {"bytes": 1938, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/indexer/src/template.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/indexer/index.js": {"bytes": 364, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/indexer/src/api.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/indexer/src/context.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/indexer/src/transform.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/ng_module_index.js": {"bytes": 4393, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/api.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/resource/src/loader.js": {"bytes": 10770, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/resource/index.js": {"bytes": 290, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/resource/src/loader.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/src/standalone.js": {"bytes": 5236, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/src/api.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/symbol_util.js": {"bytes": 1608, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/index.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/api/api.js": {"bytes": 5753, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/api/extended_template_checker.js": {"bytes": 267, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/api/index.js": {"bytes": 304, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/api/api.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/api/extended_template_checker.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/checks/interpolated_signal_not_invoked/index.js": {"bytes": 4937, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/symbol_util.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/api/index.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/checks/invalid_banana_in_box/index.js": {"bytes": 1564, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/api/index.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/checks/missing_control_flow_directive/index.js": {"bytes": 3655, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/api/index.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/checks/missing_ngforof_let/index.js": {"bytes": 1547, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/api/index.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/checks/nullish_coalescing_not_nullable/index.js": {"bytes": 2961, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/api/index.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/checks/optional_chain_not_nullable/index.js": {"bytes": 3202, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/api/index.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/checks/suffix_not_supported/index.js": {"bytes": 1444, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/api/index.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/checks/text_attribute_not_binding/index.js": {"bytes": 2503, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/api/index.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/checks/uninvoked_function_in_event_binding/index.js": {"bytes": 3677, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/api/index.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/checks/unused_let_declaration/index.js": {"bytes": 2205, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/api/index.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/checks/skip_hydration_not_static/index.js": {"bytes": 2007, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/api/index.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/api/src/adapter.js": {"bytes": 249, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/api/src/interfaces.js": {"bytes": 252, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/api/src/options.js": {"bytes": 249, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/api/src/public_options.js": {"bytes": 887, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/api/index.js": {"bytes": 370, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/api/src/adapter.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/api/src/interfaces.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/api/src/options.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/api/src/public_options.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/src/extended_template_checker.js": {"bytes": 3391, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/api/index.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/index.js": {"bytes": 2145, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/checks/interpolated_signal_not_invoked/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/checks/invalid_banana_in_box/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/checks/missing_control_flow_directive/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/checks/missing_ngforof_let/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/checks/nullish_coalescing_not_nullable/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/checks/optional_chain_not_nullable/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/checks/suffix_not_supported/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/checks/text_attribute_not_binding/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/checks/uninvoked_function_in_event_binding/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/checks/unused_let_declaration/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/checks/skip_hydration_not_static/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/src/extended_template_checker.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/template_semantics/src/template_semantics_checker.js": {"bytes": 5363, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/symbol_util.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/validation/src/rules/initializer_api_usage_rule.js": {"bytes": 3623, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/validation/src/rules/unused_standalone_imports_rule.js": {"bytes": 6085, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/validation/src/config.js": {"bytes": 432, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/validation/src/source_file_validator.js": {"bytes": 2338, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/validation/src/rules/initializer_api_usage_rule.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/validation/src/rules/unused_standalone_imports_rule.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/validation/src/config.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/validation/index.js": {"bytes": 303, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/validation/src/source_file_validator.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/src/core_version.js": {"bytes": 975, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/src/feature_detection.js": {"bytes": 1102, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/src/compiler.js": {"bytes": 65680, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/cycles/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/entry_point/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/indexer/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/ng_module_index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/perf/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/resource/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/src/standalone.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/template_semantics/src/template_semantics_checker.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/typescript.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/validation/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/api/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/src/core_version.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/src/feature_detection.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/jit/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/shims/index.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/src/host.js": {"bytes": 12529, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/entry_point/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/shims/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/path.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/typescript.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/index.js": {"bytes": 313, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/src/compiler.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/src/host.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/program.mjs": {"bytes": 46938, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/transformers/api.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/transformers/i18n.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/typescript_support.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/perf/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/program_driver/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/shims/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/index.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/transformers/program.mjs": {"bytes": 1826, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/program.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/transformers/entry_points.mjs": {"bytes": 1146, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/transformers/compiler_host.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/transformers/program.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/transformers/util.mjs": {"bytes": 3339, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/transformers/api.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/perform_compile.mjs": {"bytes": 38248, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/transformers/api.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/transformers/entry_points.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/transformers/util.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/private/tooling.mjs": {"bytes": 3479, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/jit/index.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/logging/src/logger.js": {"bytes": 498, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/logging/src/console_logger.js": {"bytes": 1225, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/logging/src/logger.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/logging/index.js": {"bytes": 331, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/logging/src/console_logger.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/logging/src/logger.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/tsc_plugin.mjs": {"bytes": 17569, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/perf/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/program_driver/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/index.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/index.mjs": {"bytes": 5578, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/version.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/jit/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/transformers/api.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/transformers/entry_points.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/perform_compile.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/private/tooling.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/logging/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/tsc_plugin.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/program.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/logging/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/perform_watch.mjs": {"bytes": 38786, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/perform_compile.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/transformers/api.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/transformers/entry_points.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/transformers/util.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/main.mjs": {"bytes": 23553, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/perform_compile.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/perform_watch.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/transformers/api.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/bin/ngc.mjs": {"bytes": 3022, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/main.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/ngcc/index.mjs": {"bytes": 6535, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/extract_i18n.mjs": {"bytes": 5030, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/main.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/transformers/api.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/bin/ng_xi18n.mjs": {"bytes": 2374, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/extract_i18n.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/fatal_linker_error.mjs": {"bytes": 2550, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/ast/utils.mjs": {"bytes": 1982, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/fatal_linker_error.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/ast/ast_value.mjs": {"bytes": 32574, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/fatal_linker_error.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/linker_import_generator.mjs": {"bytes": 4201, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/fatal_linker_error.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/emit_scopes/emit_scope.mjs": {"bytes": 10122, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/linker_import_generator.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/emit_scopes/local_emit_scope.mjs": {"bytes": 4603, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/emit_scopes/emit_scope.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/get_source_file.mjs": {"bytes": 3448, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/partial_class_metadata_async_linker_1.mjs": {"bytes": 6109, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/fatal_linker_error.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/partial_class_metadata_linker_1.mjs": {"bytes": 4378, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/util.mjs": {"bytes": 13317, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/fatal_linker_error.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/partial_directive_linker_1.mjs": {"bytes": 31815, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/fatal_linker_error.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/util.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/partial_component_linker_1.mjs": {"bytes": 47450, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/fatal_linker_error.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/partial_directive_linker_1.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/util.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/partial_factory_linker_1.mjs": {"bytes": 6797, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/fatal_linker_error.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/util.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/partial_injectable_linker_1.mjs": {"bytes": 8182, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/fatal_linker_error.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/util.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/partial_injector_linker_1.mjs": {"bytes": 5331, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/fatal_linker_error.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/util.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/partial_ng_module_linker_1.mjs": {"bytes": 14732, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/util.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/partial_pipe_linker_1.mjs": {"bytes": 6045, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/fatal_linker_error.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/util.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/partial_linker_selector.mjs": {"bytes": 27686, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/get_source_file.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/partial_class_metadata_async_linker_1.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/partial_class_metadata_linker_1.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/partial_component_linker_1.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/partial_directive_linker_1.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/partial_factory_linker_1.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/partial_injectable_linker_1.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/partial_injector_linker_1.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/partial_ng_module_linker_1.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/partial_pipe_linker_1.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/util.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/file_linker.mjs": {"bytes": 13450, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/ast/ast_value.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/emit_scopes/emit_scope.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/emit_scopes/local_emit_scope.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/partial_linker_selector.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/sourcemaps/src/content_origin.js": {"bytes": 1385, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/sourcemaps/src/segment_marker.js": {"bytes": 1394, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/sourcemaps/src/source_file.js": {"bytes": 19198, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/sourcemaps/src/segment_marker.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/sourcemaps/src/source_file_loader.js": {"bytes": 9979, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/sourcemaps/src/content_origin.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/sourcemaps/src/source_file.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/sourcemaps/index.js": {"bytes": 399, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/sourcemaps/src/content_origin.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/sourcemaps/src/source_file.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/sourcemaps/src/source_file_loader.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/linker_options.mjs": {"bytes": 2996, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/translator.mjs": {"bytes": 4145, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/index.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/linker_environment.mjs": {"bytes": 5499, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/sourcemaps/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/linker_options.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/translator.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/needs_linking.mjs": {"bytes": 3470, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/partial_linker_selector.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/index.mjs": {"bytes": 2018, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/ast/utils.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/fatal_linker_error.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/file_linker.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/linker_environment.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/linker_options.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/needs_linking.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/babel/src/ast/babel_ast_factory.mjs": {"bytes": 24004, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/index.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/babel/src/ast/babel_ast_host.mjs": {"bytes": 22941, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/index.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/babel/src/babel_declaration_scope.mjs": {"bytes": 7922, "imports": []}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/babel/src/es2015_linker_plugin.mjs": {"bytes": 21003, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/babel/src/ast/babel_ast_factory.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/babel/src/ast/babel_ast_host.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/babel/src/babel_declaration_scope.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/babel/src/babel_plugin.mjs": {"bytes": 4393, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/logging/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/babel/src/es2015_linker_plugin.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/babel/index.mjs": {"bytes": 1318, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/babel/src/babel_plugin.mjs", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/babel/src/es2015_linker_plugin.mjs", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/private/bazel.mjs": {"bytes": 1230, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/perf/index.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/private/localize.mjs": {"bytes": 1534, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/logging/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/sourcemaps/index.js", "kind": "import-statement"}]}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/private/migrations.mjs": {"bytes": 2786, "imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/index.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/index.js", "kind": "import-statement"}]}}, "outputs": {"bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/private/migrations.js.map": {"imports": [], "exports": [], "inputs": {}, "bytes": 69}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/private/migrations.js": {"imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-BSAOKZSO.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-6JLQ22O6.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-WUCT7QTW.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-ERYCP7NI.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-KPQ72R34.js", "kind": "import-statement"}], "exports": ["DynamicValue", "ImportManager", "PartialEvaluator", "PotentialImportKind", "PotentialImportMode", "Reference", "StaticInterpreter", "TypeScriptReflectionHost", "createForwardRefResolver", "reflectObjectLiteral"], "entryPoint": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/private/migrations.mjs", "inputs": {"bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/private/migrations.mjs": {"bytesInOutput": 0}}, "bytes": 981}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/private/tooling.js.map": {"imports": [], "exports": [], "inputs": {}, "bytes": 69}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/private/tooling.js": {"imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-Y7CS6SFM.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-2PYABK44.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-BSAOKZSO.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-6JLQ22O6.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-WUCT7QTW.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-ERYCP7NI.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-KPQ72R34.js", "kind": "import-statement"}], "exports": ["GLOBAL_DEFS_FOR_TERSER", "GLOBAL_DEFS_FOR_TERSER_WITH_AOT", "constructorParametersDownlevelTransform"], "entryPoint": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/private/tooling.mjs", "inputs": {}, "bytes": 611}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/index.js.map": {"imports": [], "exports": [], "inputs": {}, "bytes": 1867}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/index.js": {"imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-Y7CS6SFM.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-LCO4W4EJ.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-2PYABK44.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-BSAOKZSO.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-6JLQ22O6.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-WUCT7QTW.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-H5Y7P5GQ.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-ERYCP7NI.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-KPQ72R34.js", "kind": "import-statement"}], "exports": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DEFAULT_ERROR_CODE", "DecoratorType", "DocsExtractor", "EmitFlags", "EntryType", "ErrorCode", "GLOBAL_DEFS_FOR_TERSER", "GLOBAL_DEFS_FOR_TERSER_WITH_AOT", "InvalidFileSystem", "LogLevel", "LogicalFileSystem", "LogicalProjectPath", "MemberTags", "MemberType", "NgTscPlugin", "NgtscCompilerHost", "NgtscProgram", "NodeJSFileSystem", "OptimizeFor", "SOURCE", "UNKNOWN_ERROR_CODE", "VERSION", "absoluteFrom", "absoluteFromSourceFile", "angularJitApplicationTransform", "basename", "calcProjectFileAndBasePath", "constructorParametersDownlevelTransform", "createCompilerHost", "createFileSystemTsReadDirectoryFn", "createProgram", "defaultGatherDiagnostics", "dirname", "exitCodeFromResult", "formatDiagnostics", "getDownlevelDecoratorsTransform", "getFileSystem", "getInitializerApiJitTransform", "getSourceFileOrError", "isDocEntryWithSourceInfo", "isLocalCompilationDiagnostics", "isLocalRelativePath", "isRoot", "isRooted", "isTsDiagnostic", "join", "ngErrorCode", "performCompilation", "readConfiguration", "relative", "relativeFrom", "resolve", "setFileSystem", "toRelativeImport"], "entryPoint": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/index.mjs", "inputs": {"bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/version.mjs": {"bytesInOutput": 93}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/index.mjs": {"bytesInOutput": 39}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/tsc_plugin.mjs": {"bytesInOutput": 2645}}, "bytes": 6096}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-Y7CS6SFM.js.map": {"imports": [], "exports": [], "inputs": {}, "bytes": 292}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-Y7CS6SFM.js": {"imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-2PYABK44.js", "kind": "import-statement"}], "exports": ["GLOBAL_DEFS_FOR_TERSER", "GLOBAL_DEFS_FOR_TERSER_WITH_AOT", "constructorParametersDownlevelTransform"], "inputs": {"bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/private/tooling.mjs": {"bytesInOutput": 310}}, "bytes": 958}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/src/bin/ngc.js.map": {"imports": [], "exports": [], "inputs": {}, "bytes": 392}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/src/bin/ngc.js": {"imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-YSO53PBA.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-LCO4W4EJ.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-2PYABK44.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-BSAOKZSO.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-6JLQ22O6.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-WUCT7QTW.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-ERYCP7NI.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-KPQ72R34.js", "kind": "import-statement"}], "exports": [], "entryPoint": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/bin/ngc.mjs", "inputs": {"bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/bin/ngc.mjs": {"bytesInOutput": 337}}, "bytes": 1135}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/ngcc/index.js.map": {"imports": [], "exports": [], "inputs": {}, "bytes": 693}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/ngcc/index.js": {"imports": [], "exports": [], "entryPoint": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/ngcc/index.mjs", "inputs": {"bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/ngcc/index.mjs": {"bytesInOutput": 1397}}, "bytes": 1863}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/src/bin/ng_xi18n.js.map": {"imports": [], "exports": [], "inputs": {}, "bytes": 891}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/src/bin/ng_xi18n.js": {"imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-YSO53PBA.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-LCO4W4EJ.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-2PYABK44.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-BSAOKZSO.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-6JLQ22O6.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-WUCT7QTW.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-ERYCP7NI.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-KPQ72R34.js", "kind": "import-statement"}], "exports": [], "entryPoint": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/bin/ng_xi18n.mjs", "inputs": {"bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/bin/ng_xi18n.mjs": {"bytesInOutput": 197}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/extract_i18n.mjs": {"bytesInOutput": 859}}, "bytes": 2088}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-YSO53PBA.js.map": {"imports": [], "exports": [], "inputs": {}, "bytes": 6917}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-YSO53PBA.js": {"imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-LCO4W4EJ.js", "kind": "import-statement"}], "exports": ["main", "readCommandLineAndConfiguration"], "inputs": {"bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/main.mjs": {"bytesInOutput": 4144}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/perform_watch.mjs": {"bytesInOutput": 8117}}, "bytes": 13148}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-LCO4W4EJ.js.map": {"imports": [], "exports": [], "inputs": {}, "bytes": 110093}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-LCO4W4EJ.js": {"imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-2PYABK44.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-BSAOKZSO.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-6JLQ22O6.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-WUCT7QTW.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-ERYCP7NI.js", "kind": "import-statement"}], "exports": ["DEFAULT_ERROR_CODE", "DecoratorType", "DocsExtractor", "EmitFlags", "EntryType", "MemberTags", "MemberType", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgCompilerHost", "NgtscProgram", "PatchedProgramIncrementalBuildStrategy", "SOURCE", "UNKNOWN_ERROR_CODE", "calcProjectFileAndBasePath", "createCompilerHost", "createMessageDiagnostic", "createProgram", "defaultGatherDiagnostics", "exitCodeFromResult", "formatDiagnostics", "freshCompilationTicket", "incrementalFromStateTicket", "isDocEntryWithSourceInfo", "isTsDiagnostic", "performCompilation", "readConfiguration"], "inputs": {"bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/transformers/api.mjs": {"bytesInOutput": 618}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/transformers/compiler_host.mjs": {"bytesInOutput": 242}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/entities.js": {"bytesInOutput": 1593}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/extractor.js": {"bytesInOutput": 3839}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/class_extractor.js": {"bytesInOutput": 12800}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/filters.js": {"bytesInOutput": 158}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/function_extractor.js": {"bytesInOutput": 4616}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/generics_extractor.js": {"bytesInOutput": 404}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/jsdoc_extractor.js": {"bytesInOutput": 1833}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/type_extractor.js": {"bytesInOutput": 190}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/internal.js": {"bytesInOutput": 575}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/constant_extractor.js": {"bytesInOutput": 2409}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/decorator_extractor.js": {"bytesInOutput": 3205}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/enum_extractor.js": {"bytesInOutput": 1125}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/initializer_api_function_extractor.js": {"bytesInOutput": 4140}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/type_alias_extractor.js": {"bytesInOutput": 361}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/import_extractor.js": {"bytesInOutput": 965}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/index.js": {"bytesInOutput": 0}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/program.mjs": {"bytesInOutput": 9126}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/transformers/i18n.mjs": {"bytesInOutput": 1466}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/typescript_support.mjs": {"bytesInOutput": 520}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/version_helpers.mjs": {"bytesInOutput": 929}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/src/compiler.js": {"bytesInOutput": 41944}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/cycles/src/analyzer.js": {"bytesInOutput": 1795}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/cycles/index.js": {"bytesInOutput": 0}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/cycles/src/imports.js": {"bytesInOutput": 2655}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/entry_point/src/generator.js": {"bytesInOutput": 832}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/entry_point/index.js": {"bytesInOutput": 0}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/entry_point/src/logic.js": {"bytesInOutput": 495}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/entry_point/src/private_export_checker.js": {"bytesInOutput": 2939}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/entry_point/src/reference_graph.js": {"bytesInOutput": 1381}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/src/dependency_tracking.js": {"bytesInOutput": 1945}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/src/state.js": {"bytesInOutput": 337}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/src/incremental.js": {"bytesInOutput": 7400}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/index.js": {"bytesInOutput": 0}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/src/noop.js": {"bytesInOutput": 0}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/src/strategy.js": {"bytesInOutput": 788}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/indexer/src/api.js": {"bytesInOutput": 752}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/indexer/index.js": {"bytesInOutput": 0}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/indexer/src/context.js": {"bytesInOutput": 133}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/indexer/src/transform.js": {"bytesInOutput": 1129}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/indexer/src/template.js": {"bytesInOutput": 9973}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/ng_module_index.js": {"bytesInOutput": 3136}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/resource/src/loader.js": {"bytesInOutput": 5812}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/resource/index.js": {"bytesInOutput": 0}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/src/standalone.js": {"bytesInOutput": 3648}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/checks/interpolated_signal_not_invoked/index.js": {"bytesInOutput": 2928}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/symbol_util.js": {"bytesInOutput": 926}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/api/api.js": {"bytesInOutput": 3858}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/api/index.js": {"bytesInOutput": 0}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/api/extended_template_checker.js": {"bytesInOutput": 0}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/checks/invalid_banana_in_box/index.js": {"bytesInOutput": 964}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/checks/missing_control_flow_directive/index.js": {"bytesInOutput": 2264}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/checks/missing_ngforof_let/index.js": {"bytesInOutput": 967}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/checks/nullish_coalescing_not_nullable/index.js": {"bytesInOutput": 1719}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/checks/optional_chain_not_nullable/index.js": {"bytesInOutput": 1911}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/checks/suffix_not_supported/index.js": {"bytesInOutput": 869}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/checks/text_attribute_not_binding/index.js": {"bytesInOutput": 1401}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/checks/uninvoked_function_in_event_binding/index.js": {"bytesInOutput": 2435}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/checks/unused_let_declaration/index.js": {"bytesInOutput": 1674}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/checks/skip_hydration_not_static/index.js": {"bytesInOutput": 1293}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/src/extended_template_checker.js": {"bytesInOutput": 2079}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/api/src/adapter.js": {"bytesInOutput": 0}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/api/index.js": {"bytesInOutput": 0}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/api/src/interfaces.js": {"bytesInOutput": 0}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/api/src/options.js": {"bytesInOutput": 0}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/api/src/public_options.js": {"bytesInOutput": 281}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/index.js": {"bytesInOutput": 433}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/template_semantics/src/template_semantics_checker.js": {"bytesInOutput": 4458}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/validation/src/rules/initializer_api_usage_rule.js": {"bytesInOutput": 2460}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/validation/src/rules/unused_standalone_imports_rule.js": {"bytesInOutput": 3930}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/validation/src/config.js": {"bytesInOutput": 51}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/validation/src/source_file_validator.js": {"bytesInOutput": 1386}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/validation/index.js": {"bytesInOutput": 0}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/src/core_version.js": {"bytesInOutput": 551}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/src/feature_detection.js": {"bytesInOutput": 246}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/index.js": {"bytesInOutput": 0}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/src/host.js": {"bytesInOutput": 7110}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/transformers/program.mjs": {"bytesInOutput": 134}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/transformers/entry_points.mjs": {"bytesInOutput": 0}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/perform_compile.mjs": {"bytesInOutput": 7647}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/transformers/util.mjs": {"bytesInOutput": 267}}, "bytes": 204006}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-2PYABK44.js.map": {"imports": [], "exports": [], "inputs": {}, "bytes": 13525}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-2PYABK44.js": {"imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-BSAOKZSO.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-6JLQ22O6.js", "kind": "import-statement"}], "exports": ["angularJitApplicationTransform", "getDownlevelDecoratorsTransform", "getInitializerApiJitTransform"], "inputs": {"bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/jit/src/downlevel_decorators_transform.js": {"bytesInOutput": 13586}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/jit/src/initializer_api_transforms/transform.js": {"bytesInOutput": 2270}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/jit/src/initializer_api_transforms/transform_api.js": {"bytesInOutput": 737}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/jit/src/initializer_api_transforms/input_function.js": {"bytesInOutput": 1281}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/jit/src/initializer_api_transforms/model_function.js": {"bytesInOutput": 1914}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/jit/src/initializer_api_transforms/output_function.js": {"bytesInOutput": 914}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/jit/src/initializer_api_transforms/query_functions.js": {"bytesInOutput": 1510}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/jit/src/index.js": {"bytesInOutput": 776}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/jit/index.js": {"bytesInOutput": 0}}, "bytes": 25086}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-BSAOKZSO.js.map": {"imports": [], "exports": [], "inputs": {}, "bytes": 349940}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-BSAOKZSO.js": {"imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-6JLQ22O6.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-WUCT7QTW.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-ERYCP7NI.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-KPQ72R34.js", "kind": "import-statement"}], "exports": ["CompilationMode", "ComponentDecoratorHandler", "ComponentScopeKind", "CompoundComponentScopeReader", "CompoundMetadataReader", "CompoundMetadataRegistry", "DirectiveDecoratorHandler", "DtsMetadataReader", "DtsTransformRegistry", "DynamicValue", "ExportedProviderStatusResolver", "HostDirectivesResolver", "INPUT_INITIALIZER_FN", "InjectableClassRegistry", "InjectableDecoratorHandler", "JitDeclarationRegistry", "LocalMetadataRegistry", "LocalModuleScopeRegistry", "MODEL_INITIALIZER_FN", "<PERSON><PERSON><PERSON><PERSON>", "MetadataDtsModuleScopeResolver", "NgModuleDecoratorHandler", "NgOriginalFile", "NoopReferencesRegistry", "OUTPUT_INITIALIZER_FNS", "OptimizeFor", "PartialEvaluator", "PipeDecoratorHandler", "PotentialImportKind", "PotentialImportMode", "QUERY_INITIALIZER_FNS", "ResourceRegistry", "SemanticDepGraphUpdater", "ShimAdapter", "ShimReferenceTagger", "StaticInterpreter", "SymbolKind", "TemplateTypeCheckerImpl", "TraitCompiler", "TsCreateProgramDriver", "TypeCheckScopeRegistry", "TypeCheckShimGenerator", "aliasTransformFactory", "createForwardRefResolver", "declarationTransformFactory", "getAngularDecorators", "isAngularDecorator", "<PERSON><PERSON><PERSON>", "ivyTransformFactory", "queryDecoratorNames", "retagAllTsFiles", "tryParseInitializerApi", "tryParseInitializerBasedOutput", "tryParseSignalInputMapping", "tryParseSignalModelMapping", "tryParseSignalQueryFromInitializer", "untagAllTsFiles"], "inputs": {"bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/util.js": {"bytesInOutput": 9011}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/src/dynamic.js": {"bytesInOutput": 2296}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/src/interpreter.js": {"bytesInOutput": 23248}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/src/result.js": {"bytesInOutput": 669}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/src/builtin.js": {"bytesInOutput": 1261}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/src/synthetic.js": {"bytesInOutput": 92}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/src/interface.js": {"bytesInOutput": 643}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/src/diagnostics.js": {"bytesInOutput": 4546}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/index.js": {"bytesInOutput": 0}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/checker.js": {"bytesInOutput": 212}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/scope.js": {"bytesInOutput": 534}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/api.js": {"bytesInOutput": 0}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/index.js": {"bytesInOutput": 0}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/completion.js": {"bytesInOutput": 303}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/context.js": {"bytesInOutput": 0}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/symbols.js": {"bytesInOutput": 766}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/api.js": {"bytesInOutput": 0}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/index.js": {"bytesInOutput": 0}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/di.js": {"bytesInOutput": 5617}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/diagnostics.js": {"bytesInOutput": 11148}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/api.js": {"bytesInOutput": 433}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/index.js": {"bytesInOutput": 0}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/dts.js": {"bytesInOutput": 8053}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/property_mapping.js": {"bytesInOutput": 2538}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/util.js": {"bytesInOutput": 6689}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/inheritance.js": {"bytesInOutput": 2094}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/registry.js": {"bytesInOutput": 1620}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/resource_registry.js": {"bytesInOutput": 2266}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/providers.js": {"bytesInOutput": 1352}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/host_directives_resolver.js": {"bytesInOutput": 2282}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/src/api.js": {"bytesInOutput": 588}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/index.js": {"bytesInOutput": 0}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/src/alias.js": {"bytesInOutput": 769}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/src/compilation.js": {"bytesInOutput": 17634}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/src/trait.js": {"bytesInOutput": 1880}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/src/declaration.js": {"bytesInOutput": 4855}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/src/transform.js": {"bytesInOutput": 10790}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/visitor.js": {"bytesInOutput": 2032}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/evaluation.js": {"bytesInOutput": 1836}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/factory.js": {"bytesInOutput": 591}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/injectable_registry.js": {"bytesInOutput": 704}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/metadata.js": {"bytesInOutput": 4812}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/debug_info.js": {"bytesInOutput": 701}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/references_registry.js": {"bytesInOutput": 75}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/schema.js": {"bytesInOutput": 1168}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/input_transforms.js": {"bytesInOutput": 457}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/jit_declaration_registry.js": {"bytesInOutput": 87}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/index.js": {"bytesInOutput": 0}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/component/src/handler.js": {"bytesInOutput": 55460}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/semantic_graph/src/api.js": {"bytesInOutput": 378}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/semantic_graph/index.js": {"bytesInOutput": 0}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/semantic_graph/src/graph.js": {"bytesInOutput": 4383}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/semantic_graph/src/type_parameters.js": {"bytesInOutput": 654}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/semantic_graph/src/util.js": {"bytesInOutput": 1072}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/src/api.js": {"bytesInOutput": 260}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/index.js": {"bytesInOutput": 0}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/src/component_scope.js": {"bytesInOutput": 545}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/src/dependency.js": {"bytesInOutput": 2497}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/src/local.js": {"bytesInOutput": 16215}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/src/util.js": {"bytesInOutput": 1711}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/src/typecheck.js": {"bytesInOutput": 3139}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/handler.js": {"bytesInOutput": 9525}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/shared.js": {"bytesInOutput": 41048}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/initializer_function_access.js": {"bytesInOutput": 547}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/initializer_functions.js": {"bytesInOutput": 2977}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/input_output_parse_options.js": {"bytesInOutput": 704}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/input_function.js": {"bytesInOutput": 1077}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/model_function.js": {"bytesInOutput": 1243}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/output_function.js": {"bytesInOutput": 1391}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/query_functions.js": {"bytesInOutput": 3395}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/symbol.js": {"bytesInOutput": 3106}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/index.js": {"bytesInOutput": 0}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/ng_module/src/handler.js": {"bytesInOutput": 27786}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/ng_module/src/module_with_providers.js": {"bytesInOutput": 2987}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/ng_module/index.js": {"bytesInOutput": 0}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/component/src/diagnostics.js": {"bytesInOutput": 908}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/component/src/resources.js": {"bytesInOutput": 13558}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/component/src/symbol.js": {"bytesInOutput": 1557}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/component/src/util.js": {"bytesInOutput": 3752}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/checker.js": {"bytesInOutput": 26560}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/program_driver/src/api.js": {"bytesInOutput": 245}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/program_driver/index.js": {"bytesInOutput": 0}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/program_driver/src/ts_create_program_driver.js": {"bytesInOutput": 5478}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/shims/src/adapter.js": {"bytesInOutput": 3203}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/shims/src/expando.js": {"bytesInOutput": 1487}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/shims/src/util.js": {"bytesInOutput": 144}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/shims/index.js": {"bytesInOutput": 0}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/shims/src/reference_tagger.js": {"bytesInOutput": 989}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/diagnostics/src/diagnostic.js": {"bytesInOutput": 3745}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/diagnostics/index.js": {"bytesInOutput": 0}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/diagnostics/src/id.js": {"bytesInOutput": 397}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/completion.js": {"bytesInOutput": 5355}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/comments.js": {"bytesInOutput": 4538}, "node_modules/magic-string/dist/magic-string.es.mjs": {"bytesInOutput": 32019}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/context.js": {"bytesInOutput": 10137}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/dom.js": {"bytesInOutput": 2870}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/environment.js": {"bytesInOutput": 2645}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/reference_emit_environment.js": {"bytesInOutput": 1688}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/ts_util.js": {"bytesInOutput": 2809}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/type_constructor.js": {"bytesInOutput": 4783}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/tcb_util.js": {"bytesInOutput": 3510}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/type_parameter_emitter.js": {"bytesInOutput": 2781}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/oob.js": {"bytesInOutput": 12979}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/shim.js": {"bytesInOutput": 505}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/type_check_block.js": {"bytesInOutput": 65654}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/diagnostics.js": {"bytesInOutput": 1599}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/expression.js": {"bytesInOutput": 14293}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/type_check_file.js": {"bytesInOutput": 2032}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/source.js": {"bytesInOutput": 1550}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/line_mappings.js": {"bytesInOutput": 1140}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/template_symbol_builder.js": {"bytesInOutput": 21716}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/index.js": {"bytesInOutput": 0}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/hmr/src/metadata.js": {"bytesInOutput": 925}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/hmr/src/extract_dependencies.js": {"bytesInOutput": 8385}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/hmr/index.js": {"bytesInOutput": 0}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/hmr/src/update_declaration.js": {"bytesInOutput": 1457}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/component/index.js": {"bytesInOutput": 0}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/src/injectable.js": {"bytesInOutput": 9622}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/src/pipe.js": {"bytesInOutput": 6825}}, "bytes": 618657}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/linker/index.js.map": {"imports": [], "exports": [], "inputs": {}, "bytes": 69}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/linker/index.js": {"imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-LHXVN5NW.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-6JLQ22O6.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-NVYT6OPE.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-ERYCP7NI.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-KPQ72R34.js", "kind": "import-statement"}], "exports": ["DEFAULT_LINKER_OPTIONS", "FatalLinkerError", "FileLinker", "LinkerEnvironment", "assert", "isFatalLinkerError", "needsLinking"], "entryPoint": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/index.mjs", "inputs": {}, "bytes": 597}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/linker/babel/index.js.map": {"imports": [], "exports": [], "inputs": {}, "bytes": 9150}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/linker/babel/index.js": {"imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-LHXVN5NW.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-6JLQ22O6.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-H5Y7P5GQ.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-NVYT6OPE.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-ERYCP7NI.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-KPQ72R34.js", "kind": "import-statement"}], "exports": ["createEs2015LinkerPlugin", "default"], "entryPoint": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/babel/index.mjs", "inputs": {"bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/babel/src/es2015_linker_plugin.mjs": {"bytesInOutput": 3549}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/babel/src/ast/babel_ast_factory.mjs": {"bytesInOutput": 4705}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/babel/src/ast/babel_ast_host.mjs": {"bytesInOutput": 4406}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/babel/src/babel_declaration_scope.mjs": {"bytesInOutput": 796}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/babel/src/babel_plugin.mjs": {"bytesInOutput": 214}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/babel/index.mjs": {"bytesInOutput": 41}}, "bytes": 15220}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-LHXVN5NW.js.map": {"imports": [], "exports": [], "inputs": {}, "bytes": 26808}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-LHXVN5NW.js": {"imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-6JLQ22O6.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-NVYT6OPE.js", "kind": "import-statement"}], "exports": ["DEFAULT_LINKER_OPTIONS", "FatalLinkerError", "FileLinker", "LinkerEnvironment", "assert", "isFatalLinkerError", "needsLinking"], "inputs": {"bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/fatal_linker_error.mjs": {"bytesInOutput": 242}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/ast/utils.mjs": {"bytesInOutput": 160}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/index.mjs": {"bytesInOutput": 0}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/ast/ast_value.mjs": {"bytesInOutput": 3982}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/emit_scopes/emit_scope.mjs": {"bytesInOutput": 1390}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/linker_import_generator.mjs": {"bytesInOutput": 605}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/emit_scopes/local_emit_scope.mjs": {"bytesInOutput": 375}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/partial_linker_selector.mjs": {"bytesInOutput": 4436}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/get_source_file.mjs": {"bytesInOutput": 305}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/partial_class_metadata_async_linker_1.mjs": {"bytesInOutput": 1125}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/partial_class_metadata_linker_1.mjs": {"bytesInOutput": 629}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/partial_component_linker_1.mjs": {"bytesInOutput": 9694}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/partial_directive_linker_1.mjs": {"bytesInOutput": 6425}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/util.mjs": {"bytesInOutput": 2193}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/partial_factory_linker_1.mjs": {"bytesInOutput": 1054}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/partial_injectable_linker_1.mjs": {"bytesInOutput": 1423}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/partial_injector_linker_1.mjs": {"bytesInOutput": 760}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/partial_ng_module_linker_1.mjs": {"bytesInOutput": 2414}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/partial_pipe_linker_1.mjs": {"bytesInOutput": 943}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/file_linker.mjs": {"bytesInOutput": 2074}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/linker_options.mjs": {"bytesInOutput": 126}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/translator.mjs": {"bytesInOutput": 477}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/linker_environment.mjs": {"bytesInOutput": 1029}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/needs_linking.mjs": {"bytesInOutput": 105}}, "bytes": 45816}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-6JLQ22O6.js.map": {"imports": [], "exports": [], "inputs": {}, "bytes": 72407}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-6JLQ22O6.js": {"imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-ERYCP7NI.js", "kind": "import-statement"}], "exports": ["AbsoluteModuleStrategy", "AliasStrategy", "AmbientImport", "COMPILER_ERRORS_WITH_GUIDES", "ClassMemberAccessLevel", "ClassMemberKind", "Context", "DefaultImportTracker", "DeferredSymbolTracker", "ERROR_DETAILS_PAGE_BASE_URL", "ErrorCode", "ExpressionTranslatorVisitor", "ExtendedTemplateDiagnosticName", "FatalDiagnosticError", "ImportFlags", "ImportManager", "ImportedSymbolsTracker", "LocalCompilationExtraImportsTracker", "LocalIdentifierStrategy", "LogicalProjectStrategy", "ModuleResolver", "NoopImportRewriter", "PrivateExportAliasingHost", "R3SymbolsImportRewriter", "Reference", "ReferenceEmitKind", "ReferenceEmitter", "RelativePathStrategy", "TypeEmitter", "TypeEntityToDeclarationError", "TypeScriptReflectionHost", "UnifiedModulesAliasingHost", "UnifiedModulesStrategy", "addDiagnostic<PERSON><PERSON><PERSON>", "assertSuccessfulReferenceEmit", "attachDefaultImportDeclaration", "canEmitType", "classMemberAccessLevelToString", "entityNameToValue", "filterToMembersWithDecorator", "getDefaultImportDeclaration", "getProjectRelativePath", "getRootDirs", "getSourceFile", "getSourceFileOrNull", "getTokenAtPosition", "identifierOfNode", "isAliasImportDeclaration", "isAssignment", "isDeclaration", "isDtsPath", "isFatalDiagnosticError", "isFromDtsFile", "isLocalCompilationDiagnostics", "isNamedClassDeclaration", "isNonDeclarationTsPath", "isSymbolWithValueDeclaration", "loadIsReferencedAliasDeclarationPatch", "makeDiagnostic", "makeDiagno<PERSON><PERSON><PERSON><PERSON>", "makeRelatedInformation", "ngErrorCode", "nodeDebugInfo", "nodeNameForError", "normalizeSeparators", "presetImportManagerForceNamespaceImports", "reflectClassMember", "reflectObjectLiteral", "reflectTypeEntityToDeclaration", "relativePathBetween", "replaceTsWithNgInErrors", "toUnredirectedSourceFile", "translateExpression", "translateStatement", "translateType", "typeNodeToValueExpr"], "inputs": {"bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/src/error_code.js": {"bytesInOutput": 9318}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/src/util.js": {"bytesInOutput": 235}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/src/error.js": {"bytesInOutput": 2073}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/src/docs.js": {"bytesInOutput": 375}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.js": {"bytesInOutput": 0}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/src/error_details_base_url.js": {"bytesInOutput": 64}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/src/extended_template_diagnostic_name.js": {"bytesInOutput": 1401}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/src/typescript.js": {"bytesInOutput": 18320}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/src/host.js": {"bytesInOutput": 1231}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/src/type_to_value.js": {"bytesInOutput": 5386}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/src/util.js": {"bytesInOutput": 673}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/index.js": {"bytesInOutput": 0}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/typescript.js": {"bytesInOutput": 3362}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/references.js": {"bytesInOutput": 1974}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/alias.js": {"bytesInOutput": 2001}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/emitter.js": {"bytesInOutput": 8741}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/find_export.js": {"bytesInOutput": 517}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.js": {"bytesInOutput": 0}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/path.js": {"bytesInOutput": 599}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/core.js": {"bytesInOutput": 2164}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/patch_alias_reference_resolution.js": {"bytesInOutput": 1775}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/default.js": {"bytesInOutput": 1342}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/deferred_symbol_tracker.js": {"bytesInOutput": 4253}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/imported_symbols_tracker.js": {"bytesInOutput": 3084}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/local_compilation_extra_imports_tracker.js": {"bytesInOutput": 1652}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/resolver.js": {"bytesInOutput": 642}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/import_manager/import_manager.js": {"bytesInOutput": 11465}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/import_manager/check_unique_identifier_name.js": {"bytesInOutput": 1072}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/import_manager/import_typescript_transform.js": {"bytesInOutput": 2862}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/import_manager/reuse_generated_imports.js": {"bytesInOutput": 1148}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/import_manager/reuse_source_file_imports.js": {"bytesInOutput": 2625}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/context.js": {"bytesInOutput": 288}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/index.js": {"bytesInOutput": 0}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/translator.js": {"bytesInOutput": 12082}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/type_emitter.js": {"bytesInOutput": 2741}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/type_translator.js": {"bytesInOutput": 9529}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/ts_util.js": {"bytesInOutput": 307}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/typescript_ast_factory.js": {"bytesInOutput": 8924}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/typescript_translator.js": {"bytesInOutput": 541}}, "bytes": 131663}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/private/bazel.js.map": {"imports": [], "exports": [], "inputs": {}, "bytes": 69}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/private/bazel.js": {"imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-WUCT7QTW.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-KPQ72R34.js", "kind": "import-statement"}], "exports": ["PerfPhase"], "entryPoint": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/private/bazel.mjs", "inputs": {"bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/private/bazel.mjs": {"bytesInOutput": 0}}, "bytes": 475}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-WUCT7QTW.js.map": {"imports": [], "exports": [], "inputs": {}, "bytes": 4536}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-WUCT7QTW.js": {"imports": [], "exports": ["ActivePerfRecorder", "DelegatingPerfRecorder", "PerfCheckpoint", "PerfEvent", "PerfPhase"], "inputs": {"bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/perf/src/api.js": {"bytesInOutput": 4308}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/perf/index.js": {"bytesInOutput": 0}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/perf/src/noop.js": {"bytesInOutput": 231}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/perf/src/clock.js": {"bytesInOutput": 178}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/perf/src/recorder.js": {"bytesInOutput": 2524}}, "bytes": 8086}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/private/localize.js.map": {"imports": [], "exports": [], "inputs": {}, "bytes": 69}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/private/localize.js": {"imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-H5Y7P5GQ.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-NVYT6OPE.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-ERYCP7NI.js", "kind": "import-statement"}, {"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-KPQ72R34.js", "kind": "import-statement"}], "exports": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "InvalidFileSystem", "LogLevel", "LogicalFileSystem", "LogicalProjectPath", "NgtscCompilerHost", "NodeJSFileSystem", "SourceFile", "SourceFileLoader", "absoluteFrom", "absoluteFromSourceFile", "basename", "createFileSystemTsReadDirectoryFn", "dirname", "getFileSystem", "getSourceFileOrError", "isLocalRelativePath", "isRoot", "isRooted", "join", "relative", "relativeFrom", "resolve", "setFileSystem", "toRelativeImport"], "entryPoint": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/private/localize.mjs", "inputs": {"bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/private/localize.mjs": {"bytesInOutput": 0}}, "bytes": 1410}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-H5Y7P5GQ.js.map": {"imports": [], "exports": [], "inputs": {}, "bytes": 866}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-H5Y7P5GQ.js": {"imports": [], "exports": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "LogLevel"], "inputs": {"bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/logging/src/logger.js": {"bytesInOutput": 254}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/logging/src/console_logger.js": {"bytesInOutput": 675}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/logging/index.js": {"bytesInOutput": 0}}, "bytes": 1545}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-NVYT6OPE.js.map": {"imports": [], "exports": [], "inputs": {}, "bytes": 8685}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-NVYT6OPE.js": {"imports": [], "exports": ["SourceFile", "SourceFileLoader"], "inputs": {"bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/sourcemaps/src/source_file.js": {"bytesInOutput": 8842}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/sourcemaps/src/segment_marker.js": {"bytesInOutput": 548}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/sourcemaps/src/source_file_loader.js": {"bytesInOutput": 4539}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/sourcemaps/src/content_origin.js": {"bytesInOutput": 279}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/sourcemaps/index.js": {"bytesInOutput": 0}}, "bytes": 15269}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-ERYCP7NI.js.map": {"imports": [], "exports": [], "inputs": {}, "bytes": 8170}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-ERYCP7NI.js": {"imports": [{"path": "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-KPQ72R34.js", "kind": "import-statement"}], "exports": ["InvalidFileSystem", "LogicalFileSystem", "LogicalProjectPath", "NgtscCompilerHost", "NodeJSFileSystem", "absoluteFrom", "absoluteFromSourceFile", "basename", "createFileSystemTsReadDirectoryFn", "dirname", "getFileSystem", "getSourceFileOrError", "isLocalRelativePath", "isRoot", "isRooted", "join", "relative", "relativeFrom", "resolve", "setFileSystem", "stripExtension", "toRelativeImport"], "inputs": {"bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/src/invalid_file_system.js": {"bytesInOutput": 1486}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/src/util.js": {"bytesInOutput": 495}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/src/helpers.js": {"bytesInOutput": 1506}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/src/compiler_host.js": {"bytesInOutput": 1663}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/src/logical.js": {"bytesInOutput": 1709}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/src/node_js_file_system.js": {"bytesInOutput": 2656}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/src/ts_read_directory.js": {"bytesInOutput": 1366}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.js": {"bytesInOutput": 0}}, "bytes": 12453}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-KPQ72R34.js.map": {"imports": [], "exports": [], "inputs": {}, "bytes": 69}, "bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/bundles/chunk-KPQ72R34.js": {"imports": [], "exports": ["__publicField", "__require"], "inputs": {}, "bytes": 924}}}