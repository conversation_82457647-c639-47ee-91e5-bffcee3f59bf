"use strict";
// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.0
//   protoc               v6.30.2
// source: sigstore_verification.proto
Object.defineProperty(exports, "__esModule", { value: true });
exports.Input = exports.Artifact = exports.ArtifactVerificationOptions_ObserverTimestampOptions = exports.ArtifactVerificationOptions_TlogIntegratedTimestampOptions = exports.ArtifactVerificationOptions_TimestampAuthorityOptions = exports.ArtifactVerificationOptions_CtlogOptions = exports.ArtifactVerificationOptions_TlogOptions = exports.ArtifactVerificationOptions = exports.PublicKeyIdentities = exports.CertificateIdentities = exports.CertificateIdentity = void 0;
/* eslint-disable */
const sigstore_bundle_1 = require("./sigstore_bundle");
const sigstore_common_1 = require("./sigstore_common");
const sigstore_trustroot_1 = require("./sigstore_trustroot");
exports.CertificateIdentity = {
    fromJSON(object) {
        return {
            issuer: isSet(object.issuer) ? globalThis.String(object.issuer) : "",
            san: isSet(object.san) ? sigstore_common_1.SubjectAlternativeName.fromJSON(object.san) : undefined,
            oids: globalThis.Array.isArray(object?.oids)
                ? object.oids.map((e) => sigstore_common_1.ObjectIdentifierValuePair.fromJSON(e))
                : [],
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.issuer !== "") {
            obj.issuer = message.issuer;
        }
        if (message.san !== undefined) {
            obj.san = sigstore_common_1.SubjectAlternativeName.toJSON(message.san);
        }
        if (message.oids?.length) {
            obj.oids = message.oids.map((e) => sigstore_common_1.ObjectIdentifierValuePair.toJSON(e));
        }
        return obj;
    },
};
exports.CertificateIdentities = {
    fromJSON(object) {
        return {
            identities: globalThis.Array.isArray(object?.identities)
                ? object.identities.map((e) => exports.CertificateIdentity.fromJSON(e))
                : [],
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.identities?.length) {
            obj.identities = message.identities.map((e) => exports.CertificateIdentity.toJSON(e));
        }
        return obj;
    },
};
exports.PublicKeyIdentities = {
    fromJSON(object) {
        return {
            publicKeys: globalThis.Array.isArray(object?.publicKeys)
                ? object.publicKeys.map((e) => sigstore_common_1.PublicKey.fromJSON(e))
                : [],
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.publicKeys?.length) {
            obj.publicKeys = message.publicKeys.map((e) => sigstore_common_1.PublicKey.toJSON(e));
        }
        return obj;
    },
};
exports.ArtifactVerificationOptions = {
    fromJSON(object) {
        return {
            signers: isSet(object.certificateIdentities)
                ? {
                    $case: "certificateIdentities",
                    certificateIdentities: exports.CertificateIdentities.fromJSON(object.certificateIdentities),
                }
                : isSet(object.publicKeys)
                    ? { $case: "publicKeys", publicKeys: exports.PublicKeyIdentities.fromJSON(object.publicKeys) }
                    : undefined,
            tlogOptions: isSet(object.tlogOptions)
                ? exports.ArtifactVerificationOptions_TlogOptions.fromJSON(object.tlogOptions)
                : undefined,
            ctlogOptions: isSet(object.ctlogOptions)
                ? exports.ArtifactVerificationOptions_CtlogOptions.fromJSON(object.ctlogOptions)
                : undefined,
            tsaOptions: isSet(object.tsaOptions)
                ? exports.ArtifactVerificationOptions_TimestampAuthorityOptions.fromJSON(object.tsaOptions)
                : undefined,
            integratedTsOptions: isSet(object.integratedTsOptions)
                ? exports.ArtifactVerificationOptions_TlogIntegratedTimestampOptions.fromJSON(object.integratedTsOptions)
                : undefined,
            observerOptions: isSet(object.observerOptions)
                ? exports.ArtifactVerificationOptions_ObserverTimestampOptions.fromJSON(object.observerOptions)
                : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.signers?.$case === "certificateIdentities") {
            obj.certificateIdentities = exports.CertificateIdentities.toJSON(message.signers.certificateIdentities);
        }
        else if (message.signers?.$case === "publicKeys") {
            obj.publicKeys = exports.PublicKeyIdentities.toJSON(message.signers.publicKeys);
        }
        if (message.tlogOptions !== undefined) {
            obj.tlogOptions = exports.ArtifactVerificationOptions_TlogOptions.toJSON(message.tlogOptions);
        }
        if (message.ctlogOptions !== undefined) {
            obj.ctlogOptions = exports.ArtifactVerificationOptions_CtlogOptions.toJSON(message.ctlogOptions);
        }
        if (message.tsaOptions !== undefined) {
            obj.tsaOptions = exports.ArtifactVerificationOptions_TimestampAuthorityOptions.toJSON(message.tsaOptions);
        }
        if (message.integratedTsOptions !== undefined) {
            obj.integratedTsOptions = exports.ArtifactVerificationOptions_TlogIntegratedTimestampOptions.toJSON(message.integratedTsOptions);
        }
        if (message.observerOptions !== undefined) {
            obj.observerOptions = exports.ArtifactVerificationOptions_ObserverTimestampOptions.toJSON(message.observerOptions);
        }
        return obj;
    },
};
exports.ArtifactVerificationOptions_TlogOptions = {
    fromJSON(object) {
        return {
            threshold: isSet(object.threshold) ? globalThis.Number(object.threshold) : 0,
            performOnlineVerification: isSet(object.performOnlineVerification)
                ? globalThis.Boolean(object.performOnlineVerification)
                : false,
            disable: isSet(object.disable) ? globalThis.Boolean(object.disable) : false,
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.threshold !== 0) {
            obj.threshold = Math.round(message.threshold);
        }
        if (message.performOnlineVerification !== false) {
            obj.performOnlineVerification = message.performOnlineVerification;
        }
        if (message.disable !== false) {
            obj.disable = message.disable;
        }
        return obj;
    },
};
exports.ArtifactVerificationOptions_CtlogOptions = {
    fromJSON(object) {
        return {
            threshold: isSet(object.threshold) ? globalThis.Number(object.threshold) : 0,
            disable: isSet(object.disable) ? globalThis.Boolean(object.disable) : false,
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.threshold !== 0) {
            obj.threshold = Math.round(message.threshold);
        }
        if (message.disable !== false) {
            obj.disable = message.disable;
        }
        return obj;
    },
};
exports.ArtifactVerificationOptions_TimestampAuthorityOptions = {
    fromJSON(object) {
        return {
            threshold: isSet(object.threshold) ? globalThis.Number(object.threshold) : 0,
            disable: isSet(object.disable) ? globalThis.Boolean(object.disable) : false,
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.threshold !== 0) {
            obj.threshold = Math.round(message.threshold);
        }
        if (message.disable !== false) {
            obj.disable = message.disable;
        }
        return obj;
    },
};
exports.ArtifactVerificationOptions_TlogIntegratedTimestampOptions = {
    fromJSON(object) {
        return {
            threshold: isSet(object.threshold) ? globalThis.Number(object.threshold) : 0,
            disable: isSet(object.disable) ? globalThis.Boolean(object.disable) : false,
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.threshold !== 0) {
            obj.threshold = Math.round(message.threshold);
        }
        if (message.disable !== false) {
            obj.disable = message.disable;
        }
        return obj;
    },
};
exports.ArtifactVerificationOptions_ObserverTimestampOptions = {
    fromJSON(object) {
        return {
            threshold: isSet(object.threshold) ? globalThis.Number(object.threshold) : 0,
            disable: isSet(object.disable) ? globalThis.Boolean(object.disable) : false,
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.threshold !== 0) {
            obj.threshold = Math.round(message.threshold);
        }
        if (message.disable !== false) {
            obj.disable = message.disable;
        }
        return obj;
    },
};
exports.Artifact = {
    fromJSON(object) {
        return {
            data: isSet(object.artifactUri)
                ? { $case: "artifactUri", artifactUri: globalThis.String(object.artifactUri) }
                : isSet(object.artifact)
                    ? { $case: "artifact", artifact: Buffer.from(bytesFromBase64(object.artifact)) }
                    : isSet(object.artifactDigest)
                        ? { $case: "artifactDigest", artifactDigest: sigstore_common_1.HashOutput.fromJSON(object.artifactDigest) }
                        : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.data?.$case === "artifactUri") {
            obj.artifactUri = message.data.artifactUri;
        }
        else if (message.data?.$case === "artifact") {
            obj.artifact = base64FromBytes(message.data.artifact);
        }
        else if (message.data?.$case === "artifactDigest") {
            obj.artifactDigest = sigstore_common_1.HashOutput.toJSON(message.data.artifactDigest);
        }
        return obj;
    },
};
exports.Input = {
    fromJSON(object) {
        return {
            artifactTrustRoot: isSet(object.artifactTrustRoot) ? sigstore_trustroot_1.TrustedRoot.fromJSON(object.artifactTrustRoot) : undefined,
            artifactVerificationOptions: isSet(object.artifactVerificationOptions)
                ? exports.ArtifactVerificationOptions.fromJSON(object.artifactVerificationOptions)
                : undefined,
            bundle: isSet(object.bundle) ? sigstore_bundle_1.Bundle.fromJSON(object.bundle) : undefined,
            artifact: isSet(object.artifact) ? exports.Artifact.fromJSON(object.artifact) : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.artifactTrustRoot !== undefined) {
            obj.artifactTrustRoot = sigstore_trustroot_1.TrustedRoot.toJSON(message.artifactTrustRoot);
        }
        if (message.artifactVerificationOptions !== undefined) {
            obj.artifactVerificationOptions = exports.ArtifactVerificationOptions.toJSON(message.artifactVerificationOptions);
        }
        if (message.bundle !== undefined) {
            obj.bundle = sigstore_bundle_1.Bundle.toJSON(message.bundle);
        }
        if (message.artifact !== undefined) {
            obj.artifact = exports.Artifact.toJSON(message.artifact);
        }
        return obj;
    },
};
function bytesFromBase64(b64) {
    return Uint8Array.from(globalThis.Buffer.from(b64, "base64"));
}
function base64FromBytes(arr) {
    return globalThis.Buffer.from(arr).toString("base64");
}
function isSet(value) {
    return value !== null && value !== undefined;
}
