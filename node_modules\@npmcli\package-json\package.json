{"name": "@npmcli/package-json", "version": "6.2.0", "description": "Programmatic API to update package.json", "keywords": ["npm", "oss"], "repository": {"type": "git", "url": "git+https://github.com/npm/package-json.git"}, "license": "ISC", "author": "GitHub Inc.", "main": "lib/index.js", "files": ["bin/", "lib/"], "scripts": {"snap": "tap", "test": "tap", "lint": "npm run eslint", "lintfix": "npm run eslint -- --fix", "posttest": "npm run lint", "postsnap": "npm run lintfix --", "postlint": "template-oss-check", "template-oss-apply": "template-oss-apply --force", "eslint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\""}, "dependencies": {"@npmcli/git": "^6.0.0", "glob": "^10.2.2", "hosted-git-info": "^8.0.0", "json-parse-even-better-errors": "^4.0.0", "proc-log": "^5.0.0", "semver": "^7.5.3", "validate-npm-package-license": "^3.0.4"}, "devDependencies": {"@npmcli/eslint-config": "^5.1.0", "@npmcli/template-oss": "4.23.6", "read-package-json": "^7.0.0", "read-package-json-fast": "^4.0.0", "tap": "^16.0.1"}, "engines": {"node": "^18.17.0 || >=20.5.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.23.6", "publish": "true"}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"]}}