{"version": 3, "file": "slide-toggle.mjs", "sources": ["../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/slide-toggle/slide-toggle-config.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/slide-toggle/slide-toggle.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/slide-toggle/slide-toggle.html", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/slide-toggle/slide-toggle-required-validator.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/slide-toggle/module.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\nimport {InjectionToken} from '@angular/core';\nimport {ThemePalette} from '../core';\n\n/** Default `mat-slide-toggle` options that can be overridden. */\nexport interface MatSlideToggleDefaultOptions {\n  /** Whether toggle action triggers value changes in slide toggle. */\n  disableToggleValue?: boolean;\n\n  /**\n   * Default theme color of the slide toggle. This API is supported in M2 themes only,\n   * it has no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/slide-toggle/styling.\n   *\n   * For information on applying color variants in M3, see\n   * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n   */\n  color?: ThemePalette;\n\n  /** Whether to hide the icon inside the slide toggle. */\n  hideIcon?: boolean;\n\n  /** Whether disabled slide toggles should remain interactive. */\n  disabledInteractive?: boolean;\n}\n\n/** Injection token to be used to override the default options for `mat-slide-toggle`. */\nexport const MAT_SLIDE_TOGGLE_DEFAULT_OPTIONS = new InjectionToken<MatSlideToggleDefaultOptions>(\n  'mat-slide-toggle-default-options',\n  {\n    providedIn: 'root',\n    factory: () => ({disableToggleValue: false, hideIcon: false, disabledInteractive: false}),\n  },\n);\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  AfterContentInit,\n  booleanAttribute,\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  ElementRef,\n  EventEmitter,\n  forwardRef,\n  Input,\n  numberAttribute,\n  OnChanges,\n  OnDestroy,\n  Output,\n  SimpleChanges,\n  ViewChild,\n  ViewEncapsulation,\n  ANIMATION_MODULE_TYPE,\n  inject,\n  HostAttributeToken,\n} from '@angular/core';\nimport {\n  AbstractControl,\n  ControlValueAccessor,\n  NG_VALIDATORS,\n  NG_VALUE_ACCESSOR,\n  ValidationErrors,\n  Validator,\n} from '@angular/forms';\nimport {_IdGenerator, FocusMonitor} from '@angular/cdk/a11y';\nimport {\n  MAT_SLIDE_TOGGLE_DEFAULT_OPTIONS,\n  MatSlideToggleDefaultOptions,\n} from './slide-toggle-config';\nimport {_MatInternalFormField, _StructuralStylesLoader, MatRipple} from '../core';\nimport {_CdkPrivateStyleLoader} from '@angular/cdk/private';\n\n/**\n * @deprecated Will stop being exported.\n * @breaking-change 19.0.0\n */\nexport const MAT_SLIDE_TOGGLE_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => MatSlideToggle),\n  multi: true,\n};\n\n/** Change event object emitted by a slide toggle. */\nexport class MatSlideToggleChange {\n  constructor(\n    /** The source slide toggle of the event. */\n    public source: MatSlideToggle,\n    /** The new `checked` value of the slide toggle. */\n    public checked: boolean,\n  ) {}\n}\n\n@Component({\n  selector: 'mat-slide-toggle',\n  templateUrl: 'slide-toggle.html',\n  styleUrl: 'slide-toggle.css',\n  host: {\n    'class': 'mat-mdc-slide-toggle',\n    '[id]': 'id',\n    // Needs to be removed since it causes some a11y issues (see #21266).\n    '[attr.tabindex]': 'null',\n    '[attr.aria-label]': 'null',\n    '[attr.name]': 'null',\n    '[attr.aria-labelledby]': 'null',\n    '[class.mat-mdc-slide-toggle-focused]': '_focused',\n    '[class.mat-mdc-slide-toggle-checked]': 'checked',\n    '[class._mat-animation-noopable]': '_noopAnimations',\n    '[class]': 'color ? \"mat-\" + color : \"\"',\n  },\n  exportAs: 'matSlideToggle',\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  providers: [\n    MAT_SLIDE_TOGGLE_VALUE_ACCESSOR,\n    {\n      provide: NG_VALIDATORS,\n      useExisting: MatSlideToggle,\n      multi: true,\n    },\n  ],\n  imports: [MatRipple, _MatInternalFormField],\n})\nexport class MatSlideToggle\n  implements OnDestroy, AfterContentInit, OnChanges, ControlValueAccessor, Validator\n{\n  private _elementRef = inject(ElementRef);\n  protected _focusMonitor = inject(FocusMonitor);\n  protected _changeDetectorRef = inject(ChangeDetectorRef);\n  defaults = inject<MatSlideToggleDefaultOptions>(MAT_SLIDE_TOGGLE_DEFAULT_OPTIONS);\n\n  private _onChange = (_: any) => {};\n  private _onTouched = () => {};\n  private _validatorOnChange = () => {};\n\n  private _uniqueId: string;\n  private _checked: boolean = false;\n\n  private _createChangeEvent(isChecked: boolean) {\n    return new MatSlideToggleChange(this, isChecked);\n  }\n\n  /** Unique ID for the label element. */\n  _labelId: string;\n\n  /** Returns the unique id for the visual hidden button. */\n  get buttonId(): string {\n    return `${this.id || this._uniqueId}-button`;\n  }\n\n  /** Reference to the MDC switch element. */\n  @ViewChild('switch') _switchElement: ElementRef<HTMLElement>;\n\n  /** Focuses the slide-toggle. */\n  focus(): void {\n    this._switchElement.nativeElement.focus();\n  }\n  /** Whether noop animations are enabled. */\n  _noopAnimations: boolean;\n\n  /** Whether the slide toggle is currently focused. */\n  _focused: boolean;\n\n  /** Name value will be applied to the input element if present. */\n  @Input() name: string | null = null;\n\n  /** A unique id for the slide-toggle input. If none is supplied, it will be auto-generated. */\n  @Input() id: string;\n\n  /** Whether the label should appear after or before the slide-toggle. Defaults to 'after'. */\n  @Input() labelPosition: 'before' | 'after' = 'after';\n\n  /** Used to set the aria-label attribute on the underlying input element. */\n  @Input('aria-label') ariaLabel: string | null = null;\n\n  /** Used to set the aria-labelledby attribute on the underlying input element. */\n  @Input('aria-labelledby') ariaLabelledby: string | null = null;\n\n  /** Used to set the aria-describedby attribute on the underlying input element. */\n  @Input('aria-describedby') ariaDescribedby: string;\n\n  /** Whether the slide-toggle is required. */\n  @Input({transform: booleanAttribute}) required: boolean;\n\n  // TODO(crisbeto): this should be a ThemePalette, but some internal apps were abusing\n  // the lack of type checking previously and assigning random strings.\n  /**\n   * Theme color of the slide toggle. This API is supported in M2 themes only,\n   * it has no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/slide-toggle/styling.\n   *\n   * For information on applying color variants in M3, see\n   * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n   */\n  @Input() color: string | undefined;\n\n  /** Whether the slide toggle is disabled. */\n  @Input({transform: booleanAttribute}) disabled: boolean = false;\n\n  /** Whether the slide toggle has a ripple. */\n  @Input({transform: booleanAttribute}) disableRipple: boolean = false;\n\n  /** Tabindex of slide toggle. */\n  @Input({transform: (value: unknown) => (value == null ? 0 : numberAttribute(value))})\n  tabIndex: number = 0;\n\n  /** Whether the slide-toggle element is checked or not. */\n  @Input({transform: booleanAttribute})\n  get checked(): boolean {\n    return this._checked;\n  }\n  set checked(value: boolean) {\n    this._checked = value;\n    this._changeDetectorRef.markForCheck();\n  }\n\n  /** Whether to hide the icon inside of the slide toggle. */\n  @Input({transform: booleanAttribute}) hideIcon: boolean;\n\n  /** Whether the slide toggle should remain interactive when it is disabled. */\n  @Input({transform: booleanAttribute}) disabledInteractive: boolean;\n\n  /** An event will be dispatched each time the slide-toggle changes its value. */\n  @Output() readonly change = new EventEmitter<MatSlideToggleChange>();\n\n  /**\n   * An event will be dispatched each time the slide-toggle input is toggled.\n   * This event is always emitted when the user toggles the slide toggle, but this does not mean\n   * the slide toggle's value has changed.\n   */\n  @Output() readonly toggleChange: EventEmitter<void> = new EventEmitter<void>();\n\n  /** Returns the unique id for the visual hidden input. */\n  get inputId(): string {\n    return `${this.id || this._uniqueId}-input`;\n  }\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    inject(_CdkPrivateStyleLoader).load(_StructuralStylesLoader);\n    const tabIndex = inject(new HostAttributeToken('tabindex'), {optional: true});\n    const defaults = this.defaults;\n    const animationMode = inject(ANIMATION_MODULE_TYPE, {optional: true});\n\n    this.tabIndex = tabIndex == null ? 0 : parseInt(tabIndex) || 0;\n    this.color = defaults.color || 'accent';\n    this._noopAnimations = animationMode === 'NoopAnimations';\n    this.id = this._uniqueId = inject(_IdGenerator).getId('mat-mdc-slide-toggle-');\n    this.hideIcon = defaults.hideIcon ?? false;\n    this.disabledInteractive = defaults.disabledInteractive ?? false;\n    this._labelId = this._uniqueId + '-label';\n  }\n\n  ngAfterContentInit() {\n    this._focusMonitor.monitor(this._elementRef, true).subscribe(focusOrigin => {\n      if (focusOrigin === 'keyboard' || focusOrigin === 'program') {\n        this._focused = true;\n        this._changeDetectorRef.markForCheck();\n      } else if (!focusOrigin) {\n        // When a focused element becomes disabled, the browser *immediately* fires a blur event.\n        // Angular does not expect events to be raised during change detection, so any state\n        // change (such as a form control's ng-touched) will cause a changed-after-checked error.\n        // See https://github.com/angular/angular/issues/17793. To work around this, we defer\n        // telling the form control it has been touched until the next tick.\n        Promise.resolve().then(() => {\n          this._focused = false;\n          this._onTouched();\n          this._changeDetectorRef.markForCheck();\n        });\n      }\n    });\n  }\n\n  ngOnChanges(changes: SimpleChanges): void {\n    if (changes['required']) {\n      this._validatorOnChange();\n    }\n  }\n\n  ngOnDestroy() {\n    this._focusMonitor.stopMonitoring(this._elementRef);\n  }\n\n  /** Implemented as part of ControlValueAccessor. */\n  writeValue(value: any): void {\n    this.checked = !!value;\n  }\n\n  /** Implemented as part of ControlValueAccessor. */\n  registerOnChange(fn: any): void {\n    this._onChange = fn;\n  }\n\n  /** Implemented as part of ControlValueAccessor. */\n  registerOnTouched(fn: any): void {\n    this._onTouched = fn;\n  }\n\n  /** Implemented as a part of Validator. */\n  validate(control: AbstractControl<boolean>): ValidationErrors | null {\n    return this.required && control.value !== true ? {'required': true} : null;\n  }\n\n  /** Implemented as a part of Validator. */\n  registerOnValidatorChange(fn: () => void): void {\n    this._validatorOnChange = fn;\n  }\n\n  /** Implemented as a part of ControlValueAccessor. */\n  setDisabledState(isDisabled: boolean): void {\n    this.disabled = isDisabled;\n    this._changeDetectorRef.markForCheck();\n  }\n\n  /** Toggles the checked state of the slide-toggle. */\n  toggle(): void {\n    this.checked = !this.checked;\n    this._onChange(this.checked);\n  }\n\n  /**\n   * Emits a change event on the `change` output. Also notifies the FormControl about the change.\n   */\n  protected _emitChangeEvent() {\n    this._onChange(this.checked);\n    this.change.emit(this._createChangeEvent(this.checked));\n  }\n\n  /** Method being called whenever the underlying button is clicked. */\n  _handleClick() {\n    if (!this.disabled) {\n      this.toggleChange.emit();\n\n      if (!this.defaults.disableToggleValue) {\n        this.checked = !this.checked;\n        this._onChange(this.checked);\n        this.change.emit(new MatSlideToggleChange(this, this.checked));\n      }\n    }\n  }\n\n  _getAriaLabelledBy() {\n    if (this.ariaLabelledby) {\n      return this.ariaLabelledby;\n    }\n\n    // Even though we have a `label` element with a `for` pointing to the button, we need the\n    // `aria-labelledby`, because the button gets flagged as not having a label by tools like axe.\n    return this.ariaLabel ? null : this._labelId;\n  }\n}\n", "<div mat-internal-form-field [labelPosition]=\"labelPosition\">\n  <button\n    class=\"mdc-switch\"\n    role=\"switch\"\n    type=\"button\"\n    [class.mdc-switch--selected]=\"checked\"\n    [class.mdc-switch--unselected]=\"!checked\"\n    [class.mdc-switch--checked]=\"checked\"\n    [class.mdc-switch--disabled]=\"disabled\"\n    [class.mat-mdc-slide-toggle-disabled-interactive]=\"disabledInteractive\"\n    [tabIndex]=\"disabled && !disabledInteractive ? -1 : tabIndex\"\n    [disabled]=\"disabled && !disabledInteractive\"\n    [attr.id]=\"buttonId\"\n    [attr.name]=\"name\"\n    [attr.aria-label]=\"ariaLabel\"\n    [attr.aria-labelledby]=\"_getAriaLabelledBy()\"\n    [attr.aria-describedby]=\"ariaDescribedby\"\n    [attr.aria-required]=\"required || null\"\n    [attr.aria-checked]=\"checked\"\n    [attr.aria-disabled]=\"disabled && disabledInteractive ? 'true' : null\"\n    (click)=\"_handleClick()\"\n    #switch>\n    <span class=\"mdc-switch__track\"></span>\n    <span class=\"mdc-switch__handle-track\">\n      <span class=\"mdc-switch__handle\">\n        <span class=\"mdc-switch__shadow\">\n          <span class=\"mdc-elevation-overlay\"></span>\n        </span>\n        <span class=\"mdc-switch__ripple\">\n          <span class=\"mat-mdc-slide-toggle-ripple mat-focus-indicator\" mat-ripple\n            [matRippleTrigger]=\"switch\"\n            [matRippleDisabled]=\"disableRipple || disabled\"\n            [matRippleCentered]=\"true\"></span>\n        </span>\n        @if (!hideIcon) {\n          <span class=\"mdc-switch__icons\">\n            <svg\n              class=\"mdc-switch__icon mdc-switch__icon--on\"\n              viewBox=\"0 0 24 24\"\n              aria-hidden=\"true\">\n              <path d=\"M19.69,5.23L8.96,15.96l-4.23-4.23L2.96,13.5l6,6L21.46,7L19.69,5.23z\" />\n            </svg>\n            <svg\n              class=\"mdc-switch__icon mdc-switch__icon--off\"\n              viewBox=\"0 0 24 24\"\n              aria-hidden=\"true\">\n              <path d=\"M20 13H4v-2h16v2z\" />\n            </svg>\n          </span>\n        }\n      </span>\n    </span>\n  </button>\n\n  <!--\n    Clicking on the label will trigger another click event from the button.\n    Stop propagation here so other listeners further up in the DOM don't execute twice.\n  -->\n  <label class=\"mdc-label\" [for]=\"buttonId\" [attr.id]=\"_labelId\" (click)=\"$event.stopPropagation()\">\n    <ng-content></ng-content>\n  </label>\n</div>\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Directive, forwardRef, Provider} from '@angular/core';\nimport {CheckboxRequiredValidator, NG_VALIDATORS} from '@angular/forms';\n\n/**\n * @deprecated No longer used, `MatCheckbox` implements required validation directly.\n * @breaking-change 19.0.0\n */\nexport const MAT_SLIDE_TOGGLE_REQUIRED_VALIDATOR: Provider = {\n  provide: NG_VALIDATORS,\n  useExisting: forwardRef(() => MatSlideToggleRequiredValidator),\n  multi: true,\n};\n\n/**\n * Validator for Material slide-toggle components with the required attribute in a\n * template-driven form. The default validator for required form controls asserts\n * that the control value is not undefined but that is not appropriate for a slide-toggle\n * where the value is always defined.\n *\n * Required slide-toggle form controls are valid when checked.\n *\n * @deprecated No longer used, `MatCheckbox` implements required validation directly.\n * @breaking-change 19.0.0\n */\n@Directive({\n  selector: `mat-slide-toggle[required][formControlName],\n             mat-slide-toggle[required][formControl], mat-slide-toggle[required][ngModel]`,\n  providers: [MAT_SLIDE_TOGGLE_REQUIRED_VALIDATOR],\n})\nexport class MatSlideToggleRequiredValidator extends CheckboxRequiredValidator {}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {NgModule} from '@angular/core';\nimport {MatCommonModule} from '../core';\nimport {MatSlideToggle} from './slide-toggle';\nimport {MatSlideToggleRequiredValidator} from './slide-toggle-required-validator';\n\n/**\n * @deprecated No longer used, `MatSlideToggle` implements required validation directly.\n * @breaking-change 19.0.0\n */\n@NgModule({\n  imports: [MatSlideToggleRequiredValidator],\n  exports: [MatSlideToggleRequiredValidator],\n})\nexport class _MatSlideToggleRequiredValidatorModule {}\n\n@NgModule({\n  imports: [MatSlideToggle, MatCommonModule],\n  exports: [MatSlideToggle, MatCommonModule],\n})\nexport class MatSlideToggleModule {}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AA+BA;MACa,gCAAgC,GAAG,IAAI,cAAc,CAChE,kCAAkC,EAClC;AACE,IAAA,UAAU,EAAE,MAAM;AAClB,IAAA,OAAO,EAAE,OAAO,EAAC,kBAAkB,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,mBAAmB,EAAE,KAAK,EAAC,CAAC;AAC1F,CAAA;;ACQH;;;AAGG;AACU,MAAA,+BAA+B,GAAG;AAC7C,IAAA,OAAO,EAAE,iBAAiB;AAC1B,IAAA,WAAW,EAAE,UAAU,CAAC,MAAM,cAAc,CAAC;AAC7C,IAAA,KAAK,EAAE,IAAI;;AAGb;MACa,oBAAoB,CAAA;AAGtB,IAAA,MAAA;AAEA,IAAA,OAAA;AAJT,IAAA,WAAA;;IAES,MAAsB;;IAEtB,OAAgB,EAAA;QAFhB,IAAM,CAAA,MAAA,GAAN,MAAM;QAEN,IAAO,CAAA,OAAA,GAAP,OAAO;;AAEjB;MAgCY,cAAc,CAAA;AAGjB,IAAA,WAAW,GAAG,MAAM,CAAC,UAAU,CAAC;AAC9B,IAAA,aAAa,GAAG,MAAM,CAAC,YAAY,CAAC;AACpC,IAAA,kBAAkB,GAAG,MAAM,CAAC,iBAAiB,CAAC;AACxD,IAAA,QAAQ,GAAG,MAAM,CAA+B,gCAAgC,CAAC;AAEzE,IAAA,SAAS,GAAG,CAAC,CAAM,KAAI,GAAG;AAC1B,IAAA,UAAU,GAAG,MAAK,GAAG;AACrB,IAAA,kBAAkB,GAAG,MAAK,GAAG;AAE7B,IAAA,SAAS;IACT,QAAQ,GAAY,KAAK;AAEzB,IAAA,kBAAkB,CAAC,SAAkB,EAAA;AAC3C,QAAA,OAAO,IAAI,oBAAoB,CAAC,IAAI,EAAE,SAAS,CAAC;;;AAIlD,IAAA,QAAQ;;AAGR,IAAA,IAAI,QAAQ,GAAA;QACV,OAAO,CAAA,EAAG,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,SAAS,CAAA,OAAA,CAAS;;;AAIzB,IAAA,cAAc;;IAGnC,KAAK,GAAA;AACH,QAAA,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,KAAK,EAAE;;;AAG3C,IAAA,eAAe;;AAGf,IAAA,QAAQ;;IAGC,IAAI,GAAkB,IAAI;;AAG1B,IAAA,EAAE;;IAGF,aAAa,GAAuB,OAAO;;IAG/B,SAAS,GAAkB,IAAI;;IAG1B,cAAc,GAAkB,IAAI;;AAGnC,IAAA,eAAe;;AAGJ,IAAA,QAAQ;;;AAI9C;;;;;;AAMG;AACM,IAAA,KAAK;;IAGwB,QAAQ,GAAY,KAAK;;IAGzB,aAAa,GAAY,KAAK;;IAIpE,QAAQ,GAAW,CAAC;;AAGpB,IAAA,IACI,OAAO,GAAA;QACT,OAAO,IAAI,CAAC,QAAQ;;IAEtB,IAAI,OAAO,CAAC,KAAc,EAAA;AACxB,QAAA,IAAI,CAAC,QAAQ,GAAG,KAAK;AACrB,QAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE;;;AAIF,IAAA,QAAQ;;AAGR,IAAA,mBAAmB;;AAGtC,IAAA,MAAM,GAAG,IAAI,YAAY,EAAwB;AAEpE;;;;AAIG;AACgB,IAAA,YAAY,GAAuB,IAAI,YAAY,EAAQ;;AAG9E,IAAA,IAAI,OAAO,GAAA;QACT,OAAO,CAAA,EAAG,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,SAAS,CAAA,MAAA,CAAQ;;AAK7C,IAAA,WAAA,GAAA;QACE,MAAM,CAAC,sBAAsB,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC;AAC5D,QAAA,MAAM,QAAQ,GAAG,MAAM,CAAC,IAAI,kBAAkB,CAAC,UAAU,CAAC,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;AAC7E,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ;AAC9B,QAAA,MAAM,aAAa,GAAG,MAAM,CAAC,qBAAqB,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;AAErE,QAAA,IAAI,CAAC,QAAQ,GAAG,QAAQ,IAAI,IAAI,GAAG,CAAC,GAAG,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC;QAC9D,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,IAAI,QAAQ;AACvC,QAAA,IAAI,CAAC,eAAe,GAAG,aAAa,KAAK,gBAAgB;AACzD,QAAA,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC,YAAY,CAAC,CAAC,KAAK,CAAC,uBAAuB,CAAC;QAC9E,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC,QAAQ,IAAI,KAAK;QAC1C,IAAI,CAAC,mBAAmB,GAAG,QAAQ,CAAC,mBAAmB,IAAI,KAAK;QAChE,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,SAAS,GAAG,QAAQ;;IAG3C,kBAAkB,GAAA;AAChB,QAAA,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC,SAAS,CAAC,WAAW,IAAG;YACzE,IAAI,WAAW,KAAK,UAAU,IAAI,WAAW,KAAK,SAAS,EAAE;AAC3D,gBAAA,IAAI,CAAC,QAAQ,GAAG,IAAI;AACpB,gBAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE;;iBACjC,IAAI,CAAC,WAAW,EAAE;;;;;;AAMvB,gBAAA,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,MAAK;AAC1B,oBAAA,IAAI,CAAC,QAAQ,GAAG,KAAK;oBACrB,IAAI,CAAC,UAAU,EAAE;AACjB,oBAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE;AACxC,iBAAC,CAAC;;AAEN,SAAC,CAAC;;AAGJ,IAAA,WAAW,CAAC,OAAsB,EAAA;AAChC,QAAA,IAAI,OAAO,CAAC,UAAU,CAAC,EAAE;YACvB,IAAI,CAAC,kBAAkB,EAAE;;;IAI7B,WAAW,GAAA;QACT,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,IAAI,CAAC,WAAW,CAAC;;;AAIrD,IAAA,UAAU,CAAC,KAAU,EAAA;AACnB,QAAA,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC,KAAK;;;AAIxB,IAAA,gBAAgB,CAAC,EAAO,EAAA;AACtB,QAAA,IAAI,CAAC,SAAS,GAAG,EAAE;;;AAIrB,IAAA,iBAAiB,CAAC,EAAO,EAAA;AACvB,QAAA,IAAI,CAAC,UAAU,GAAG,EAAE;;;AAItB,IAAA,QAAQ,CAAC,OAAiC,EAAA;QACxC,OAAO,IAAI,CAAC,QAAQ,IAAI,OAAO,CAAC,KAAK,KAAK,IAAI,GAAG,EAAC,UAAU,EAAE,IAAI,EAAC,GAAG,IAAI;;;AAI5E,IAAA,yBAAyB,CAAC,EAAc,EAAA;AACtC,QAAA,IAAI,CAAC,kBAAkB,GAAG,EAAE;;;AAI9B,IAAA,gBAAgB,CAAC,UAAmB,EAAA;AAClC,QAAA,IAAI,CAAC,QAAQ,GAAG,UAAU;AAC1B,QAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE;;;IAIxC,MAAM,GAAA;AACJ,QAAA,IAAI,CAAC,OAAO,GAAG,CAAC,IAAI,CAAC,OAAO;AAC5B,QAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC;;AAG9B;;AAEG;IACO,gBAAgB,GAAA;AACxB,QAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC;AAC5B,QAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;;;IAIzD,YAAY,GAAA;AACV,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AAClB,YAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE;AAExB,YAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,kBAAkB,EAAE;AACrC,gBAAA,IAAI,CAAC,OAAO,GAAG,CAAC,IAAI,CAAC,OAAO;AAC5B,gBAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC;AAC5B,gBAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,oBAAoB,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;;;;IAKpE,kBAAkB,GAAA;AAChB,QAAA,IAAI,IAAI,CAAC,cAAc,EAAE;YACvB,OAAO,IAAI,CAAC,cAAc;;;;AAK5B,QAAA,OAAO,IAAI,CAAC,SAAS,GAAG,IAAI,GAAG,IAAI,CAAC,QAAQ;;uGAjOnC,cAAc,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAd,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,cAAc,EA2DN,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,kBAAA,EAAA,MAAA,EAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,EAAA,IAAA,EAAA,aAAA,EAAA,eAAA,EAAA,SAAA,EAAA,CAAA,YAAA,EAAA,WAAA,CAAA,EAAA,cAAA,EAAA,CAAA,iBAAA,EAAA,gBAAA,CAAA,EAAA,eAAA,EAAA,CAAA,kBAAA,EAAA,iBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAAA,gBAAgB,CAchB,EAAA,KAAA,EAAA,OAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAAA,gBAAgB,qDAGhB,gBAAgB,CAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAGhB,CAAC,KAAc,MAAM,KAAK,IAAI,IAAI,GAAG,CAAC,GAAG,eAAe,CAAC,KAAK,CAAC,CAAC,mCAIhE,gBAAgB,CAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAUhB,gBAAgB,CAAA,EAAA,mBAAA,EAAA,CAAA,qBAAA,EAAA,qBAAA,EAGhB,gBAAgB,CA1GxB,EAAA,EAAA,OAAA,EAAA,EAAA,MAAA,EAAA,QAAA,EAAA,YAAA,EAAA,cAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,IAAA,EAAA,IAAA,EAAA,eAAA,EAAA,MAAA,EAAA,iBAAA,EAAA,MAAA,EAAA,WAAA,EAAA,MAAA,EAAA,sBAAA,EAAA,MAAA,EAAA,oCAAA,EAAA,UAAA,EAAA,oCAAA,EAAA,SAAA,EAAA,+BAAA,EAAA,iBAAA,EAAA,OAAA,EAAA,iCAAA,EAAA,EAAA,cAAA,EAAA,sBAAA,EAAA,EAAA,SAAA,EAAA;YACT,+BAA+B;AAC/B,YAAA;AACE,gBAAA,OAAO,EAAE,aAAa;AACtB,gBAAA,WAAW,EAAE,cAAc;AAC3B,gBAAA,KAAK,EAAE,IAAI;AACZ,aAAA;AACF,SAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,gBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,QAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EC5FH,g7EA8DA,EAAA,MAAA,EAAA,CAAA,ulfAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,ED+BY,SAAS,EAAA,QAAA,EAAA,2BAAA,EAAA,MAAA,EAAA,CAAA,gBAAA,EAAA,oBAAA,EAAA,mBAAA,EAAA,iBAAA,EAAA,oBAAA,EAAA,mBAAA,EAAA,kBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,WAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAE,qBAAqB,EAAA,QAAA,EAAA,8BAAA,EAAA,MAAA,EAAA,CAAA,eAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAE/B,cAAc,EAAA,UAAA,EAAA,CAAA;kBA9B1B,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,kBAAkB,EAGtB,IAAA,EAAA;AACJ,wBAAA,OAAO,EAAE,sBAAsB;AAC/B,wBAAA,MAAM,EAAE,IAAI;;AAEZ,wBAAA,iBAAiB,EAAE,MAAM;AACzB,wBAAA,mBAAmB,EAAE,MAAM;AAC3B,wBAAA,aAAa,EAAE,MAAM;AACrB,wBAAA,wBAAwB,EAAE,MAAM;AAChC,wBAAA,sCAAsC,EAAE,UAAU;AAClD,wBAAA,sCAAsC,EAAE,SAAS;AACjD,wBAAA,iCAAiC,EAAE,iBAAiB;AACpD,wBAAA,SAAS,EAAE,6BAA6B;qBACzC,EACS,QAAA,EAAA,gBAAgB,iBACX,iBAAiB,CAAC,IAAI,EACpB,eAAA,EAAA,uBAAuB,CAAC,MAAM,EACpC,SAAA,EAAA;wBACT,+BAA+B;AAC/B,wBAAA;AACE,4BAAA,OAAO,EAAE,aAAa;AACtB,4BAAA,WAAW,EAAgB,cAAA;AAC3B,4BAAA,KAAK,EAAE,IAAI;AACZ,yBAAA;AACF,qBAAA,EAAA,OAAA,EACQ,CAAC,SAAS,EAAE,qBAAqB,CAAC,EAAA,QAAA,EAAA,g7EAAA,EAAA,MAAA,EAAA,CAAA,ulfAAA,CAAA,EAAA;wDA8BtB,cAAc,EAAA,CAAA;sBAAlC,SAAS;uBAAC,QAAQ;gBAaV,IAAI,EAAA,CAAA;sBAAZ;gBAGQ,EAAE,EAAA,CAAA;sBAAV;gBAGQ,aAAa,EAAA,CAAA;sBAArB;gBAGoB,SAAS,EAAA,CAAA;sBAA7B,KAAK;uBAAC,YAAY;gBAGO,cAAc,EAAA,CAAA;sBAAvC,KAAK;uBAAC,iBAAiB;gBAGG,eAAe,EAAA,CAAA;sBAAzC,KAAK;uBAAC,kBAAkB;gBAGa,QAAQ,EAAA,CAAA;sBAA7C,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBAW3B,KAAK,EAAA,CAAA;sBAAb;gBAGqC,QAAQ,EAAA,CAAA;sBAA7C,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBAGE,aAAa,EAAA,CAAA;sBAAlD,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBAIpC,QAAQ,EAAA,CAAA;sBADP,KAAK;uBAAC,EAAC,SAAS,EAAE,CAAC,KAAc,MAAM,KAAK,IAAI,IAAI,GAAG,CAAC,GAAG,eAAe,CAAC,KAAK,CAAC,CAAC,EAAC;gBAKhF,OAAO,EAAA,CAAA;sBADV,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBAUE,QAAQ,EAAA,CAAA;sBAA7C,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBAGE,mBAAmB,EAAA,CAAA;sBAAxD,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBAGjB,MAAM,EAAA,CAAA;sBAAxB;gBAOkB,YAAY,EAAA,CAAA;sBAA9B;;;AE9LH;;;AAGG;AACU,MAAA,mCAAmC,GAAa;AAC3D,IAAA,OAAO,EAAE,aAAa;AACtB,IAAA,WAAW,EAAE,UAAU,CAAC,MAAM,+BAA+B,CAAC;AAC9D,IAAA,KAAK,EAAE,IAAI;;AAGb;;;;;;;;;;AAUG;AAMG,MAAO,+BAAgC,SAAQ,yBAAyB,CAAA;uGAAjE,+BAA+B,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAA/B,+BAA+B,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,yIAAA,EAAA,SAAA,EAF/B,CAAC,mCAAmC,CAAC,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAErC,+BAA+B,EAAA,UAAA,EAAA,CAAA;kBAL3C,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,CAAA;AAC8E,yFAAA,CAAA;oBACxF,SAAS,EAAE,CAAC,mCAAmC,CAAC;AACjD,iBAAA;;;ACvBD;;;AAGG;MAKU,sCAAsC,CAAA;uGAAtC,sCAAsC,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;wGAAtC,sCAAsC,EAAA,OAAA,EAAA,CAHvC,+BAA+B,CAAA,EAAA,OAAA,EAAA,CAC/B,+BAA+B,CAAA,EAAA,CAAA;wGAE9B,sCAAsC,EAAA,CAAA;;2FAAtC,sCAAsC,EAAA,UAAA,EAAA,CAAA;kBAJlD,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACR,OAAO,EAAE,CAAC,+BAA+B,CAAC;oBAC1C,OAAO,EAAE,CAAC,+BAA+B,CAAC;AAC3C,iBAAA;;MAOY,oBAAoB,CAAA;uGAApB,oBAAoB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;AAApB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,oBAAoB,YAHrB,cAAc,EAAE,eAAe,CAC/B,EAAA,OAAA,EAAA,CAAA,cAAc,EAAE,eAAe,CAAA,EAAA,CAAA;AAE9B,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,oBAAoB,EAHrB,OAAA,EAAA,CAAA,cAAc,EAAE,eAAe,EACf,eAAe,CAAA,EAAA,CAAA;;2FAE9B,oBAAoB,EAAA,UAAA,EAAA,CAAA;kBAJhC,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,OAAO,EAAE,CAAC,cAAc,EAAE,eAAe,CAAC;AAC1C,oBAAA,OAAO,EAAE,CAAC,cAAc,EAAE,eAAe,CAAC;AAC3C,iBAAA;;;;;"}