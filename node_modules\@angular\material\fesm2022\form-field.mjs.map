{"version": 3, "file": "form-field.mjs", "sources": ["../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/form-field/form-field-animations.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/**\n * Animations used by the MatFormField.\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nexport const matFormFieldAnimations: {\n  readonly transitionMessages: any;\n} = {\n  // Represents:\n  // trigger('transitionMessages', [\n  //   // TODO(mmalerba): Use angular animations for label animation as well.\n  //   state('enter', style({opacity: 1, transform: 'translateY(0%)'})),\n  //   transition('void => enter', [\n  //     style({opacity: 0, transform: 'translateY(-5px)'}),\n  //     animate('300ms cubic-bezier(0.55, 0, 0.55, 0.2)'),\n  //   ]),\n  // ])\n\n  /** Animation that transitions the form field's error and hint messages. */\n  transitionMessages: {\n    type: 7,\n    name: 'transitionMessages',\n    definitions: [\n      {\n        type: 0,\n        name: 'enter',\n        styles: {\n          type: 6,\n          styles: {opacity: 1, transform: 'translateY(0%)'},\n          offset: null,\n        },\n      },\n      {\n        type: 1,\n        expr: 'void => enter',\n        animation: [\n          {type: 6, styles: {opacity: 0, transform: 'translateY(-5px)'}, offset: null},\n          {type: 4, styles: null, timings: '300ms cubic-bezier(0.55, 0, 0.55, 0.2)'},\n        ],\n        options: null,\n      },\n    ],\n    options: {},\n  },\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAQA;;;;;AAKG;AACU,MAAA,sBAAsB,GAE/B;;;;;;;;;;;AAYF,IAAA,kBAAkB,EAAE;AAClB,QAAA,IAAI,EAAE,CAAC;AACP,QAAA,IAAI,EAAE,oBAAoB;AAC1B,QAAA,WAAW,EAAE;AACX,YAAA;AACE,gBAAA,IAAI,EAAE,CAAC;AACP,gBAAA,IAAI,EAAE,OAAO;AACb,gBAAA,MAAM,EAAE;AACN,oBAAA,IAAI,EAAE,CAAC;oBACP,MAAM,EAAE,EAAC,OAAO,EAAE,CAAC,EAAE,SAAS,EAAE,gBAAgB,EAAC;AACjD,oBAAA,MAAM,EAAE,IAAI;AACb,iBAAA;AACF,aAAA;AACD,YAAA;AACE,gBAAA,IAAI,EAAE,CAAC;AACP,gBAAA,IAAI,EAAE,eAAe;AACrB,gBAAA,SAAS,EAAE;AACT,oBAAA,EAAC,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,EAAC,OAAO,EAAE,CAAC,EAAE,SAAS,EAAE,kBAAkB,EAAC,EAAE,MAAM,EAAE,IAAI,EAAC;oBAC5E,EAAC,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,wCAAwC,EAAC;AAC3E,iBAAA;AACD,gBAAA,OAAO,EAAE,IAAI;AACd,aAAA;AACF,SAAA;AACD,QAAA,OAAO,EAAE,EAAE;AACZ,KAAA;;;;;"}