{"version": 3, "file": "timepicker.mjs", "sources": ["../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/timepicker/util.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/timepicker/timepicker.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/timepicker/timepicker.html", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/timepicker/timepicker-input.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/timepicker/timepicker-toggle.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/timepicker/timepicker-toggle.html", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/timepicker/timepicker-module.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {InjectionToken} from '@angular/core';\nimport {DateAdapter, MatDateFormats} from '../core';\n\n/** Pattern that interval strings have to match. */\nconst INTERVAL_PATTERN = /^(\\d*\\.?\\d+)\\s*(h|hour|hours|m|min|minute|minutes|s|second|seconds)?$/i;\n\n/**\n * Object that can be used to configure the default options for the timepicker component.\n */\nexport interface MatTimepickerConfig {\n  /** Default interval for all time pickers. */\n  interval?: string | number;\n\n  /** Whether ripples inside the timepicker should be disabled by default. */\n  disableRipple?: boolean;\n}\n\n/**\n * Injection token that can be used to configure the default options for the timepicker component.\n */\nexport const MAT_TIMEPICKER_CONFIG = new InjectionToken<MatTimepickerConfig>(\n  'MAT_TIMEPICKER_CONFIG',\n);\n\n/**\n * Time selection option that can be displayed within a `mat-timepicker`.\n */\nexport interface MatTimepickerOption<D = unknown> {\n  /** Date value of the option. */\n  value: D;\n\n  /** Label to show to the user. */\n  label: string;\n}\n\n/** Parses an interval value into seconds. */\nexport function parseInterval(value: number | string | null): number | null {\n  let result: number;\n\n  if (value === null) {\n    return null;\n  } else if (typeof value === 'number') {\n    result = value;\n  } else {\n    if (value.trim().length === 0) {\n      return null;\n    }\n\n    const parsed = value.match(INTERVAL_PATTERN);\n    const amount = parsed ? parseFloat(parsed[1]) : null;\n    const unit = parsed?.[2]?.toLowerCase() || null;\n\n    if (!parsed || amount === null || isNaN(amount)) {\n      return null;\n    }\n\n    if (unit === 'h' || unit === 'hour' || unit === 'hours') {\n      result = amount * 3600;\n    } else if (unit === 'm' || unit === 'min' || unit === 'minute' || unit === 'minutes') {\n      result = amount * 60;\n    } else {\n      result = amount;\n    }\n  }\n\n  return result;\n}\n\n/**\n * Generates the options to show in a timepicker.\n * @param adapter Date adapter to be used to generate the options.\n * @param formats Formatting config to use when displaying the options.\n * @param min Time from which to start generating the options.\n * @param max Time at which to stop generating the options.\n * @param interval Amount of seconds between each option.\n */\nexport function generateOptions<D>(\n  adapter: DateAdapter<D>,\n  formats: MatDateFormats,\n  min: D,\n  max: D,\n  interval: number,\n): MatTimepickerOption<D>[] {\n  const options: MatTimepickerOption<D>[] = [];\n  let current = adapter.compareTime(min, max) < 1 ? min : max;\n\n  while (\n    adapter.sameDate(current, min) &&\n    adapter.compareTime(current, max) < 1 &&\n    adapter.isValid(current)\n  ) {\n    options.push({value: current, label: adapter.format(current, formats.display.timeOptionLabel)});\n    current = adapter.addSeconds(current, interval);\n  }\n\n  return options;\n}\n\n/** Checks whether a date adapter is set up correctly for use with the timepicker. */\nexport function validateAdapter(\n  adapter: DateAdapter<unknown> | null,\n  formats: MatDateFormats | null,\n) {\n  function missingAdapterError(provider: string) {\n    return Error(\n      `MatTimepicker: No provider found for ${provider}. You must add one of the following ` +\n        `to your app config: provideNativeDateAdapter, provideDateFnsAdapter, ` +\n        `provideLuxonDateAdapter, provideMomentDateAdapter, or provide a custom implementation.`,\n    );\n  }\n\n  if (!adapter) {\n    throw missingAdapterError('DateAdapter');\n  }\n\n  if (!formats) {\n    throw missingAdapterError('MAT_DATE_FORMATS');\n  }\n\n  if (\n    formats.display.timeInput === undefined ||\n    formats.display.timeOptionLabel === undefined ||\n    formats.parse.timeInput === undefined\n  ) {\n    throw new Error(\n      'MatTimepicker: Incomplete `MAT_DATE_FORMATS` has been provided. ' +\n        '`MAT_DATE_FORMATS` must provide `display.timeInput`, `display.timeOptionLabel` ' +\n        'and `parse.timeInput` formats in order to be compatible with MatTimepicker.',\n    );\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  afterNextRender,\n  AfterRenderRef,\n  ANIMATION_MODULE_TYPE,\n  booleanAttribute,\n  ChangeDetectionStrategy,\n  Component,\n  computed,\n  effect,\n  ElementRef,\n  inject,\n  InjectionToken,\n  Injector,\n  input,\n  InputSignal,\n  InputSignalWithTransform,\n  OnDestroy,\n  output,\n  OutputEmitterRef,\n  Signal,\n  signal,\n  TemplateRef,\n  untracked,\n  viewChild,\n  viewChildren,\n  ViewContainerRef,\n  ViewEncapsulation,\n} from '@angular/core';\nimport {\n  DateAdapter,\n  MAT_DATE_FORMATS,\n  MAT_OPTION_PARENT_COMPONENT,\n  MatOption,\n  MatOptionParentComponent,\n} from '../core';\nimport {Directionality} from '@angular/cdk/bidi';\nimport {Overlay, OverlayRef, ScrollStrategy} from '@angular/cdk/overlay';\nimport {TemplatePortal} from '@angular/cdk/portal';\nimport {_getEventTarget} from '@angular/cdk/platform';\nimport {ENTER, ESCAPE, hasModifierKey, TAB} from '@angular/cdk/keycodes';\nimport {_IdGenerator, ActiveDescendantKeyManager} from '@angular/cdk/a11y';\nimport type {MatTimepickerInput} from './timepicker-input';\nimport {\n  generateOptions,\n  MAT_TIMEPICKER_CONFIG,\n  MatTimepickerOption,\n  parseInterval,\n  validateAdapter,\n} from './util';\nimport {Subscription} from 'rxjs';\n\n/** Event emitted when a value is selected in the timepicker. */\nexport interface MatTimepickerSelected<D> {\n  value: D;\n  source: MatTimepicker<D>;\n}\n\n/** Injection token used to configure the behavior of the timepicker dropdown while scrolling. */\nexport const MAT_TIMEPICKER_SCROLL_STRATEGY = new InjectionToken<() => ScrollStrategy>(\n  'MAT_TIMEPICKER_SCROLL_STRATEGY',\n  {\n    providedIn: 'root',\n    factory: () => {\n      const overlay = inject(Overlay);\n      return () => overlay.scrollStrategies.reposition();\n    },\n  },\n);\n\n/**\n * Renders out a listbox that can be used to select a time of day.\n * Intended to be used together with `MatTimepickerInput`.\n */\n@Component({\n  selector: 'mat-timepicker',\n  exportAs: 'matTimepicker',\n  templateUrl: 'timepicker.html',\n  styleUrl: 'timepicker.css',\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  encapsulation: ViewEncapsulation.None,\n  imports: [MatOption],\n  providers: [\n    {\n      provide: MAT_OPTION_PARENT_COMPONENT,\n      useExisting: MatTimepicker,\n    },\n  ],\n})\nexport class MatTimepicker<D> implements OnDestroy, MatOptionParentComponent {\n  private _overlay = inject(Overlay);\n  private _dir = inject(Directionality, {optional: true});\n  private _viewContainerRef = inject(ViewContainerRef);\n  private _injector = inject(Injector);\n  private _defaultConfig = inject(MAT_TIMEPICKER_CONFIG, {optional: true});\n  private _dateAdapter = inject<DateAdapter<D>>(DateAdapter, {optional: true})!;\n  private _dateFormats = inject(MAT_DATE_FORMATS, {optional: true})!;\n  private _scrollStrategyFactory = inject(MAT_TIMEPICKER_SCROLL_STRATEGY);\n  protected _animationsDisabled =\n    inject(ANIMATION_MODULE_TYPE, {optional: true}) === 'NoopAnimations';\n\n  private _isOpen = signal(false);\n  private _activeDescendant = signal<string | null>(null);\n\n  private _input = signal<MatTimepickerInput<D> | null>(null);\n  private _overlayRef: OverlayRef | null = null;\n  private _portal: TemplatePortal<unknown> | null = null;\n  private _optionsCacheKey: string | null = null;\n  private _localeChanges: Subscription;\n  private _onOpenRender: AfterRenderRef | null = null;\n\n  protected _panelTemplate = viewChild.required<TemplateRef<unknown>>('panelTemplate');\n  protected _timeOptions: readonly MatTimepickerOption<D>[] = [];\n  protected _options = viewChildren(MatOption);\n\n  private _keyManager = new ActiveDescendantKeyManager(this._options, this._injector)\n    .withHomeAndEnd(true)\n    .withPageUpDown(true)\n    .withVerticalOrientation(true);\n\n  /**\n   * Interval between each option in the timepicker. The value can either be an amount of\n   * seconds (e.g. 90) or a number with a unit (e.g. 45m). Supported units are `s` for seconds,\n   * `m` for minutes or `h` for hours.\n   */\n  readonly interval: InputSignalWithTransform<number | null, number | string | null> = input(\n    parseInterval(this._defaultConfig?.interval || null),\n    {transform: parseInterval},\n  );\n\n  /**\n   * Array of pre-defined options that the user can select from, as an alternative to using the\n   * `interval` input. An error will be thrown if both `options` and `interval` are specified.\n   */\n  readonly options: InputSignal<readonly MatTimepickerOption<D>[] | null> = input<\n    readonly MatTimepickerOption<D>[] | null\n  >(null);\n\n  /** Whether the timepicker is open. */\n  readonly isOpen: Signal<boolean> = this._isOpen.asReadonly();\n\n  /** Emits when the user selects a time. */\n  readonly selected: OutputEmitterRef<MatTimepickerSelected<D>> = output();\n\n  /** Emits when the timepicker is opened. */\n  readonly opened: OutputEmitterRef<void> = output();\n\n  /** Emits when the timepicker is closed. */\n  readonly closed: OutputEmitterRef<void> = output();\n\n  /** ID of the active descendant option. */\n  readonly activeDescendant: Signal<string | null> = this._activeDescendant.asReadonly();\n\n  /** Unique ID of the timepicker's panel */\n  readonly panelId: string = inject(_IdGenerator).getId('mat-timepicker-panel-');\n\n  /** Whether ripples within the timepicker should be disabled. */\n  readonly disableRipple: InputSignalWithTransform<boolean, unknown> = input(\n    this._defaultConfig?.disableRipple ?? false,\n    {\n      transform: booleanAttribute,\n    },\n  );\n\n  /** ARIA label for the timepicker panel. */\n  readonly ariaLabel: InputSignal<string | null> = input<string | null>(null, {\n    alias: 'aria-label',\n  });\n\n  /** ID of the label element for the timepicker panel. */\n  readonly ariaLabelledby: InputSignal<string | null> = input<string | null>(null, {\n    alias: 'aria-labelledby',\n  });\n\n  /** Whether the timepicker is currently disabled. */\n  readonly disabled: Signal<boolean> = computed(() => !!this._input()?.disabled());\n\n  constructor() {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      validateAdapter(this._dateAdapter, this._dateFormats);\n\n      effect(() => {\n        const options = this.options();\n        const interval = this.interval();\n\n        if (options !== null && interval !== null) {\n          throw new Error(\n            'Cannot specify both the `options` and `interval` inputs at the same time',\n          );\n        } else if (options?.length === 0) {\n          throw new Error('Value of `options` input cannot be an empty array');\n        }\n      });\n    }\n\n    // Since the panel ID is static, we can set it once without having to maintain a host binding.\n    const element = inject<ElementRef<HTMLElement>>(ElementRef);\n    element.nativeElement.setAttribute('mat-timepicker-panel-id', this.panelId);\n    this._handleLocaleChanges();\n    this._handleInputStateChanges();\n    this._keyManager.change.subscribe(() =>\n      this._activeDescendant.set(this._keyManager.activeItem?.id || null),\n    );\n  }\n\n  /** Opens the timepicker. */\n  open(): void {\n    const input = this._input();\n\n    if (!input) {\n      return;\n    }\n\n    // Focus should already be on the input, but this call is in case the timepicker is opened\n    // programmatically. We need to call this even if the timepicker is already open, because\n    // the user might be clicking the toggle.\n    input.focus();\n\n    if (this._isOpen()) {\n      return;\n    }\n\n    this._isOpen.set(true);\n    this._generateOptions();\n    const overlayRef = this._getOverlayRef();\n    overlayRef.updateSize({width: input.getOverlayOrigin().nativeElement.offsetWidth});\n    this._portal ??= new TemplatePortal(this._panelTemplate(), this._viewContainerRef);\n\n    // We need to check this in case `isOpen` was flipped, but change detection hasn't\n    // had a chance to run yet. See https://github.com/angular/components/issues/30637\n    if (!overlayRef.hasAttached()) {\n      overlayRef.attach(this._portal);\n    }\n\n    this._onOpenRender?.destroy();\n    this._onOpenRender = afterNextRender(\n      () => {\n        const options = this._options();\n        this._syncSelectedState(input.value(), options, options[0]);\n        this._onOpenRender = null;\n      },\n      {injector: this._injector},\n    );\n\n    this.opened.emit();\n  }\n\n  /** Closes the timepicker. */\n  close(): void {\n    if (this._isOpen()) {\n      this._isOpen.set(false);\n      this.closed.emit();\n\n      if (this._animationsDisabled) {\n        this._overlayRef?.detach();\n      }\n    }\n  }\n\n  /** Registers an input with the timepicker. */\n  registerInput(input: MatTimepickerInput<D>): void {\n    const currentInput = this._input();\n\n    if (currentInput && input !== currentInput && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw new Error('MatTimepicker can only be registered with one input at a time');\n    }\n\n    this._input.set(input);\n  }\n\n  ngOnDestroy(): void {\n    this._keyManager.destroy();\n    this._localeChanges.unsubscribe();\n    this._onOpenRender?.destroy();\n    this._overlayRef?.dispose();\n  }\n\n  /** Selects a specific time value. */\n  protected _selectValue(option: MatOption<D>) {\n    this.close();\n    this._keyManager.setActiveItem(option);\n    this._options().forEach(current => {\n      // This is primarily here so we don't show two selected options while animating away.\n      if (current !== option) {\n        current.deselect(false);\n      }\n    });\n    this.selected.emit({value: option.value, source: this});\n    this._input()?.focus();\n  }\n\n  /** Gets the value of the `aria-labelledby` attribute. */\n  protected _getAriaLabelledby(): string | null {\n    if (this.ariaLabel()) {\n      return null;\n    }\n    return this.ariaLabelledby() || this._input()?._getLabelId() || null;\n  }\n\n  /** Handles animation events coming from the panel. */\n  protected _handleAnimationEnd(event: AnimationEvent) {\n    if (event.animationName === '_mat-timepicker-exit') {\n      this._overlayRef?.detach();\n    }\n  }\n\n  /** Creates an overlay reference for the timepicker panel. */\n  private _getOverlayRef(): OverlayRef {\n    if (this._overlayRef) {\n      return this._overlayRef;\n    }\n\n    const positionStrategy = this._overlay\n      .position()\n      .flexibleConnectedTo(this._input()!.getOverlayOrigin())\n      .withFlexibleDimensions(false)\n      .withPush(false)\n      .withTransformOriginOn('.mat-timepicker-panel')\n      .withPositions([\n        {\n          originX: 'start',\n          originY: 'bottom',\n          overlayX: 'start',\n          overlayY: 'top',\n        },\n        {\n          originX: 'start',\n          originY: 'top',\n          overlayX: 'start',\n          overlayY: 'bottom',\n          panelClass: 'mat-timepicker-above',\n        },\n      ]);\n\n    this._overlayRef = this._overlay.create({\n      positionStrategy,\n      scrollStrategy: this._scrollStrategyFactory(),\n      direction: this._dir || 'ltr',\n      hasBackdrop: false,\n    });\n\n    this._overlayRef.detachments().subscribe(() => this.close());\n    this._overlayRef.keydownEvents().subscribe(event => this._handleKeydown(event));\n    this._overlayRef.outsidePointerEvents().subscribe(event => {\n      const target = _getEventTarget(event) as HTMLElement;\n      const origin = this._input()?.getOverlayOrigin().nativeElement;\n\n      if (target && origin && target !== origin && !origin.contains(target)) {\n        this.close();\n      }\n    });\n\n    return this._overlayRef;\n  }\n\n  /** Generates the list of options from which the user can select.. */\n  private _generateOptions(): void {\n    // Default the interval to 30 minutes.\n    const interval = this.interval() ?? 30 * 60;\n    const options = this.options();\n\n    if (options !== null) {\n      this._timeOptions = options;\n    } else {\n      const input = this._input();\n      const adapter = this._dateAdapter;\n      const timeFormat = this._dateFormats.display.timeInput;\n      const min = input?.min() || adapter.setTime(adapter.today(), 0, 0, 0);\n      const max = input?.max() || adapter.setTime(adapter.today(), 23, 59, 0);\n      const cacheKey =\n        interval + '/' + adapter.format(min, timeFormat) + '/' + adapter.format(max, timeFormat);\n\n      // Don't re-generate the options if the inputs haven't changed.\n      if (cacheKey !== this._optionsCacheKey) {\n        this._optionsCacheKey = cacheKey;\n        this._timeOptions = generateOptions(adapter, this._dateFormats, min, max, interval);\n      }\n    }\n  }\n\n  /**\n   * Synchronizes the internal state of the component based on a specific selected date.\n   * @param value Currently selected date.\n   * @param options Options rendered out in the timepicker.\n   * @param fallback Option to set as active if no option is selected.\n   */\n  private _syncSelectedState(\n    value: D | null,\n    options: readonly MatOption[],\n    fallback: MatOption | null,\n  ): void {\n    let hasSelected = false;\n\n    for (const option of options) {\n      if (value && this._dateAdapter.sameTime(option.value, value)) {\n        option.select(false);\n        scrollOptionIntoView(option, 'center');\n        untracked(() => this._keyManager.setActiveItem(option));\n        hasSelected = true;\n      } else {\n        option.deselect(false);\n      }\n    }\n\n    // If no option was selected, we need to reset the key manager since\n    // it might be holding onto an option that no longer exists.\n    if (!hasSelected) {\n      if (fallback) {\n        untracked(() => this._keyManager.setActiveItem(fallback));\n        scrollOptionIntoView(fallback, 'center');\n      } else {\n        untracked(() => this._keyManager.setActiveItem(-1));\n      }\n    }\n  }\n\n  /** Handles keyboard events while the overlay is open. */\n  private _handleKeydown(event: KeyboardEvent): void {\n    const keyCode = event.keyCode;\n\n    if (keyCode === TAB) {\n      this.close();\n    } else if (keyCode === ESCAPE && !hasModifierKey(event)) {\n      event.preventDefault();\n      this.close();\n    } else if (keyCode === ENTER) {\n      event.preventDefault();\n\n      if (this._keyManager.activeItem) {\n        this._selectValue(this._keyManager.activeItem);\n      } else {\n        this.close();\n      }\n    } else {\n      const previousActive = this._keyManager.activeItem;\n      this._keyManager.onKeydown(event);\n      const currentActive = this._keyManager.activeItem;\n\n      if (currentActive && currentActive !== previousActive) {\n        scrollOptionIntoView(currentActive, 'nearest');\n      }\n    }\n  }\n\n  /** Sets up the logic that updates the timepicker when the locale changes. */\n  private _handleLocaleChanges(): void {\n    // Re-generate the options list if the locale changes.\n    this._localeChanges = this._dateAdapter.localeChanges.subscribe(() => {\n      this._optionsCacheKey = null;\n\n      if (this.isOpen()) {\n        this._generateOptions();\n      }\n    });\n  }\n\n  /**\n   * Sets up the logic that updates the timepicker when the state of the connected input changes.\n   */\n  private _handleInputStateChanges(): void {\n    effect(() => {\n      const input = this._input();\n      const options = this._options();\n\n      if (this._isOpen() && input) {\n        this._syncSelectedState(input.value(), options, null);\n      }\n    });\n  }\n}\n\n/**\n * Scrolls an option into view.\n * @param option Option to be scrolled into view.\n * @param position Position to which to align the option relative to the scrollable container.\n */\nfunction scrollOptionIntoView(option: MatOption, position: ScrollLogicalPosition) {\n  option._getHostElement().scrollIntoView({block: position, inline: position});\n}\n", "<ng-template #panelTemplate>\n  <div\n    role=\"listbox\"\n    class=\"mat-timepicker-panel\"\n    [class.mat-timepicker-panel-animations-enabled]=\"!_animationsDisabled\"\n    [class.mat-timepicker-panel-exit]=\"!isOpen()\"\n    [attr.aria-label]=\"ariaLabel() || null\"\n    [attr.aria-labelledby]=\"_getAriaLabelledby()\"\n    [id]=\"panelId\"\n    (animationend)=\"_handleAnimationEnd($event)\">\n    @for (option of _timeOptions; track option.value) {\n      <mat-option\n        [value]=\"option.value\"\n        (onSelectionChange)=\"_selectValue($event.source)\">{{option.label}}</mat-option>\n    }\n  </div>\n</ng-template>\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  booleanAttribute,\n  computed,\n  Directive,\n  effect,\n  ElementRef,\n  inject,\n  input,\n  InputSignal,\n  InputSignalWithTransform,\n  model,\n  ModelSignal,\n  OnDestroy,\n  OutputRefSubscription,\n  Renderer2,\n  Signal,\n  signal,\n} from '@angular/core';\nimport {DateAdapter, MAT_DATE_FORMATS} from '../core';\nimport {\n  AbstractControl,\n  ControlValueAccessor,\n  NG_VALIDATORS,\n  NG_VALUE_ACCESSOR,\n  ValidationErrors,\n  Validator,\n  ValidatorFn,\n  Validators,\n} from '@angular/forms';\nimport {MAT_FORM_FIELD} from '../form-field';\nimport {MatTimepicker} from './timepicker';\nimport {MAT_INPUT_VALUE_ACCESSOR} from '../input';\nimport {Subscription} from 'rxjs';\nimport {DOWN_ARROW, ESCAPE, hasMod<PERSON><PERSON><PERSON>, UP_ARROW} from '@angular/cdk/keycodes';\nimport {validateAdapter} from './util';\nimport {_getFocusedElementPierceShadowDom} from '@angular/cdk/platform';\n\n/**\n * Input that can be used to enter time and connect to a `mat-timepicker`.\n */\n@Directive({\n  selector: 'input[matTimepicker]',\n  exportAs: 'matTimepickerInput',\n  host: {\n    'class': 'mat-timepicker-input',\n    'role': 'combobox',\n    'type': 'text',\n    'aria-haspopup': 'listbox',\n    '[attr.aria-activedescendant]': '_ariaActiveDescendant()',\n    '[attr.aria-expanded]': '_ariaExpanded()',\n    '[attr.aria-controls]': '_ariaControls()',\n    '[attr.mat-timepicker-id]': 'timepicker()?.panelId',\n    '[disabled]': 'disabled()',\n    '(blur)': '_handleBlur()',\n    '(input)': '_handleInput($event.target.value)',\n    '(keydown)': '_handleKeydown($event)',\n  },\n  providers: [\n    {\n      provide: NG_VALUE_ACCESSOR,\n      useExisting: MatTimepickerInput,\n      multi: true,\n    },\n    {\n      provide: NG_VALIDATORS,\n      useExisting: MatTimepickerInput,\n      multi: true,\n    },\n    {\n      provide: MAT_INPUT_VALUE_ACCESSOR,\n      useExisting: MatTimepickerInput,\n    },\n  ],\n})\nexport class MatTimepickerInput<D> implements ControlValueAccessor, Validator, OnDestroy {\n  private _elementRef = inject<ElementRef<HTMLInputElement>>(ElementRef);\n  private _dateAdapter = inject<DateAdapter<D>>(DateAdapter, {optional: true})!;\n  private _dateFormats = inject(MAT_DATE_FORMATS, {optional: true})!;\n  private _formField = inject(MAT_FORM_FIELD, {optional: true});\n\n  private _onChange: ((value: any) => void) | undefined;\n  private _onTouched: (() => void) | undefined;\n  private _validatorOnChange: (() => void) | undefined;\n  private _cleanupClick: () => void;\n  private _accessorDisabled = signal(false);\n  private _localeSubscription: Subscription;\n  private _timepickerSubscription: OutputRefSubscription | undefined;\n  private _validator: ValidatorFn;\n  private _lastValueValid = true;\n  private _lastValidDate: D | null = null;\n\n  /** Value of the `aria-activedescendant` attribute. */\n  protected readonly _ariaActiveDescendant = computed(() => {\n    const timepicker = this.timepicker();\n    const isOpen = timepicker.isOpen();\n    const activeDescendant = timepicker.activeDescendant();\n    return isOpen && activeDescendant ? activeDescendant : null;\n  });\n\n  /** Value of the `aria-expanded` attribute. */\n  protected readonly _ariaExpanded = computed(() => this.timepicker().isOpen() + '');\n\n  /** Value of the `aria-controls` attribute. */\n  protected readonly _ariaControls = computed(() => {\n    const timepicker = this.timepicker();\n    return timepicker.isOpen() ? timepicker.panelId : null;\n  });\n\n  /** Current value of the input. */\n  readonly value: ModelSignal<D | null> = model<D | null>(null);\n\n  /** Timepicker that the input is associated with. */\n  readonly timepicker: InputSignal<MatTimepicker<D>> = input.required<MatTimepicker<D>>({\n    alias: 'matTimepicker',\n  });\n\n  /**\n   * Minimum time that can be selected or typed in. Can be either\n   * a date object (only time will be used) or a valid time string.\n   */\n  readonly min: InputSignalWithTransform<D | null, unknown> = input(null, {\n    alias: 'matTimepickerMin',\n    transform: (value: unknown) => this._transformDateInput<D>(value),\n  });\n\n  /**\n   * Maximum time that can be selected or typed in. Can be either\n   * a date object (only time will be used) or a valid time string.\n   */\n  readonly max: InputSignalWithTransform<D | null, unknown> = input(null, {\n    alias: 'matTimepickerMax',\n    transform: (value: unknown) => this._transformDateInput<D>(value),\n  });\n\n  /** Whether the input is disabled. */\n  readonly disabled: Signal<boolean> = computed(\n    () => this.disabledInput() || this._accessorDisabled(),\n  );\n\n  /**\n   * Whether the input should be disabled through the template.\n   * @docs-private\n   */\n  readonly disabledInput: InputSignalWithTransform<boolean, unknown> = input(false, {\n    transform: booleanAttribute,\n    alias: 'disabled',\n  });\n\n  constructor() {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      validateAdapter(this._dateAdapter, this._dateFormats);\n    }\n\n    const renderer = inject(Renderer2);\n    this._validator = this._getValidator();\n    this._respondToValueChanges();\n    this._respondToMinMaxChanges();\n    this._registerTimepicker();\n    this._localeSubscription = this._dateAdapter.localeChanges.subscribe(() => {\n      if (!this._hasFocus()) {\n        this._formatValue(this.value());\n      }\n    });\n\n    // Bind the click listener manually to the overlay origin, because we want the entire\n    // form field to be clickable, if the timepicker is used in `mat-form-field`.\n    this._cleanupClick = renderer.listen(\n      this.getOverlayOrigin().nativeElement,\n      'click',\n      this._handleClick,\n    );\n  }\n\n  /**\n   * Implemented as a part of `ControlValueAccessor`.\n   * @docs-private\n   */\n  writeValue(value: any): void {\n    // Note that we need to deserialize here, rather than depend on the value change effect,\n    // because `getValidDateOrNull` will clobber the value if it's parseable, but not created by\n    // the current adapter (see #30140).\n    const deserialized = this._dateAdapter.deserialize(value);\n    this.value.set(this._dateAdapter.getValidDateOrNull(deserialized));\n  }\n\n  /**\n   * Implemented as a part of `ControlValueAccessor`.\n   * @docs-private\n   */\n  registerOnChange(fn: (value: any) => void): void {\n    this._onChange = fn;\n  }\n\n  /**\n   * Implemented as a part of `ControlValueAccessor`.\n   * @docs-private\n   */\n  registerOnTouched(fn: () => void): void {\n    this._onTouched = fn;\n  }\n\n  /**\n   * Implemented as a part of `ControlValueAccessor`.\n   * @docs-private\n   */\n  setDisabledState(isDisabled: boolean): void {\n    this._accessorDisabled.set(isDisabled);\n  }\n\n  /**\n   * Implemented as a part of `Validator`.\n   * @docs-private\n   */\n  validate(control: AbstractControl): ValidationErrors | null {\n    return this._validator(control);\n  }\n\n  /**\n   * Implemented as a part of `Validator`.\n   * @docs-private\n   */\n  registerOnValidatorChange(fn: () => void): void {\n    this._validatorOnChange = fn;\n  }\n\n  /** Gets the element to which the timepicker popup should be attached. */\n  getOverlayOrigin(): ElementRef<HTMLElement> {\n    return this._formField?.getConnectedOverlayOrigin() || this._elementRef;\n  }\n\n  /** Focuses the input. */\n  focus(): void {\n    this._elementRef.nativeElement.focus();\n  }\n\n  ngOnDestroy(): void {\n    this._cleanupClick();\n    this._timepickerSubscription?.unsubscribe();\n    this._localeSubscription.unsubscribe();\n  }\n\n  /** Gets the ID of the input's label. */\n  _getLabelId(): string | null {\n    return this._formField?.getLabelId() || null;\n  }\n\n  /** Handles clicks on the input or the containing form field. */\n  private _handleClick = (): void => {\n    if (!this.disabled()) {\n      this.timepicker().open();\n    }\n  };\n\n  /** Handles the `input` event. */\n  protected _handleInput(value: string) {\n    const currentValue = this.value();\n    const date = this._dateAdapter.parseTime(value, this._dateFormats.parse.timeInput);\n    const hasChanged = !this._dateAdapter.sameTime(date, currentValue);\n\n    if (!date || hasChanged || !!(value && !currentValue)) {\n      // We need to fire the CVA change event for all nulls, otherwise the validators won't run.\n      this._assignUserSelection(date, true);\n    } else {\n      // Call the validator even if the value hasn't changed since\n      // some fields change depending on what the user has entered.\n      this._validatorOnChange?.();\n    }\n  }\n\n  /** Handles the `blur` event. */\n  protected _handleBlur() {\n    const value = this.value();\n\n    // Only reformat on blur so the value doesn't change while the user is interacting.\n    if (value && this._isValid(value)) {\n      this._formatValue(value);\n    }\n\n    if (!this.timepicker().isOpen()) {\n      this._onTouched?.();\n    }\n  }\n\n  /** Handles the `keydown` event. */\n  protected _handleKeydown(event: KeyboardEvent) {\n    // All keyboard events while open are handled through the timepicker.\n    if (this.timepicker().isOpen() || this.disabled()) {\n      return;\n    }\n\n    if (event.keyCode === ESCAPE && !hasModifierKey(event) && this.value() !== null) {\n      event.preventDefault();\n      this.value.set(null);\n      this._formatValue(null);\n    } else if (event.keyCode === DOWN_ARROW || event.keyCode === UP_ARROW) {\n      event.preventDefault();\n      this.timepicker().open();\n    }\n  }\n\n  /** Sets up the code that watches for changes in the value and adjusts the input. */\n  private _respondToValueChanges(): void {\n    effect(() => {\n      const value = this._dateAdapter.deserialize(this.value());\n      const wasValid = this._lastValueValid;\n      this._lastValueValid = this._isValid(value);\n\n      // Reformat the value if it changes while the user isn't interacting.\n      if (!this._hasFocus()) {\n        this._formatValue(value);\n      }\n\n      if (value && this._lastValueValid) {\n        this._lastValidDate = value;\n      }\n\n      // Trigger the validator if the state changed.\n      if (wasValid !== this._lastValueValid) {\n        this._validatorOnChange?.();\n      }\n    });\n  }\n\n  /** Sets up the logic that registers the input with the timepicker. */\n  private _registerTimepicker(): void {\n    effect(() => {\n      const timepicker = this.timepicker();\n      timepicker.registerInput(this);\n      timepicker.closed.subscribe(() => this._onTouched?.());\n      timepicker.selected.subscribe(({value}) => {\n        if (!this._dateAdapter.sameTime(value, this.value())) {\n          this._assignUserSelection(value, true);\n          this._formatValue(value);\n        }\n      });\n    });\n  }\n\n  /** Sets up the logic that adjusts the input if the min/max changes. */\n  private _respondToMinMaxChanges(): void {\n    effect(() => {\n      // Read the min/max so the effect knows when to fire.\n      this.min();\n      this.max();\n      this._validatorOnChange?.();\n    });\n  }\n\n  /**\n   * Assigns a value set by the user to the input's model.\n   * @param selection Time selected by the user that should be assigned.\n   * @param propagateToAccessor Whether the value should be propagated to the ControlValueAccessor.\n   */\n  private _assignUserSelection(selection: D | null, propagateToAccessor: boolean) {\n    if (selection == null || !this._isValid(selection)) {\n      this.value.set(selection);\n    } else {\n      // If a datepicker and timepicker are writing to the same object and the user enters an\n      // invalid time into the timepicker, we may end up clearing their selection from the\n      // datepicker. If the user enters a valid time afterwards, the datepicker's selection will\n      // have been lost. This logic restores the previously-valid date and sets its time to\n      // the newly-selected time.\n      const adapter = this._dateAdapter;\n      const target = adapter.getValidDateOrNull(this._lastValidDate || this.value());\n      const hours = adapter.getHours(selection);\n      const minutes = adapter.getMinutes(selection);\n      const seconds = adapter.getSeconds(selection);\n      this.value.set(target ? adapter.setTime(target, hours, minutes, seconds) : selection);\n    }\n\n    if (propagateToAccessor) {\n      this._onChange?.(this.value());\n    }\n  }\n\n  /** Formats the current value and assigns it to the input. */\n  private _formatValue(value: D | null): void {\n    value = this._dateAdapter.getValidDateOrNull(value);\n    this._elementRef.nativeElement.value =\n      value == null ? '' : this._dateAdapter.format(value, this._dateFormats.display.timeInput);\n  }\n\n  /** Checks whether a value is valid. */\n  private _isValid(value: D | null): boolean {\n    return !value || this._dateAdapter.isValid(value);\n  }\n\n  /** Transforms an arbitrary value into a value that can be assigned to a date-based input. */\n  private _transformDateInput<D>(value: unknown): D | null {\n    const date =\n      typeof value === 'string'\n        ? this._dateAdapter.parseTime(value, this._dateFormats.parse.timeInput)\n        : this._dateAdapter.deserialize(value);\n    return date && this._dateAdapter.isValid(date) ? (date as D) : null;\n  }\n\n  /** Whether the input is currently focused. */\n  private _hasFocus(): boolean {\n    return _getFocusedElementPierceShadowDom() === this._elementRef.nativeElement;\n  }\n\n  /** Gets a function that can be used to validate the input. */\n  private _getValidator(): ValidatorFn {\n    return Validators.compose([\n      () =>\n        this._lastValueValid\n          ? null\n          : {'matTimepickerParse': {'text': this._elementRef.nativeElement.value}},\n      control => {\n        const controlValue = this._dateAdapter.getValidDateOrNull(\n          this._dateAdapter.deserialize(control.value),\n        );\n        const min = this.min();\n        return !min || !controlValue || this._dateAdapter.compareTime(min, controlValue) <= 0\n          ? null\n          : {'matTimepickerMin': {'min': min, 'actual': controlValue}};\n      },\n      control => {\n        const controlValue = this._dateAdapter.getValidDateOrNull(\n          this._dateAdapter.deserialize(control.value),\n        );\n        const max = this.max();\n        return !max || !controlValue || this._dateAdapter.compareTime(max, controlValue) >= 0\n          ? null\n          : {'matTimepickerMax': {'max': max, 'actual': controlValue}};\n      },\n    ])!;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  booleanAttribute,\n  ChangeDetectionStrategy,\n  Component,\n  computed,\n  HostAttributeToken,\n  inject,\n  input,\n  InputSignal,\n  InputSignalWithTransform,\n  ViewEncapsulation,\n} from '@angular/core';\nimport {MatIconButton} from '../button';\nimport {MAT_TIMEPICKER_CONFIG} from './util';\nimport type {MatTimepicker} from './timepicker';\n\n/** Button that can be used to open a `mat-timepicker`. */\n@Component({\n  selector: 'mat-timepicker-toggle',\n  templateUrl: 'timepicker-toggle.html',\n  host: {\n    'class': 'mat-timepicker-toggle',\n    '[attr.tabindex]': 'null',\n    // Bind the `click` on the host, rather than the inner `button`, so that we can call\n    // `stopPropagation` on it without affecting the user's `click` handlers. We need to stop\n    // it so that the input doesn't get focused automatically by the form field (See #21836).\n    '(click)': '_open($event)',\n  },\n  exportAs: 'matTimepickerToggle',\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  imports: [MatIconButton],\n})\nexport class MatTimepickerToggle<D> {\n  private _defaultConfig = inject(MAT_TIMEPICKER_CONFIG, {optional: true});\n  private _defaultTabIndex = (() => {\n    const value = inject(new HostAttributeToken('tabindex'), {optional: true});\n    const parsed = Number(value);\n    return isNaN(parsed) ? null : parsed;\n  })();\n\n  protected _isDisabled = computed(() => {\n    const timepicker = this.timepicker();\n    return this.disabled() || timepicker.disabled();\n  });\n\n  /** Timepicker instance that the button will toggle. */\n  readonly timepicker: InputSignal<MatTimepicker<D>> = input.required<MatTimepicker<D>>({\n    alias: 'for',\n  });\n\n  /** Screen-reader label for the button. */\n  readonly ariaLabel = input<string | undefined>(undefined, {\n    alias: 'aria-label',\n  });\n\n  /** Screen-reader labelled by id for the button. */\n  readonly ariaLabelledby = input<string | undefined>(undefined, {\n    alias: 'aria-labelledby',\n  });\n\n  /** Default aria-label for the toggle if none is provided. */\n  private readonly _defaultAriaLabel = 'Open timepicker options';\n\n  /** Whether the toggle button is disabled. */\n  readonly disabled: InputSignalWithTransform<boolean, unknown> = input(false, {\n    transform: booleanAttribute,\n    alias: 'disabled',\n  });\n\n  /** Tabindex for the toggle. */\n  readonly tabIndex: InputSignal<number | null> = input(this._defaultTabIndex);\n\n  /** Whether ripples on the toggle should be disabled. */\n  readonly disableRipple: InputSignalWithTransform<boolean, unknown> = input(\n    this._defaultConfig?.disableRipple ?? false,\n    {transform: booleanAttribute},\n  );\n\n  /** Opens the connected timepicker. */\n  protected _open(event: Event): void {\n    if (this.timepicker() && !this._isDisabled()) {\n      this.timepicker().open();\n      event.stopPropagation();\n    }\n  }\n\n  /**\n   * Checks for ariaLabelledby and if empty uses custom\n   * aria-label or defaultAriaLabel if neither is provided.\n   */\n  getAriaLabel(): string | null {\n    return this.ariaLabelledby() ? null : this.ariaLabel() || this._defaultAriaLabel;\n  }\n}\n", "<button\n  mat-icon-button\n  type=\"button\"\n  aria-haspopup=\"listbox\"\n  [attr.aria-label]=\"getAriaLabel()\"\n  [attr.aria-labelledby]=\"ariaLabelledby()\"\n  [attr.aria-expanded]=\"timepicker().isOpen()\"\n  [attr.tabindex]=\"_isDisabled() ? -1 : tabIndex()\"\n  [disabled]=\"_isDisabled()\"\n  [disableRipple]=\"disableRipple()\">\n\n  <ng-content select=\"[matTimepickerToggleIcon]\">\n    <svg\n      class=\"mat-timepicker-toggle-default-icon\"\n      height=\"24px\"\n      width=\"24px\"\n      viewBox=\"0 -960 960 960\"\n      fill=\"currentColor\"\n      focusable=\"false\"\n      aria-hidden=\"true\">\n      <path d=\"m612-292 56-56-148-148v-184h-80v216l172 172ZM480-80q-83 0-156-31.5T197-197q-54-54-85.5-127T80-480q0-83 31.5-156T197-763q54-54 127-85.5T480-880q83 0 156 31.5T763-763q54 54 85.5 127T880-480q0 83-31.5 156T763-197q-54 54-127 85.5T480-80Zm0-400Zm0 320q133 0 226.5-93.5T800-480q0-133-93.5-226.5T480-800q-133 0-226.5 93.5T160-480q0 133 93.5 226.5T480-160Z\"/>\n    </svg>\n  </ng-content>\n</button>\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {NgModule} from '@angular/core';\nimport {CdkScrollableModule} from '@angular/cdk/scrolling';\nimport {MatTimepicker} from './timepicker';\nimport {MatTimepickerInput} from './timepicker-input';\nimport {MatTimepickerToggle} from './timepicker-toggle';\n\n@NgModule({\n  imports: [MatTimepicker, MatTimepickerInput, MatTimepickerToggle],\n  exports: [CdkScrollableModule, MatTimepicker, MatTimepickerInput, MatTimepickerToggle],\n})\nexport class MatTimepickerModule {}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAWA;AACA,MAAM,gBAAgB,GAAG,wEAAwE;AAajG;;AAEG;MACU,qBAAqB,GAAG,IAAI,cAAc,CACrD,uBAAuB;AAczB;AACM,SAAU,aAAa,CAAC,KAA6B,EAAA;AACzD,IAAA,IAAI,MAAc;AAElB,IAAA,IAAI,KAAK,KAAK,IAAI,EAAE;AAClB,QAAA,OAAO,IAAI;;AACN,SAAA,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;QACpC,MAAM,GAAG,KAAK;;SACT;QACL,IAAI,KAAK,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE;AAC7B,YAAA,OAAO,IAAI;;QAGb,MAAM,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,gBAAgB,CAAC;AAC5C,QAAA,MAAM,MAAM,GAAG,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI;AACpD,QAAA,MAAM,IAAI,GAAG,MAAM,GAAG,CAAC,CAAC,EAAE,WAAW,EAAE,IAAI,IAAI;AAE/C,QAAA,IAAI,CAAC,MAAM,IAAI,MAAM,KAAK,IAAI,IAAI,KAAK,CAAC,MAAM,CAAC,EAAE;AAC/C,YAAA,OAAO,IAAI;;AAGb,QAAA,IAAI,IAAI,KAAK,GAAG,IAAI,IAAI,KAAK,MAAM,IAAI,IAAI,KAAK,OAAO,EAAE;AACvD,YAAA,MAAM,GAAG,MAAM,GAAG,IAAI;;AACjB,aAAA,IAAI,IAAI,KAAK,GAAG,IAAI,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,QAAQ,IAAI,IAAI,KAAK,SAAS,EAAE;AACpF,YAAA,MAAM,GAAG,MAAM,GAAG,EAAE;;aACf;YACL,MAAM,GAAG,MAAM;;;AAInB,IAAA,OAAO,MAAM;AACf;AAEA;;;;;;;AAOG;AACG,SAAU,eAAe,CAC7B,OAAuB,EACvB,OAAuB,EACvB,GAAM,EACN,GAAM,EACN,QAAgB,EAAA;IAEhB,MAAM,OAAO,GAA6B,EAAE;IAC5C,IAAI,OAAO,GAAG,OAAO,CAAC,WAAW,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG;AAE3D,IAAA,OACE,OAAO,CAAC,QAAQ,CAAC,OAAO,EAAE,GAAG,CAAC;QAC9B,OAAO,CAAC,WAAW,CAAC,OAAO,EAAE,GAAG,CAAC,GAAG,CAAC;AACrC,QAAA,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,EACxB;QACA,OAAO,CAAC,IAAI,CAAC,EAAC,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,CAAC,MAAM,CAAC,OAAO,EAAE,OAAO,CAAC,OAAO,CAAC,eAAe,CAAC,EAAC,CAAC;QAC/F,OAAO,GAAG,OAAO,CAAC,UAAU,CAAC,OAAO,EAAE,QAAQ,CAAC;;AAGjD,IAAA,OAAO,OAAO;AAChB;AAEA;AACgB,SAAA,eAAe,CAC7B,OAAoC,EACpC,OAA8B,EAAA;IAE9B,SAAS,mBAAmB,CAAC,QAAgB,EAAA;AAC3C,QAAA,OAAO,KAAK,CACV,CAAwC,qCAAA,EAAA,QAAQ,CAAsC,oCAAA,CAAA;YACpF,CAAuE,qEAAA,CAAA;AACvE,YAAA,CAAA,sFAAA,CAAwF,CAC3F;;IAGH,IAAI,CAAC,OAAO,EAAE;AACZ,QAAA,MAAM,mBAAmB,CAAC,aAAa,CAAC;;IAG1C,IAAI,CAAC,OAAO,EAAE;AACZ,QAAA,MAAM,mBAAmB,CAAC,kBAAkB,CAAC;;AAG/C,IAAA,IACE,OAAO,CAAC,OAAO,CAAC,SAAS,KAAK,SAAS;AACvC,QAAA,OAAO,CAAC,OAAO,CAAC,eAAe,KAAK,SAAS;AAC7C,QAAA,OAAO,CAAC,KAAK,CAAC,SAAS,KAAK,SAAS,EACrC;QACA,MAAM,IAAI,KAAK,CACb,kEAAkE;YAChE,iFAAiF;AACjF,YAAA,6EAA6E,CAChF;;AAEL;;ACzEA;MACa,8BAA8B,GAAG,IAAI,cAAc,CAC9D,gCAAgC,EAChC;AACE,IAAA,UAAU,EAAE,MAAM;IAClB,OAAO,EAAE,MAAK;AACZ,QAAA,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC;QAC/B,OAAO,MAAM,OAAO,CAAC,gBAAgB,CAAC,UAAU,EAAE;KACnD;AACF,CAAA;AAGH;;;AAGG;MAgBU,aAAa,CAAA;AAChB,IAAA,QAAQ,GAAG,MAAM,CAAC,OAAO,CAAC;IAC1B,IAAI,GAAG,MAAM,CAAC,cAAc,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;AAC/C,IAAA,iBAAiB,GAAG,MAAM,CAAC,gBAAgB,CAAC;AAC5C,IAAA,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC;IAC5B,cAAc,GAAG,MAAM,CAAC,qBAAqB,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;IAChE,YAAY,GAAG,MAAM,CAAiB,WAAW,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAE;IACrE,YAAY,GAAG,MAAM,CAAC,gBAAgB,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAE;AAC1D,IAAA,sBAAsB,GAAG,MAAM,CAAC,8BAA8B,CAAC;AAC7D,IAAA,mBAAmB,GAC3B,MAAM,CAAC,qBAAqB,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC,KAAK,gBAAgB;AAE9D,IAAA,OAAO,GAAG,MAAM,CAAC,KAAK,CAAC;AACvB,IAAA,iBAAiB,GAAG,MAAM,CAAgB,IAAI,CAAC;AAE/C,IAAA,MAAM,GAAG,MAAM,CAA+B,IAAI,CAAC;IACnD,WAAW,GAAsB,IAAI;IACrC,OAAO,GAAmC,IAAI;IAC9C,gBAAgB,GAAkB,IAAI;AACtC,IAAA,cAAc;IACd,aAAa,GAA0B,IAAI;AAEzC,IAAA,cAAc,GAAG,SAAS,CAAC,QAAQ,CAAuB,eAAe,CAAC;IAC1E,YAAY,GAAsC,EAAE;AACpD,IAAA,QAAQ,GAAG,YAAY,CAAC,SAAS,CAAC;IAEpC,WAAW,GAAG,IAAI,0BAA0B,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS;SAC/E,cAAc,CAAC,IAAI;SACnB,cAAc,CAAC,IAAI;SACnB,uBAAuB,CAAC,IAAI,CAAC;AAEhC;;;;AAIG;IACM,QAAQ,GAAoE,KAAK,CACxF,aAAa,CAAC,IAAI,CAAC,cAAc,EAAE,QAAQ,IAAI,IAAI,CAAC,EACpD,EAAC,SAAS,EAAE,aAAa,EAAC,CAC3B;AAED;;;AAGG;AACM,IAAA,OAAO,GAA0D,KAAK,CAE7E,IAAI,CAAC;;AAGE,IAAA,MAAM,GAAoB,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE;;IAGnD,QAAQ,GAA+C,MAAM,EAAE;;IAG/D,MAAM,GAA2B,MAAM,EAAE;;IAGzC,MAAM,GAA2B,MAAM,EAAE;;AAGzC,IAAA,gBAAgB,GAA0B,IAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE;;IAG7E,OAAO,GAAW,MAAM,CAAC,YAAY,CAAC,CAAC,KAAK,CAAC,uBAAuB,CAAC;;IAGrE,aAAa,GAA+C,KAAK,CACxE,IAAI,CAAC,cAAc,EAAE,aAAa,IAAI,KAAK,EAC3C;AACE,QAAA,SAAS,EAAE,gBAAgB;AAC5B,KAAA,CACF;;AAGQ,IAAA,SAAS,GAA+B,KAAK,CAAgB,IAAI,EAAE;AAC1E,QAAA,KAAK,EAAE,YAAY;AACpB,KAAA,CAAC;;AAGO,IAAA,cAAc,GAA+B,KAAK,CAAgB,IAAI,EAAE;AAC/E,QAAA,KAAK,EAAE,iBAAiB;AACzB,KAAA,CAAC;;AAGO,IAAA,QAAQ,GAAoB,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,QAAQ,EAAE,CAAC;AAEhF,IAAA,WAAA,GAAA;AACE,QAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;YACjD,eAAe,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,YAAY,CAAC;YAErD,MAAM,CAAC,MAAK;AACV,gBAAA,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,EAAE;AAC9B,gBAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,EAAE;gBAEhC,IAAI,OAAO,KAAK,IAAI,IAAI,QAAQ,KAAK,IAAI,EAAE;AACzC,oBAAA,MAAM,IAAI,KAAK,CACb,0EAA0E,CAC3E;;AACI,qBAAA,IAAI,OAAO,EAAE,MAAM,KAAK,CAAC,EAAE;AAChC,oBAAA,MAAM,IAAI,KAAK,CAAC,mDAAmD,CAAC;;AAExE,aAAC,CAAC;;;AAIJ,QAAA,MAAM,OAAO,GAAG,MAAM,CAA0B,UAAU,CAAC;QAC3D,OAAO,CAAC,aAAa,CAAC,YAAY,CAAC,yBAAyB,EAAE,IAAI,CAAC,OAAO,CAAC;QAC3E,IAAI,CAAC,oBAAoB,EAAE;QAC3B,IAAI,CAAC,wBAAwB,EAAE;QAC/B,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,SAAS,CAAC,MAChC,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,UAAU,EAAE,EAAE,IAAI,IAAI,CAAC,CACpE;;;IAIH,IAAI,GAAA;AACF,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE;QAE3B,IAAI,CAAC,KAAK,EAAE;YACV;;;;;QAMF,KAAK,CAAC,KAAK,EAAE;AAEb,QAAA,IAAI,IAAI,CAAC,OAAO,EAAE,EAAE;YAClB;;AAGF,QAAA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC;QACtB,IAAI,CAAC,gBAAgB,EAAE;AACvB,QAAA,MAAM,UAAU,GAAG,IAAI,CAAC,cAAc,EAAE;AACxC,QAAA,UAAU,CAAC,UAAU,CAAC,EAAC,KAAK,EAAE,KAAK,CAAC,gBAAgB,EAAE,CAAC,aAAa,CAAC,WAAW,EAAC,CAAC;AAClF,QAAA,IAAI,CAAC,OAAO,KAAK,IAAI,cAAc,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,IAAI,CAAC,iBAAiB,CAAC;;;AAIlF,QAAA,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE,EAAE;AAC7B,YAAA,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC;;AAGjC,QAAA,IAAI,CAAC,aAAa,EAAE,OAAO,EAAE;AAC7B,QAAA,IAAI,CAAC,aAAa,GAAG,eAAe,CAClC,MAAK;AACH,YAAA,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,EAAE;AAC/B,YAAA,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,KAAK,EAAE,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;AAC3D,YAAA,IAAI,CAAC,aAAa,GAAG,IAAI;SAC1B,EACD,EAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,EAAC,CAC3B;AAED,QAAA,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE;;;IAIpB,KAAK,GAAA;AACH,QAAA,IAAI,IAAI,CAAC,OAAO,EAAE,EAAE;AAClB,YAAA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC;AACvB,YAAA,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE;AAElB,YAAA,IAAI,IAAI,CAAC,mBAAmB,EAAE;AAC5B,gBAAA,IAAI,CAAC,WAAW,EAAE,MAAM,EAAE;;;;;AAMhC,IAAA,aAAa,CAAC,KAA4B,EAAA;AACxC,QAAA,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,EAAE;AAElC,QAAA,IAAI,YAAY,IAAI,KAAK,KAAK,YAAY,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAAE;AAC7F,YAAA,MAAM,IAAI,KAAK,CAAC,+DAA+D,CAAC;;AAGlF,QAAA,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC;;IAGxB,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE;AAC1B,QAAA,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE;AACjC,QAAA,IAAI,CAAC,aAAa,EAAE,OAAO,EAAE;AAC7B,QAAA,IAAI,CAAC,WAAW,EAAE,OAAO,EAAE;;;AAInB,IAAA,YAAY,CAAC,MAAoB,EAAA;QACzC,IAAI,CAAC,KAAK,EAAE;AACZ,QAAA,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,MAAM,CAAC;QACtC,IAAI,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,OAAO,IAAG;;AAEhC,YAAA,IAAI,OAAO,KAAK,MAAM,EAAE;AACtB,gBAAA,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC;;AAE3B,SAAC,CAAC;AACF,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAC,KAAK,EAAE,MAAM,CAAC,KAAK,EAAE,MAAM,EAAE,IAAI,EAAC,CAAC;AACvD,QAAA,IAAI,CAAC,MAAM,EAAE,EAAE,KAAK,EAAE;;;IAId,kBAAkB,GAAA;AAC1B,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE,EAAE;AACpB,YAAA,OAAO,IAAI;;AAEb,QAAA,OAAO,IAAI,CAAC,cAAc,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,EAAE,WAAW,EAAE,IAAI,IAAI;;;AAI5D,IAAA,mBAAmB,CAAC,KAAqB,EAAA;AACjD,QAAA,IAAI,KAAK,CAAC,aAAa,KAAK,sBAAsB,EAAE;AAClD,YAAA,IAAI,CAAC,WAAW,EAAE,MAAM,EAAE;;;;IAKtB,cAAc,GAAA;AACpB,QAAA,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,OAAO,IAAI,CAAC,WAAW;;AAGzB,QAAA,MAAM,gBAAgB,GAAG,IAAI,CAAC;AAC3B,aAAA,QAAQ;aACR,mBAAmB,CAAC,IAAI,CAAC,MAAM,EAAG,CAAC,gBAAgB,EAAE;aACrD,sBAAsB,CAAC,KAAK;aAC5B,QAAQ,CAAC,KAAK;aACd,qBAAqB,CAAC,uBAAuB;AAC7C,aAAA,aAAa,CAAC;AACb,YAAA;AACE,gBAAA,OAAO,EAAE,OAAO;AAChB,gBAAA,OAAO,EAAE,QAAQ;AACjB,gBAAA,QAAQ,EAAE,OAAO;AACjB,gBAAA,QAAQ,EAAE,KAAK;AAChB,aAAA;AACD,YAAA;AACE,gBAAA,OAAO,EAAE,OAAO;AAChB,gBAAA,OAAO,EAAE,KAAK;AACd,gBAAA,QAAQ,EAAE,OAAO;AACjB,gBAAA,QAAQ,EAAE,QAAQ;AAClB,gBAAA,UAAU,EAAE,sBAAsB;AACnC,aAAA;AACF,SAAA,CAAC;QAEJ,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;YACtC,gBAAgB;AAChB,YAAA,cAAc,EAAE,IAAI,CAAC,sBAAsB,EAAE;AAC7C,YAAA,SAAS,EAAE,IAAI,CAAC,IAAI,IAAI,KAAK;AAC7B,YAAA,WAAW,EAAE,KAAK;AACnB,SAAA,CAAC;AAEF,QAAA,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,KAAK,EAAE,CAAC;AAC5D,QAAA,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,CAAC,SAAS,CAAC,KAAK,IAAI,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;QAC/E,IAAI,CAAC,WAAW,CAAC,oBAAoB,EAAE,CAAC,SAAS,CAAC,KAAK,IAAG;AACxD,YAAA,MAAM,MAAM,GAAG,eAAe,CAAC,KAAK,CAAgB;YACpD,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE,EAAE,gBAAgB,EAAE,CAAC,aAAa;AAE9D,YAAA,IAAI,MAAM,IAAI,MAAM,IAAI,MAAM,KAAK,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;gBACrE,IAAI,CAAC,KAAK,EAAE;;AAEhB,SAAC,CAAC;QAEF,OAAO,IAAI,CAAC,WAAW;;;IAIjB,gBAAgB,GAAA;;QAEtB,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,EAAE,IAAI,EAAE,GAAG,EAAE;AAC3C,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,EAAE;AAE9B,QAAA,IAAI,OAAO,KAAK,IAAI,EAAE;AACpB,YAAA,IAAI,CAAC,YAAY,GAAG,OAAO;;aACtB;AACL,YAAA,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE;AAC3B,YAAA,MAAM,OAAO,GAAG,IAAI,CAAC,YAAY;YACjC,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,SAAS;YACtD,MAAM,GAAG,GAAG,KAAK,EAAE,GAAG,EAAE,IAAI,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACrE,MAAM,GAAG,GAAG,KAAK,EAAE,GAAG,EAAE,IAAI,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;YACvE,MAAM,QAAQ,GACZ,QAAQ,GAAG,GAAG,GAAG,OAAO,CAAC,MAAM,CAAC,GAAG,EAAE,UAAU,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,MAAM,CAAC,GAAG,EAAE,UAAU,CAAC;;AAG1F,YAAA,IAAI,QAAQ,KAAK,IAAI,CAAC,gBAAgB,EAAE;AACtC,gBAAA,IAAI,CAAC,gBAAgB,GAAG,QAAQ;AAChC,gBAAA,IAAI,CAAC,YAAY,GAAG,eAAe,CAAC,OAAO,EAAE,IAAI,CAAC,YAAY,EAAE,GAAG,EAAE,GAAG,EAAE,QAAQ,CAAC;;;;AAKzF;;;;;AAKG;AACK,IAAA,kBAAkB,CACxB,KAAe,EACf,OAA6B,EAC7B,QAA0B,EAAA;QAE1B,IAAI,WAAW,GAAG,KAAK;AAEvB,QAAA,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE;AAC5B,YAAA,IAAI,KAAK,IAAI,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,EAAE;AAC5D,gBAAA,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC;AACpB,gBAAA,oBAAoB,CAAC,MAAM,EAAE,QAAQ,CAAC;AACtC,gBAAA,SAAS,CAAC,MAAM,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;gBACvD,WAAW,GAAG,IAAI;;iBACb;AACL,gBAAA,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC;;;;;QAM1B,IAAI,CAAC,WAAW,EAAE;YAChB,IAAI,QAAQ,EAAE;AACZ,gBAAA,SAAS,CAAC,MAAM,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;AACzD,gBAAA,oBAAoB,CAAC,QAAQ,EAAE,QAAQ,CAAC;;iBACnC;AACL,gBAAA,SAAS,CAAC,MAAM,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;;;;;AAMjD,IAAA,cAAc,CAAC,KAAoB,EAAA;AACzC,QAAA,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO;AAE7B,QAAA,IAAI,OAAO,KAAK,GAAG,EAAE;YACnB,IAAI,CAAC,KAAK,EAAE;;aACP,IAAI,OAAO,KAAK,MAAM,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE;YACvD,KAAK,CAAC,cAAc,EAAE;YACtB,IAAI,CAAC,KAAK,EAAE;;AACP,aAAA,IAAI,OAAO,KAAK,KAAK,EAAE;YAC5B,KAAK,CAAC,cAAc,EAAE;AAEtB,YAAA,IAAI,IAAI,CAAC,WAAW,CAAC,UAAU,EAAE;gBAC/B,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC;;iBACzC;gBACL,IAAI,CAAC,KAAK,EAAE;;;aAET;AACL,YAAA,MAAM,cAAc,GAAG,IAAI,CAAC,WAAW,CAAC,UAAU;AAClD,YAAA,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,KAAK,CAAC;AACjC,YAAA,MAAM,aAAa,GAAG,IAAI,CAAC,WAAW,CAAC,UAAU;AAEjD,YAAA,IAAI,aAAa,IAAI,aAAa,KAAK,cAAc,EAAE;AACrD,gBAAA,oBAAoB,CAAC,aAAa,EAAE,SAAS,CAAC;;;;;IAM5C,oBAAoB,GAAA;;AAE1B,QAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,SAAS,CAAC,MAAK;AACnE,YAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI;AAE5B,YAAA,IAAI,IAAI,CAAC,MAAM,EAAE,EAAE;gBACjB,IAAI,CAAC,gBAAgB,EAAE;;AAE3B,SAAC,CAAC;;AAGJ;;AAEG;IACK,wBAAwB,GAAA;QAC9B,MAAM,CAAC,MAAK;AACV,YAAA,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE;AAC3B,YAAA,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,EAAE;AAE/B,YAAA,IAAI,IAAI,CAAC,OAAO,EAAE,IAAI,KAAK,EAAE;AAC3B,gBAAA,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,KAAK,EAAE,EAAE,OAAO,EAAE,IAAI,CAAC;;AAEzD,SAAC,CAAC;;uGA1XO,aAAa,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAb,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,aAAa,EAPb,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,gBAAA,EAAA,MAAA,EAAA,EAAA,QAAA,EAAA,EAAA,iBAAA,EAAA,UAAA,EAAA,UAAA,EAAA,UAAA,EAAA,QAAA,EAAA,IAAA,EAAA,UAAA,EAAA,KAAA,EAAA,iBAAA,EAAA,IAAA,EAAA,EAAA,OAAA,EAAA,EAAA,iBAAA,EAAA,SAAA,EAAA,UAAA,EAAA,SAAA,EAAA,QAAA,EAAA,IAAA,EAAA,UAAA,EAAA,KAAA,EAAA,iBAAA,EAAA,IAAA,EAAA,EAAA,aAAA,EAAA,EAAA,iBAAA,EAAA,eAAA,EAAA,UAAA,EAAA,eAAA,EAAA,QAAA,EAAA,IAAA,EAAA,UAAA,EAAA,KAAA,EAAA,iBAAA,EAAA,IAAA,EAAA,EAAA,SAAA,EAAA,EAAA,iBAAA,EAAA,WAAA,EAAA,UAAA,EAAA,YAAA,EAAA,QAAA,EAAA,IAAA,EAAA,UAAA,EAAA,KAAA,EAAA,iBAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,EAAA,iBAAA,EAAA,gBAAA,EAAA,UAAA,EAAA,iBAAA,EAAA,QAAA,EAAA,IAAA,EAAA,UAAA,EAAA,KAAA,EAAA,iBAAA,EAAA,IAAA,EAAA,EAAA,EAAA,OAAA,EAAA,EAAA,QAAA,EAAA,UAAA,EAAA,MAAA,EAAA,QAAA,EAAA,MAAA,EAAA,QAAA,EAAA,EAAA,SAAA,EAAA;AACT,YAAA;AACE,gBAAA,OAAO,EAAE,2BAA2B;AACpC,gBAAA,WAAW,EAAE,aAAa;AAC3B,aAAA;AACF,SAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,gBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,eAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,QAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,UAAA,EAAA,SAAA,EA0BiC,SAAS,EAAA,WAAA,EAAA,IAAA,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,eAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,ECxH7C,8nBAiBA,EAAA,MAAA,EAAA,CAAA,i/CAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EDuEY,SAAS,EAAA,QAAA,EAAA,YAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,IAAA,EAAA,UAAA,CAAA,EAAA,OAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,WAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAQR,aAAa,EAAA,UAAA,EAAA,CAAA;kBAfzB,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,gBAAgB,EAChB,QAAA,EAAA,eAAe,EAGR,eAAA,EAAA,uBAAuB,CAAC,MAAM,EAAA,aAAA,EAChC,iBAAiB,CAAC,IAAI,EAAA,OAAA,EAC5B,CAAC,SAAS,CAAC,EACT,SAAA,EAAA;AACT,wBAAA;AACE,4BAAA,OAAO,EAAE,2BAA2B;AACpC,4BAAA,WAAW,EAAe,aAAA;AAC3B,yBAAA;AACF,qBAAA,EAAA,QAAA,EAAA,8nBAAA,EAAA,MAAA,EAAA,CAAA,i/CAAA,CAAA,EAAA;;AAgYH;;;;AAIG;AACH,SAAS,oBAAoB,CAAC,MAAiB,EAAE,QAA+B,EAAA;AAC9E,IAAA,MAAM,CAAC,eAAe,EAAE,CAAC,cAAc,CAAC,EAAC,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAC,CAAC;AAC9E;;AExbA;;AAEG;MAmCU,kBAAkB,CAAA;AACrB,IAAA,WAAW,GAAG,MAAM,CAA+B,UAAU,CAAC;IAC9D,YAAY,GAAG,MAAM,CAAiB,WAAW,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAE;IACrE,YAAY,GAAG,MAAM,CAAC,gBAAgB,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAE;IAC1D,UAAU,GAAG,MAAM,CAAC,cAAc,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;AAErD,IAAA,SAAS;AACT,IAAA,UAAU;AACV,IAAA,kBAAkB;AAClB,IAAA,aAAa;AACb,IAAA,iBAAiB,GAAG,MAAM,CAAC,KAAK,CAAC;AACjC,IAAA,mBAAmB;AACnB,IAAA,uBAAuB;AACvB,IAAA,UAAU;IACV,eAAe,GAAG,IAAI;IACtB,cAAc,GAAa,IAAI;;AAGpB,IAAA,qBAAqB,GAAG,QAAQ,CAAC,MAAK;AACvD,QAAA,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,EAAE;AACpC,QAAA,MAAM,MAAM,GAAG,UAAU,CAAC,MAAM,EAAE;AAClC,QAAA,MAAM,gBAAgB,GAAG,UAAU,CAAC,gBAAgB,EAAE;QACtD,OAAO,MAAM,IAAI,gBAAgB,GAAG,gBAAgB,GAAG,IAAI;AAC7D,KAAC,CAAC;;AAGiB,IAAA,aAAa,GAAG,QAAQ,CAAC,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC;;AAG/D,IAAA,aAAa,GAAG,QAAQ,CAAC,MAAK;AAC/C,QAAA,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,EAAE;AACpC,QAAA,OAAO,UAAU,CAAC,MAAM,EAAE,GAAG,UAAU,CAAC,OAAO,GAAG,IAAI;AACxD,KAAC,CAAC;;AAGO,IAAA,KAAK,GAA0B,KAAK,CAAW,IAAI,CAAC;;AAGpD,IAAA,UAAU,GAAkC,KAAK,CAAC,QAAQ,CAAmB;AACpF,QAAA,KAAK,EAAE,eAAe;AACvB,KAAA,CAAC;AAEF;;;AAGG;AACM,IAAA,GAAG,GAAgD,KAAK,CAAC,IAAI,EAAE;AACtE,QAAA,KAAK,EAAE,kBAAkB;QACzB,SAAS,EAAE,CAAC,KAAc,KAAK,IAAI,CAAC,mBAAmB,CAAI,KAAK,CAAC;AAClE,KAAA,CAAC;AAEF;;;AAGG;AACM,IAAA,GAAG,GAAgD,KAAK,CAAC,IAAI,EAAE;AACtE,QAAA,KAAK,EAAE,kBAAkB;QACzB,SAAS,EAAE,CAAC,KAAc,KAAK,IAAI,CAAC,mBAAmB,CAAI,KAAK,CAAC;AAClE,KAAA,CAAC;;AAGO,IAAA,QAAQ,GAAoB,QAAQ,CAC3C,MAAM,IAAI,CAAC,aAAa,EAAE,IAAI,IAAI,CAAC,iBAAiB,EAAE,CACvD;AAED;;;AAGG;AACM,IAAA,aAAa,GAA+C,KAAK,CAAC,KAAK,EAAE;AAChF,QAAA,SAAS,EAAE,gBAAgB;AAC3B,QAAA,KAAK,EAAE,UAAU;AAClB,KAAA,CAAC;AAEF,IAAA,WAAA,GAAA;AACE,QAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;YACjD,eAAe,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,YAAY,CAAC;;AAGvD,QAAA,MAAM,QAAQ,GAAG,MAAM,CAAC,SAAS,CAAC;AAClC,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,aAAa,EAAE;QACtC,IAAI,CAAC,sBAAsB,EAAE;QAC7B,IAAI,CAAC,uBAAuB,EAAE;QAC9B,IAAI,CAAC,mBAAmB,EAAE;AAC1B,QAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,SAAS,CAAC,MAAK;AACxE,YAAA,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE;gBACrB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;;AAEnC,SAAC,CAAC;;;QAIF,IAAI,CAAC,aAAa,GAAG,QAAQ,CAAC,MAAM,CAClC,IAAI,CAAC,gBAAgB,EAAE,CAAC,aAAa,EACrC,OAAO,EACP,IAAI,CAAC,YAAY,CAClB;;AAGH;;;AAGG;AACH,IAAA,UAAU,CAAC,KAAU,EAAA;;;;QAInB,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,KAAK,CAAC;AACzD,QAAA,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAC;;AAGpE;;;AAGG;AACH,IAAA,gBAAgB,CAAC,EAAwB,EAAA;AACvC,QAAA,IAAI,CAAC,SAAS,GAAG,EAAE;;AAGrB;;;AAGG;AACH,IAAA,iBAAiB,CAAC,EAAc,EAAA;AAC9B,QAAA,IAAI,CAAC,UAAU,GAAG,EAAE;;AAGtB;;;AAGG;AACH,IAAA,gBAAgB,CAAC,UAAmB,EAAA;AAClC,QAAA,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,UAAU,CAAC;;AAGxC;;;AAGG;AACH,IAAA,QAAQ,CAAC,OAAwB,EAAA;AAC/B,QAAA,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;;AAGjC;;;AAGG;AACH,IAAA,yBAAyB,CAAC,EAAc,EAAA;AACtC,QAAA,IAAI,CAAC,kBAAkB,GAAG,EAAE;;;IAI9B,gBAAgB,GAAA;QACd,OAAO,IAAI,CAAC,UAAU,EAAE,yBAAyB,EAAE,IAAI,IAAI,CAAC,WAAW;;;IAIzE,KAAK,GAAA;AACH,QAAA,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,KAAK,EAAE;;IAGxC,WAAW,GAAA;QACT,IAAI,CAAC,aAAa,EAAE;AACpB,QAAA,IAAI,CAAC,uBAAuB,EAAE,WAAW,EAAE;AAC3C,QAAA,IAAI,CAAC,mBAAmB,CAAC,WAAW,EAAE;;;IAIxC,WAAW,GAAA;QACT,OAAO,IAAI,CAAC,UAAU,EAAE,UAAU,EAAE,IAAI,IAAI;;;IAItC,YAAY,GAAG,MAAW;AAChC,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE;AACpB,YAAA,IAAI,CAAC,UAAU,EAAE,CAAC,IAAI,EAAE;;AAE5B,KAAC;;AAGS,IAAA,YAAY,CAAC,KAAa,EAAA;AAClC,QAAA,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,EAAE;AACjC,QAAA,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,SAAS,CAAC;AAClF,QAAA,MAAM,UAAU,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,EAAE,YAAY,CAAC;AAElE,QAAA,IAAI,CAAC,IAAI,IAAI,UAAU,IAAI,CAAC,EAAE,KAAK,IAAI,CAAC,YAAY,CAAC,EAAE;;AAErD,YAAA,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,IAAI,CAAC;;aAChC;;;AAGL,YAAA,IAAI,CAAC,kBAAkB,IAAI;;;;IAKrB,WAAW,GAAA;AACnB,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,EAAE;;QAG1B,IAAI,KAAK,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;AACjC,YAAA,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC;;QAG1B,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,MAAM,EAAE,EAAE;AAC/B,YAAA,IAAI,CAAC,UAAU,IAAI;;;;AAKb,IAAA,cAAc,CAAC,KAAoB,EAAA;;AAE3C,QAAA,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC,MAAM,EAAE,IAAI,IAAI,CAAC,QAAQ,EAAE,EAAE;YACjD;;AAGF,QAAA,IAAI,KAAK,CAAC,OAAO,KAAK,MAAM,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,KAAK,EAAE,KAAK,IAAI,EAAE;YAC/E,KAAK,CAAC,cAAc,EAAE;AACtB,YAAA,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC;AACpB,YAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;;AAClB,aAAA,IAAI,KAAK,CAAC,OAAO,KAAK,UAAU,IAAI,KAAK,CAAC,OAAO,KAAK,QAAQ,EAAE;YACrE,KAAK,CAAC,cAAc,EAAE;AACtB,YAAA,IAAI,CAAC,UAAU,EAAE,CAAC,IAAI,EAAE;;;;IAKpB,sBAAsB,GAAA;QAC5B,MAAM,CAAC,MAAK;AACV,YAAA,MAAM,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;AACzD,YAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe;YACrC,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC;;AAG3C,YAAA,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE;AACrB,gBAAA,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC;;AAG1B,YAAA,IAAI,KAAK,IAAI,IAAI,CAAC,eAAe,EAAE;AACjC,gBAAA,IAAI,CAAC,cAAc,GAAG,KAAK;;;AAI7B,YAAA,IAAI,QAAQ,KAAK,IAAI,CAAC,eAAe,EAAE;AACrC,gBAAA,IAAI,CAAC,kBAAkB,IAAI;;AAE/B,SAAC,CAAC;;;IAII,mBAAmB,GAAA;QACzB,MAAM,CAAC,MAAK;AACV,YAAA,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,EAAE;AACpC,YAAA,UAAU,CAAC,aAAa,CAAC,IAAI,CAAC;AAC9B,YAAA,UAAU,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,UAAU,IAAI,CAAC;YACtD,UAAU,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,EAAC,KAAK,EAAC,KAAI;AACxC,gBAAA,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,EAAE;AACpD,oBAAA,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,IAAI,CAAC;AACtC,oBAAA,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC;;AAE5B,aAAC,CAAC;AACJ,SAAC,CAAC;;;IAII,uBAAuB,GAAA;QAC7B,MAAM,CAAC,MAAK;;YAEV,IAAI,CAAC,GAAG,EAAE;YACV,IAAI,CAAC,GAAG,EAAE;AACV,YAAA,IAAI,CAAC,kBAAkB,IAAI;AAC7B,SAAC,CAAC;;AAGJ;;;;AAIG;IACK,oBAAoB,CAAC,SAAmB,EAAE,mBAA4B,EAAA;AAC5E,QAAA,IAAI,SAAS,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE;AAClD,YAAA,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,SAAS,CAAC;;aACpB;;;;;;AAML,YAAA,MAAM,OAAO,GAAG,IAAI,CAAC,YAAY;AACjC,YAAA,MAAM,MAAM,GAAG,OAAO,CAAC,kBAAkB,CAAC,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;YAC9E,MAAM,KAAK,GAAG,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC;YACzC,MAAM,OAAO,GAAG,OAAO,CAAC,UAAU,CAAC,SAAS,CAAC;YAC7C,MAAM,OAAO,GAAG,OAAO,CAAC,UAAU,CAAC,SAAS,CAAC;YAC7C,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,CAAC,GAAG,SAAS,CAAC;;QAGvF,IAAI,mBAAmB,EAAE;YACvB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;;;;AAK1B,IAAA,YAAY,CAAC,KAAe,EAAA;QAClC,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC,KAAK,CAAC;AACnD,QAAA,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,KAAK;YAClC,KAAK,IAAI,IAAI,GAAG,EAAE,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,SAAS,CAAC;;;AAIrF,IAAA,QAAQ,CAAC,KAAe,EAAA;QAC9B,OAAO,CAAC,KAAK,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,KAAK,CAAC;;;AAI3C,IAAA,mBAAmB,CAAI,KAAc,EAAA;AAC3C,QAAA,MAAM,IAAI,GACR,OAAO,KAAK,KAAK;AACf,cAAE,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,SAAS;cACpE,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,KAAK,CAAC;AAC1C,QAAA,OAAO,IAAI,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,GAAI,IAAU,GAAG,IAAI;;;IAI7D,SAAS,GAAA;QACf,OAAO,iCAAiC,EAAE,KAAK,IAAI,CAAC,WAAW,CAAC,aAAa;;;IAIvE,aAAa,GAAA;QACnB,OAAO,UAAU,CAAC,OAAO,CAAC;AACxB,YAAA,MACE,IAAI,CAAC;AACH,kBAAE;AACF,kBAAE,EAAC,oBAAoB,EAAE,EAAC,MAAM,EAAE,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,KAAK,EAAC,EAAC;AAC5E,YAAA,OAAO,IAAG;AACR,gBAAA,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,kBAAkB,CACvD,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC,CAC7C;AACD,gBAAA,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE;AACtB,gBAAA,OAAO,CAAC,GAAG,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,GAAG,EAAE,YAAY,CAAC,IAAI;AAClF,sBAAE;AACF,sBAAE,EAAC,kBAAkB,EAAE,EAAC,KAAK,EAAE,GAAG,EAAE,QAAQ,EAAE,YAAY,EAAC,EAAC;aAC/D;AACD,YAAA,OAAO,IAAG;AACR,gBAAA,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,kBAAkB,CACvD,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC,CAC7C;AACD,gBAAA,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE;AACtB,gBAAA,OAAO,CAAC,GAAG,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,GAAG,EAAE,YAAY,CAAC,IAAI;AAClF,sBAAE;AACF,sBAAE,EAAC,kBAAkB,EAAE,EAAC,KAAK,EAAE,GAAG,EAAE,QAAQ,EAAE,YAAY,EAAC,EAAC;aAC/D;AACF,SAAA,CAAE;;uGAhWM,kBAAkB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAlB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,kBAAkB,EAjBlB,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,sBAAA,EAAA,MAAA,EAAA,EAAA,KAAA,EAAA,EAAA,iBAAA,EAAA,OAAA,EAAA,UAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,UAAA,EAAA,KAAA,EAAA,iBAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,iBAAA,EAAA,YAAA,EAAA,UAAA,EAAA,eAAA,EAAA,QAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EAAA,iBAAA,EAAA,IAAA,EAAA,EAAA,GAAA,EAAA,EAAA,iBAAA,EAAA,KAAA,EAAA,UAAA,EAAA,kBAAA,EAAA,QAAA,EAAA,IAAA,EAAA,UAAA,EAAA,KAAA,EAAA,iBAAA,EAAA,IAAA,EAAA,EAAA,GAAA,EAAA,EAAA,iBAAA,EAAA,KAAA,EAAA,UAAA,EAAA,kBAAA,EAAA,QAAA,EAAA,IAAA,EAAA,UAAA,EAAA,KAAA,EAAA,iBAAA,EAAA,IAAA,EAAA,EAAA,aAAA,EAAA,EAAA,iBAAA,EAAA,eAAA,EAAA,UAAA,EAAA,UAAA,EAAA,QAAA,EAAA,IAAA,EAAA,UAAA,EAAA,KAAA,EAAA,iBAAA,EAAA,IAAA,EAAA,EAAA,EAAA,OAAA,EAAA,EAAA,KAAA,EAAA,aAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,MAAA,EAAA,UAAA,EAAA,MAAA,EAAA,MAAA,EAAA,eAAA,EAAA,SAAA,EAAA,EAAA,SAAA,EAAA,EAAA,MAAA,EAAA,eAAA,EAAA,OAAA,EAAA,mCAAA,EAAA,SAAA,EAAA,wBAAA,EAAA,EAAA,UAAA,EAAA,EAAA,4BAAA,EAAA,yBAAA,EAAA,oBAAA,EAAA,iBAAA,EAAA,oBAAA,EAAA,iBAAA,EAAA,wBAAA,EAAA,uBAAA,EAAA,UAAA,EAAA,YAAA,EAAA,EAAA,cAAA,EAAA,sBAAA,EAAA,EAAA,SAAA,EAAA;AACT,YAAA;AACE,gBAAA,OAAO,EAAE,iBAAiB;AAC1B,gBAAA,WAAW,EAAE,kBAAkB;AAC/B,gBAAA,KAAK,EAAE,IAAI;AACZ,aAAA;AACD,YAAA;AACE,gBAAA,OAAO,EAAE,aAAa;AACtB,gBAAA,WAAW,EAAE,kBAAkB;AAC/B,gBAAA,KAAK,EAAE,IAAI;AACZ,aAAA;AACD,YAAA;AACE,gBAAA,OAAO,EAAE,wBAAwB;AACjC,gBAAA,WAAW,EAAE,kBAAkB;AAChC,aAAA;AACF,SAAA,EAAA,QAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAEU,kBAAkB,EAAA,UAAA,EAAA,CAAA;kBAlC9B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,sBAAsB;AAChC,oBAAA,QAAQ,EAAE,oBAAoB;AAC9B,oBAAA,IAAI,EAAE;AACJ,wBAAA,OAAO,EAAE,sBAAsB;AAC/B,wBAAA,MAAM,EAAE,UAAU;AAClB,wBAAA,MAAM,EAAE,MAAM;AACd,wBAAA,eAAe,EAAE,SAAS;AAC1B,wBAAA,8BAA8B,EAAE,yBAAyB;AACzD,wBAAA,sBAAsB,EAAE,iBAAiB;AACzC,wBAAA,sBAAsB,EAAE,iBAAiB;AACzC,wBAAA,0BAA0B,EAAE,uBAAuB;AACnD,wBAAA,YAAY,EAAE,YAAY;AAC1B,wBAAA,QAAQ,EAAE,eAAe;AACzB,wBAAA,SAAS,EAAE,mCAAmC;AAC9C,wBAAA,WAAW,EAAE,wBAAwB;AACtC,qBAAA;AACD,oBAAA,SAAS,EAAE;AACT,wBAAA;AACE,4BAAA,OAAO,EAAE,iBAAiB;AAC1B,4BAAA,WAAW,EAAoB,kBAAA;AAC/B,4BAAA,KAAK,EAAE,IAAI;AACZ,yBAAA;AACD,wBAAA;AACE,4BAAA,OAAO,EAAE,aAAa;AACtB,4BAAA,WAAW,EAAoB,kBAAA;AAC/B,4BAAA,KAAK,EAAE,IAAI;AACZ,yBAAA;AACD,wBAAA;AACE,4BAAA,OAAO,EAAE,wBAAwB;AACjC,4BAAA,WAAW,EAAoB,kBAAA;AAChC,yBAAA;AACF,qBAAA;AACF,iBAAA;;;ACzDD;MAiBa,mBAAmB,CAAA;IACtB,cAAc,GAAG,MAAM,CAAC,qBAAqB,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;IAChE,gBAAgB,GAAG,CAAC,MAAK;AAC/B,QAAA,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,kBAAkB,CAAC,UAAU,CAAC,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;AAC1E,QAAA,MAAM,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC;AAC5B,QAAA,OAAO,KAAK,CAAC,MAAM,CAAC,GAAG,IAAI,GAAG,MAAM;KACrC,GAAG;AAEM,IAAA,WAAW,GAAG,QAAQ,CAAC,MAAK;AACpC,QAAA,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,EAAE;QACpC,OAAO,IAAI,CAAC,QAAQ,EAAE,IAAI,UAAU,CAAC,QAAQ,EAAE;AACjD,KAAC,CAAC;;AAGO,IAAA,UAAU,GAAkC,KAAK,CAAC,QAAQ,CAAmB;AACpF,QAAA,KAAK,EAAE,KAAK;AACb,KAAA,CAAC;;AAGO,IAAA,SAAS,GAAG,KAAK,CAAqB,SAAS,EAAE;AACxD,QAAA,KAAK,EAAE,YAAY;AACpB,KAAA,CAAC;;AAGO,IAAA,cAAc,GAAG,KAAK,CAAqB,SAAS,EAAE;AAC7D,QAAA,KAAK,EAAE,iBAAiB;AACzB,KAAA,CAAC;;IAGe,iBAAiB,GAAG,yBAAyB;;AAGrD,IAAA,QAAQ,GAA+C,KAAK,CAAC,KAAK,EAAE;AAC3E,QAAA,SAAS,EAAE,gBAAgB;AAC3B,QAAA,KAAK,EAAE,UAAU;AAClB,KAAA,CAAC;;AAGO,IAAA,QAAQ,GAA+B,KAAK,CAAC,IAAI,CAAC,gBAAgB,CAAC;;AAGnE,IAAA,aAAa,GAA+C,KAAK,CACxE,IAAI,CAAC,cAAc,EAAE,aAAa,IAAI,KAAK,EAC3C,EAAC,SAAS,EAAE,gBAAgB,EAAC,CAC9B;;AAGS,IAAA,KAAK,CAAC,KAAY,EAAA;QAC1B,IAAI,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE;AAC5C,YAAA,IAAI,CAAC,UAAU,EAAE,CAAC,IAAI,EAAE;YACxB,KAAK,CAAC,eAAe,EAAE;;;AAI3B;;;AAGG;IACH,YAAY,GAAA;AACV,QAAA,OAAO,IAAI,CAAC,cAAc,EAAE,GAAG,IAAI,GAAG,IAAI,CAAC,SAAS,EAAE,IAAI,IAAI,CAAC,iBAAiB;;uGA3DvE,mBAAmB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAnB,mBAAmB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,uBAAA,EAAA,MAAA,EAAA,EAAA,UAAA,EAAA,EAAA,iBAAA,EAAA,YAAA,EAAA,UAAA,EAAA,KAAA,EAAA,QAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EAAA,iBAAA,EAAA,IAAA,EAAA,EAAA,SAAA,EAAA,EAAA,iBAAA,EAAA,WAAA,EAAA,UAAA,EAAA,YAAA,EAAA,QAAA,EAAA,IAAA,EAAA,UAAA,EAAA,KAAA,EAAA,iBAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,EAAA,iBAAA,EAAA,gBAAA,EAAA,UAAA,EAAA,iBAAA,EAAA,QAAA,EAAA,IAAA,EAAA,UAAA,EAAA,KAAA,EAAA,iBAAA,EAAA,IAAA,EAAA,EAAA,QAAA,EAAA,EAAA,iBAAA,EAAA,UAAA,EAAA,UAAA,EAAA,UAAA,EAAA,QAAA,EAAA,IAAA,EAAA,UAAA,EAAA,KAAA,EAAA,iBAAA,EAAA,IAAA,EAAA,EAAA,QAAA,EAAA,EAAA,iBAAA,EAAA,UAAA,EAAA,UAAA,EAAA,UAAA,EAAA,QAAA,EAAA,IAAA,EAAA,UAAA,EAAA,KAAA,EAAA,iBAAA,EAAA,IAAA,EAAA,EAAA,aAAA,EAAA,EAAA,iBAAA,EAAA,eAAA,EAAA,UAAA,EAAA,eAAA,EAAA,QAAA,EAAA,IAAA,EAAA,UAAA,EAAA,KAAA,EAAA,iBAAA,EAAA,IAAA,EAAA,EAAA,EAAA,IAAA,EAAA,EAAA,SAAA,EAAA,EAAA,OAAA,EAAA,eAAA,EAAA,EAAA,UAAA,EAAA,EAAA,eAAA,EAAA,MAAA,EAAA,EAAA,cAAA,EAAA,uBAAA,EAAA,EAAA,QAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,ECzChC,ygCAwBA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EDeY,aAAa,EAAA,QAAA,EAAA,yBAAA,EAAA,QAAA,EAAA,CAAA,WAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAEZ,mBAAmB,EAAA,UAAA,EAAA,CAAA;kBAhB/B,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,uBAAuB,EAE3B,IAAA,EAAA;AACJ,wBAAA,OAAO,EAAE,uBAAuB;AAChC,wBAAA,iBAAiB,EAAE,MAAM;;;;AAIzB,wBAAA,SAAS,EAAE,eAAe;AAC3B,qBAAA,EAAA,QAAA,EACS,qBAAqB,EAAA,aAAA,EAChB,iBAAiB,CAAC,IAAI,EAAA,eAAA,EACpB,uBAAuB,CAAC,MAAM,EAAA,OAAA,EACtC,CAAC,aAAa,CAAC,EAAA,QAAA,EAAA,ygCAAA,EAAA;;;MErBb,mBAAmB,CAAA;uGAAnB,mBAAmB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;AAAnB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,mBAAmB,EAHpB,OAAA,EAAA,CAAA,aAAa,EAAE,kBAAkB,EAAE,mBAAmB,CACtD,EAAA,OAAA,EAAA,CAAA,mBAAmB,EAAE,aAAa,EAAE,kBAAkB,EAAE,mBAAmB,CAAA,EAAA,CAAA;AAE1E,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,mBAAmB,EAHpB,OAAA,EAAA,CAAA,aAAa,EAAsB,mBAAmB,EACtD,mBAAmB,CAAA,EAAA,CAAA;;2FAElB,mBAAmB,EAAA,UAAA,EAAA,CAAA;kBAJ/B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,OAAO,EAAE,CAAC,aAAa,EAAE,kBAAkB,EAAE,mBAAmB,CAAC;oBACjE,OAAO,EAAE,CAAC,mBAAmB,EAAE,aAAa,EAAE,kBAAkB,EAAE,mBAAmB,CAAC;AACvF,iBAAA;;;;;"}