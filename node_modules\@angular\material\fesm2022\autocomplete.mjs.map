{"version": 3, "file": "autocomplete.mjs", "sources": ["../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/autocomplete/autocomplete.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/autocomplete/autocomplete.html", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/autocomplete/autocomplete-origin.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/autocomplete/autocomplete-trigger.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/autocomplete/module.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  ANIMATION_MODULE_TYPE,\n  AfterContentInit,\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  ContentChildren,\n  ElementRef,\n  EventEmitter,\n  InjectionToken,\n  Input,\n  OnDestroy,\n  Output,\n  QueryList,\n  TemplateRef,\n  ViewChild,\n  ViewEncapsulation,\n  booleanAttribute,\n  inject,\n} from '@angular/core';\nimport {\n  MAT_OPTGROUP,\n  MAT_OPTION_PARENT_COMPONENT,\n  MatOptgroup,\n  MatOption,\n  ThemePalette,\n} from '../core';\nimport {_IdGenerator, ActiveDescendantKeyManager} from '@angular/cdk/a11y';\nimport {Platform} from '@angular/cdk/platform';\nimport {Subscription} from 'rxjs';\n\n/** Event object that is emitted when an autocomplete option is selected. */\nexport class MatAutocompleteSelectedEvent {\n  constructor(\n    /** Reference to the autocomplete panel that emitted the event. */\n    public source: MatAutocomplete,\n    /** Option that was selected. */\n    public option: MatOption,\n  ) {}\n}\n\n/** Event object that is emitted when an autocomplete option is activated. */\nexport interface MatAutocompleteActivatedEvent {\n  /** Reference to the autocomplete panel that emitted the event. */\n  source: MatAutocomplete;\n\n  /** Option that was selected. */\n  option: MatOption | null;\n}\n\n/** Default `mat-autocomplete` options that can be overridden. */\nexport interface MatAutocompleteDefaultOptions {\n  /** Whether the first option should be highlighted when an autocomplete panel is opened. */\n  autoActiveFirstOption?: boolean;\n\n  /** Whether the active option should be selected as the user is navigating. */\n  autoSelectActiveOption?: boolean;\n\n  /**\n   * Whether the user is required to make a selection when\n   * they're interacting with the autocomplete.\n   */\n  requireSelection?: boolean;\n\n  /** Class to be applied to the autocomplete's backdrop. */\n  backdropClass?: string;\n\n  /** Whether the autocomplete has a backdrop. */\n  hasBackdrop?: boolean;\n\n  /** Class or list of classes to be applied to the autocomplete's overlay panel. */\n  overlayPanelClass?: string | string[];\n\n  /** Whether icon indicators should be hidden for single-selection. */\n  hideSingleSelectionIndicator?: boolean;\n}\n\n/** Injection token to be used to override the default options for `mat-autocomplete`. */\nexport const MAT_AUTOCOMPLETE_DEFAULT_OPTIONS = new InjectionToken<MatAutocompleteDefaultOptions>(\n  'mat-autocomplete-default-options',\n  {\n    providedIn: 'root',\n    factory: MAT_AUTOCOMPLETE_DEFAULT_OPTIONS_FACTORY,\n  },\n);\n\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nexport function MAT_AUTOCOMPLETE_DEFAULT_OPTIONS_FACTORY(): MatAutocompleteDefaultOptions {\n  return {\n    autoActiveFirstOption: false,\n    autoSelectActiveOption: false,\n    hideSingleSelectionIndicator: false,\n    requireSelection: false,\n    hasBackdrop: false,\n  };\n}\n\n/** Autocomplete component. */\n@Component({\n  selector: 'mat-autocomplete',\n  templateUrl: 'autocomplete.html',\n  styleUrl: 'autocomplete.css',\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  exportAs: 'matAutocomplete',\n  host: {\n    'class': 'mat-mdc-autocomplete',\n  },\n  providers: [{provide: MAT_OPTION_PARENT_COMPONENT, useExisting: MatAutocomplete}],\n})\nexport class MatAutocomplete implements AfterContentInit, OnDestroy {\n  private _changeDetectorRef = inject(ChangeDetectorRef);\n  private _elementRef = inject<ElementRef<HTMLElement>>(ElementRef);\n  protected _defaults = inject<MatAutocompleteDefaultOptions>(MAT_AUTOCOMPLETE_DEFAULT_OPTIONS);\n  protected _animationsDisabled =\n    inject(ANIMATION_MODULE_TYPE, {optional: true}) === 'NoopAnimations';\n  private _activeOptionChanges = Subscription.EMPTY;\n\n  /** Manages active item in option list based on key events. */\n  _keyManager: ActiveDescendantKeyManager<MatOption>;\n\n  /** Whether the autocomplete panel should be visible, depending on option length. */\n  showPanel: boolean = false;\n\n  /** Whether the autocomplete panel is open. */\n  get isOpen(): boolean {\n    return this._isOpen && this.showPanel;\n  }\n  _isOpen: boolean = false;\n\n  /** Latest trigger that opened the autocomplete. */\n  _latestOpeningTrigger: unknown;\n\n  /** @docs-private Sets the theme color of the panel. */\n  _setColor(value: ThemePalette) {\n    this._color = value;\n    this._changeDetectorRef.markForCheck();\n  }\n  /** @docs-private theme color of the panel */\n  protected _color: ThemePalette;\n\n  // The @ViewChild query for TemplateRef here needs to be static because some code paths\n  // lead to the overlay being created before change detection has finished for this component.\n  // Notably, another component may trigger `focus` on the autocomplete-trigger.\n\n  /** @docs-private */\n  @ViewChild(TemplateRef, {static: true}) template: TemplateRef<any>;\n\n  /** Element for the panel containing the autocomplete options. */\n  @ViewChild('panel') panel: ElementRef;\n\n  /** Reference to all options within the autocomplete. */\n  @ContentChildren(MatOption, {descendants: true}) options: QueryList<MatOption>;\n\n  /** Reference to all option groups within the autocomplete. */\n  @ContentChildren(MAT_OPTGROUP, {descendants: true}) optionGroups: QueryList<MatOptgroup>;\n\n  /** Aria label of the autocomplete. */\n  @Input('aria-label') ariaLabel: string;\n\n  /** Input that can be used to specify the `aria-labelledby` attribute. */\n  @Input('aria-labelledby') ariaLabelledby: string;\n\n  /** Function that maps an option's control value to its display value in the trigger. */\n  @Input() displayWith: ((value: any) => string) | null = null;\n\n  /**\n   * Whether the first option should be highlighted when the autocomplete panel is opened.\n   * Can be configured globally through the `MAT_AUTOCOMPLETE_DEFAULT_OPTIONS` token.\n   */\n  @Input({transform: booleanAttribute}) autoActiveFirstOption: boolean;\n\n  /** Whether the active option should be selected as the user is navigating. */\n  @Input({transform: booleanAttribute}) autoSelectActiveOption: boolean;\n\n  /**\n   * Whether the user is required to make a selection when they're interacting with the\n   * autocomplete. If the user moves away from the autocomplete without selecting an option from\n   * the list, the value will be reset. If the user opens the panel and closes it without\n   * interacting or selecting a value, the initial value will be kept.\n   */\n  @Input({transform: booleanAttribute}) requireSelection: boolean;\n\n  /**\n   * Specify the width of the autocomplete panel.  Can be any CSS sizing value, otherwise it will\n   * match the width of its host.\n   */\n  @Input() panelWidth: string | number;\n\n  /** Whether ripples are disabled within the autocomplete panel. */\n  @Input({transform: booleanAttribute}) disableRipple: boolean;\n\n  /** Event that is emitted whenever an option from the list is selected. */\n  @Output() readonly optionSelected: EventEmitter<MatAutocompleteSelectedEvent> =\n    new EventEmitter<MatAutocompleteSelectedEvent>();\n\n  /** Event that is emitted when the autocomplete panel is opened. */\n  @Output() readonly opened: EventEmitter<void> = new EventEmitter<void>();\n\n  /** Event that is emitted when the autocomplete panel is closed. */\n  @Output() readonly closed: EventEmitter<void> = new EventEmitter<void>();\n\n  /** Emits whenever an option is activated. */\n  @Output() readonly optionActivated: EventEmitter<MatAutocompleteActivatedEvent> =\n    new EventEmitter<MatAutocompleteActivatedEvent>();\n\n  /**\n   * Takes classes set on the host mat-autocomplete element and applies them to the panel\n   * inside the overlay container to allow for easy styling.\n   */\n  @Input('class')\n  set classList(value: string | string[]) {\n    this._classList = value;\n    this._elementRef.nativeElement.className = '';\n  }\n  _classList: string | string[];\n\n  /** Whether checkmark indicator for single-selection options is hidden. */\n  @Input({transform: booleanAttribute})\n  get hideSingleSelectionIndicator(): boolean {\n    return this._hideSingleSelectionIndicator;\n  }\n  set hideSingleSelectionIndicator(value: boolean) {\n    this._hideSingleSelectionIndicator = value;\n    this._syncParentProperties();\n  }\n  private _hideSingleSelectionIndicator: boolean;\n\n  /** Syncs the parent state with the individual options. */\n  _syncParentProperties(): void {\n    if (this.options) {\n      for (const option of this.options) {\n        option._changeDetectorRef.markForCheck();\n      }\n    }\n  }\n\n  /** Unique ID to be used by autocomplete trigger's \"aria-owns\" property. */\n  id: string = inject(_IdGenerator).getId('mat-autocomplete-');\n\n  /**\n   * Tells any descendant `mat-optgroup` to use the inert a11y pattern.\n   * @docs-private\n   */\n  readonly inertGroups: boolean;\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    const platform = inject(Platform);\n\n    // TODO(crisbeto): the problem that the `inertGroups` option resolves is only present on\n    // Safari using VoiceOver. We should occasionally check back to see whether the bug\n    // wasn't resolved in VoiceOver, and if it has, we can remove this and the `inertGroups`\n    // option altogether.\n    this.inertGroups = platform?.SAFARI || false;\n    this.autoActiveFirstOption = !!this._defaults.autoActiveFirstOption;\n    this.autoSelectActiveOption = !!this._defaults.autoSelectActiveOption;\n    this.requireSelection = !!this._defaults.requireSelection;\n    this._hideSingleSelectionIndicator = this._defaults.hideSingleSelectionIndicator ?? false;\n  }\n\n  ngAfterContentInit() {\n    this._keyManager = new ActiveDescendantKeyManager<MatOption>(this.options)\n      .withWrap()\n      .skipPredicate(this._skipPredicate);\n    this._activeOptionChanges = this._keyManager.change.subscribe(index => {\n      if (this.isOpen) {\n        this.optionActivated.emit({source: this, option: this.options.toArray()[index] || null});\n      }\n    });\n\n    // Set the initial visibility state.\n    this._setVisibility();\n  }\n\n  ngOnDestroy() {\n    this._keyManager?.destroy();\n    this._activeOptionChanges.unsubscribe();\n  }\n\n  /**\n   * Sets the panel scrollTop. This allows us to manually scroll to display options\n   * above or below the fold, as they are not actually being focused when active.\n   */\n  _setScrollTop(scrollTop: number): void {\n    if (this.panel) {\n      this.panel.nativeElement.scrollTop = scrollTop;\n    }\n  }\n\n  /** Returns the panel's scrollTop. */\n  _getScrollTop(): number {\n    return this.panel ? this.panel.nativeElement.scrollTop : 0;\n  }\n\n  /** Panel should hide itself when the option list is empty. */\n  _setVisibility() {\n    this.showPanel = !!this.options?.length;\n    this._changeDetectorRef.markForCheck();\n  }\n\n  /** Emits the `select` event. */\n  _emitSelectEvent(option: MatOption): void {\n    const event = new MatAutocompleteSelectedEvent(this, option);\n    this.optionSelected.emit(event);\n  }\n\n  /** Gets the aria-labelledby for the autocomplete panel. */\n  _getPanelAriaLabelledby(labelId: string | null): string | null {\n    if (this.ariaLabel) {\n      return null;\n    }\n\n    const labelExpression = labelId ? labelId + ' ' : '';\n    return this.ariaLabelledby ? labelExpression + this.ariaLabelledby : labelId;\n  }\n\n  // `skipPredicate` determines if key manager should avoid putting a given option in the tab\n  // order. Allow disabled list items to receive focus via keyboard to align with WAI ARIA\n  // recommendation.\n  //\n  // Normally WAI ARIA's instructions are to exclude disabled items from the tab order, but it\n  // makes a few exceptions for compound widgets.\n  //\n  // From [Developing a Keyboard Interface](\n  // https://www.w3.org/WAI/ARIA/apg/practices/keyboard-interface/):\n  //   \"For the following composite widget elements, keep them focusable when disabled: Options in a\n  //   Listbox...\"\n  //\n  // The user can focus disabled options using the keyboard, but the user cannot click disabled\n  // options.\n  protected _skipPredicate() {\n    return false;\n  }\n}\n", "<ng-template let-formFieldId=\"id\">\n  <div\n    class=\"mat-mdc-autocomplete-panel mdc-menu-surface mdc-menu-surface--open\"\n    role=\"listbox\"\n    [id]=\"id\"\n    [class]=\"_classList\"\n    [class.mat-mdc-autocomplete-visible]=\"showPanel\"\n    [class.mat-mdc-autocomplete-hidden]=\"!showPanel\"\n    [class.mat-autocomplete-panel-animations-enabled]=\"!_animationsDisabled\"\n    [class.mat-primary]=\"_color === 'primary'\"\n    [class.mat-accent]=\"_color === 'accent'\"\n    [class.mat-warn]=\"_color === 'warn'\"\n    [attr.aria-label]=\"ariaLabel || null\"\n    [attr.aria-labelledby]=\"_getPanelAriaLabelledby(formFieldId)\"\n    #panel>\n    <ng-content></ng-content>\n  </div>\n</ng-template>\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Directive, ElementRef, inject} from '@angular/core';\n\n/**\n * Directive applied to an element to make it usable\n * as a connection point for an autocomplete panel.\n */\n@Directive({\n  selector: '[matAutocompleteOrigin]',\n  exportAs: 'matAutocompleteOrigin',\n})\nexport class MatAutocompleteOrigin {\n  elementRef = inject<ElementRef<HTMLElement>>(ElementRef);\n\n  constructor(...args: unknown[]);\n  constructor() {}\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {addAriaReferencedId, removeAriaReferencedId} from '@angular/cdk/a11y';\nimport {Directionality} from '@angular/cdk/bidi';\nimport {DOWN_ARROW, ENTER, ESCAPE, TAB, UP_ARROW, hasModifierKey} from '@angular/cdk/keycodes';\nimport {BreakpointObserver, Breakpoints} from '@angular/cdk/layout';\nimport {\n  ConnectedPosition,\n  FlexibleConnectedPositionStrategy,\n  Overlay,\n  OverlayConfig,\n  OverlayRef,\n  PositionStrategy,\n  ScrollStrategy,\n} from '@angular/cdk/overlay';\nimport {_getEventTarget, _getFocusedElementPierceShadowDom} from '@angular/cdk/platform';\nimport {TemplatePortal} from '@angular/cdk/portal';\nimport {ViewportRuler} from '@angular/cdk/scrolling';\nimport {\n  AfterViewInit,\n  ChangeDetectorRef,\n  Directive,\n  ElementRef,\n  EnvironmentInjector,\n  InjectionToken,\n  Input,\n  NgZone,\n  OnChanges,\n  OnDestroy,\n  Renderer2,\n  SimpleChanges,\n  ViewContainerRef,\n  afterNextRender,\n  booleanAttribute,\n  forwardRef,\n  inject,\n} from '@angular/core';\nimport {ControlValueAccessor, NG_VALUE_ACCESSOR} from '@angular/forms';\nimport {\n  MatOption,\n  MatOptionSelectionChange,\n  _countGroupLabelsBeforeOption,\n  _getOptionScrollPosition,\n} from '../core';\nimport {MAT_FORM_FIELD, MatFormField} from '../form-field';\nimport {Observable, Subject, Subscription, defer, merge, of as observableOf} from 'rxjs';\nimport {delay, filter, map, startWith, switchMap, take, tap} from 'rxjs/operators';\nimport {\n  MAT_AUTOCOMPLETE_DEFAULT_OPTIONS,\n  MatAutocomplete,\n  MatAutocompleteDefaultOptions,\n} from './autocomplete';\nimport {MatAutocompleteOrigin} from './autocomplete-origin';\n\n/**\n * Provider that allows the autocomplete to register as a ControlValueAccessor.\n * @docs-private\n */\nexport const MAT_AUTOCOMPLETE_VALUE_ACCESSOR: any = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => MatAutocompleteTrigger),\n  multi: true,\n};\n\n/**\n * Creates an error to be thrown when attempting to use an autocomplete trigger without a panel.\n * @docs-private\n */\nexport function getMatAutocompleteMissingPanelError(): Error {\n  return Error(\n    'Attempting to open an undefined instance of `mat-autocomplete`. ' +\n      'Make sure that the id passed to the `matAutocomplete` is correct and that ' +\n      \"you're attempting to open it after the ngAfterContentInit hook.\",\n  );\n}\n\n/** Injection token that determines the scroll handling while the autocomplete panel is open. */\nexport const MAT_AUTOCOMPLETE_SCROLL_STRATEGY = new InjectionToken<() => ScrollStrategy>(\n  'mat-autocomplete-scroll-strategy',\n  {\n    providedIn: 'root',\n    factory: () => {\n      const overlay = inject(Overlay);\n      return () => overlay.scrollStrategies.reposition();\n    },\n  },\n);\n\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nexport function MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY(overlay: Overlay): () => ScrollStrategy {\n  return () => overlay.scrollStrategies.reposition();\n}\n\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nexport const MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY_PROVIDER = {\n  provide: MAT_AUTOCOMPLETE_SCROLL_STRATEGY,\n  deps: [Overlay],\n  useFactory: MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY,\n};\n\n/** Base class with all of the `MatAutocompleteTrigger` functionality. */\n@Directive({\n  selector: `input[matAutocomplete], textarea[matAutocomplete]`,\n  host: {\n    'class': 'mat-mdc-autocomplete-trigger',\n    '[attr.autocomplete]': 'autocompleteAttribute',\n    '[attr.role]': 'autocompleteDisabled ? null : \"combobox\"',\n    '[attr.aria-autocomplete]': 'autocompleteDisabled ? null : \"list\"',\n    '[attr.aria-activedescendant]': '(panelOpen && activeOption) ? activeOption.id : null',\n    '[attr.aria-expanded]': 'autocompleteDisabled ? null : panelOpen.toString()',\n    '[attr.aria-controls]': '(autocompleteDisabled || !panelOpen) ? null : autocomplete?.id',\n    '[attr.aria-haspopup]': 'autocompleteDisabled ? null : \"listbox\"',\n    // Note: we use `focusin`, as opposed to `focus`, in order to open the panel\n    // a little earlier. This avoids issues where IE delays the focusing of the input.\n    '(focusin)': '_handleFocus()',\n    '(blur)': '_onTouched()',\n    '(input)': '_handleInput($event)',\n    '(keydown)': '_handleKeydown($event)',\n    '(click)': '_handleClick()',\n  },\n  exportAs: 'matAutocompleteTrigger',\n  providers: [MAT_AUTOCOMPLETE_VALUE_ACCESSOR],\n})\nexport class MatAutocompleteTrigger\n  implements ControlValueAccessor, AfterViewInit, OnChanges, OnDestroy\n{\n  private _environmentInjector = inject(EnvironmentInjector);\n  private _element = inject<ElementRef<HTMLInputElement>>(ElementRef);\n  private _overlay = inject(Overlay);\n  private _viewContainerRef = inject(ViewContainerRef);\n  private _zone = inject(NgZone);\n  private _changeDetectorRef = inject(ChangeDetectorRef);\n  private _dir = inject(Directionality, {optional: true});\n  private _formField = inject<MatFormField | null>(MAT_FORM_FIELD, {optional: true, host: true});\n  private _viewportRuler = inject(ViewportRuler);\n  private _scrollStrategy = inject(MAT_AUTOCOMPLETE_SCROLL_STRATEGY);\n  private _renderer = inject(Renderer2);\n  private _defaults = inject<MatAutocompleteDefaultOptions | null>(\n    MAT_AUTOCOMPLETE_DEFAULT_OPTIONS,\n    {optional: true},\n  );\n\n  private _overlayRef: OverlayRef | null;\n  private _portal: TemplatePortal;\n  private _componentDestroyed = false;\n  private _initialized = new Subject();\n  private _keydownSubscription: Subscription | null;\n  private _outsideClickSubscription: Subscription | null;\n  private _cleanupWindowBlur: (() => void) | undefined;\n\n  /** Old value of the native input. Used to work around issues with the `input` event on IE. */\n  private _previousValue: string | number | null;\n\n  /** Value of the input element when the panel was attached (even if there are no options). */\n  private _valueOnAttach: string | number | null;\n\n  /** Value on the previous keydown event. */\n  private _valueOnLastKeydown: string | null;\n\n  /** Strategy that is used to position the panel. */\n  private _positionStrategy: FlexibleConnectedPositionStrategy;\n\n  /** Whether or not the label state is being overridden. */\n  private _manuallyFloatingLabel = false;\n\n  /** The subscription for closing actions (some are bound to document). */\n  private _closingActionsSubscription: Subscription;\n\n  /** Subscription to viewport size changes. */\n  private _viewportSubscription = Subscription.EMPTY;\n\n  /** Implements BreakpointObserver to be used to detect handset landscape */\n  private _breakpointObserver = inject(BreakpointObserver);\n  private _handsetLandscapeSubscription = Subscription.EMPTY;\n\n  /**\n   * Whether the autocomplete can open the next time it is focused. Used to prevent a focused,\n   * closed autocomplete from being reopened if the user switches to another browser tab and then\n   * comes back.\n   */\n  private _canOpenOnNextFocus = true;\n\n  /** Value inside the input before we auto-selected an option. */\n  private _valueBeforeAutoSelection: string | undefined;\n\n  /**\n   * Current option that we have auto-selected as the user is navigating,\n   * but which hasn't been propagated to the model value yet.\n   */\n  private _pendingAutoselectedOption: MatOption | null;\n\n  /** Stream of keyboard events that can close the panel. */\n  private readonly _closeKeyEventStream = new Subject<void>();\n\n  /**\n   * Event handler for when the window is blurred. Needs to be an\n   * arrow function in order to preserve the context.\n   */\n  private _windowBlurHandler = () => {\n    // If the user blurred the window while the autocomplete is focused, it means that it'll be\n    // refocused when they come back. In this case we want to skip the first focus event, if the\n    // pane was closed, in order to avoid reopening it unintentionally.\n    this._canOpenOnNextFocus = this.panelOpen || !this._hasFocus();\n  };\n\n  /** `View -> model callback called when value changes` */\n  _onChange: (value: any) => void = () => {};\n\n  /** `View -> model callback called when autocomplete has been touched` */\n  _onTouched = () => {};\n\n  /** The autocomplete panel to be attached to this trigger. */\n  @Input('matAutocomplete') autocomplete: MatAutocomplete;\n\n  /**\n   * Position of the autocomplete panel relative to the trigger element. A position of `auto`\n   * will render the panel underneath the trigger if there is enough space for it to fit in\n   * the viewport, otherwise the panel will be shown above it. If the position is set to\n   * `above` or `below`, the panel will always be shown above or below the trigger. no matter\n   * whether it fits completely in the viewport.\n   */\n  @Input('matAutocompletePosition') position: 'auto' | 'above' | 'below' = 'auto';\n\n  /**\n   * Reference relative to which to position the autocomplete panel.\n   * Defaults to the autocomplete trigger element.\n   */\n  @Input('matAutocompleteConnectedTo') connectedTo: MatAutocompleteOrigin;\n\n  /**\n   * `autocomplete` attribute to be set on the input element.\n   * @docs-private\n   */\n  @Input('autocomplete') autocompleteAttribute: string = 'off';\n\n  /**\n   * Whether the autocomplete is disabled. When disabled, the element will\n   * act as a regular input and the user won't be able to open the panel.\n   */\n  @Input({alias: 'matAutocompleteDisabled', transform: booleanAttribute})\n  autocompleteDisabled: boolean;\n\n  constructor(...args: unknown[]);\n  constructor() {}\n\n  /** Class to apply to the panel when it's above the input. */\n  private _aboveClass = 'mat-mdc-autocomplete-panel-above';\n\n  ngAfterViewInit() {\n    this._initialized.next();\n    this._initialized.complete();\n    this._cleanupWindowBlur = this._renderer.listen('window', 'blur', this._windowBlurHandler);\n  }\n\n  ngOnChanges(changes: SimpleChanges) {\n    if (changes['position'] && this._positionStrategy) {\n      this._setStrategyPositions(this._positionStrategy);\n\n      if (this.panelOpen) {\n        this._overlayRef!.updatePosition();\n      }\n    }\n  }\n\n  ngOnDestroy() {\n    this._cleanupWindowBlur?.();\n    this._handsetLandscapeSubscription.unsubscribe();\n    this._viewportSubscription.unsubscribe();\n    this._componentDestroyed = true;\n    this._destroyPanel();\n    this._closeKeyEventStream.complete();\n    this._clearFromModal();\n  }\n\n  /** Whether or not the autocomplete panel is open. */\n  get panelOpen(): boolean {\n    return this._overlayAttached && this.autocomplete.showPanel;\n  }\n  private _overlayAttached: boolean = false;\n\n  /** Opens the autocomplete suggestion panel. */\n  openPanel(): void {\n    this._openPanelInternal();\n  }\n\n  /** Closes the autocomplete suggestion panel. */\n  closePanel(): void {\n    this._resetLabel();\n\n    if (!this._overlayAttached) {\n      return;\n    }\n\n    if (this.panelOpen) {\n      // Only emit if the panel was visible.\n      // `afterNextRender` always runs outside of the Angular zone, so all the subscriptions from\n      // `_subscribeToClosingActions()` are also outside of the Angular zone.\n      // We should manually run in Angular zone to update UI after panel closing.\n      this._zone.run(() => {\n        this.autocomplete.closed.emit();\n      });\n    }\n\n    // Only reset if this trigger is the latest one that opened the\n    // autocomplete since another may have taken it over.\n    if (this.autocomplete._latestOpeningTrigger === this) {\n      this.autocomplete._isOpen = false;\n      this.autocomplete._latestOpeningTrigger = null;\n    }\n\n    this._overlayAttached = false;\n    this._pendingAutoselectedOption = null;\n\n    if (this._overlayRef && this._overlayRef.hasAttached()) {\n      this._overlayRef.detach();\n      this._closingActionsSubscription.unsubscribe();\n    }\n\n    this._updatePanelState();\n\n    // Note that in some cases this can end up being called after the component is destroyed.\n    // Add a check to ensure that we don't try to run change detection on a destroyed view.\n    if (!this._componentDestroyed) {\n      // We need to trigger change detection manually, because\n      // `fromEvent` doesn't seem to do it at the proper time.\n      // This ensures that the label is reset when the\n      // user clicks outside.\n      this._changeDetectorRef.detectChanges();\n    }\n\n    // Remove aria-owns attribute when the autocomplete is no longer visible.\n    if (this._trackedModal) {\n      removeAriaReferencedId(this._trackedModal, 'aria-owns', this.autocomplete.id);\n    }\n  }\n\n  /**\n   * Updates the position of the autocomplete suggestion panel to ensure that it fits all options\n   * within the viewport.\n   */\n  updatePosition(): void {\n    if (this._overlayAttached) {\n      this._overlayRef!.updatePosition();\n    }\n  }\n\n  /**\n   * A stream of actions that should close the autocomplete panel, including\n   * when an option is selected, on blur, and when TAB is pressed.\n   */\n  get panelClosingActions(): Observable<MatOptionSelectionChange | null> {\n    return merge(\n      this.optionSelections,\n      this.autocomplete._keyManager.tabOut.pipe(filter(() => this._overlayAttached)),\n      this._closeKeyEventStream,\n      this._getOutsideClickStream(),\n      this._overlayRef\n        ? this._overlayRef.detachments().pipe(filter(() => this._overlayAttached))\n        : observableOf(),\n    ).pipe(\n      // Normalize the output so we return a consistent type.\n      map(event => (event instanceof MatOptionSelectionChange ? event : null)),\n    );\n  }\n\n  /** Stream of changes to the selection state of the autocomplete options. */\n  readonly optionSelections: Observable<MatOptionSelectionChange> = defer(() => {\n    const options = this.autocomplete ? this.autocomplete.options : null;\n\n    if (options) {\n      return options.changes.pipe(\n        startWith(options),\n        switchMap(() => merge(...options.map(option => option.onSelectionChange))),\n      );\n    }\n\n    // If there are any subscribers before `ngAfterViewInit`, the `autocomplete` will be undefined.\n    // Return a stream that we'll replace with the real one once everything is in place.\n    return this._initialized.pipe(switchMap(() => this.optionSelections));\n  }) as Observable<MatOptionSelectionChange>;\n\n  /** The currently active option, coerced to MatOption type. */\n  get activeOption(): MatOption | null {\n    if (this.autocomplete && this.autocomplete._keyManager) {\n      return this.autocomplete._keyManager.activeItem;\n    }\n\n    return null;\n  }\n\n  /** Stream of clicks outside of the autocomplete panel. */\n  private _getOutsideClickStream(): Observable<any> {\n    return new Observable(observer => {\n      const listener = (event: MouseEvent | TouchEvent) => {\n        // If we're in the Shadow DOM, the event target will be the shadow root, so we have to\n        // fall back to check the first element in the path of the click event.\n        const clickTarget = _getEventTarget<HTMLElement>(event)!;\n        const formField = this._formField\n          ? this._formField.getConnectedOverlayOrigin().nativeElement\n          : null;\n        const customOrigin = this.connectedTo ? this.connectedTo.elementRef.nativeElement : null;\n\n        if (\n          this._overlayAttached &&\n          clickTarget !== this._element.nativeElement &&\n          // Normally focus moves inside `mousedown` so this condition will almost always be\n          // true. Its main purpose is to handle the case where the input is focused from an\n          // outside click which propagates up to the `body` listener within the same sequence\n          // and causes the panel to close immediately (see #3106).\n          !this._hasFocus() &&\n          (!formField || !formField.contains(clickTarget)) &&\n          (!customOrigin || !customOrigin.contains(clickTarget)) &&\n          !!this._overlayRef &&\n          !this._overlayRef.overlayElement.contains(clickTarget)\n        ) {\n          observer.next(event);\n        }\n      };\n\n      const cleanups = [\n        this._renderer.listen('document', 'click', listener),\n        this._renderer.listen('document', 'auxclick', listener),\n        this._renderer.listen('document', 'touchend', listener),\n      ];\n\n      return () => {\n        cleanups.forEach(current => current());\n      };\n    });\n  }\n\n  // Implemented as part of ControlValueAccessor.\n  writeValue(value: any): void {\n    Promise.resolve(null).then(() => this._assignOptionValue(value));\n  }\n\n  // Implemented as part of ControlValueAccessor.\n  registerOnChange(fn: (value: any) => {}): void {\n    this._onChange = fn;\n  }\n\n  // Implemented as part of ControlValueAccessor.\n  registerOnTouched(fn: () => {}) {\n    this._onTouched = fn;\n  }\n\n  // Implemented as part of ControlValueAccessor.\n  setDisabledState(isDisabled: boolean) {\n    this._element.nativeElement.disabled = isDisabled;\n  }\n\n  _handleKeydown(event: KeyboardEvent): void {\n    const keyCode = event.keyCode;\n    const hasModifier = hasModifierKey(event);\n\n    // Prevent the default action on all escape key presses. This is here primarily to bring IE\n    // in line with other browsers. By default, pressing escape on IE will cause it to revert\n    // the input value to the one that it had on focus, however it won't dispatch any events\n    // which means that the model value will be out of sync with the view.\n    if (keyCode === ESCAPE && !hasModifier) {\n      event.preventDefault();\n    }\n\n    this._valueOnLastKeydown = this._element.nativeElement.value;\n\n    if (this.activeOption && keyCode === ENTER && this.panelOpen && !hasModifier) {\n      this.activeOption._selectViaInteraction();\n      this._resetActiveItem();\n      event.preventDefault();\n    } else if (this.autocomplete) {\n      const prevActiveItem = this.autocomplete._keyManager.activeItem;\n      const isArrowKey = keyCode === UP_ARROW || keyCode === DOWN_ARROW;\n\n      if (keyCode === TAB || (isArrowKey && !hasModifier && this.panelOpen)) {\n        this.autocomplete._keyManager.onKeydown(event);\n      } else if (isArrowKey && this._canOpen()) {\n        this._openPanelInternal(this._valueOnLastKeydown);\n      }\n\n      if (isArrowKey || this.autocomplete._keyManager.activeItem !== prevActiveItem) {\n        this._scrollToOption(this.autocomplete._keyManager.activeItemIndex || 0);\n\n        if (this.autocomplete.autoSelectActiveOption && this.activeOption) {\n          if (!this._pendingAutoselectedOption) {\n            this._valueBeforeAutoSelection = this._valueOnLastKeydown;\n          }\n\n          this._pendingAutoselectedOption = this.activeOption;\n          this._assignOptionValue(this.activeOption.value);\n        }\n      }\n    }\n  }\n\n  _handleInput(event: KeyboardEvent): void {\n    let target = event.target as HTMLInputElement;\n    let value: number | string | null = target.value;\n\n    // Based on `NumberValueAccessor` from forms.\n    if (target.type === 'number') {\n      value = value == '' ? null : parseFloat(value);\n    }\n\n    // If the input has a placeholder, IE will fire the `input` event on page load,\n    // focus and blur, in addition to when the user actually changed the value. To\n    // filter out all of the extra events, we save the value on focus and between\n    // `input` events, and we check whether it changed.\n    // See: https://connect.microsoft.com/IE/feedback/details/885747/\n    if (this._previousValue !== value) {\n      this._previousValue = value;\n      this._pendingAutoselectedOption = null;\n\n      // If selection is required we don't write to the CVA while the user is typing.\n      // At the end of the selection either the user will have picked something\n      // or we'll reset the value back to null.\n      if (!this.autocomplete || !this.autocomplete.requireSelection) {\n        this._onChange(value);\n      }\n\n      if (!value) {\n        this._clearPreviousSelectedOption(null, false);\n      } else if (this.panelOpen && !this.autocomplete.requireSelection) {\n        // Note that we don't reset this when `requireSelection` is enabled,\n        // because the option will be reset when the panel is closed.\n        const selectedOption = this.autocomplete.options?.find(option => option.selected);\n\n        if (selectedOption) {\n          const display = this._getDisplayValue(selectedOption.value);\n\n          if (value !== display) {\n            selectedOption.deselect(false);\n          }\n        }\n      }\n\n      if (this._canOpen() && this._hasFocus()) {\n        // When the `input` event fires, the input's value will have already changed. This means\n        // that if we take the `this._element.nativeElement.value` directly, it'll be one keystroke\n        // behind. This can be a problem when the user selects a value, changes a character while\n        // the input still has focus and then clicks away (see #28432). To work around it, we\n        // capture the value in `keydown` so we can use it here.\n        const valueOnAttach = this._valueOnLastKeydown ?? this._element.nativeElement.value;\n        this._valueOnLastKeydown = null;\n        this._openPanelInternal(valueOnAttach);\n      }\n    }\n  }\n\n  _handleFocus(): void {\n    if (!this._canOpenOnNextFocus) {\n      this._canOpenOnNextFocus = true;\n    } else if (this._canOpen()) {\n      this._previousValue = this._element.nativeElement.value;\n      this._attachOverlay(this._previousValue);\n      this._floatLabel(true);\n    }\n  }\n\n  _handleClick(): void {\n    if (this._canOpen() && !this.panelOpen) {\n      this._openPanelInternal();\n    }\n  }\n\n  /** Whether the input currently has focus. */\n  private _hasFocus(): boolean {\n    return _getFocusedElementPierceShadowDom() === this._element.nativeElement;\n  }\n\n  /**\n   * In \"auto\" mode, the label will animate down as soon as focus is lost.\n   * This causes the value to jump when selecting an option with the mouse.\n   * This method manually floats the label until the panel can be closed.\n   * @param shouldAnimate Whether the label should be animated when it is floated.\n   */\n  private _floatLabel(shouldAnimate = false): void {\n    if (this._formField && this._formField.floatLabel === 'auto') {\n      if (shouldAnimate) {\n        this._formField._animateAndLockLabel();\n      } else {\n        this._formField.floatLabel = 'always';\n      }\n\n      this._manuallyFloatingLabel = true;\n    }\n  }\n\n  /** If the label has been manually elevated, return it to its normal state. */\n  private _resetLabel(): void {\n    if (this._manuallyFloatingLabel) {\n      if (this._formField) {\n        this._formField.floatLabel = 'auto';\n      }\n      this._manuallyFloatingLabel = false;\n    }\n  }\n\n  /**\n   * This method listens to a stream of panel closing actions and resets the\n   * stream every time the option list changes.\n   */\n  private _subscribeToClosingActions(): Subscription {\n    const initialRender = new Observable(subscriber => {\n      afterNextRender(\n        () => {\n          subscriber.next();\n        },\n        {injector: this._environmentInjector},\n      );\n    });\n    const optionChanges = this.autocomplete.options?.changes.pipe(\n      tap(() => this._positionStrategy.reapplyLastPosition()),\n      // Defer emitting to the stream until the next tick, because changing\n      // bindings in here will cause \"changed after checked\" errors.\n      delay(0),\n    ) ?? observableOf();\n\n    // When the options are initially rendered, and when the option list changes...\n    return (\n      merge(initialRender, optionChanges)\n        .pipe(\n          // create a new stream of panelClosingActions, replacing any previous streams\n          // that were created, and flatten it so our stream only emits closing events...\n          switchMap(() =>\n            this._zone.run(() => {\n              // `afterNextRender` always runs outside of the Angular zone, thus we have to re-enter\n              // the Angular zone. This will lead to change detection being called outside of the Angular\n              // zone and the `autocomplete.opened` will also emit outside of the Angular.\n              const wasOpen = this.panelOpen;\n              this._resetActiveItem();\n              this._updatePanelState();\n              this._changeDetectorRef.detectChanges();\n\n              if (this.panelOpen) {\n                this._overlayRef!.updatePosition();\n              }\n\n              if (wasOpen !== this.panelOpen) {\n                // If the `panelOpen` state changed, we need to make sure to emit the `opened` or\n                // `closed` event, because we may not have emitted it. This can happen\n                // - if the users opens the panel and there are no options, but the\n                //   options come in slightly later or as a result of the value changing,\n                // - if the panel is closed after the user entered a string that did not match any\n                //   of the available options,\n                // - if a valid string is entered after an invalid one.\n                if (this.panelOpen) {\n                  this._emitOpened();\n                } else {\n                  this.autocomplete.closed.emit();\n                }\n              }\n\n              return this.panelClosingActions;\n            }),\n          ),\n          // when the first closing event occurs...\n          take(1),\n        )\n        // set the value, close the panel, and complete.\n        .subscribe(event => this._setValueAndClose(event))\n    );\n  }\n\n  /**\n   * Emits the opened event once it's known that the panel will be shown and stores\n   * the state of the trigger right before the opening sequence was finished.\n   */\n  private _emitOpened() {\n    this.autocomplete.opened.emit();\n  }\n\n  /** Destroys the autocomplete suggestion panel. */\n  private _destroyPanel(): void {\n    if (this._overlayRef) {\n      this.closePanel();\n      this._overlayRef.dispose();\n      this._overlayRef = null;\n    }\n  }\n\n  /** Given a value, returns the string that should be shown within the input. */\n  private _getDisplayValue<T>(value: T): T | string {\n    const autocomplete = this.autocomplete;\n    return autocomplete && autocomplete.displayWith ? autocomplete.displayWith(value) : value;\n  }\n\n  private _assignOptionValue(value: any): void {\n    const toDisplay = this._getDisplayValue(value);\n\n    if (value == null) {\n      this._clearPreviousSelectedOption(null, false);\n    }\n\n    // Simply falling back to an empty string if the display value is falsy does not work properly.\n    // The display value can also be the number zero and shouldn't fall back to an empty string.\n    this._updateNativeInputValue(toDisplay != null ? toDisplay : '');\n  }\n\n  private _updateNativeInputValue(value: string): void {\n    // If it's used within a `MatFormField`, we should set it through the property so it can go\n    // through change detection.\n    if (this._formField) {\n      this._formField._control.value = value;\n    } else {\n      this._element.nativeElement.value = value;\n    }\n\n    this._previousValue = value;\n  }\n\n  /**\n   * This method closes the panel, and if a value is specified, also sets the associated\n   * control to that value. It will also mark the control as dirty if this interaction\n   * stemmed from the user.\n   */\n  private _setValueAndClose(event: MatOptionSelectionChange | null): void {\n    const panel = this.autocomplete;\n    const toSelect = event ? event.source : this._pendingAutoselectedOption;\n\n    if (toSelect) {\n      this._clearPreviousSelectedOption(toSelect);\n      this._assignOptionValue(toSelect.value);\n      // TODO(crisbeto): this should wait until the animation is done, otherwise the value\n      // gets reset while the panel is still animating which looks glitchy. It'll likely break\n      // some tests to change it at this point.\n      this._onChange(toSelect.value);\n      panel._emitSelectEvent(toSelect);\n      this._element.nativeElement.focus();\n    } else if (\n      panel.requireSelection &&\n      this._element.nativeElement.value !== this._valueOnAttach\n    ) {\n      this._clearPreviousSelectedOption(null);\n      this._assignOptionValue(null);\n      this._onChange(null);\n    }\n\n    this.closePanel();\n  }\n\n  /**\n   * Clear any previous selected option and emit a selection change event for this option\n   */\n  private _clearPreviousSelectedOption(skip: MatOption | null, emitEvent?: boolean) {\n    // Null checks are necessary here, because the autocomplete\n    // or its options may not have been assigned yet.\n    this.autocomplete?.options?.forEach(option => {\n      if (option !== skip && option.selected) {\n        option.deselect(emitEvent);\n      }\n    });\n  }\n\n  private _openPanelInternal(valueOnAttach = this._element.nativeElement.value) {\n    this._attachOverlay(valueOnAttach);\n    this._floatLabel();\n    // Add aria-owns attribute when the autocomplete becomes visible.\n    if (this._trackedModal) {\n      const panelId = this.autocomplete.id;\n      addAriaReferencedId(this._trackedModal, 'aria-owns', panelId);\n    }\n  }\n\n  private _attachOverlay(valueOnAttach: string): void {\n    if (!this.autocomplete && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getMatAutocompleteMissingPanelError();\n    }\n\n    let overlayRef = this._overlayRef;\n\n    if (!overlayRef) {\n      this._portal = new TemplatePortal(this.autocomplete.template, this._viewContainerRef, {\n        id: this._formField?.getLabelId(),\n      });\n      overlayRef = this._overlay.create(this._getOverlayConfig());\n      this._overlayRef = overlayRef;\n      this._viewportSubscription = this._viewportRuler.change().subscribe(() => {\n        if (this.panelOpen && overlayRef) {\n          overlayRef.updateSize({width: this._getPanelWidth()});\n        }\n      });\n      // Subscribe to the breakpoint events stream to detect when screen is in\n      // handsetLandscape.\n      this._handsetLandscapeSubscription = this._breakpointObserver\n        .observe(Breakpoints.HandsetLandscape)\n        .subscribe(result => {\n          const isHandsetLandscape = result.matches;\n          // Check if result.matches Breakpoints.HandsetLandscape. Apply HandsetLandscape\n          // settings to prevent overlay cutoff in that breakpoint. Fixes b/284148377\n          if (isHandsetLandscape) {\n            this._positionStrategy\n              .withFlexibleDimensions(true)\n              .withGrowAfterOpen(true)\n              .withViewportMargin(8);\n          } else {\n            this._positionStrategy\n              .withFlexibleDimensions(false)\n              .withGrowAfterOpen(false)\n              .withViewportMargin(0);\n          }\n        });\n    } else {\n      // Update the trigger, panel width and direction, in case anything has changed.\n      this._positionStrategy.setOrigin(this._getConnectedElement());\n      overlayRef.updateSize({width: this._getPanelWidth()});\n    }\n\n    if (overlayRef && !overlayRef.hasAttached()) {\n      overlayRef.attach(this._portal);\n      this._valueOnAttach = valueOnAttach;\n      this._valueOnLastKeydown = null;\n      this._closingActionsSubscription = this._subscribeToClosingActions();\n    }\n\n    const wasOpen = this.panelOpen;\n\n    this.autocomplete._isOpen = this._overlayAttached = true;\n    this.autocomplete._latestOpeningTrigger = this;\n    this.autocomplete._setColor(this._formField?.color);\n    this._updatePanelState();\n    this._applyModalPanelOwnership();\n\n    // We need to do an extra `panelOpen` check in here, because the\n    // autocomplete won't be shown if there are no options.\n    if (this.panelOpen && wasOpen !== this.panelOpen) {\n      this._emitOpened();\n    }\n  }\n\n  /** Handles keyboard events coming from the overlay panel. */\n  private _handlePanelKeydown = (event: KeyboardEvent) => {\n    // Close when pressing ESCAPE or ALT + UP_ARROW, based on the a11y guidelines.\n    // See: https://www.w3.org/TR/wai-aria-practices-1.1/#textbox-keyboard-interaction\n    if (\n      (event.keyCode === ESCAPE && !hasModifierKey(event)) ||\n      (event.keyCode === UP_ARROW && hasModifierKey(event, 'altKey'))\n    ) {\n      // If the user had typed something in before we autoselected an option, and they decided\n      // to cancel the selection, restore the input value to the one they had typed in.\n      if (this._pendingAutoselectedOption) {\n        this._updateNativeInputValue(this._valueBeforeAutoSelection ?? '');\n        this._pendingAutoselectedOption = null;\n      }\n      this._closeKeyEventStream.next();\n      this._resetActiveItem();\n      // We need to stop propagation, otherwise the event will eventually\n      // reach the input itself and cause the overlay to be reopened.\n      event.stopPropagation();\n      event.preventDefault();\n    }\n  };\n\n  /** Updates the panel's visibility state and any trigger state tied to id. */\n  private _updatePanelState() {\n    this.autocomplete._setVisibility();\n\n    // Note that here we subscribe and unsubscribe based on the panel's visiblity state,\n    // because the act of subscribing will prevent events from reaching other overlays and\n    // we don't want to block the events if there are no options.\n    if (this.panelOpen) {\n      const overlayRef = this._overlayRef!;\n\n      if (!this._keydownSubscription) {\n        // Use the `keydownEvents` in order to take advantage of\n        // the overlay event targeting provided by the CDK overlay.\n        this._keydownSubscription = overlayRef.keydownEvents().subscribe(this._handlePanelKeydown);\n      }\n\n      if (!this._outsideClickSubscription) {\n        // Subscribe to the pointer events stream so that it doesn't get picked up by other overlays.\n        // TODO(crisbeto): we should switch `_getOutsideClickStream` eventually to use this stream,\n        // but the behvior isn't exactly the same and it ends up breaking some internal tests.\n        this._outsideClickSubscription = overlayRef.outsidePointerEvents().subscribe();\n      }\n    } else {\n      this._keydownSubscription?.unsubscribe();\n      this._outsideClickSubscription?.unsubscribe();\n      this._keydownSubscription = this._outsideClickSubscription = null;\n    }\n  }\n\n  private _getOverlayConfig(): OverlayConfig {\n    return new OverlayConfig({\n      positionStrategy: this._getOverlayPosition(),\n      scrollStrategy: this._scrollStrategy(),\n      width: this._getPanelWidth(),\n      direction: this._dir ?? undefined,\n      hasBackdrop: this._defaults?.hasBackdrop,\n      backdropClass: this._defaults?.backdropClass,\n      panelClass: this._defaults?.overlayPanelClass,\n    });\n  }\n\n  private _getOverlayPosition(): PositionStrategy {\n    // Set default Overlay Position\n    const strategy = this._overlay\n      .position()\n      .flexibleConnectedTo(this._getConnectedElement())\n      .withFlexibleDimensions(false)\n      .withPush(false);\n\n    this._setStrategyPositions(strategy);\n    this._positionStrategy = strategy;\n    return strategy;\n  }\n\n  /** Sets the positions on a position strategy based on the directive's input state. */\n  private _setStrategyPositions(positionStrategy: FlexibleConnectedPositionStrategy) {\n    // Note that we provide horizontal fallback positions, even though by default the dropdown\n    // width matches the input, because consumers can override the width. See #18854.\n    const belowPositions: ConnectedPosition[] = [\n      {originX: 'start', originY: 'bottom', overlayX: 'start', overlayY: 'top'},\n      {originX: 'end', originY: 'bottom', overlayX: 'end', overlayY: 'top'},\n    ];\n\n    // The overlay edge connected to the trigger should have squared corners, while\n    // the opposite end has rounded corners. We apply a CSS class to swap the\n    // border-radius based on the overlay position.\n    const panelClass = this._aboveClass;\n    const abovePositions: ConnectedPosition[] = [\n      {originX: 'start', originY: 'top', overlayX: 'start', overlayY: 'bottom', panelClass},\n      {originX: 'end', originY: 'top', overlayX: 'end', overlayY: 'bottom', panelClass},\n    ];\n\n    let positions: ConnectedPosition[];\n\n    if (this.position === 'above') {\n      positions = abovePositions;\n    } else if (this.position === 'below') {\n      positions = belowPositions;\n    } else {\n      positions = [...belowPositions, ...abovePositions];\n    }\n\n    positionStrategy.withPositions(positions);\n  }\n\n  private _getConnectedElement(): ElementRef<HTMLElement> {\n    if (this.connectedTo) {\n      return this.connectedTo.elementRef;\n    }\n\n    return this._formField ? this._formField.getConnectedOverlayOrigin() : this._element;\n  }\n\n  private _getPanelWidth(): number | string {\n    return this.autocomplete.panelWidth || this._getHostWidth();\n  }\n\n  /** Returns the width of the input element, so the panel width can match it. */\n  private _getHostWidth(): number {\n    return this._getConnectedElement().nativeElement.getBoundingClientRect().width;\n  }\n\n  /**\n   * Reset the active item to -1. This is so that pressing arrow keys will activate the correct\n   * option.\n   *\n   * If the consumer opted-in to automatically activatating the first option, activate the first\n   * *enabled* option.\n   */\n  private _resetActiveItem(): void {\n    const autocomplete = this.autocomplete;\n\n    if (autocomplete.autoActiveFirstOption) {\n      // Find the index of the first *enabled* option. Avoid calling `_keyManager.setActiveItem`\n      // because it activates the first option that passes the skip predicate, rather than the\n      // first *enabled* option.\n      let firstEnabledOptionIndex = -1;\n\n      for (let index = 0; index < autocomplete.options.length; index++) {\n        const option = autocomplete.options.get(index)!;\n        if (!option.disabled) {\n          firstEnabledOptionIndex = index;\n          break;\n        }\n      }\n      autocomplete._keyManager.setActiveItem(firstEnabledOptionIndex);\n    } else {\n      autocomplete._keyManager.setActiveItem(-1);\n    }\n  }\n\n  /** Determines whether the panel can be opened. */\n  private _canOpen(): boolean {\n    const element = this._element.nativeElement;\n    return !element.readOnly && !element.disabled && !this.autocompleteDisabled;\n  }\n\n  /** Scrolls to a particular option in the list. */\n  private _scrollToOption(index: number): void {\n    // Given that we are not actually focusing active options, we must manually adjust scroll\n    // to reveal options below the fold. First, we find the offset of the option from the top\n    // of the panel. If that offset is below the fold, the new scrollTop will be the offset -\n    // the panel height + the option height, so the active option will be just visible at the\n    // bottom of the panel. If that offset is above the top of the visible panel, the new scrollTop\n    // will become the offset. If that offset is visible within the panel already, the scrollTop is\n    // not adjusted.\n    const autocomplete = this.autocomplete;\n    const labelCount = _countGroupLabelsBeforeOption(\n      index,\n      autocomplete.options,\n      autocomplete.optionGroups,\n    );\n\n    if (index === 0 && labelCount === 1) {\n      // If we've got one group label before the option and we're at the top option,\n      // scroll the list to the top. This is better UX than scrolling the list to the\n      // top of the option, because it allows the user to read the top group's label.\n      autocomplete._setScrollTop(0);\n    } else if (autocomplete.panel) {\n      const option = autocomplete.options.toArray()[index];\n\n      if (option) {\n        const element = option._getHostElement();\n        const newScrollPosition = _getOptionScrollPosition(\n          element.offsetTop,\n          element.offsetHeight,\n          autocomplete._getScrollTop(),\n          autocomplete.panel.nativeElement.offsetHeight,\n        );\n\n        autocomplete._setScrollTop(newScrollPosition);\n      }\n    }\n  }\n\n  /**\n   * Track which modal we have modified the `aria-owns` attribute of. When the combobox trigger is\n   * inside an aria-modal, we apply aria-owns to the parent modal with the `id` of the options\n   * panel. Track the modal we have changed so we can undo the changes on destroy.\n   */\n  private _trackedModal: Element | null = null;\n\n  /**\n   * If the autocomplete trigger is inside of an `aria-modal` element, connect\n   * that modal to the options panel with `aria-owns`.\n   *\n   * For some browser + screen reader combinations, when navigation is inside\n   * of an `aria-modal` element, the screen reader treats everything outside\n   * of that modal as hidden or invisible.\n   *\n   * This causes a problem when the combobox trigger is _inside_ of a modal, because the\n   * options panel is rendered _outside_ of that modal, preventing screen reader navigation\n   * from reaching the panel.\n   *\n   * We can work around this issue by applying `aria-owns` to the modal with the `id` of\n   * the options panel. This effectively communicates to assistive technology that the\n   * options panel is part of the same interaction as the modal.\n   *\n   * At time of this writing, this issue is present in VoiceOver.\n   * See https://github.com/angular/components/issues/20694\n   */\n  private _applyModalPanelOwnership() {\n    // TODO(http://github.com/angular/components/issues/26853): consider de-duplicating this with\n    // the `LiveAnnouncer` and any other usages.\n    //\n    // Note that the selector here is limited to CDK overlays at the moment in order to reduce the\n    // section of the DOM we need to look through. This should cover all the cases we support, but\n    // the selector can be expanded if it turns out to be too narrow.\n    const modal = this._element.nativeElement.closest(\n      'body > .cdk-overlay-container [aria-modal=\"true\"]',\n    );\n\n    if (!modal) {\n      // Most commonly, the autocomplete trigger is not inside a modal.\n      return;\n    }\n\n    const panelId = this.autocomplete.id;\n\n    if (this._trackedModal) {\n      removeAriaReferencedId(this._trackedModal, 'aria-owns', panelId);\n    }\n\n    addAriaReferencedId(modal, 'aria-owns', panelId);\n    this._trackedModal = modal;\n  }\n\n  /** Clears the references to the listbox overlay element from the modal it was added to. */\n  private _clearFromModal() {\n    if (this._trackedModal) {\n      const panelId = this.autocomplete.id;\n\n      removeAriaReferencedId(this._trackedModal, 'aria-owns', panelId);\n      this._trackedModal = null;\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {NgModule} from '@angular/core';\nimport {MatCommonModule, MatOptionModule} from '../core';\nimport {CdkScrollableModule} from '@angular/cdk/scrolling';\nimport {OverlayModule} from '@angular/cdk/overlay';\nimport {MatAutocomplete} from './autocomplete';\nimport {\n  MatAutocompleteTrigger,\n  MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY_PROVIDER,\n} from './autocomplete-trigger';\nimport {MatAutocompleteOrigin} from './autocomplete-origin';\n\n@NgModule({\n  imports: [\n    OverlayModule,\n    MatOptionModule,\n    MatCommonModule,\n    MatAutocomplete,\n    MatAutocompleteTrigger,\n    MatAutocompleteOrigin,\n  ],\n  exports: [\n    CdkScrollableModule,\n    MatAutocomplete,\n    MatOptionModule,\n    MatCommonModule,\n    MatAutocompleteTrigger,\n    MatAutocompleteOrigin,\n  ],\n  providers: [MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY_PROVIDER],\n})\nexport class MatAutocompleteModule {}\n"], "names": ["observableOf"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuCA;MACa,4BAA4B,CAAA;AAG9B,IAAA,MAAA;AAEA,IAAA,MAAA;AAJT,IAAA,WAAA;;IAES,MAAuB;;IAEvB,MAAiB,EAAA;QAFjB,IAAM,CAAA,MAAA,GAAN,MAAM;QAEN,IAAM,CAAA,MAAA,GAAN,MAAM;;AAEhB;AAsCD;MACa,gCAAgC,GAAG,IAAI,cAAc,CAChE,kCAAkC,EAClC;AACE,IAAA,UAAU,EAAE,MAAM;AAClB,IAAA,OAAO,EAAE,wCAAwC;AAClD,CAAA;AAGH;;;;AAIG;SACa,wCAAwC,GAAA;IACtD,OAAO;AACL,QAAA,qBAAqB,EAAE,KAAK;AAC5B,QAAA,sBAAsB,EAAE,KAAK;AAC7B,QAAA,4BAA4B,EAAE,KAAK;AACnC,QAAA,gBAAgB,EAAE,KAAK;AACvB,QAAA,WAAW,EAAE,KAAK;KACnB;AACH;AAEA;MAaa,eAAe,CAAA;AAClB,IAAA,kBAAkB,GAAG,MAAM,CAAC,iBAAiB,CAAC;AAC9C,IAAA,WAAW,GAAG,MAAM,CAA0B,UAAU,CAAC;AACvD,IAAA,SAAS,GAAG,MAAM,CAAgC,gCAAgC,CAAC;AACnF,IAAA,mBAAmB,GAC3B,MAAM,CAAC,qBAAqB,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC,KAAK,gBAAgB;AAC9D,IAAA,oBAAoB,GAAG,YAAY,CAAC,KAAK;;AAGjD,IAAA,WAAW;;IAGX,SAAS,GAAY,KAAK;;AAG1B,IAAA,IAAI,MAAM,GAAA;AACR,QAAA,OAAO,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,SAAS;;IAEvC,OAAO,GAAY,KAAK;;AAGxB,IAAA,qBAAqB;;AAGrB,IAAA,SAAS,CAAC,KAAmB,EAAA;AAC3B,QAAA,IAAI,CAAC,MAAM,GAAG,KAAK;AACnB,QAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE;;;AAG9B,IAAA,MAAM;;;;;AAOwB,IAAA,QAAQ;;AAG5B,IAAA,KAAK;;AAGwB,IAAA,OAAO;;AAGJ,IAAA,YAAY;;AAG3C,IAAA,SAAS;;AAGJ,IAAA,cAAc;;IAG/B,WAAW,GAAoC,IAAI;AAE5D;;;AAGG;AACmC,IAAA,qBAAqB;;AAGrB,IAAA,sBAAsB;AAE5D;;;;;AAKG;AACmC,IAAA,gBAAgB;AAEtD;;;AAGG;AACM,IAAA,UAAU;;AAGmB,IAAA,aAAa;;AAGhC,IAAA,cAAc,GAC/B,IAAI,YAAY,EAAgC;;AAG/B,IAAA,MAAM,GAAuB,IAAI,YAAY,EAAQ;;AAGrD,IAAA,MAAM,GAAuB,IAAI,YAAY,EAAQ;;AAGrD,IAAA,eAAe,GAChC,IAAI,YAAY,EAAiC;AAEnD;;;AAGG;IACH,IACI,SAAS,CAAC,KAAwB,EAAA;AACpC,QAAA,IAAI,CAAC,UAAU,GAAG,KAAK;QACvB,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,SAAS,GAAG,EAAE;;AAE/C,IAAA,UAAU;;AAGV,IAAA,IACI,4BAA4B,GAAA;QAC9B,OAAO,IAAI,CAAC,6BAA6B;;IAE3C,IAAI,4BAA4B,CAAC,KAAc,EAAA;AAC7C,QAAA,IAAI,CAAC,6BAA6B,GAAG,KAAK;QAC1C,IAAI,CAAC,qBAAqB,EAAE;;AAEtB,IAAA,6BAA6B;;IAGrC,qBAAqB,GAAA;AACnB,QAAA,IAAI,IAAI,CAAC,OAAO,EAAE;AAChB,YAAA,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,OAAO,EAAE;AACjC,gBAAA,MAAM,CAAC,kBAAkB,CAAC,YAAY,EAAE;;;;;IAM9C,EAAE,GAAW,MAAM,CAAC,YAAY,CAAC,CAAC,KAAK,CAAC,mBAAmB,CAAC;AAE5D;;;AAGG;AACM,IAAA,WAAW;AAIpB,IAAA,WAAA,GAAA;AACE,QAAA,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;;;;;QAMjC,IAAI,CAAC,WAAW,GAAG,QAAQ,EAAE,MAAM,IAAI,KAAK;QAC5C,IAAI,CAAC,qBAAqB,GAAG,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,qBAAqB;QACnE,IAAI,CAAC,sBAAsB,GAAG,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,sBAAsB;QACrE,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,gBAAgB;QACzD,IAAI,CAAC,6BAA6B,GAAG,IAAI,CAAC,SAAS,CAAC,4BAA4B,IAAI,KAAK;;IAG3F,kBAAkB,GAAA;QAChB,IAAI,CAAC,WAAW,GAAG,IAAI,0BAA0B,CAAY,IAAI,CAAC,OAAO;AACtE,aAAA,QAAQ;AACR,aAAA,aAAa,CAAC,IAAI,CAAC,cAAc,CAAC;AACrC,QAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,IAAG;AACpE,YAAA,IAAI,IAAI,CAAC,MAAM,EAAE;gBACf,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,EAAC,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,KAAK,CAAC,IAAI,IAAI,EAAC,CAAC;;AAE5F,SAAC,CAAC;;QAGF,IAAI,CAAC,cAAc,EAAE;;IAGvB,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,WAAW,EAAE,OAAO,EAAE;AAC3B,QAAA,IAAI,CAAC,oBAAoB,CAAC,WAAW,EAAE;;AAGzC;;;AAGG;AACH,IAAA,aAAa,CAAC,SAAiB,EAAA;AAC7B,QAAA,IAAI,IAAI,CAAC,KAAK,EAAE;YACd,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,SAAS,GAAG,SAAS;;;;IAKlD,aAAa,GAAA;AACX,QAAA,OAAO,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,SAAS,GAAG,CAAC;;;IAI5D,cAAc,GAAA;QACZ,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM;AACvC,QAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE;;;AAIxC,IAAA,gBAAgB,CAAC,MAAiB,EAAA;QAChC,MAAM,KAAK,GAAG,IAAI,4BAA4B,CAAC,IAAI,EAAE,MAAM,CAAC;AAC5D,QAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC;;;AAIjC,IAAA,uBAAuB,CAAC,OAAsB,EAAA;AAC5C,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;AAClB,YAAA,OAAO,IAAI;;AAGb,QAAA,MAAM,eAAe,GAAG,OAAO,GAAG,OAAO,GAAG,GAAG,GAAG,EAAE;AACpD,QAAA,OAAO,IAAI,CAAC,cAAc,GAAG,eAAe,GAAG,IAAI,CAAC,cAAc,GAAG,OAAO;;;;;;;;;;;;;;;;IAiBpE,cAAc,GAAA;AACtB,QAAA,OAAO,KAAK;;uGA/NH,eAAe,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAf,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,eAAe,EA4DP,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,kBAAA,EAAA,MAAA,EAAA,EAAA,SAAA,EAAA,CAAA,YAAA,EAAA,WAAA,CAAA,EAAA,cAAA,EAAA,CAAA,iBAAA,EAAA,gBAAA,CAAA,EAAA,WAAA,EAAA,aAAA,EAAA,qBAAA,EAAA,CAAA,uBAAA,EAAA,uBAAA,EAAA,gBAAgB,CAGhB,EAAA,sBAAA,EAAA,CAAA,wBAAA,EAAA,wBAAA,EAAA,gBAAgB,CAQhB,EAAA,gBAAA,EAAA,CAAA,kBAAA,EAAA,kBAAA,EAAA,gBAAgB,CAShB,EAAA,UAAA,EAAA,YAAA,EAAA,aAAA,EAAA,CAAA,eAAA,EAAA,eAAA,EAAA,gBAAgB,CA4BhB,EAAA,SAAA,EAAA,CAAA,OAAA,EAAA,WAAA,CAAA,EAAA,4BAAA,EAAA,CAAA,8BAAA,EAAA,8BAAA,EAAA,gBAAgB,CA9GxB,EAAA,EAAA,OAAA,EAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,MAAA,EAAA,QAAA,EAAA,MAAA,EAAA,QAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,sBAAA,EAAA,EAAA,SAAA,EAAA,CAAC,EAAC,OAAO,EAAE,2BAA2B,EAAE,WAAW,EAAE,eAAe,EAAC,CAAC,EA4ChE,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,SAAA,EAAA,SAAA,EAAA,SAAS,EAGT,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,cAAA,EAAA,SAAA,EAAA,YAAY,EATlB,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,UAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,WAAW,8KC9JxB,2sBAkBA,EAAA,MAAA,EAAA,CAAA,wyCAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FDwGa,eAAe,EAAA,UAAA,EAAA,CAAA;kBAZ3B,SAAS;+BACE,kBAAkB,EAAA,aAAA,EAGb,iBAAiB,CAAC,IAAI,EAAA,eAAA,EACpB,uBAAuB,CAAC,MAAM,EACrC,QAAA,EAAA,iBAAiB,EACrB,IAAA,EAAA;AACJ,wBAAA,OAAO,EAAE,sBAAsB;qBAChC,EACU,SAAA,EAAA,CAAC,EAAC,OAAO,EAAE,2BAA2B,EAAE,WAAW,EAAiB,eAAA,EAAC,CAAC,EAAA,QAAA,EAAA,2sBAAA,EAAA,MAAA,EAAA,CAAA,wyCAAA,CAAA,EAAA;wDAsCzC,QAAQ,EAAA,CAAA;sBAA/C,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,WAAW,EAAE,EAAC,MAAM,EAAE,IAAI,EAAC;gBAGlB,KAAK,EAAA,CAAA;sBAAxB,SAAS;uBAAC,OAAO;gBAG+B,OAAO,EAAA,CAAA;sBAAvD,eAAe;AAAC,gBAAA,IAAA,EAAA,CAAA,SAAS,EAAE,EAAC,WAAW,EAAE,IAAI,EAAC;gBAGK,YAAY,EAAA,CAAA;sBAA/D,eAAe;AAAC,gBAAA,IAAA,EAAA,CAAA,YAAY,EAAE,EAAC,WAAW,EAAE,IAAI,EAAC;gBAG7B,SAAS,EAAA,CAAA;sBAA7B,KAAK;uBAAC,YAAY;gBAGO,cAAc,EAAA,CAAA;sBAAvC,KAAK;uBAAC,iBAAiB;gBAGf,WAAW,EAAA,CAAA;sBAAnB;gBAMqC,qBAAqB,EAAA,CAAA;sBAA1D,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBAGE,sBAAsB,EAAA,CAAA;sBAA3D,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBAQE,gBAAgB,EAAA,CAAA;sBAArD,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBAM3B,UAAU,EAAA,CAAA;sBAAlB;gBAGqC,aAAa,EAAA,CAAA;sBAAlD,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBAGjB,cAAc,EAAA,CAAA;sBAAhC;gBAIkB,MAAM,EAAA,CAAA;sBAAxB;gBAGkB,MAAM,EAAA,CAAA;sBAAxB;gBAGkB,eAAe,EAAA,CAAA;sBAAjC;gBAQG,SAAS,EAAA,CAAA;sBADZ,KAAK;uBAAC,OAAO;gBASV,4BAA4B,EAAA,CAAA;sBAD/B,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;;;AE5NtC;;;AAGG;MAKU,qBAAqB,CAAA;AAChC,IAAA,UAAU,GAAG,MAAM,CAA0B,UAAU,CAAC;AAGxD,IAAA,WAAA,GAAA;uGAJW,qBAAqB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAArB,qBAAqB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,yBAAA,EAAA,QAAA,EAAA,CAAA,uBAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAArB,qBAAqB,EAAA,UAAA,EAAA,CAAA;kBAJjC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,yBAAyB;AACnC,oBAAA,QAAQ,EAAE,uBAAuB;AAClC,iBAAA;;;AC2CD;;;AAGG;AACU,MAAA,+BAA+B,GAAQ;AAClD,IAAA,OAAO,EAAE,iBAAiB;AAC1B,IAAA,WAAW,EAAE,UAAU,CAAC,MAAM,sBAAsB,CAAC;AACrD,IAAA,KAAK,EAAE,IAAI;;AAGb;;;AAGG;SACa,mCAAmC,GAAA;IACjD,OAAO,KAAK,CACV,kEAAkE;QAChE,4EAA4E;AAC5E,QAAA,iEAAiE,CACpE;AACH;AAEA;MACa,gCAAgC,GAAG,IAAI,cAAc,CAChE,kCAAkC,EAClC;AACE,IAAA,UAAU,EAAE,MAAM;IAClB,OAAO,EAAE,MAAK;AACZ,QAAA,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC;QAC/B,OAAO,MAAM,OAAO,CAAC,gBAAgB,CAAC,UAAU,EAAE;KACnD;AACF,CAAA;AAGH;;;;AAIG;AACG,SAAU,wCAAwC,CAAC,OAAgB,EAAA;IACvE,OAAO,MAAM,OAAO,CAAC,gBAAgB,CAAC,UAAU,EAAE;AACpD;AAEA;;;;AAIG;AACU,MAAA,iDAAiD,GAAG;AAC/D,IAAA,OAAO,EAAE,gCAAgC;IACzC,IAAI,EAAE,CAAC,OAAO,CAAC;AACf,IAAA,UAAU,EAAE,wCAAwC;;AAGtD;MAuBa,sBAAsB,CAAA;AAGzB,IAAA,oBAAoB,GAAG,MAAM,CAAC,mBAAmB,CAAC;AAClD,IAAA,QAAQ,GAAG,MAAM,CAA+B,UAAU,CAAC;AAC3D,IAAA,QAAQ,GAAG,MAAM,CAAC,OAAO,CAAC;AAC1B,IAAA,iBAAiB,GAAG,MAAM,CAAC,gBAAgB,CAAC;AAC5C,IAAA,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC;AACtB,IAAA,kBAAkB,GAAG,MAAM,CAAC,iBAAiB,CAAC;IAC9C,IAAI,GAAG,MAAM,CAAC,cAAc,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;AAC/C,IAAA,UAAU,GAAG,MAAM,CAAsB,cAAc,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAC,CAAC;AACtF,IAAA,cAAc,GAAG,MAAM,CAAC,aAAa,CAAC;AACtC,IAAA,eAAe,GAAG,MAAM,CAAC,gCAAgC,CAAC;AAC1D,IAAA,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC;IAC7B,SAAS,GAAG,MAAM,CACxB,gCAAgC,EAChC,EAAC,QAAQ,EAAE,IAAI,EAAC,CACjB;AAEO,IAAA,WAAW;AACX,IAAA,OAAO;IACP,mBAAmB,GAAG,KAAK;AAC3B,IAAA,YAAY,GAAG,IAAI,OAAO,EAAE;AAC5B,IAAA,oBAAoB;AACpB,IAAA,yBAAyB;AACzB,IAAA,kBAAkB;;AAGlB,IAAA,cAAc;;AAGd,IAAA,cAAc;;AAGd,IAAA,mBAAmB;;AAGnB,IAAA,iBAAiB;;IAGjB,sBAAsB,GAAG,KAAK;;AAG9B,IAAA,2BAA2B;;AAG3B,IAAA,qBAAqB,GAAG,YAAY,CAAC,KAAK;;AAG1C,IAAA,mBAAmB,GAAG,MAAM,CAAC,kBAAkB,CAAC;AAChD,IAAA,6BAA6B,GAAG,YAAY,CAAC,KAAK;AAE1D;;;;AAIG;IACK,mBAAmB,GAAG,IAAI;;AAG1B,IAAA,yBAAyB;AAEjC;;;AAGG;AACK,IAAA,0BAA0B;;AAGjB,IAAA,oBAAoB,GAAG,IAAI,OAAO,EAAQ;AAE3D;;;AAGG;IACK,kBAAkB,GAAG,MAAK;;;;AAIhC,QAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;AAChE,KAAC;;AAGD,IAAA,SAAS,GAAyB,MAAK,GAAG;;AAG1C,IAAA,UAAU,GAAG,MAAK,GAAG;;AAGK,IAAA,YAAY;AAEtC;;;;;;AAMG;IAC+B,QAAQ,GAA+B,MAAM;AAE/E;;;AAGG;AACkC,IAAA,WAAW;AAEhD;;;AAGG;IACoB,qBAAqB,GAAW,KAAK;AAE5D;;;AAGG;AAEH,IAAA,oBAAoB;AAGpB,IAAA,WAAA,GAAA;;IAGQ,WAAW,GAAG,kCAAkC;IAExD,eAAe,GAAA;AACb,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE;AACxB,QAAA,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE;AAC5B,QAAA,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,EAAE,MAAM,EAAE,IAAI,CAAC,kBAAkB,CAAC;;AAG5F,IAAA,WAAW,CAAC,OAAsB,EAAA;QAChC,IAAI,OAAO,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC,iBAAiB,EAAE;AACjD,YAAA,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,iBAAiB,CAAC;AAElD,YAAA,IAAI,IAAI,CAAC,SAAS,EAAE;AAClB,gBAAA,IAAI,CAAC,WAAY,CAAC,cAAc,EAAE;;;;IAKxC,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,kBAAkB,IAAI;AAC3B,QAAA,IAAI,CAAC,6BAA6B,CAAC,WAAW,EAAE;AAChD,QAAA,IAAI,CAAC,qBAAqB,CAAC,WAAW,EAAE;AACxC,QAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI;QAC/B,IAAI,CAAC,aAAa,EAAE;AACpB,QAAA,IAAI,CAAC,oBAAoB,CAAC,QAAQ,EAAE;QACpC,IAAI,CAAC,eAAe,EAAE;;;AAIxB,IAAA,IAAI,SAAS,GAAA;QACX,OAAO,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,YAAY,CAAC,SAAS;;IAErD,gBAAgB,GAAY,KAAK;;IAGzC,SAAS,GAAA;QACP,IAAI,CAAC,kBAAkB,EAAE;;;IAI3B,UAAU,GAAA;QACR,IAAI,CAAC,WAAW,EAAE;AAElB,QAAA,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;YAC1B;;AAGF,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;;;;;AAKlB,YAAA,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAK;AAClB,gBAAA,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,EAAE;AACjC,aAAC,CAAC;;;;QAKJ,IAAI,IAAI,CAAC,YAAY,CAAC,qBAAqB,KAAK,IAAI,EAAE;AACpD,YAAA,IAAI,CAAC,YAAY,CAAC,OAAO,GAAG,KAAK;AACjC,YAAA,IAAI,CAAC,YAAY,CAAC,qBAAqB,GAAG,IAAI;;AAGhD,QAAA,IAAI,CAAC,gBAAgB,GAAG,KAAK;AAC7B,QAAA,IAAI,CAAC,0BAA0B,GAAG,IAAI;QAEtC,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE,EAAE;AACtD,YAAA,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE;AACzB,YAAA,IAAI,CAAC,2BAA2B,CAAC,WAAW,EAAE;;QAGhD,IAAI,CAAC,iBAAiB,EAAE;;;AAIxB,QAAA,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE;;;;;AAK7B,YAAA,IAAI,CAAC,kBAAkB,CAAC,aAAa,EAAE;;;AAIzC,QAAA,IAAI,IAAI,CAAC,aAAa,EAAE;AACtB,YAAA,sBAAsB,CAAC,IAAI,CAAC,aAAa,EAAE,WAAW,EAAE,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC;;;AAIjF;;;AAGG;IACH,cAAc,GAAA;AACZ,QAAA,IAAI,IAAI,CAAC,gBAAgB,EAAE;AACzB,YAAA,IAAI,CAAC,WAAY,CAAC,cAAc,EAAE;;;AAItC;;;AAGG;AACH,IAAA,IAAI,mBAAmB,GAAA;AACrB,QAAA,OAAO,KAAK,CACV,IAAI,CAAC,gBAAgB,EACrB,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,gBAAgB,CAAC,CAAC,EAC9E,IAAI,CAAC,oBAAoB,EACzB,IAAI,CAAC,sBAAsB,EAAE,EAC7B,IAAI,CAAC;AACH,cAAE,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,gBAAgB,CAAC;AACzE,cAAEA,EAAY,EAAE,CACnB,CAAC,IAAI;;QAEJ,GAAG,CAAC,KAAK,KAAK,KAAK,YAAY,wBAAwB,GAAG,KAAK,GAAG,IAAI,CAAC,CAAC,CACzE;;;AAIM,IAAA,gBAAgB,GAAyC,KAAK,CAAC,MAAK;AAC3E,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,GAAG,IAAI;QAEpE,IAAI,OAAO,EAAE;AACX,YAAA,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CACzB,SAAS,CAAC,OAAO,CAAC,EAClB,SAAS,CAAC,MAAM,KAAK,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC,MAAM,IAAI,MAAM,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAC3E;;;;AAKH,QAAA,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,gBAAgB,CAAC,CAAC;AACvE,KAAC,CAAyC;;AAG1C,IAAA,IAAI,YAAY,GAAA;QACd,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE;AACtD,YAAA,OAAO,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,UAAU;;AAGjD,QAAA,OAAO,IAAI;;;IAIL,sBAAsB,GAAA;AAC5B,QAAA,OAAO,IAAI,UAAU,CAAC,QAAQ,IAAG;AAC/B,YAAA,MAAM,QAAQ,GAAG,CAAC,KAA8B,KAAI;;;AAGlD,gBAAA,MAAM,WAAW,GAAG,eAAe,CAAc,KAAK,CAAE;AACxD,gBAAA,MAAM,SAAS,GAAG,IAAI,CAAC;sBACnB,IAAI,CAAC,UAAU,CAAC,yBAAyB,EAAE,CAAC;sBAC5C,IAAI;AACR,gBAAA,MAAM,YAAY,GAAG,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,aAAa,GAAG,IAAI;gBAExF,IACE,IAAI,CAAC,gBAAgB;AACrB,oBAAA,WAAW,KAAK,IAAI,CAAC,QAAQ,CAAC,aAAa;;;;;oBAK3C,CAAC,IAAI,CAAC,SAAS,EAAE;qBAChB,CAAC,SAAS,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;qBAC/C,CAAC,YAAY,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;oBACtD,CAAC,CAAC,IAAI,CAAC,WAAW;oBAClB,CAAC,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC,QAAQ,CAAC,WAAW,CAAC,EACtD;AACA,oBAAA,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC;;AAExB,aAAC;AAED,YAAA,MAAM,QAAQ,GAAG;gBACf,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,UAAU,EAAE,OAAO,EAAE,QAAQ,CAAC;gBACpD,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,UAAU,EAAE,UAAU,EAAE,QAAQ,CAAC;gBACvD,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,UAAU,EAAE,UAAU,EAAE,QAAQ,CAAC;aACxD;AAED,YAAA,OAAO,MAAK;gBACV,QAAQ,CAAC,OAAO,CAAC,OAAO,IAAI,OAAO,EAAE,CAAC;AACxC,aAAC;AACH,SAAC,CAAC;;;AAIJ,IAAA,UAAU,CAAC,KAAU,EAAA;AACnB,QAAA,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;;;AAIlE,IAAA,gBAAgB,CAAC,EAAsB,EAAA;AACrC,QAAA,IAAI,CAAC,SAAS,GAAG,EAAE;;;AAIrB,IAAA,iBAAiB,CAAC,EAAY,EAAA;AAC5B,QAAA,IAAI,CAAC,UAAU,GAAG,EAAE;;;AAItB,IAAA,gBAAgB,CAAC,UAAmB,EAAA;QAClC,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,QAAQ,GAAG,UAAU;;AAGnD,IAAA,cAAc,CAAC,KAAoB,EAAA;AACjC,QAAA,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO;AAC7B,QAAA,MAAM,WAAW,GAAG,cAAc,CAAC,KAAK,CAAC;;;;;AAMzC,QAAA,IAAI,OAAO,KAAK,MAAM,IAAI,CAAC,WAAW,EAAE;YACtC,KAAK,CAAC,cAAc,EAAE;;QAGxB,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK;AAE5D,QAAA,IAAI,IAAI,CAAC,YAAY,IAAI,OAAO,KAAK,KAAK,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,WAAW,EAAE;AAC5E,YAAA,IAAI,CAAC,YAAY,CAAC,qBAAqB,EAAE;YACzC,IAAI,CAAC,gBAAgB,EAAE;YACvB,KAAK,CAAC,cAAc,EAAE;;AACjB,aAAA,IAAI,IAAI,CAAC,YAAY,EAAE;YAC5B,MAAM,cAAc,GAAG,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,UAAU;YAC/D,MAAM,UAAU,GAAG,OAAO,KAAK,QAAQ,IAAI,OAAO,KAAK,UAAU;AAEjE,YAAA,IAAI,OAAO,KAAK,GAAG,KAAK,UAAU,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,SAAS,CAAC,EAAE;gBACrE,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,SAAS,CAAC,KAAK,CAAC;;AACzC,iBAAA,IAAI,UAAU,IAAI,IAAI,CAAC,QAAQ,EAAE,EAAE;AACxC,gBAAA,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,mBAAmB,CAAC;;AAGnD,YAAA,IAAI,UAAU,IAAI,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,UAAU,KAAK,cAAc,EAAE;AAC7E,gBAAA,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,eAAe,IAAI,CAAC,CAAC;gBAExE,IAAI,IAAI,CAAC,YAAY,CAAC,sBAAsB,IAAI,IAAI,CAAC,YAAY,EAAE;AACjE,oBAAA,IAAI,CAAC,IAAI,CAAC,0BAA0B,EAAE;AACpC,wBAAA,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC,mBAAmB;;AAG3D,oBAAA,IAAI,CAAC,0BAA0B,GAAG,IAAI,CAAC,YAAY;oBACnD,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC;;;;;AAMxD,IAAA,YAAY,CAAC,KAAoB,EAAA;AAC/B,QAAA,IAAI,MAAM,GAAG,KAAK,CAAC,MAA0B;AAC7C,QAAA,IAAI,KAAK,GAA2B,MAAM,CAAC,KAAK;;AAGhD,QAAA,IAAI,MAAM,CAAC,IAAI,KAAK,QAAQ,EAAE;AAC5B,YAAA,KAAK,GAAG,KAAK,IAAI,EAAE,GAAG,IAAI,GAAG,UAAU,CAAC,KAAK,CAAC;;;;;;;AAQhD,QAAA,IAAI,IAAI,CAAC,cAAc,KAAK,KAAK,EAAE;AACjC,YAAA,IAAI,CAAC,cAAc,GAAG,KAAK;AAC3B,YAAA,IAAI,CAAC,0BAA0B,GAAG,IAAI;;;;AAKtC,YAAA,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,gBAAgB,EAAE;AAC7D,gBAAA,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;;YAGvB,IAAI,CAAC,KAAK,EAAE;AACV,gBAAA,IAAI,CAAC,4BAA4B,CAAC,IAAI,EAAE,KAAK,CAAC;;iBACzC,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,gBAAgB,EAAE;;;AAGhE,gBAAA,MAAM,cAAc,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,QAAQ,CAAC;gBAEjF,IAAI,cAAc,EAAE;oBAClB,MAAM,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,KAAK,CAAC;AAE3D,oBAAA,IAAI,KAAK,KAAK,OAAO,EAAE;AACrB,wBAAA,cAAc,CAAC,QAAQ,CAAC,KAAK,CAAC;;;;YAKpC,IAAI,IAAI,CAAC,QAAQ,EAAE,IAAI,IAAI,CAAC,SAAS,EAAE,EAAE;;;;;;AAMvC,gBAAA,MAAM,aAAa,GAAG,IAAI,CAAC,mBAAmB,IAAI,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK;AACnF,gBAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI;AAC/B,gBAAA,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC;;;;IAK5C,YAAY,GAAA;AACV,QAAA,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE;AAC7B,YAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI;;AAC1B,aAAA,IAAI,IAAI,CAAC,QAAQ,EAAE,EAAE;YAC1B,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK;AACvD,YAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,cAAc,CAAC;AACxC,YAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;;;IAI1B,YAAY,GAAA;QACV,IAAI,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACtC,IAAI,CAAC,kBAAkB,EAAE;;;;IAKrB,SAAS,GAAA;QACf,OAAO,iCAAiC,EAAE,KAAK,IAAI,CAAC,QAAQ,CAAC,aAAa;;AAG5E;;;;;AAKG;IACK,WAAW,CAAC,aAAa,GAAG,KAAK,EAAA;AACvC,QAAA,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,UAAU,KAAK,MAAM,EAAE;YAC5D,IAAI,aAAa,EAAE;AACjB,gBAAA,IAAI,CAAC,UAAU,CAAC,oBAAoB,EAAE;;iBACjC;AACL,gBAAA,IAAI,CAAC,UAAU,CAAC,UAAU,GAAG,QAAQ;;AAGvC,YAAA,IAAI,CAAC,sBAAsB,GAAG,IAAI;;;;IAK9B,WAAW,GAAA;AACjB,QAAA,IAAI,IAAI,CAAC,sBAAsB,EAAE;AAC/B,YAAA,IAAI,IAAI,CAAC,UAAU,EAAE;AACnB,gBAAA,IAAI,CAAC,UAAU,CAAC,UAAU,GAAG,MAAM;;AAErC,YAAA,IAAI,CAAC,sBAAsB,GAAG,KAAK;;;AAIvC;;;AAGG;IACK,0BAA0B,GAAA;AAChC,QAAA,MAAM,aAAa,GAAG,IAAI,UAAU,CAAC,UAAU,IAAG;YAChD,eAAe,CACb,MAAK;gBACH,UAAU,CAAC,IAAI,EAAE;aAClB,EACD,EAAC,QAAQ,EAAE,IAAI,CAAC,oBAAoB,EAAC,CACtC;AACH,SAAC,CAAC;QACF,MAAM,aAAa,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,OAAO,CAAC,IAAI,CAC3D,GAAG,CAAC,MAAM,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,EAAE,CAAC;;;AAGvD,QAAA,KAAK,CAAC,CAAC,CAAC,CACT,IAAIA,EAAY,EAAE;;AAGnB,QAAA,QACE,KAAK,CAAC,aAAa,EAAE,aAAa;aAC/B,IAAI;;;QAGH,SAAS,CAAC,MACR,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAK;;;;AAIlB,YAAA,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS;YAC9B,IAAI,CAAC,gBAAgB,EAAE;YACvB,IAAI,CAAC,iBAAiB,EAAE;AACxB,YAAA,IAAI,CAAC,kBAAkB,CAAC,aAAa,EAAE;AAEvC,YAAA,IAAI,IAAI,CAAC,SAAS,EAAE;AAClB,gBAAA,IAAI,CAAC,WAAY,CAAC,cAAc,EAAE;;AAGpC,YAAA,IAAI,OAAO,KAAK,IAAI,CAAC,SAAS,EAAE;;;;;;;;AAQ9B,gBAAA,IAAI,IAAI,CAAC,SAAS,EAAE;oBAClB,IAAI,CAAC,WAAW,EAAE;;qBACb;AACL,oBAAA,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,EAAE;;;YAInC,OAAO,IAAI,CAAC,mBAAmB;AACjC,SAAC,CAAC,CACH;;QAED,IAAI,CAAC,CAAC,CAAC;;AAGR,aAAA,SAAS,CAAC,KAAK,IAAI,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;;AAIxD;;;AAGG;IACK,WAAW,GAAA;AACjB,QAAA,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,EAAE;;;IAIzB,aAAa,GAAA;AACnB,QAAA,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,IAAI,CAAC,UAAU,EAAE;AACjB,YAAA,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE;AAC1B,YAAA,IAAI,CAAC,WAAW,GAAG,IAAI;;;;AAKnB,IAAA,gBAAgB,CAAI,KAAQ,EAAA;AAClC,QAAA,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY;AACtC,QAAA,OAAO,YAAY,IAAI,YAAY,CAAC,WAAW,GAAG,YAAY,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG,KAAK;;AAGnF,IAAA,kBAAkB,CAAC,KAAU,EAAA;QACnC,MAAM,SAAS,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC;AAE9C,QAAA,IAAI,KAAK,IAAI,IAAI,EAAE;AACjB,YAAA,IAAI,CAAC,4BAA4B,CAAC,IAAI,EAAE,KAAK,CAAC;;;;AAKhD,QAAA,IAAI,CAAC,uBAAuB,CAAC,SAAS,IAAI,IAAI,GAAG,SAAS,GAAG,EAAE,CAAC;;AAG1D,IAAA,uBAAuB,CAAC,KAAa,EAAA;;;AAG3C,QAAA,IAAI,IAAI,CAAC,UAAU,EAAE;YACnB,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,GAAG,KAAK;;aACjC;YACL,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,GAAG,KAAK;;AAG3C,QAAA,IAAI,CAAC,cAAc,GAAG,KAAK;;AAG7B;;;;AAIG;AACK,IAAA,iBAAiB,CAAC,KAAsC,EAAA;AAC9D,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,YAAY;AAC/B,QAAA,MAAM,QAAQ,GAAG,KAAK,GAAG,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,0BAA0B;QAEvE,IAAI,QAAQ,EAAE;AACZ,YAAA,IAAI,CAAC,4BAA4B,CAAC,QAAQ,CAAC;AAC3C,YAAA,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,KAAK,CAAC;;;;AAIvC,YAAA,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC;AAC9B,YAAA,KAAK,CAAC,gBAAgB,CAAC,QAAQ,CAAC;AAChC,YAAA,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,EAAE;;aAC9B,IACL,KAAK,CAAC,gBAAgB;YACtB,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,KAAK,IAAI,CAAC,cAAc,EACzD;AACA,YAAA,IAAI,CAAC,4BAA4B,CAAC,IAAI,CAAC;AACvC,YAAA,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC;AAC7B,YAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;;QAGtB,IAAI,CAAC,UAAU,EAAE;;AAGnB;;AAEG;IACK,4BAA4B,CAAC,IAAsB,EAAE,SAAmB,EAAA;;;QAG9E,IAAI,CAAC,YAAY,EAAE,OAAO,EAAE,OAAO,CAAC,MAAM,IAAG;YAC3C,IAAI,MAAM,KAAK,IAAI,IAAI,MAAM,CAAC,QAAQ,EAAE;AACtC,gBAAA,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC;;AAE9B,SAAC,CAAC;;IAGI,kBAAkB,CAAC,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,EAAA;AAC1E,QAAA,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC;QAClC,IAAI,CAAC,WAAW,EAAE;;AAElB,QAAA,IAAI,IAAI,CAAC,aAAa,EAAE;AACtB,YAAA,MAAM,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,EAAE;YACpC,mBAAmB,CAAC,IAAI,CAAC,aAAa,EAAE,WAAW,EAAE,OAAO,CAAC;;;AAIzD,IAAA,cAAc,CAAC,aAAqB,EAAA;AAC1C,QAAA,IAAI,CAAC,IAAI,CAAC,YAAY,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAAE;YACzE,MAAM,mCAAmC,EAAE;;AAG7C,QAAA,IAAI,UAAU,GAAG,IAAI,CAAC,WAAW;QAEjC,IAAI,CAAC,UAAU,EAAE;AACf,YAAA,IAAI,CAAC,OAAO,GAAG,IAAI,cAAc,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,IAAI,CAAC,iBAAiB,EAAE;AACpF,gBAAA,EAAE,EAAE,IAAI,CAAC,UAAU,EAAE,UAAU,EAAE;AAClC,aAAA,CAAC;AACF,YAAA,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC;AAC3D,YAAA,IAAI,CAAC,WAAW,GAAG,UAAU;AAC7B,YAAA,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,SAAS,CAAC,MAAK;AACvE,gBAAA,IAAI,IAAI,CAAC,SAAS,IAAI,UAAU,EAAE;AAChC,oBAAA,UAAU,CAAC,UAAU,CAAC,EAAC,KAAK,EAAE,IAAI,CAAC,cAAc,EAAE,EAAC,CAAC;;AAEzD,aAAC,CAAC;;;AAGF,YAAA,IAAI,CAAC,6BAA6B,GAAG,IAAI,CAAC;AACvC,iBAAA,OAAO,CAAC,WAAW,CAAC,gBAAgB;iBACpC,SAAS,CAAC,MAAM,IAAG;AAClB,gBAAA,MAAM,kBAAkB,GAAG,MAAM,CAAC,OAAO;;;gBAGzC,IAAI,kBAAkB,EAAE;AACtB,oBAAA,IAAI,CAAC;yBACF,sBAAsB,CAAC,IAAI;yBAC3B,iBAAiB,CAAC,IAAI;yBACtB,kBAAkB,CAAC,CAAC,CAAC;;qBACnB;AACL,oBAAA,IAAI,CAAC;yBACF,sBAAsB,CAAC,KAAK;yBAC5B,iBAAiB,CAAC,KAAK;yBACvB,kBAAkB,CAAC,CAAC,CAAC;;AAE5B,aAAC,CAAC;;aACC;;YAEL,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,IAAI,CAAC,oBAAoB,EAAE,CAAC;AAC7D,YAAA,UAAU,CAAC,UAAU,CAAC,EAAC,KAAK,EAAE,IAAI,CAAC,cAAc,EAAE,EAAC,CAAC;;QAGvD,IAAI,UAAU,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE,EAAE;AAC3C,YAAA,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC;AAC/B,YAAA,IAAI,CAAC,cAAc,GAAG,aAAa;AACnC,YAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI;AAC/B,YAAA,IAAI,CAAC,2BAA2B,GAAG,IAAI,CAAC,0BAA0B,EAAE;;AAGtE,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS;QAE9B,IAAI,CAAC,YAAY,CAAC,OAAO,GAAG,IAAI,CAAC,gBAAgB,GAAG,IAAI;AACxD,QAAA,IAAI,CAAC,YAAY,CAAC,qBAAqB,GAAG,IAAI;QAC9C,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,EAAE,KAAK,CAAC;QACnD,IAAI,CAAC,iBAAiB,EAAE;QACxB,IAAI,CAAC,yBAAyB,EAAE;;;QAIhC,IAAI,IAAI,CAAC,SAAS,IAAI,OAAO,KAAK,IAAI,CAAC,SAAS,EAAE;YAChD,IAAI,CAAC,WAAW,EAAE;;;;AAKd,IAAA,mBAAmB,GAAG,CAAC,KAAoB,KAAI;;;AAGrD,QAAA,IACE,CAAC,KAAK,CAAC,OAAO,KAAK,MAAM,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC;AACnD,aAAC,KAAK,CAAC,OAAO,KAAK,QAAQ,IAAI,cAAc,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC,EAC/D;;;AAGA,YAAA,IAAI,IAAI,CAAC,0BAA0B,EAAE;gBACnC,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,yBAAyB,IAAI,EAAE,CAAC;AAClE,gBAAA,IAAI,CAAC,0BAA0B,GAAG,IAAI;;AAExC,YAAA,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE;YAChC,IAAI,CAAC,gBAAgB,EAAE;;;YAGvB,KAAK,CAAC,eAAe,EAAE;YACvB,KAAK,CAAC,cAAc,EAAE;;AAE1B,KAAC;;IAGO,iBAAiB,GAAA;AACvB,QAAA,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE;;;;AAKlC,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;AAClB,YAAA,MAAM,UAAU,GAAG,IAAI,CAAC,WAAY;AAEpC,YAAA,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE;;;AAG9B,gBAAA,IAAI,CAAC,oBAAoB,GAAG,UAAU,CAAC,aAAa,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,mBAAmB,CAAC;;AAG5F,YAAA,IAAI,CAAC,IAAI,CAAC,yBAAyB,EAAE;;;;gBAInC,IAAI,CAAC,yBAAyB,GAAG,UAAU,CAAC,oBAAoB,EAAE,CAAC,SAAS,EAAE;;;aAE3E;AACL,YAAA,IAAI,CAAC,oBAAoB,EAAE,WAAW,EAAE;AACxC,YAAA,IAAI,CAAC,yBAAyB,EAAE,WAAW,EAAE;YAC7C,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,yBAAyB,GAAG,IAAI;;;IAI7D,iBAAiB,GAAA;QACvB,OAAO,IAAI,aAAa,CAAC;AACvB,YAAA,gBAAgB,EAAE,IAAI,CAAC,mBAAmB,EAAE;AAC5C,YAAA,cAAc,EAAE,IAAI,CAAC,eAAe,EAAE;AACtC,YAAA,KAAK,EAAE,IAAI,CAAC,cAAc,EAAE;AAC5B,YAAA,SAAS,EAAE,IAAI,CAAC,IAAI,IAAI,SAAS;AACjC,YAAA,WAAW,EAAE,IAAI,CAAC,SAAS,EAAE,WAAW;AACxC,YAAA,aAAa,EAAE,IAAI,CAAC,SAAS,EAAE,aAAa;AAC5C,YAAA,UAAU,EAAE,IAAI,CAAC,SAAS,EAAE,iBAAiB;AAC9C,SAAA,CAAC;;IAGI,mBAAmB,GAAA;;AAEzB,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC;AACnB,aAAA,QAAQ;AACR,aAAA,mBAAmB,CAAC,IAAI,CAAC,oBAAoB,EAAE;aAC/C,sBAAsB,CAAC,KAAK;aAC5B,QAAQ,CAAC,KAAK,CAAC;AAElB,QAAA,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC;AACpC,QAAA,IAAI,CAAC,iBAAiB,GAAG,QAAQ;AACjC,QAAA,OAAO,QAAQ;;;AAIT,IAAA,qBAAqB,CAAC,gBAAmD,EAAA;;;AAG/E,QAAA,MAAM,cAAc,GAAwB;AAC1C,YAAA,EAAC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAC;AACzE,YAAA,EAAC,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAC;SACtE;;;;AAKD,QAAA,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW;AACnC,QAAA,MAAM,cAAc,GAAwB;AAC1C,YAAA,EAAC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,UAAU,EAAC;AACrF,YAAA,EAAC,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,UAAU,EAAC;SAClF;AAED,QAAA,IAAI,SAA8B;AAElC,QAAA,IAAI,IAAI,CAAC,QAAQ,KAAK,OAAO,EAAE;YAC7B,SAAS,GAAG,cAAc;;AACrB,aAAA,IAAI,IAAI,CAAC,QAAQ,KAAK,OAAO,EAAE;YACpC,SAAS,GAAG,cAAc;;aACrB;YACL,SAAS,GAAG,CAAC,GAAG,cAAc,EAAE,GAAG,cAAc,CAAC;;AAGpD,QAAA,gBAAgB,CAAC,aAAa,CAAC,SAAS,CAAC;;IAGnC,oBAAoB,GAAA;AAC1B,QAAA,IAAI,IAAI,CAAC,WAAW,EAAE;AACpB,YAAA,OAAO,IAAI,CAAC,WAAW,CAAC,UAAU;;AAGpC,QAAA,OAAO,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,yBAAyB,EAAE,GAAG,IAAI,CAAC,QAAQ;;IAG9E,cAAc,GAAA;QACpB,OAAO,IAAI,CAAC,YAAY,CAAC,UAAU,IAAI,IAAI,CAAC,aAAa,EAAE;;;IAIrD,aAAa,GAAA;QACnB,OAAO,IAAI,CAAC,oBAAoB,EAAE,CAAC,aAAa,CAAC,qBAAqB,EAAE,CAAC,KAAK;;AAGhF;;;;;;AAMG;IACK,gBAAgB,GAAA;AACtB,QAAA,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY;AAEtC,QAAA,IAAI,YAAY,CAAC,qBAAqB,EAAE;;;;AAItC,YAAA,IAAI,uBAAuB,GAAG,CAAC,CAAC;AAEhC,YAAA,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,YAAY,CAAC,OAAO,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;gBAChE,MAAM,MAAM,GAAG,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAE;AAC/C,gBAAA,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;oBACpB,uBAAuB,GAAG,KAAK;oBAC/B;;;AAGJ,YAAA,YAAY,CAAC,WAAW,CAAC,aAAa,CAAC,uBAAuB,CAAC;;aAC1D;YACL,YAAY,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;;;;IAKtC,QAAQ,GAAA;AACd,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,aAAa;AAC3C,QAAA,OAAO,CAAC,OAAO,CAAC,QAAQ,IAAI,CAAC,OAAO,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,oBAAoB;;;AAIrE,IAAA,eAAe,CAAC,KAAa,EAAA;;;;;;;;AAQnC,QAAA,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY;AACtC,QAAA,MAAM,UAAU,GAAG,6BAA6B,CAC9C,KAAK,EACL,YAAY,CAAC,OAAO,EACpB,YAAY,CAAC,YAAY,CAC1B;QAED,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,KAAK,CAAC,EAAE;;;;AAInC,YAAA,YAAY,CAAC,aAAa,CAAC,CAAC,CAAC;;AACxB,aAAA,IAAI,YAAY,CAAC,KAAK,EAAE;YAC7B,MAAM,MAAM,GAAG,YAAY,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,KAAK,CAAC;YAEpD,IAAI,MAAM,EAAE;AACV,gBAAA,MAAM,OAAO,GAAG,MAAM,CAAC,eAAe,EAAE;gBACxC,MAAM,iBAAiB,GAAG,wBAAwB,CAChD,OAAO,CAAC,SAAS,EACjB,OAAO,CAAC,YAAY,EACpB,YAAY,CAAC,aAAa,EAAE,EAC5B,YAAY,CAAC,KAAK,CAAC,aAAa,CAAC,YAAY,CAC9C;AAED,gBAAA,YAAY,CAAC,aAAa,CAAC,iBAAiB,CAAC;;;;AAKnD;;;;AAIG;IACK,aAAa,GAAmB,IAAI;AAE5C;;;;;;;;;;;;;;;;;;AAkBG;IACK,yBAAyB,GAAA;;;;;;;AAO/B,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,OAAO,CAC/C,mDAAmD,CACpD;QAED,IAAI,CAAC,KAAK,EAAE;;YAEV;;AAGF,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,EAAE;AAEpC,QAAA,IAAI,IAAI,CAAC,aAAa,EAAE;YACtB,sBAAsB,CAAC,IAAI,CAAC,aAAa,EAAE,WAAW,EAAE,OAAO,CAAC;;AAGlE,QAAA,mBAAmB,CAAC,KAAK,EAAE,WAAW,EAAE,OAAO,CAAC;AAChD,QAAA,IAAI,CAAC,aAAa,GAAG,KAAK;;;IAIpB,eAAe,GAAA;AACrB,QAAA,IAAI,IAAI,CAAC,aAAa,EAAE;AACtB,YAAA,MAAM,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,EAAE;YAEpC,sBAAsB,CAAC,IAAI,CAAC,aAAa,EAAE,WAAW,EAAE,OAAO,CAAC;AAChE,YAAA,IAAI,CAAC,aAAa,GAAG,IAAI;;;uGAl8BlB,sBAAsB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAtB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,sBAAsB,EAoHoB,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,mDAAA,EAAA,MAAA,EAAA,EAAA,YAAA,EAAA,CAAA,iBAAA,EAAA,cAAA,CAAA,EAAA,QAAA,EAAA,CAAA,yBAAA,EAAA,UAAA,CAAA,EAAA,WAAA,EAAA,CAAA,4BAAA,EAAA,aAAA,CAAA,EAAA,qBAAA,EAAA,CAAA,cAAA,EAAA,uBAAA,CAAA,EAAA,oBAAA,EAAA,CAAA,yBAAA,EAAA,sBAAA,EAAA,gBAAgB,CAtH1D,EAAA,EAAA,IAAA,EAAA,EAAA,SAAA,EAAA,EAAA,SAAA,EAAA,gBAAA,EAAA,MAAA,EAAA,cAAA,EAAA,OAAA,EAAA,sBAAA,EAAA,SAAA,EAAA,wBAAA,EAAA,OAAA,EAAA,gBAAA,EAAA,EAAA,UAAA,EAAA,EAAA,mBAAA,EAAA,uBAAA,EAAA,WAAA,EAAA,4CAAA,EAAA,wBAAA,EAAA,wCAAA,EAAA,4BAAA,EAAA,sDAAA,EAAA,oBAAA,EAAA,oDAAA,EAAA,oBAAA,EAAA,gEAAA,EAAA,oBAAA,EAAA,2CAAA,EAAA,EAAA,cAAA,EAAA,8BAAA,EAAA,EAAA,SAAA,EAAA,CAAC,+BAA+B,CAAC,EAAA,QAAA,EAAA,CAAA,wBAAA,CAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAEjC,sBAAsB,EAAA,UAAA,EAAA,CAAA;kBAtBlC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,CAAmD,iDAAA,CAAA;AAC7D,oBAAA,IAAI,EAAE;AACJ,wBAAA,OAAO,EAAE,8BAA8B;AACvC,wBAAA,qBAAqB,EAAE,uBAAuB;AAC9C,wBAAA,aAAa,EAAE,0CAA0C;AACzD,wBAAA,0BAA0B,EAAE,sCAAsC;AAClE,wBAAA,8BAA8B,EAAE,sDAAsD;AACtF,wBAAA,sBAAsB,EAAE,oDAAoD;AAC5E,wBAAA,sBAAsB,EAAE,gEAAgE;AACxF,wBAAA,sBAAsB,EAAE,yCAAyC;;;AAGjE,wBAAA,WAAW,EAAE,gBAAgB;AAC7B,wBAAA,QAAQ,EAAE,cAAc;AACxB,wBAAA,SAAS,EAAE,sBAAsB;AACjC,wBAAA,WAAW,EAAE,wBAAwB;AACrC,wBAAA,SAAS,EAAE,gBAAgB;AAC5B,qBAAA;AACD,oBAAA,QAAQ,EAAE,wBAAwB;oBAClC,SAAS,EAAE,CAAC,+BAA+B,CAAC;AAC7C,iBAAA;wDA0F2B,YAAY,EAAA,CAAA;sBAArC,KAAK;uBAAC,iBAAiB;gBASU,QAAQ,EAAA,CAAA;sBAAzC,KAAK;uBAAC,yBAAyB;gBAMK,WAAW,EAAA,CAAA;sBAA/C,KAAK;uBAAC,4BAA4B;gBAMZ,qBAAqB,EAAA,CAAA;sBAA3C,KAAK;uBAAC,cAAc;gBAOrB,oBAAoB,EAAA,CAAA;sBADnB,KAAK;AAAC,gBAAA,IAAA,EAAA,CAAA,EAAC,KAAK,EAAE,yBAAyB,EAAE,SAAS,EAAE,gBAAgB,EAAC;;;MCvN3D,qBAAqB,CAAA;uGAArB,qBAAqB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;AAArB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,qBAAqB,YAjB9B,aAAa;YACb,eAAe;YACf,eAAe;YACf,eAAe;YACf,sBAAsB;AACtB,YAAA,qBAAqB,aAGrB,mBAAmB;YACnB,eAAe;YACf,eAAe;YACf,eAAe;YACf,sBAAsB;YACtB,qBAAqB,CAAA,EAAA,CAAA;AAIZ,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,qBAAqB,EAFrB,SAAA,EAAA,CAAC,iDAAiD,CAAC,YAf5D,aAAa;YACb,eAAe;AACf,YAAA,eAAe,EAMf,mBAAmB;YAEnB,eAAe;YACf,eAAe,CAAA,EAAA,CAAA;;2FAMN,qBAAqB,EAAA,UAAA,EAAA,CAAA;kBAnBjC,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,OAAO,EAAE;wBACP,aAAa;wBACb,eAAe;wBACf,eAAe;wBACf,eAAe;wBACf,sBAAsB;wBACtB,qBAAqB;AACtB,qBAAA;AACD,oBAAA,OAAO,EAAE;wBACP,mBAAmB;wBACnB,eAAe;wBACf,eAAe;wBACf,eAAe;wBACf,sBAAsB;wBACtB,qBAAqB;AACtB,qBAAA;oBACD,SAAS,EAAE,CAAC,iDAAiD,CAAC;AAC/D,iBAAA;;;;;"}