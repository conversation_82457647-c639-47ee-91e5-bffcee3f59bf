"use strict";
// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.0
//   protoc               v6.30.2
// source: sigstore_trustroot.proto
Object.defineProperty(exports, "__esModule", { value: true });
exports.ClientTrustConfig = exports.ServiceConfiguration = exports.Service = exports.SigningConfig = exports.TrustedRoot = exports.CertificateAuthority = exports.TransparencyLogInstance = exports.ServiceSelector = void 0;
exports.serviceSelectorFromJSON = serviceSelectorFromJSON;
exports.serviceSelectorToJSON = serviceSelectorToJSON;
/* eslint-disable */
const sigstore_common_1 = require("./sigstore_common");
/**
 * ServiceSelector specifies how a client SHOULD select a set of
 * Services to connect to. A client SHOULD throw an error if
 * the value is SERVICE_SELECTOR_UNDEFINED.
 */
var ServiceSelector;
(function (ServiceSelector) {
    ServiceSelector[ServiceSelector["SERVICE_SELECTOR_UNDEFINED"] = 0] = "SERVICE_SELECTOR_UNDEFINED";
    /**
     * ALL - Clients SHOULD select all Services based on supported API version
     * and validity window.
     */
    ServiceSelector[ServiceSelector["ALL"] = 1] = "ALL";
    /**
     * ANY - Clients SHOULD select one Service based on supported API version
     * and validity window. It is up to the client implementation to
     * decide how to select the Service, e.g. random or round-robin.
     */
    ServiceSelector[ServiceSelector["ANY"] = 2] = "ANY";
    /**
     * EXACT - Clients SHOULD select a specific number of Services based on
     * supported API version and validity window, using the provided
     * `count`. It is up to the client implementation to decide how to
     * select the Service, e.g. random or round-robin.
     */
    ServiceSelector[ServiceSelector["EXACT"] = 3] = "EXACT";
})(ServiceSelector || (exports.ServiceSelector = ServiceSelector = {}));
function serviceSelectorFromJSON(object) {
    switch (object) {
        case 0:
        case "SERVICE_SELECTOR_UNDEFINED":
            return ServiceSelector.SERVICE_SELECTOR_UNDEFINED;
        case 1:
        case "ALL":
            return ServiceSelector.ALL;
        case 2:
        case "ANY":
            return ServiceSelector.ANY;
        case 3:
        case "EXACT":
            return ServiceSelector.EXACT;
        default:
            throw new globalThis.Error("Unrecognized enum value " + object + " for enum ServiceSelector");
    }
}
function serviceSelectorToJSON(object) {
    switch (object) {
        case ServiceSelector.SERVICE_SELECTOR_UNDEFINED:
            return "SERVICE_SELECTOR_UNDEFINED";
        case ServiceSelector.ALL:
            return "ALL";
        case ServiceSelector.ANY:
            return "ANY";
        case ServiceSelector.EXACT:
            return "EXACT";
        default:
            throw new globalThis.Error("Unrecognized enum value " + object + " for enum ServiceSelector");
    }
}
exports.TransparencyLogInstance = {
    fromJSON(object) {
        return {
            baseUrl: isSet(object.baseUrl) ? globalThis.String(object.baseUrl) : "",
            hashAlgorithm: isSet(object.hashAlgorithm) ? (0, sigstore_common_1.hashAlgorithmFromJSON)(object.hashAlgorithm) : 0,
            publicKey: isSet(object.publicKey) ? sigstore_common_1.PublicKey.fromJSON(object.publicKey) : undefined,
            logId: isSet(object.logId) ? sigstore_common_1.LogId.fromJSON(object.logId) : undefined,
            checkpointKeyId: isSet(object.checkpointKeyId) ? sigstore_common_1.LogId.fromJSON(object.checkpointKeyId) : undefined,
            operator: isSet(object.operator) ? globalThis.String(object.operator) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.baseUrl !== "") {
            obj.baseUrl = message.baseUrl;
        }
        if (message.hashAlgorithm !== 0) {
            obj.hashAlgorithm = (0, sigstore_common_1.hashAlgorithmToJSON)(message.hashAlgorithm);
        }
        if (message.publicKey !== undefined) {
            obj.publicKey = sigstore_common_1.PublicKey.toJSON(message.publicKey);
        }
        if (message.logId !== undefined) {
            obj.logId = sigstore_common_1.LogId.toJSON(message.logId);
        }
        if (message.checkpointKeyId !== undefined) {
            obj.checkpointKeyId = sigstore_common_1.LogId.toJSON(message.checkpointKeyId);
        }
        if (message.operator !== "") {
            obj.operator = message.operator;
        }
        return obj;
    },
};
exports.CertificateAuthority = {
    fromJSON(object) {
        return {
            subject: isSet(object.subject) ? sigstore_common_1.DistinguishedName.fromJSON(object.subject) : undefined,
            uri: isSet(object.uri) ? globalThis.String(object.uri) : "",
            certChain: isSet(object.certChain) ? sigstore_common_1.X509CertificateChain.fromJSON(object.certChain) : undefined,
            validFor: isSet(object.validFor) ? sigstore_common_1.TimeRange.fromJSON(object.validFor) : undefined,
            operator: isSet(object.operator) ? globalThis.String(object.operator) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.subject !== undefined) {
            obj.subject = sigstore_common_1.DistinguishedName.toJSON(message.subject);
        }
        if (message.uri !== "") {
            obj.uri = message.uri;
        }
        if (message.certChain !== undefined) {
            obj.certChain = sigstore_common_1.X509CertificateChain.toJSON(message.certChain);
        }
        if (message.validFor !== undefined) {
            obj.validFor = sigstore_common_1.TimeRange.toJSON(message.validFor);
        }
        if (message.operator !== "") {
            obj.operator = message.operator;
        }
        return obj;
    },
};
exports.TrustedRoot = {
    fromJSON(object) {
        return {
            mediaType: isSet(object.mediaType) ? globalThis.String(object.mediaType) : "",
            tlogs: globalThis.Array.isArray(object?.tlogs)
                ? object.tlogs.map((e) => exports.TransparencyLogInstance.fromJSON(e))
                : [],
            certificateAuthorities: globalThis.Array.isArray(object?.certificateAuthorities)
                ? object.certificateAuthorities.map((e) => exports.CertificateAuthority.fromJSON(e))
                : [],
            ctlogs: globalThis.Array.isArray(object?.ctlogs)
                ? object.ctlogs.map((e) => exports.TransparencyLogInstance.fromJSON(e))
                : [],
            timestampAuthorities: globalThis.Array.isArray(object?.timestampAuthorities)
                ? object.timestampAuthorities.map((e) => exports.CertificateAuthority.fromJSON(e))
                : [],
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.mediaType !== "") {
            obj.mediaType = message.mediaType;
        }
        if (message.tlogs?.length) {
            obj.tlogs = message.tlogs.map((e) => exports.TransparencyLogInstance.toJSON(e));
        }
        if (message.certificateAuthorities?.length) {
            obj.certificateAuthorities = message.certificateAuthorities.map((e) => exports.CertificateAuthority.toJSON(e));
        }
        if (message.ctlogs?.length) {
            obj.ctlogs = message.ctlogs.map((e) => exports.TransparencyLogInstance.toJSON(e));
        }
        if (message.timestampAuthorities?.length) {
            obj.timestampAuthorities = message.timestampAuthorities.map((e) => exports.CertificateAuthority.toJSON(e));
        }
        return obj;
    },
};
exports.SigningConfig = {
    fromJSON(object) {
        return {
            mediaType: isSet(object.mediaType) ? globalThis.String(object.mediaType) : "",
            caUrls: globalThis.Array.isArray(object?.caUrls) ? object.caUrls.map((e) => exports.Service.fromJSON(e)) : [],
            oidcUrls: globalThis.Array.isArray(object?.oidcUrls) ? object.oidcUrls.map((e) => exports.Service.fromJSON(e)) : [],
            rekorTlogUrls: globalThis.Array.isArray(object?.rekorTlogUrls)
                ? object.rekorTlogUrls.map((e) => exports.Service.fromJSON(e))
                : [],
            rekorTlogConfig: isSet(object.rekorTlogConfig)
                ? exports.ServiceConfiguration.fromJSON(object.rekorTlogConfig)
                : undefined,
            tsaUrls: globalThis.Array.isArray(object?.tsaUrls) ? object.tsaUrls.map((e) => exports.Service.fromJSON(e)) : [],
            tsaConfig: isSet(object.tsaConfig) ? exports.ServiceConfiguration.fromJSON(object.tsaConfig) : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.mediaType !== "") {
            obj.mediaType = message.mediaType;
        }
        if (message.caUrls?.length) {
            obj.caUrls = message.caUrls.map((e) => exports.Service.toJSON(e));
        }
        if (message.oidcUrls?.length) {
            obj.oidcUrls = message.oidcUrls.map((e) => exports.Service.toJSON(e));
        }
        if (message.rekorTlogUrls?.length) {
            obj.rekorTlogUrls = message.rekorTlogUrls.map((e) => exports.Service.toJSON(e));
        }
        if (message.rekorTlogConfig !== undefined) {
            obj.rekorTlogConfig = exports.ServiceConfiguration.toJSON(message.rekorTlogConfig);
        }
        if (message.tsaUrls?.length) {
            obj.tsaUrls = message.tsaUrls.map((e) => exports.Service.toJSON(e));
        }
        if (message.tsaConfig !== undefined) {
            obj.tsaConfig = exports.ServiceConfiguration.toJSON(message.tsaConfig);
        }
        return obj;
    },
};
exports.Service = {
    fromJSON(object) {
        return {
            url: isSet(object.url) ? globalThis.String(object.url) : "",
            majorApiVersion: isSet(object.majorApiVersion) ? globalThis.Number(object.majorApiVersion) : 0,
            validFor: isSet(object.validFor) ? sigstore_common_1.TimeRange.fromJSON(object.validFor) : undefined,
            operator: isSet(object.operator) ? globalThis.String(object.operator) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.url !== "") {
            obj.url = message.url;
        }
        if (message.majorApiVersion !== 0) {
            obj.majorApiVersion = Math.round(message.majorApiVersion);
        }
        if (message.validFor !== undefined) {
            obj.validFor = sigstore_common_1.TimeRange.toJSON(message.validFor);
        }
        if (message.operator !== "") {
            obj.operator = message.operator;
        }
        return obj;
    },
};
exports.ServiceConfiguration = {
    fromJSON(object) {
        return {
            selector: isSet(object.selector) ? serviceSelectorFromJSON(object.selector) : 0,
            count: isSet(object.count) ? globalThis.Number(object.count) : 0,
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.selector !== 0) {
            obj.selector = serviceSelectorToJSON(message.selector);
        }
        if (message.count !== 0) {
            obj.count = Math.round(message.count);
        }
        return obj;
    },
};
exports.ClientTrustConfig = {
    fromJSON(object) {
        return {
            mediaType: isSet(object.mediaType) ? globalThis.String(object.mediaType) : "",
            trustedRoot: isSet(object.trustedRoot) ? exports.TrustedRoot.fromJSON(object.trustedRoot) : undefined,
            signingConfig: isSet(object.signingConfig) ? exports.SigningConfig.fromJSON(object.signingConfig) : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.mediaType !== "") {
            obj.mediaType = message.mediaType;
        }
        if (message.trustedRoot !== undefined) {
            obj.trustedRoot = exports.TrustedRoot.toJSON(message.trustedRoot);
        }
        if (message.signingConfig !== undefined) {
            obj.signingConfig = exports.SigningConfig.toJSON(message.signingConfig);
        }
        return obj;
    },
};
function isSet(value) {
    return value !== null && value !== undefined;
}
