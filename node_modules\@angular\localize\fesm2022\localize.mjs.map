{"version": 3, "file": "localize.mjs", "sources": ["../../../../../darwin_arm64-fastbuild-ST-2d99d9656325/bin/packages/localize/src/utils/src/translations.ts", "../../../../../darwin_arm64-fastbuild-ST-2d99d9656325/bin/packages/localize/src/translate.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\nimport {BLOCK_MARKER} from './constants';\nimport {MessageId, MessageMetadata, ParsedMessage, parseMessage, TargetMessage} from './messages';\n\n/**\n * A translation message that has been processed to extract the message parts and placeholders.\n */\nexport interface ParsedTranslation extends MessageMetadata {\n  messageParts: TemplateStringsArray;\n  placeholderNames: string[];\n}\n\n/**\n * The internal structure used by the runtime localization to translate messages.\n */\nexport type ParsedTranslations = Record<MessageId, ParsedTranslation>;\n\nexport class MissingTranslationError extends Error {\n  private readonly type = 'MissingTranslationError';\n  constructor(readonly parsedMessage: ParsedMessage) {\n    super(`No translation found for ${describeMessage(parsedMessage)}.`);\n  }\n}\n\nexport function isMissingTranslationError(e: any): e is MissingTranslationError {\n  return e.type === 'MissingTranslationError';\n}\n\n/**\n * Translate the text of the `$localize` tagged-string (i.e. `messageParts` and\n * `substitutions`) using the given `translations`.\n *\n * The tagged-string is parsed to extract its `messageId` which is used to find an appropriate\n * `ParsedTranslation`. If this doesn't match and there are legacy ids then try matching a\n * translation using those.\n *\n * If one is found then it is used to translate the message into a new set of `messageParts` and\n * `substitutions`.\n * The translation may reorder (or remove) substitutions as appropriate.\n *\n * If there is no translation with a matching message id then an error is thrown.\n * If a translation contains a placeholder that is not found in the message being translated then an\n * error is thrown.\n */\nexport function translate(\n  translations: Record<string, ParsedTranslation>,\n  messageParts: TemplateStringsArray,\n  substitutions: readonly any[],\n): [TemplateStringsArray, readonly any[]] {\n  const message = parseMessage(messageParts, substitutions);\n  // Look up the translation using the messageId, and then the legacyId if available.\n  let translation = translations[message.id];\n  // If the messageId did not match a translation, try matching the legacy ids instead\n  if (message.legacyIds !== undefined) {\n    for (let i = 0; i < message.legacyIds.length && translation === undefined; i++) {\n      translation = translations[message.legacyIds[i]];\n    }\n  }\n  if (translation === undefined) {\n    throw new MissingTranslationError(message);\n  }\n  return [\n    translation.messageParts,\n    translation.placeholderNames.map((placeholder) => {\n      if (message.substitutions.hasOwnProperty(placeholder)) {\n        return message.substitutions[placeholder];\n      } else {\n        throw new Error(\n          `There is a placeholder name mismatch with the translation provided for the message ${describeMessage(\n            message,\n          )}.\\n` +\n            `The translation contains a placeholder with name ${placeholder}, which does not exist in the message.`,\n        );\n      }\n    }),\n  ];\n}\n\n/**\n * Parse the `messageParts` and `placeholderNames` out of a target `message`.\n *\n * Used by `loadTranslations()` to convert target message strings into a structure that is more\n * appropriate for doing translation.\n *\n * @param message the message to be parsed.\n */\nexport function parseTranslation(messageString: TargetMessage): ParsedTranslation {\n  const parts = messageString.split(/{\\$([^}]*)}/);\n  const messageParts = [parts[0]];\n  const placeholderNames: string[] = [];\n  for (let i = 1; i < parts.length - 1; i += 2) {\n    placeholderNames.push(parts[i]);\n    messageParts.push(`${parts[i + 1]}`);\n  }\n  const rawMessageParts = messageParts.map((part) =>\n    part.charAt(0) === BLOCK_MARKER ? '\\\\' + part : part,\n  );\n  return {\n    text: messageString,\n    messageParts: makeTemplateObject(messageParts, rawMessageParts),\n    placeholderNames,\n  };\n}\n\n/**\n * Create a `ParsedTranslation` from a set of `messageParts` and `placeholderNames`.\n *\n * @param messageParts The message parts to appear in the ParsedTranslation.\n * @param placeholderNames The names of the placeholders to intersperse between the `messageParts`.\n */\nexport function makeParsedTranslation(\n  messageParts: string[],\n  placeholderNames: string[] = [],\n): ParsedTranslation {\n  let messageString = messageParts[0];\n  for (let i = 0; i < placeholderNames.length; i++) {\n    messageString += `{$${placeholderNames[i]}}${messageParts[i + 1]}`;\n  }\n  return {\n    text: messageString,\n    messageParts: makeTemplateObject(messageParts, messageParts),\n    placeholderNames,\n  };\n}\n\n/**\n * Create the specialized array that is passed to tagged-string tag functions.\n *\n * @param cooked The message parts with their escape codes processed.\n * @param raw The message parts with their escaped codes as-is.\n */\nexport function makeTemplateObject(cooked: string[], raw: string[]): TemplateStringsArray {\n  Object.defineProperty(cooked, 'raw', {value: raw});\n  return cooked as any;\n}\n\nfunction describeMessage(message: ParsedMessage): string {\n  const meaningString = message.meaning && ` - \"${message.meaning}\"`;\n  const legacy =\n    message.legacyIds && message.legacyIds.length > 0\n      ? ` [${message.legacyIds.map((l) => `\"${l}\"`).join(', ')}]`\n      : '';\n  return `\"${message.id}\"${legacy} (\"${message.text}\"${meaningString})`;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\nimport {LocalizeFn} from './localize';\nimport {\n  MessageId,\n  ParsedTranslation,\n  parseTranslation,\n  TargetMessage,\n  translate as _translate,\n} from './utils';\n\n/**\n * We augment the `$localize` object to also store the translations.\n *\n * Note that because the TRANSLATIONS are attached to a global object, they will be shared between\n * all applications that are running in a single page of the browser.\n */\ndeclare const $localize: LocalizeFn & {TRANSLATIONS: Record<MessageId, ParsedTranslation>};\n\n/**\n * Load translations for use by `$localize`, if doing runtime translation.\n *\n * If the `$localize` tagged strings are not going to be replaced at compiled time, it is possible\n * to load a set of translations that will be applied to the `$localize` tagged strings at runtime,\n * in the browser.\n *\n * Loading a new translation will overwrite a previous translation if it has the same `MessageId`.\n *\n * Note that `$localize` messages are only processed once, when the tagged string is first\n * encountered, and does not provide dynamic language changing without refreshing the browser.\n * Loading new translations later in the application life-cycle will not change the translated text\n * of messages that have already been translated.\n *\n * The message IDs and translations are in the same format as that rendered to \"simple JSON\"\n * translation files when extracting messages. In particular, placeholders in messages are rendered\n * using the `{$PLACEHOLDER_NAME}` syntax. For example the message from the following template:\n *\n * ```html\n * <div i18n>pre<span>inner-pre<b>bold</b>inner-post</span>post</div>\n * ```\n *\n * would have the following form in the `translations` map:\n *\n * ```ts\n * {\n *   \"2932901491976224757\":\n *      \"pre{$START_TAG_SPAN}inner-pre{$START_BOLD_TEXT}bold{$CLOSE_BOLD_TEXT}inner-post{$CLOSE_TAG_SPAN}post\"\n * }\n * ```\n *\n * @param translations A map from message ID to translated message.\n *\n * These messages are processed and added to a lookup based on their `MessageId`.\n *\n * @see {@link clearTranslations} for removing translations loaded using this function.\n * @see {@link /api/localize/init/$localize $localize} for tagging messages as needing to be translated.\n * @publicApi\n */\nexport function loadTranslations(translations: Record<MessageId, TargetMessage>) {\n  // Ensure the translate function exists\n  if (!$localize.translate) {\n    $localize.translate = translate;\n  }\n  if (!$localize.TRANSLATIONS) {\n    $localize.TRANSLATIONS = {};\n  }\n  Object.keys(translations).forEach((key) => {\n    $localize.TRANSLATIONS[key] = parseTranslation(translations[key]);\n  });\n}\n\n/**\n * Remove all translations for `$localize`, if doing runtime translation.\n *\n * All translations that had been loading into memory using `loadTranslations()` will be removed.\n *\n * @see {@link loadTranslations} for loading translations at runtime.\n * @see {@link /api/localize/init/$localize $localize} for tagging messages as needing to be translated.\n *\n * @publicApi\n */\nexport function clearTranslations() {\n  $localize.translate = undefined;\n  $localize.TRANSLATIONS = {};\n}\n\n/**\n * Translate the text of the given message, using the loaded translations.\n *\n * This function may reorder (or remove) substitutions as indicated in the matching translation.\n */\nexport function translate(\n  messageParts: TemplateStringsArray,\n  substitutions: readonly any[],\n): [TemplateStringsArray, readonly any[]] {\n  try {\n    return _translate($localize.TRANSLATIONS, messageParts, substitutions);\n  } catch (e) {\n    console.warn((e as Error).message);\n    return [messageParts, substitutions];\n  }\n}\n"], "names": ["translate", "_translate"], "mappings": ";;;;;;;;;AAuBM,MAAO,uBAAwB,SAAQ,KAAK,CAAA;AAE3B,IAAA,aAAA;IADJ,IAAI,GAAG,yBAAyB;AACjD,IAAA,WAAA,CAAqB,aAA4B,EAAA;QAC/C,KAAK,CAAC,4BAA4B,eAAe,CAAC,aAAa,CAAC,CAAA,CAAA,CAAG,CAAC;QADjD,IAAa,CAAA,aAAA,GAAb,aAAa;;AAGnC;AAEK,SAAU,yBAAyB,CAAC,CAAM,EAAA;AAC9C,IAAA,OAAO,CAAC,CAAC,IAAI,KAAK,yBAAyB;AAC7C;AAEA;;;;;;;;;;;;;;;AAeG;SACaA,WAAS,CACvB,YAA+C,EAC/C,YAAkC,EAClC,aAA6B,EAAA;IAE7B,MAAM,OAAO,GAAG,YAAY,CAAC,YAAY,EAAE,aAAa,CAAC;;IAEzD,IAAI,WAAW,GAAG,YAAY,CAAC,OAAO,CAAC,EAAE,CAAC;;AAE1C,IAAA,IAAI,OAAO,CAAC,SAAS,KAAK,SAAS,EAAE;QACnC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,SAAS,CAAC,MAAM,IAAI,WAAW,KAAK,SAAS,EAAE,CAAC,EAAE,EAAE;YAC9E,WAAW,GAAG,YAAY,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;;;AAGpD,IAAA,IAAI,WAAW,KAAK,SAAS,EAAE;AAC7B,QAAA,MAAM,IAAI,uBAAuB,CAAC,OAAO,CAAC;;IAE5C,OAAO;AACL,QAAA,WAAW,CAAC,YAAY;QACxB,WAAW,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,WAAW,KAAI;YAC/C,IAAI,OAAO,CAAC,aAAa,CAAC,cAAc,CAAC,WAAW,CAAC,EAAE;AACrD,gBAAA,OAAO,OAAO,CAAC,aAAa,CAAC,WAAW,CAAC;;iBACpC;gBACL,MAAM,IAAI,KAAK,CACb,CAAA,mFAAA,EAAsF,eAAe,CACnG,OAAO,CACR,CAAK,GAAA,CAAA;oBACJ,CAAoD,iDAAA,EAAA,WAAW,CAAwC,sCAAA,CAAA,CAC1G;;AAEL,SAAC,CAAC;KACH;AACH;AAEA;;;;;;;AAOG;AACG,SAAU,gBAAgB,CAAC,aAA4B,EAAA;IAC3D,MAAM,KAAK,GAAG,aAAa,CAAC,KAAK,CAAC,aAAa,CAAC;IAChD,MAAM,YAAY,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IAC/B,MAAM,gBAAgB,GAAa,EAAE;AACrC,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE;QAC5C,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAC/B,QAAA,YAAY,CAAC,IAAI,CAAC,CAAA,EAAG,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAE,CAAA,CAAC;;AAEtC,IAAA,MAAM,eAAe,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC,IAAI,KAC5C,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,YAAY,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,CACrD;IACD,OAAO;AACL,QAAA,IAAI,EAAE,aAAa;AACnB,QAAA,YAAY,EAAE,kBAAkB,CAAC,YAAY,EAAE,eAAe,CAAC;QAC/D,gBAAgB;KACjB;AACH;AAEA;;;;;AAKG;SACa,qBAAqB,CACnC,YAAsB,EACtB,mBAA6B,EAAE,EAAA;AAE/B,IAAA,IAAI,aAAa,GAAG,YAAY,CAAC,CAAC,CAAC;AACnC,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,gBAAgB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAChD,QAAA,aAAa,IAAI,CAAA,EAAA,EAAK,gBAAgB,CAAC,CAAC,CAAC,CAAA,CAAA,EAAI,YAAY,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE;;IAEpE,OAAO;AACL,QAAA,IAAI,EAAE,aAAa;AACnB,QAAA,YAAY,EAAE,kBAAkB,CAAC,YAAY,EAAE,YAAY,CAAC;QAC5D,gBAAgB;KACjB;AACH;AAEA;;;;;AAKG;AACa,SAAA,kBAAkB,CAAC,MAAgB,EAAE,GAAa,EAAA;AAChE,IAAA,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,KAAK,EAAE,EAAC,KAAK,EAAE,GAAG,EAAC,CAAC;AAClD,IAAA,OAAO,MAAa;AACtB;AAEA,SAAS,eAAe,CAAC,OAAsB,EAAA;IAC7C,MAAM,aAAa,GAAG,OAAO,CAAC,OAAO,IAAI,CAAA,IAAA,EAAO,OAAO,CAAC,OAAO,CAAA,CAAA,CAAG;AAClE,IAAA,MAAM,MAAM,GACV,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC,SAAS,CAAC,MAAM,GAAG;UAC5C,KAAK,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAA,CAAA,EAAI,CAAC,CAAG,CAAA,CAAA,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAG,CAAA;UACzD,EAAE;AACR,IAAA,OAAO,CAAI,CAAA,EAAA,OAAO,CAAC,EAAE,CAAI,CAAA,EAAA,MAAM,CAAM,GAAA,EAAA,OAAO,CAAC,IAAI,CAAI,CAAA,EAAA,aAAa,GAAG;AACvE;;AC7HA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsCG;AACG,SAAU,gBAAgB,CAAC,YAA8C,EAAA;;AAE7E,IAAA,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE;AACxB,QAAA,SAAS,CAAC,SAAS,GAAG,SAAS;;AAEjC,IAAA,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE;AAC3B,QAAA,SAAS,CAAC,YAAY,GAAG,EAAE;;IAE7B,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC,CAAC,GAAG,KAAI;AACxC,QAAA,SAAS,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,gBAAgB,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;AACnE,KAAC,CAAC;AACJ;AAEA;;;;;;;;;AASG;SACa,iBAAiB,GAAA;AAC/B,IAAA,SAAS,CAAC,SAAS,GAAG,SAAS;AAC/B,IAAA,SAAS,CAAC,YAAY,GAAG,EAAE;AAC7B;AAEA;;;;AAIG;AACa,SAAA,SAAS,CACvB,YAAkC,EAClC,aAA6B,EAAA;AAE7B,IAAA,IAAI;QACF,OAAOC,WAAU,CAAC,SAAS,CAAC,YAAY,EAAE,YAAY,EAAE,aAAa,CAAC;;IACtE,OAAO,CAAC,EAAE;AACV,QAAA,OAAO,CAAC,IAAI,CAAE,CAAW,CAAC,OAAO,CAAC;AAClC,QAAA,OAAO,CAAC,YAAY,EAAE,aAAa,CAAC;;AAExC;;;;"}